{"name": "digital-realtor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build && node scripts/generate-csp.js", "build:prod": "node build-prod.js", "build:netlify": "npx tsc -p tsconfig.prod.json && vite build --outDir prod-build && node scripts/generate-csp.js", "build:netlify:full": "node build-netlify.js", "build:netlify:simple": "node netlify-build.js", "build:netlify:prod": "node netlify-prod-build.js", "build:netlify:static": "node netlify-static-build.js", "build:netlify:full-static": "node netlify-full-static-build.js", "build:netlify:react": "node netlify-react-build.js", "lint": "eslint .", "preview": "vite preview", "preview:netlify": "vite preview --outDir prod-build", "server": "node server/server.js", "server:dev": "nodemon server/server.js", "start": "node server/server.js", "data:import": "node server/seeder.js", "data:destroy": "node server/seeder.js -d", "dev:full": "concurrently \"npm run server:dev\" \"npm run dev\"", "test:db": "node server/testConnection.js", "test:zillow": "node backend/scripts/test-zillow-api.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@hookform/resolvers": "^5.0.1", "@react-oauth/google": "^0.12.1", "@types/react-calendar": "^3.9.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.1.5", "gapi-script": "^1.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "nodemailer": "^6.10.1", "openai": "^4.28.0", "pdf-lib": "^1.17.1", "react": "^19.0.0", "react-big-calendar": "^1.18.0", "react-calendar": "^5.1.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-microsoft-clarity": "^2.0.0", "react-router-dom": "^7.5.0", "react-to-pdf": "^2.0.0", "react-toastify": "^10.0.4", "remark-gfm": "^4.0.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "babel-plugin-transform-remove-console": "^6.9.4", "concurrently": "^9.1.2", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "nodemon": "^3.1.9", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "terser": "^5.39.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "engines": {"node": "20.11.1", "npm": "10.8.2"}}