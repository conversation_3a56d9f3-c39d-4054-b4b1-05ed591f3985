{"name": "digital-realtor-server", "version": "1.0.0", "description": "Server for Digital Realtor application", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.1.5", "mongoose": "^8.13.2", "openai": "^4.28.0", "pdf-lib": "^1.17.1"}}