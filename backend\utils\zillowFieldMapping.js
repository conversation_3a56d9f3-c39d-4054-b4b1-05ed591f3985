/**
 * Utility functions for mapping Zillow API response fields to UI form fields
 */

/**
 * Helper function to parse a string to a number, returning null if invalid
 * @param {any} value - The value to parse
 * @returns {number|null} The parsed number or null
 */
function parseNumberOrNull(value) {
  if (value === null || value === undefined) return null;

  // If it's already a number, return it
  if (typeof value === 'number') return value;

  // If it's a string, try to parse it
  if (typeof value === 'string') {
    // Remove any non-numeric characters except decimal point
    const cleanedValue = value.replace(/[^0-9.]/g, '');
    const parsed = parseFloat(cleanedValue);
    return isNaN(parsed) ? null : parsed;
  }

  return null;
}

/**
 * Helper function to clean HOA fee values
 * @param {any} value - The HOA fee value to clean
 * @returns {string|number|null} The cleaned HOA fee value
 */
function cleanHOA(value) {
  if (value === null || value === undefined) return null;

  console.log('Cleaning HOA fee:', { value, type: typeof value });

  // If it's a number, return it
  if (typeof value === 'number') return value;

  // If it's a string, clean it
  if (typeof value === 'string') {
    // Extract just the numeric part for consistent processing
    const numericPart = value
      .replace(/\$/g, '')
      .replace(/monthly/i, '')
      .replace(/per month/i, '')
      .replace(/month/i, '')
      .replace(/,/g, '')
      .trim();

    // Try to parse as a number
    const parsed = parseFloat(numericPart);
    if (!isNaN(parsed)) {
      return parsed;
    }

    // If we couldn't parse it, return the cleaned string
    return numericPart;
  }

  return null;
}

/**
 * Helper function to extract a fact from the atAGlanceFacts array
 * @param {Array} facts - The atAGlanceFacts array
 * @param {string} label - The fact label to search for
 * @returns {string|null} The fact value or null
 */
function getFact(facts, label) {
  const fact = facts.find(f =>
    f?.factLabel?.toLowerCase().includes(label.toLowerCase())
  );
  return fact?.factValue ?? null;
}

/**
 * Parse Zillow property details from API response
 * @param {Object} response - The Zillow API response
 * @returns {Object} Mapped property details
 */
function parseZillowPropertyDetails(response) {
  // Handle null or undefined response
  if (!response) {
    return {
      bedrooms: null,
      bathrooms: null,
      squareFeet: null,
      yearBuilt: null,
      parking: null,
      hoaFee: null,
      propertyType: null,
      lotSize: null,
      price: null,
      pricePerSqFt: null,
      address: null,
      city: null,
      state: null,
      zip: null,
      mlsId: null,
      appliances: null,
      features: null
    };
  }

  // Extract resoFacts for nested properties
  const resoFacts = response.resoFacts || {};

  // Extract atAGlanceFacts array for fact-based properties
  const facts = resoFacts.atAGlanceFacts || [];

  // Log the structure for debugging
  console.log('Parsing Zillow response:', {
    hasResoFacts: !!response.resoFacts,
    hasAtAGlanceFacts: facts.length > 0,
    factLabels: facts.map((f) => f.factLabel)
  });

  // Extract address components with multiple fallback options
  const addressObj = response.address || {};

  // Log address components for debugging
  console.log('Address components:', {
    addressObj,
    fullAddress: response.fullAddress || response.address?.fullAddress,
    streetAddress: response.streetAddress || addressObj.streetAddress,
    city: response.city || addressObj.city,
    state: response.state || addressObj.state,
    zipcode: response.zipcode || addressObj.zipcode,
    formattedAddress: response.formattedAddress,
    // Additional fields from the example JSON
    address_object: response.address,
    address_structure: JSON.stringify(response.address),
    has_address: !!response.address
  });

  // Log the entire response structure for debugging
  console.log('Full Zillow response structure:', {
    keys: Object.keys(response),
    hasAddress: !!response.address,
    addressType: response.address ? typeof response.address : 'undefined',
    addressKeys: response.address ? Object.keys(response.address) : []
  });

  // Extract address from various possible locations in the response
  // Based on the example JSON, we need to handle the specific structure
  let extractedAddress = {
    streetAddress: null,
    city: null,
    state: null,
    zip: null
  };

  // First try to extract from the address object directly
  if (response.address && typeof response.address === 'object') {
    // The example JSON has address.streetAddress, address.city, address.state, address.zipcode
    extractedAddress = {
      streetAddress: response.address.streetAddress || addressObj.streetAddress || response.streetAddress,
      city: response.address.city || addressObj.city || response.city,
      state: response.address.state || addressObj.state || response.state,
      zip: response.address.zipcode || addressObj.zipcode || response.zipcode
    };

    console.log('Extracted from address object:', extractedAddress);
  }
  // If we couldn't extract from address object, try other methods
  else {
    extractedAddress = {
      streetAddress: addressObj.streetAddress || response.streetAddress ||
                    (response.fullAddress ? response.fullAddress.split(',')[0] : null) ||
                    (response.formattedAddress ? response.formattedAddress.split(',')[0] : null),

      city: addressObj.city || response.city ||
           (addressObj.fullAddress ? addressObj.fullAddress.split(',')[1]?.trim() : null) ||
           (response.fullAddress ? response.fullAddress.split(',')[1]?.trim() : null),

      state: addressObj.state || response.state ||
            (addressObj.fullAddress ? addressObj.fullAddress.split(',')[2]?.trim()?.split(' ')[0] : null) ||
            (response.fullAddress ? response.fullAddress.split(',')[2]?.trim()?.split(' ')[0] : null),

      zip: addressObj.zipcode || response.zipcode ||
          (addressObj.fullAddress ? addressObj.fullAddress.split(',')[2]?.trim()?.split(' ')[1] : null) ||
          (response.fullAddress ? response.fullAddress.split(',')[2]?.trim()?.split(' ')[1] : null)
    };

    console.log('Extracted using fallback methods:', extractedAddress);
  }

  // Additional fallback for direct top-level properties
  if (!extractedAddress.streetAddress && response.streetAddress) {
    extractedAddress.streetAddress = response.streetAddress;
  }

  if (!extractedAddress.city && response.city) {
    extractedAddress.city = response.city;
  }

  if (!extractedAddress.state && response.state) {
    extractedAddress.state = response.state;
  }

  if (!extractedAddress.zip && (response.zipcode || response.zip)) {
    extractedAddress.zip = response.zipcode || response.zip;
  }

  // Log the final extracted address
  console.log('Final extracted address before coordinate fallback:', extractedAddress);

  // If we have coordinates but no address, create a placeholder
  if (!extractedAddress.streetAddress && response.latitude && response.longitude) {
    console.log('Could not extract address components, falling back to coordinates');
    extractedAddress.streetAddress = `Property at ${response.latitude.toFixed(6)}, ${response.longitude.toFixed(6)}`;
  }

  // Final check - if we have the example JSON structure with address components
  // This is a specific check for the format in the example JSON
  if (!extractedAddress.streetAddress &&
      response.address &&
      typeof response.address === 'object' &&
      'streetAddress' in response.address) {
    console.log('Found address in nested object structure, using it directly');
    extractedAddress.streetAddress = response.address.streetAddress;
    extractedAddress.city = response.address.city;
    extractedAddress.state = response.address.state;
    extractedAddress.zip = response.address.zipcode;
  }

  // Log the specific fields we're looking for according to the instructions
  console.log('Zillow field mapping - specific fields:', {
    bedrooms: {
      fromResponse: response.bedrooms,
      fromResoFacts: resoFacts.bedrooms,
      final: response.bedrooms ?? resoFacts.bedrooms
    },
    bathrooms: {
      fromResponse: response.bathrooms,
      fromResoFactsFull: resoFacts.bathroomsFull,
      final: response.bathrooms ?? resoFacts.bathroomsFull
    },
    squareFeet: {
      fromLivingArea: response.livingArea,
      fromLivingAreaValue: response.livingAreaValue,
      fromResoFacts: resoFacts.livingArea ?
        typeof resoFacts.livingArea === 'string' ?
          parseInt(resoFacts.livingArea.replace(/[^\d]/g, '')) :
          resoFacts.livingArea :
        null,
      final: response.livingArea ?? response.livingAreaValue ??
        (resoFacts.livingArea ?
          typeof resoFacts.livingArea === 'string' ?
            parseInt(resoFacts.livingArea.replace(/[^\d]/g, '')) :
            resoFacts.livingArea :
          null)
    },
    yearBuilt: {
      fromResponse: response.yearBuilt,
      fromResoFacts: resoFacts.yearBuilt,
      final: response.yearBuilt ?? resoFacts.yearBuilt
    },
    parking: {
      fromResoFacts: resoFacts.parkingFeatures,
      fromFacts: getFact(facts, 'parking'),
      final: Array.isArray(resoFacts.parkingFeatures) ?
        resoFacts.parkingFeatures.join(', ') :
        (resoFacts.parkingFeatures || getFact(facts, 'parking') || 'N/A')
    },
    hoaFee: {
      fromMonthlyHoaFee: response.monthlyHoaFee,
      fromResoFacts: resoFacts.hoaFee,
      fromFacts: getFact(facts, 'hoa'),
      fromHoaFeeTotal: resoFacts.hoaFeeTotal,
      final: response.monthlyHoaFee ?? resoFacts.hoaFee ?? getFact(facts, 'hoa') ?? resoFacts.hoaFeeTotal
    }
  });

  // Parse the response
  return {
    // Basic property details with fallbacks - updated according to instructions
    bedrooms: parseNumberOrNull(response.bedrooms ?? resoFacts.bedrooms),
    bathrooms: parseNumberOrNull(response.bathrooms ?? resoFacts.bathroomsFull ?? response.bathrooms ?? resoFacts.bathrooms),
    squareFeet: parseNumberOrNull(
      response.livingArea ??
      response.livingAreaValue ??
      (resoFacts.livingArea ?
        typeof resoFacts.livingArea === 'string' ?
          parseInt(resoFacts.livingArea.replace(/[^\d]/g, '')) :
          resoFacts.livingArea :
        null)
    ),
    yearBuilt: parseNumberOrNull(response.yearBuilt ?? resoFacts.yearBuilt),

    // Fact-based properties - updated according to instructions
    parking: Array.isArray(resoFacts.parkingFeatures) ?
      resoFacts.parkingFeatures.join(', ') :
      (resoFacts.parkingFeatures || getFact(facts, 'parking') || 'N/A'),
    hoaFee: cleanHOA(response.monthlyHoaFee ?? resoFacts.hoaFee ?? getFact(facts, 'hoa') ?? resoFacts.hoaFeeTotal),

    // Additional properties
    propertyType: response.homeType || response.propertyTypeDimension || resoFacts.propertyType || null,
    lotSize: response.lotSize || resoFacts.lotSize || null,
    price: parseNumberOrNull(response.price || response.zestimate),
    pricePerSqFt: parseNumberOrNull(response.pricePerSquareFoot || response.pricePerSqFt),

    // Address information with fallbacks
    address: extractedAddress.streetAddress,
    city: extractedAddress.city,
    state: extractedAddress.state,
    zip: extractedAddress.zip,

    // Additional data
    mlsId: response.mlsid || response.mlsId || null,
    appliances: Array.isArray(resoFacts.appliances)
      ? resoFacts.appliances
      : (Array.isArray(response.appliances) ? response.appliances : null),
    features: Array.isArray(resoFacts.features)
      ? resoFacts.features
      : (Array.isArray(response.features) ? response.features : null)
  };
}

/**
 * Validate the mapped Zillow property details
 * @param {Object} details - The mapped property details
 * @returns {Object} Validation result
 */
function validateZillowPropertyDetails(details) {
  const issues = [];

  // Validate bedrooms
  if (details.bedrooms !== null && (details.bedrooms < 0 || !Number.isInteger(details.bedrooms))) {
    issues.push('Bedrooms must be a non-negative integer');
  }

  // Validate bathrooms
  if (details.bathrooms !== null && details.bathrooms < 0) {
    issues.push('Bathrooms must be a non-negative number');
  }

  // Validate square feet
  if (details.squareFeet !== null && details.squareFeet <= 0) {
    issues.push('Square feet must be a positive number');
  }

  // Validate year built
  if (details.yearBuilt !== null) {
    const currentYear = new Date().getFullYear();
    if (details.yearBuilt < 1800 || details.yearBuilt > currentYear + 1) {
      issues.push(`Year built must be between 1800 and ${currentYear + 1}`);
    }
  }

  // Validate price
  if (details.price !== null && details.price <= 0) {
    issues.push('Price must be a positive number');
  }

  // Validate price per square foot
  if (details.pricePerSqFt !== null && details.pricePerSqFt <= 0) {
    issues.push('Price per square foot must be a positive number');
  }

  return {
    valid: issues.length === 0,
    issues
  };
}

/**
 * Convert the mapped Zillow property details to form field values
 * @param {Object} details - The mapped property details
 * @returns {Object} Form field values
 */
function zillowDetailsToFormFields(details) {
  // Create a properly formatted location string
  let location = '';

  // If we have an address, use it
  if (details.address) {
    const addressParts = [details.address];

    // Add city, state, zip if available
    if (details.city || details.state || details.zip) {
      const cityStateZip = [details.city, details.state, details.zip]
        .filter(Boolean)
        .join(', ');

      if (cityStateZip) {
        addressParts.push(cityStateZip);
      }
    }

    location = addressParts.join(', ');
  }

  // If we couldn't create a location string, check if there's a fallback in the address field
  if (!location && details.address && details.address.includes('Property at')) {
    location = details.address;
  }

  // Log the location creation process
  console.log('Creating location field from details:', {
    address: details.address,
    city: details.city,
    state: details.state,
    zip: details.zip,
    result: location,
    isCoordinateBased: details.address && details.address.includes('Property at')
  });

  // Format HOA fee as $X/month
  const formattedHoaFee = details.hoaFee ?
    typeof details.hoaFee === 'number' ?
      `$${details.hoaFee}/month` :
      details.hoaFee.toString().startsWith('$') ?
        details.hoaFee.toString().includes('/month') ?
          details.hoaFee.toString() :
          `${details.hoaFee.toString()}/month` :
        `$${details.hoaFee.toString()}/month` :
    '';

  console.log('Formatting HOA fee:', {
    original: details.hoaFee,
    formatted: formattedHoaFee
  });

  return {
    bedrooms: details.bedrooms !== null ? String(details.bedrooms) : '',
    bathrooms: details.bathrooms !== null ? String(details.bathrooms) : '',
    squareFeet: details.squareFeet !== null ? String(details.squareFeet) : '',
    yearBuilt: details.yearBuilt !== null ? String(details.yearBuilt) : '',
    parking: details.parking || 'N/A',
    hoaFees: formattedHoaFee,
    propertyType: details.propertyType || '',
    lotSize: details.lotSize !== null ? String(details.lotSize) : '',
    price: details.price !== null ? String(details.price) : '',
    pricePerSqFt: details.pricePerSqFt !== null ? String(details.pricePerSqFt) : '',
    location: location,
    mlsId: details.mlsId || '',
    appliances: Array.isArray(details.appliances) ? details.appliances.join(', ') : '',
    features: Array.isArray(details.features) ? details.features.join(', ') : ''
  };
}

/**
 * Create an AI context object from the mapped Zillow property details
 * @param {Object} details - The mapped property details
 * @returns {Object} AI context object
 */
function zillowDetailsToAiContext(details) {
  // Create a formatted full address for the AI context
  const fullAddress = [details.address, details.city, details.state, details.zip]
    .filter(Boolean)
    .join(', ');

  // Format HOA fee for AI context
  const formattedHoaFee = details.hoaFee ?
    typeof details.hoaFee === 'number' ?
      `$${details.hoaFee}/month` :
      details.hoaFee.toString().startsWith('$') ?
        details.hoaFee.toString().includes('/month') ?
          details.hoaFee.toString() :
          `${details.hoaFee.toString()}/month` :
        `$${details.hoaFee.toString()}/month` :
    null;

  return {
    // Individual address components
    address: details.address,
    city: details.city,
    state: details.state,
    zip: details.zip,

    // Full formatted address
    fullAddress: fullAddress,

    // Property details
    bedrooms: details.bedrooms,
    bathrooms: details.bathrooms,
    squareFeet: details.squareFeet,
    yearBuilt: details.yearBuilt,
    propertyType: details.propertyType,
    lotSize: details.lotSize,
    price: details.price,
    pricePerSqFt: details.pricePerSqFt,
    parking: details.parking,
    hoaFee: formattedHoaFee,
    mlsId: details.mlsId,
    appliances: details.appliances,
    features: details.features
  };
}

module.exports = {
  parseZillowPropertyDetails,
  validateZillowPropertyDetails,
  zillowDetailsToFormFields,
  zillowDetailsToAiContext
};
