// This script starts the server for the Contract Generator API
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the server.js file
const serverPath = path.join(__dirname, 'server', 'server.js');

// Start the server
console.log(`Starting server from: ${serverPath}`);
const server = spawn('node', [serverPath], {
  stdio: 'inherit',
  env: {
    ...process.env,
    PORT: '5000',
    NODE_ENV: 'development'
  }
});

// Handle server events
server.on('error', (error) => {
  console.error(`Server error: ${error.message}`);
});

server.on('exit', (code, signal) => {
  if (code) {
    console.error(`Server exited with code: ${code}`);
  } else if (signal) {
    console.error(`Server was killed with signal: ${signal}`);
  } else {
    console.log('Server stopped');
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Stopping server...');
  server.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Stopping server...');
  server.kill('SIGTERM');
  process.exit(0);
});

console.log('Server started. Press Ctrl+C to stop.');
