import { Appointment, CreateAppointmentData } from '../models/Appointment';

// In-memory database for appointments (replace with actual database in production)
let appointments: Appointment[] = [];

// Generate a unique ID
const generateId = (): string => {
  return `appt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Format date to ISO string
const formatDate = (date: Date): string => {
  return date.toISOString();
};

export const LocalAppointmentService = {
  // Create a new appointment
  createAppointment: (data: CreateAppointmentData): Appointment => {
    const now = formatDate(new Date());

    // Ensure we have a user ID
    const userId = data.userId || 'guest';
    console.log('Creating appointment for user:', userId);

    const newAppointment: Appointment = {
      id: generateId(),
      ...data,
      userId, // Ensure userId is set
      createdAt: now,
      updatedAt: now
    };

    // Load existing appointments first to avoid overwriting
    if (appointments.length === 0) {
      loadAppointmentsFromStorage();
    }

    appointments.push(newAppointment);
    console.log('Added new appointment, total count:', appointments.length);

    // Save to localStorage for persistence
    saveAppointmentsToStorage();

    return newAppointment;
  },

  // Get all appointments
  getAllAppointments: (): Appointment[] => {
    // Load from localStorage if empty
    if (appointments.length === 0) {
      loadAppointmentsFromStorage();
    }
    return appointments;
  },

  // Get appointments for a specific user
  getUserAppointments: (userId: string): Appointment[] => {
    // Load from localStorage if empty
    if (appointments.length === 0) {
      loadAppointmentsFromStorage();
    }

    // Filter appointments by user ID
    const userAppointments = appointments.filter(appointment => appointment.userId === userId);
    console.log(`Filtered appointments for user ${userId}:`, userAppointments.length);

    return userAppointments;
  },

  // Get appointment by ID
  getAppointmentById: (id: string): Appointment | undefined => {
    return appointments.find(appointment => appointment.id === id);
  },

  // Update an appointment
  updateAppointment: (id: string, data: Partial<Appointment>): Appointment | null => {
    const index = appointments.findIndex(appointment => appointment.id === id);

    if (index === -1) {
      return null;
    }

    const updatedAppointment = {
      ...appointments[index],
      ...data,
      updatedAt: formatDate(new Date())
    };

    appointments[index] = updatedAppointment;

    // Save to localStorage for persistence
    saveAppointmentsToStorage();

    return updatedAppointment;
  },

  // Delete an appointment
  deleteAppointment: (id: string): boolean => {
    const initialLength = appointments.length;
    appointments = appointments.filter(appointment => appointment.id !== id);

    // Save to localStorage for persistence
    saveAppointmentsToStorage();

    return appointments.length < initialLength;
  },

  // Clear all appointments (for testing)
  clearAppointments: (): void => {
    appointments = [];
    localStorage.removeItem('appointments');
  }
};

// Helper functions for localStorage persistence
function saveAppointmentsToStorage(): void {
  try {
    localStorage.setItem('localAppointments', JSON.stringify(appointments));
    console.log('Saved appointments to localStorage:', appointments.length);
  } catch (error) {
    console.error('Error saving appointments to localStorage:', error);
  }
}

function loadAppointmentsFromStorage(): void {
  try {
    const storedAppointments = localStorage.getItem('localAppointments');
    if (storedAppointments) {
      appointments = JSON.parse(storedAppointments);
      console.log('Loaded appointments from localStorage:', appointments.length);

      // Log user IDs for debugging
      const userIds = new Set(appointments.map(a => a.userId));
      console.log('Appointments for users:', Array.from(userIds));
    } else {
      console.log('No appointments found in localStorage');
    }
  } catch (error) {
    console.error('Error loading appointments from localStorage:', error);
  }
}
