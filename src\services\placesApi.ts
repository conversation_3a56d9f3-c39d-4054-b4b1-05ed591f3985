/**
 * Google Places API Service
 * This service handles interactions with the Google Places API
 */

// Define the API base URL
const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000';

/**
 * Fetches latitude and longitude for an address using Google Places API
 * @param address - The address to geocode
 * @returns Promise with geocode result or null
 */
export async function fetchLatLngForAddress(address: string) {
  if (!address) return null;

  try {
    const params = new URLSearchParams({
      address: encodeURIComponent(address)
    });

    const response = await fetch(
      `${backendUrl}/api/places/geocode?${params.toString()}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Geocoding error: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error geocoding address:', error);
    throw error;
  }
}

/**
 * Extracts coordinates from a Google Place result
 * @param place - The Google Place result object
 * @returns Object with lat and lng properties
 */
export function extractCoordinatesFromPlace(place: any): { lat: number, lng: number } | null {
  if (!place || !place.geometry || !place.geometry.location) {
    return null;
  }

  // Handle both function-based and object-based location formats
  const lat = typeof place.geometry.location.lat === 'function'
    ? place.geometry.location.lat()
    : place.geometry.location.lat;

  const lng = typeof place.geometry.location.lng === 'function'
    ? place.geometry.location.lng()
    : place.geometry.location.lng;

  if (typeof lat !== 'number' || typeof lng !== 'number') {
    console.error('Invalid coordinates format from Google Place result');
    return null;
  }

  return { lat, lng };
}

/**
 * Fetches place details using Google Places API
 * @param placeId - The Google Place ID
 * @returns Promise with place details or null
 */
export async function fetchPlaceDetails(placeId: string) {
  if (!placeId) return null;

  try {
    const params = new URLSearchParams({
      placeId: encodeURIComponent(placeId)
    });

    const response = await fetch(
      `${backendUrl}/api/places/details?${params.toString()}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Place details error: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching place details:', error);
    throw error;
  }
}

/**
 * Fetches autocomplete suggestions for an address
 * @param input - The input text to get suggestions for
 * @returns Promise with autocomplete suggestions or empty array
 */
export async function fetchAddressSuggestions(input: string) {
  if (!input || input.length < 3) return [];

  try {
    const params = new URLSearchParams({
      input: encodeURIComponent(input)
    });

    const response = await fetch(
      `${backendUrl}/api/places/autocomplete?${params.toString()}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Autocomplete error: ${response.status}`);
    }

    const data = await response.json();
    return data.predictions || [];
  } catch (error) {
    console.error('Error fetching address suggestions:', error);
    return [];
  }
}
