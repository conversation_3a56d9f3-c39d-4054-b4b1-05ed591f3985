/* Modern Home Page Styles */

/* Grid pattern background */
.gridPattern {
  background-size: 50px 50px;
  background-image:
    linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  opacity: 0.3;
}

/* Feature card hover effects */
.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 255, 255, 0.2);
  transition: all 0.3s ease;
}

/* Navigation link hover effects */
.navLink:hover {
  color: #38bdf8;
  transition: color 0.3s ease;
}

/* Button hover effects */
.btnHover:hover {
  background: linear-gradient(to right, #3b82f6, #06b6d4);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* Hero Section */
.heroSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.heroBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.heroContent {
  max-width: 800px;
  z-index: 1;
}

.heroTitle {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.heroTitleHighlight {
  color: #38bdf8;
  display: block;
}

.heroSubtitle {
  font-size: 1.25rem;
  margin-bottom: 0;
  color: #94a3b8;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButton {
  background: linear-gradient(to right, #3b82f6, #06b6d4);
  color: white;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.3);
}

.ctaButton::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.5), rgba(6, 182, 212, 0.5));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ctaButton:hover::after {
  opacity: 1;
}

.ctaButton span {
  position: relative;
  z-index: 1;
}

/* Features Section */
.featuresSection {
  padding: 4rem 1.5rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .featuresGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .featuresGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.featureCard {
  border: 1px solid #3b82f6;
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  text-decoration: none;
  background-color: rgba(30, 41, 59, 0.4);
  position: relative;
  overflow: hidden;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.featureCard:hover::before {
  opacity: 1;
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.featureDescription {
  color: #94a3b8;
  flex-grow: 1;
}

/* Technology Section */
.technologySection {
  padding: 4rem 1.5rem;
  text-align: center;
}

.sectionTitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #38bdf8;
}

.sectionDescription {
  color: #94a3b8;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .heroTitle {
    font-size: 2.25rem;
  }

  .heroSubtitle {
    font-size: 1rem;
  }

  .heroSection {
    padding: 3rem 1rem;
  }
}
