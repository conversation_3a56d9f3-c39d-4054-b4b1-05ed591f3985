const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { Anthropic } = require('@anthropic-ai/sdk');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Production logging configuration
const isProduction = process.env.NODE_ENV === 'production';
if (isProduction) {
  console.log('🚀 Starting Digital Realtor API in production mode');
  console.log(`📍 Port: ${PORT}`);
  console.log(`🔑 API Keys configured: ${!!process.env.OPENAI_API_KEY ? '✅' : '❌'} OpenAI, ${!!process.env.ANTHROPIC_API_KEY ? '✅' : '❌'} Claude`);
}

// Import routes
const proxyRoutes = require('./routes/proxyRoutes');
const virtualStagingRoutes = require('./routes/virtualStagingRoutes');
const placesRoutes = require('./routes/places');
const zillowRoutes = require('./routes/zillow');
const envCheckRoutes = require('./routes/envCheck');

// Initialize Anthropic client with environment variable
const CLAUDE_API_KEY = process.env.ANTHROPIC_API_KEY;
if (!CLAUDE_API_KEY && process.env.NODE_ENV === 'production') {
  console.error('ANTHROPIC_API_KEY is required in production');
  process.exit(1);
}

const anthropic = CLAUDE_API_KEY ? new Anthropic({
  apiKey: CLAUDE_API_KEY,
}) : null;

// Configure OpenAI API key
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
if (!OPENAI_API_KEY && process.env.NODE_ENV === 'production') {
  console.error('OPENAI_API_KEY is required in production');
  process.exit(1);
}

// Initialize axios for HTTP requests
const axios = require('axios');

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:5174',
      'https://aidigitalrealtor.com',
      'https://www.aidigitalrealtor.com',
      'https://digital-realtor.netlify.app',
      'https://aidigitalrealtor.netlify.app'
    ];

    // In development, allow all localhost origins
    if (process.env.NODE_ENV !== 'production' && origin.includes('localhost')) {
      return callback(null, true);
    }

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Length', 'Content-Type'],
  credentials: true,
  optionsSuccessStatus: 200,
  preflightContinue: false,
  maxAge: 86400 // 24 hours
};

// Middleware
app.use(cors(corsOptions));

// Add a general CORS middleware for all routes
app.use((req, res, next) => {
  // Check if the origin is in our allowed origins list
  const origin = req.headers.origin;
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://aidigitalrealtor.com',
    'https://www.aidigitalrealtor.com',
    'https://digital-realtor.netlify.app',
    'https://aidigitalrealtor.netlify.app'
  ];

  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (process.env.NODE_ENV !== 'production') {
    // In development, allow all origins
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Use proxy routes
app.use('/api/proxy', proxyRoutes);

// Use virtual staging routes
app.use('/api/virtual-staging', virtualStagingRoutes);

// Use places routes
app.use('/api/places', placesRoutes);

// Use Zillow routes
app.use('/api/zillow', zillowRoutes);

// Use environment check routes
app.use('/api/env-check', envCheckRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// Endpoint to analyze images for inspection reports
app.post('/api/claude/analyze-images', async (req, res) => {
  try {
    if (!anthropic) {
      return res.status(500).json({ error: 'Claude API is not configured' });
    }

    const { images } = req.body;

    if (!images || images.length === 0) {
      return res.status(400).json({ error: 'No images provided' });
    }

    // Prepare the message content
    let messageContent = "Please analyze these property images and identify any issues or defects. For each issue, provide the Item/Area, Issue description, Analysis, and Recommendation in a structured format that can be easily converted to a table:";

    // Add images to the message content
    const imageContents = images.map(image => {
      // Determine the media type from the data URL
      const mediaType = image.match(/^data:image\/(jpeg|png|gif|webp);base64,/)?.[1] || 'jpeg';

      return {
        type: "image",
        source: {
          type: "base64",
          media_type: `image/${mediaType}`,
          data: image.replace(/^data:image\/\w+;base64,/, "")
        }
      };
    });

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional property inspector analyzing images of a property.
    Identify any visible issues, defects, or concerns in the provided images that would be relevant for a property inspection report.
    Focus on structural issues, water damage, electrical problems, HVAC issues, plumbing problems, safety hazards, and other significant defects.

    For each issue identified, provide the following information in a structured format:
    1. Item/Area: The specific item or area visible in the photo (e.g., "Bathroom Ceiling", "Kitchen Sink", "Basement Wall")
    2. Issue: A clear description of the problem
    3. Analysis: The potential implications or risks, including severity
    4. Recommendation: Specific actions to address the issue

    Format your response as a structured list where each issue is clearly separated and includes all four elements above. This will be used to create a table in the final report.`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-opus-20240229",
      max_tokens: 1000,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: messageContent },
            ...imageContents
          ]
        }
      ]
    });

    // Extract the analysis
    const content = response.content[0];
    if (content.type === 'text') {
      return res.status(200).json({ analysis: content.text });
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error analyzing images with Claude:', error);
    return res.status(500).json({ error: 'Failed to analyze images with Claude. Please try again.' });
  }
});

// Endpoint to analyze property images for listing generation
app.post('/api/analyze-property-images', async (req, res) => {
  try {
    const { images, provider, propertyType, targetAudience, priceRange, locationHints } = req.body;

    if (!images || images.length === 0) {
      return res.status(400).json({ error: 'No images provided' });
    }

    if (!provider || (provider !== 'openai' && provider !== 'claude')) {
      return res.status(400).json({ error: 'Invalid provider specified' });
    }

    // Prepare the base prompt
    let basePrompt = "You're a vision model helping with real estate listings. Based on the image, provide two things:\n";
    basePrompt += "1. A polished, buyer-friendly description based on what you see.\n";
    basePrompt += "2. A short list of 3-7 visual features recognized in the image.\n\n";
    basePrompt += "Format your response as:\n";
    basePrompt += "{\n";
    basePrompt += '  "description": "Write a polished, compelling description of a home based only on the attached image(s). Highlight visible features such as design style, appliances, finishes, lighting, flooring, space layout, or upgrades. The tone should be confident, descriptive, and appealing to buyers. End your description naturally—do not say \"based on the image.\"",\n';
    basePrompt += '  "features": ["feature 1", "feature 2", "feature 3", "etc."]\n';
    basePrompt += "}\n\n";
    basePrompt += "Return valid JSON that can be parsed with JSON.parse().";

    // Add additional context if provided
    if (propertyType || targetAudience || priceRange || locationHints) {
      basePrompt += "\n\nAdditional Context:";
      if (targetAudience) basePrompt += `\n• Target audience: ${targetAudience}`;
      if (propertyType) basePrompt += `\n• Property type: ${propertyType}`;
      if (locationHints) basePrompt += `\n• Location hints: ${locationHints}`;
      if (priceRange) basePrompt += `\n• Price range: ${priceRange}`;
    }

    let result;

    if (provider === 'claude') {
      // Process with Claude
      // Add images to the message content
      const imageContents = images.map(image => {
        // Determine the media type from the data URL
        const mediaType = image.match(/^data:image\/(jpeg|png|gif|webp);base64,/)?.[1] || 'jpeg';

        return {
          type: "image",
          source: {
            type: "base64",
            media_type: `image/${mediaType}`,
            data: image.replace(/^data:image\/\w+;base64,/, "")
          }
        };
      });

      // Create the system message with instructions for Claude
      const systemMessage = `You are a real estate listing assistant. Your task is to analyze property images and provide both a compelling description and a list of key features visible in the images.

      For the description: Write a polished, compelling description highlighting visible features such as design style, appliances, finishes, lighting, flooring, space layout, or upgrades. The tone should be confident, descriptive, and appealing to buyers.

      For the features list: Identify 3-7 specific visual elements or features that stand out in the image, such as "granite countertops", "hardwood floors", "stainless steel appliances", etc.

      Return your response as a valid JSON object with two fields: "description" and "features" (an array of strings).`;

      // Make the API request to Claude
      const response = await anthropic.messages.create({
        model: "claude-3-5-sonnet-20241022",
        max_tokens: 1000,
        system: systemMessage,
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: basePrompt },
              ...imageContents
            ]
          }
        ]
      });

      // Extract the analysis
      const content = response.content[0];
      if (content.type === 'text') {
        try {
          // Try to parse the JSON response
          result = JSON.parse(content.text);
        } catch (parseError) {
          console.error('Error parsing Claude response as JSON:', parseError);
          // Fallback to using the raw text as the description
          result = {
            description: content.text,
            features: ["Unable to extract features"]
          };
        }
      } else {
        throw new Error('Unexpected response format from Claude');
      }
    } else {
      // Process with OpenAI
      // We need to use the OpenAI API directly since we're working with images
      const axios = require('axios');
      const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

      // Prepare the message content for OpenAI
      const messages = [
        {
          role: "system",
          content: "You are a real estate listing assistant. Your task is to analyze property images and provide both a compelling description and a list of key features visible in the images. For the description: Write a polished, compelling description highlighting visible features such as design style, appliances, finishes, lighting, flooring, space layout, or upgrades. The tone should be confident, descriptive, and appealing to buyers. For the features list: Identify 3-7 specific visual elements or features that stand out in the image, such as 'granite countertops', 'hardwood floors', 'stainless steel appliances', etc. IMPORTANT: You must return your response as a valid JSON object with exactly two fields: 'description' (a string) and 'features' (an array of strings). Do not include any text outside of the JSON structure. The response must be valid JSON that can be parsed with JSON.parse()."
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze the following property images and provide a compelling description and list of key features. Return your response as a valid JSON object with the format: {\"description\": \"Your compelling description here\", \"features\": [\"feature 1\", \"feature 2\", \"feature 3\"]}"
            }
          ]
        }
      ];

      // Add images to the user message content
      for (const image of images) {
        messages[1].content.push({
          type: "image_url",
          image_url: {
            url: image
          }
        });
      }

      // Make the API request to OpenAI
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: "gpt-4o",
          messages: messages,
          max_tokens: 500
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${OPENAI_API_KEY}`
          }
        }
      );

      // Extract the result
      const content = response.data.choices[0].message.content;
      try {
        // Log the raw response for debugging
        console.log('Raw OpenAI response:', content.substring(0, 100) + '...');

        // Try to extract JSON from the response
        let jsonText = content.trim();

        // If the response starts with text before the JSON, try to find the JSON part
        if (jsonText.charAt(0) !== '{') {
          const jsonStartIndex = jsonText.indexOf('{');
          if (jsonStartIndex !== -1) {
            jsonText = jsonText.substring(jsonStartIndex);
            console.log('Extracted JSON part starting with:', jsonText.substring(0, 50) + '...');
          }
        }

        // If the response has text after the JSON, try to find the end of the JSON
        const lastBraceIndex = jsonText.lastIndexOf('}');
        if (lastBraceIndex !== -1 && lastBraceIndex < jsonText.length - 1) {
          jsonText = jsonText.substring(0, lastBraceIndex + 1);
          console.log('Trimmed JSON part ending with:', '...' + jsonText.substring(jsonText.length - 50));
        }

        // Parse the JSON
        result = JSON.parse(jsonText);

        // Validate the required fields
        if (!result.description || !result.features || !Array.isArray(result.features)) {
          throw new Error('Missing required fields in the response');
        }
      } catch (parseError) {
        console.error('Error parsing OpenAI response as JSON:', parseError);
        // Fallback to using the raw text as the description
        result = {
          description: content,
          features: ["Unable to extract features"]
        };
      }
    }

    return res.status(200).json(result);
  } catch (error) {
    console.error(`Error analyzing property images with ${req.body.provider}:`, error);
    return res.status(500).json({ error: `Failed to analyze images with ${req.body.provider}. Please try again.` });
  }
});

// Endpoint to generate inspection report
app.post('/api/claude/generate-report', async (req, res) => {
  try {
    const inspectionData = req.body;

    // Validate required fields
    if (!inspectionData.propertyAddress || !inspectionData.inspectionDate ||
        !inspectionData.inspectorName || !inspectionData.clientName || !inspectionData.generalNotes) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional property inspector. Create a detailed inspection report based on the provided information.
    The report should include:

    1. A professional header with property details
    2. A summary of findings
    3. Detailed analysis of issues found - THIS MUST BE FORMATTED AS A MARKDOWN TABLE with the following columns:
       - Item: The specific item or area with the issue
       - Issue: A clear description of the problem
       - Analysis: Your professional analysis of the issue, its severity, and potential implications
       - Recommendation: Specific actions to address the issue
    4. If image analysis is provided, include a section titled "Issues Identified from Photos" with the findings
       - THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns as above:
       - Item: The specific item or area visible in the photo
       - Issue: A clear description of the problem identified in the photo
       - Analysis: Your professional analysis of the issue, its severity, and potential implications
       - Recommendation: Specific actions to address the issue
    5. A conclusion

    Format the report using Markdown with appropriate headers, bullet points, and sections. BOTH the detailed analysis section AND the issues identified from photos section MUST be formatted as markdown tables with the columns specified above. Be specific, professional, and thorough.`;

    // Create the user message with the inspection details
    const userMessage = `Property Inspection Details:
    - Property Address: ${inspectionData.propertyAddress}
    - Inspection Date: ${new Date(inspectionData.inspectionDate).toLocaleDateString()}
    - Inspector: ${inspectionData.inspectorName}
    - Client: ${inspectionData.clientName}
    - Property Type: ${inspectionData.propertyType || 'Not specified'}
    - Year Built: ${inspectionData.yearBuilt || 'Not specified'}
    - Square Footage: ${inspectionData.squareFootage || 'Not specified'}

    General Notes and Issues Found:
    ${inspectionData.generalNotes}

    ${inspectionData.imageAnalysis ? `
    Image Analysis Results:
    ${inspectionData.imageAnalysis}
    ` : ''}

    Please analyze these notes${inspectionData.imageAnalysis ? ' and image analysis results' : ''}, identify all issues mentioned, and provide professional recommendations for addressing each issue. Format the report professionally with clear sections.

    IMPORTANT: The "Detailed Analysis of Issues" section MUST be formatted as a markdown table with these exact columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.
    ${inspectionData.imageAnalysis ? 'Include a separate section titled "Issues Identified from Photos" that summarizes the findings from the image analysis. THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.' : ''}`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1500,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: userMessage }
          ]
        }
      ]
    });

    // Extract the generated report
    const content = response.content[0];
    if (content.type === 'text') {
      return res.status(200).json({ report: content.text });
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error generating inspection report with Claude:', error);
    return res.status(500).json({ error: 'Failed to generate inspection report with Claude. Please try again.' });
  }
});

// Endpoint to generate property listing with Claude
app.post('/api/claude/generate-listing', async (req, res) => {
  try {
    const propertyData = req.body;

    // Validate required fields
    if (!propertyData.propertyType || !propertyData.bedrooms || !propertyData.bathrooms ||
        !propertyData.squareFeet || !propertyData.location || !propertyData.price) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create the system message with instructions for the AI
    const listingType = propertyData.listingType || 'sale';

    const systemMessage = listingType === 'sale'
      ? `You are a professional real estate copywriter. Create a compelling property listing for a property that is FOR SALE based on the provided information.
      The listing should include:

      1. An attention-grabbing title
      2. The property price prominently displayed
      3. A property overview section
      4. A location description
      5. A features list
      6. Additional information if provided
      7. A compelling closing statement

      Format the listing using Markdown with appropriate headers, bullet points, and sections. Be specific, professional, and persuasive. Emphasize ownership benefits, investment value, and long-term advantages.`

      : `You are a professional real estate copywriter. Create a compelling RENTAL property listing based on the provided information.
      The listing should include:

      1. An attention-grabbing title that appeals to renters
      2. The monthly rent prominently displayed
      3. A property overview section focused on livability
      4. A location description emphasizing convenience and nearby amenities
      5. A features list highlighting aspects important to renters
      6. Additional information if provided
      7. A compelling closing statement with rental-specific call to action

      Format the listing using Markdown with appropriate headers, bullet points, and sections. Be specific, professional, and persuasive. Use phrases like "available now," "monthly rent," and "ideal for tenants." Avoid ownership language like "asking price" or "purchase opportunity."
      Focus on livability, convenience, and comfort.`;

    // Create the user message with the property details
    const priceLabel = listingType === 'sale' ? 'Price' : 'Monthly Rent';
    const audienceLabel = listingType === 'sale' ? 'buyers' : 'renters';

    const userMessage = `Property Details:
    - Property Type: ${propertyData.propertyType}
    - Bedrooms: ${propertyData.bedrooms}
    - Bathrooms: ${propertyData.bathrooms}
    - Square Feet: ${propertyData.squareFeet}
    - Location: ${propertyData.location}
    - ${priceLabel}: ${propertyData.price}
    - Listing Type: ${listingType === 'sale' ? 'For Sale' : 'For Rent'}
    - Year Built: ${propertyData.yearBuilt || 'Not specified'}
    - Lot Size: ${propertyData.lotSize || 'Not specified'}
    - Parking: ${propertyData.parking || 'Not specified'}
    - Appliances: ${propertyData.appliances || 'Not specified'}
    - Price Per Sq Ft: ${propertyData.pricePerSqFt || 'Not specified'}
    - MLS ID: ${propertyData.mlsId || 'Not specified'}
    - Features: ${propertyData.features || 'Not specified'}
    - Description: ${propertyData.description || 'Not specified'}

    Please create a compelling property listing based on these details. The listing should highlight the property's best features and appeal to potential ${audienceLabel}.`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1000,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: userMessage }
          ]
        }
      ]
    });

    // Extract the generated listing
    const content = response.content[0];
    if (content.type === 'text') {
      return res.status(200).json({ listing: content.text });
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error generating property listing with Claude:', error);
    return res.status(500).json({ error: 'Failed to generate property listing with Claude. Please try again.' });
  }
});

// Endpoint to extract property data from natural language with Claude
app.post('/api/claude/extract-property-data', async (req, res) => {
  try {
    const { naturalLanguageInput } = req.body;

    if (!naturalLanguageInput) {
      return res.status(400).json({ error: 'No input provided' });
    }

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional real estate data extractor. Extract structured property details from the natural language description provided by the user.
    You must extract the following fields if present in the text:
    - propertyType: The type of property (House, Condo, Townhouse, etc.)
    - bedrooms: Number of bedrooms
    - bathrooms: Number of bathrooms
    - squareFeet: Square footage of the property
    - location: Location of the property
    - price: Price of the property (numeric value only)
    - yearBuilt: Year the property was built (optional)
    - features: Key features of the property, comma separated (optional)
    - description: Additional details about the property (optional)

    Return the data in a structured JSON format. Make reasonable inferences for missing information based on context.`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1000,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: naturalLanguageInput }
          ]
        }
      ]
    });

    // Extract the structured data
    const content = response.content[0];
    if (content.type === 'text') {
      try {
        const extractedData = JSON.parse(content.text);
        return res.status(200).json({ extractedData });
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        return res.status(500).json({ error: 'Failed to parse structured data' });
      }
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error extracting property data with Claude:', error);
    return res.status(500).json({ error: 'Failed to extract property data with Claude. Please try again.' });
  }
});

// Endpoint to analyze comparable properties with Claude
app.post('/api/claude/analyze-comps', async (req, res) => {
  try {
    const { address, propertyDetails, marketTrends } = req.body;

    if (!address) {
      return res.status(400).json({ error: 'No address provided' });
    }

    // Create the system message with instructions for the AI
    const searchRadius = propertyDetails?.searchRadius || 1.5;
    const filterMode = propertyDetails?.filterMode || 'strict';

    const systemMessage = `You are a highly analytical real estate valuation expert with 20+ years of experience in comparative market analysis.
    Given a subject property and market data, you will determine a precise, data-driven competitive listing price using advanced valuation methodologies.

    The analysis should include:

    1. A recommended listing price with a well-justified price range (low to high)
    2. A list of comparable properties with the following details for each:
       - Address
       - Price
       - Bedrooms
       - Bathrooms
       - Square Feet
       - Year Built
       - Distance from target property (in miles)
       - Date sold
       - Similarity score (as a percentage, calculated with precision)
       - Zillow link (formatted as a search URL)
       - Detailed adjustments made for differences between this property and the subject property
       - Whether the comp was included or excluded from analysis, with specific reason if excluded

    3. Comprehensive market trends including:
       - Average price per square foot in the area (calculated with precision)
       - Median sale price
       - Average days on market
       - Year-over-year price change percentage
       - Current inventory level with specific metrics
       - Market classification (buyer's, seller's, or balanced) with supporting evidence

    4. A confidence score (1-10) with detailed justification based on:
       - Quality and quantity of comparable properties
       - Recency of sales data
       - Consistency of pricing across comparable properties
       - Market stability indicators
       - Seasonal factors
       - Neighborhood-specific trends

    5. Two detailed lists:
       - keyCompsUsed: An array of strings describing the key comparable properties used in your analysis with specific reasons why each was weighted heavily
       - excludedCompsAndWhy: An array of strings explaining which properties were excluded with precise reasoning

    💡 Advanced Valuation Instructions:
    - Apply a weighted scoring system for comps: 40% for location proximity, 30% for property similarity, 20% for recency, 10% for market conditions
    - Only give significant weight to comps within ${searchRadius} miles and sold within the last 6 months
    - ${filterMode === 'strict'
        ? 'Apply strict filtering criteria: only include comps that have EXACT same number of bedrooms and bathrooms, within ±10% sqft, within 5 years of construction, and within the specified search radius'
        : 'Apply sophisticated similarity scoring: rank properties using a weighted algorithm that considers beds (15%), baths (15%), sqft (25%), year built (15%), distance (20%), and condition/features (10%)'}
    - Make precise dollar adjustments for each difference: $X per sqft difference, $Y per bedroom/bathroom difference, $Z per decade of age difference
    - Apply neighborhood-specific multipliers based on school districts, crime rates, and amenity access
    - Factor in current interest rates and their impact on buyer purchasing power
    - Consider seasonal market fluctuations and their impact on the current valuation
    - For each comp, provide a detailed similarity score calculation and adjustment methodology

    IMPORTANT: Your response MUST be a valid JSON object containing all the fields described above. Do not include any text before or after the JSON object.

    IMPORTANT: You MUST return the data in a structured JSON format with the following structure and nothing else:
    {
      "recommendedPrice": "$X,XXX,XXX",
      "recommendedListingPrice": "$X,XXX,XXX",
      "priceRange": "$X,XXX,XXX - $X,XXX,XXX",
      "confidenceScore": 8,
      "confidenceExplanation": "High confidence due to 5 very similar properties in the same neighborhood sold within the last 3 months",
      "priceJustification": "Short explanation of how you arrived at this price",
      "keyCompsUsed": ["123 Main St, City, State", "456 Oak Ave, City, State"],
      "excludedCompsAndWhy": ["789 Pine St: too far (2.3 miles)", "321 Elm St: sold 14 months ago"],
      "comparableProperties": [
        {
          "address": "123 Main St, City, State",
          "price": "$XXX,XXX",
          "bedrooms": 3,
          "bathrooms": 2,
          "squareFeet": 1500,
          "yearBuilt": 2005,
          "distanceFromTarget": "0.5 miles",
          "soldDate": "March 15, 2023",
          "similarityScore": 85,
          "adjustments": "+$10,000 for extra bathroom, -$5,000 for smaller lot",
          "zillowLink": "https://www.zillow.com/homes/123-Main-St,-City,-State_rb/",
          "included": true,
          "exclusionReason": null
        },
        {
          "address": "789 Pine St, City, State",
          "price": "$XXX,XXX",
          "bedrooms": 3,
          "bathrooms": 2,
          "squareFeet": 1600,
          "yearBuilt": 2008,
          "distanceFromTarget": "2.3 miles",
          "soldDate": "April 10, 2023",
          "similarityScore": 70,
          "adjustments": "-$15,000 for distance",
          "zillowLink": "https://www.zillow.com/homes/789-Pine-St,-City,-State_rb/",
          "included": false,
          "exclusionReason": "Property is too far (> 1.5 miles)"
        }
      ],
      "marketTrends": {
        "averagePricePerSqFt": "$XXX",
        "medianSalePrice": "$XXX,XXX",
        "averageDaysOnMarket": 30,
        "priceChangeLastYear": "+5%",
        "inventoryLevel": "Low",
        "marketType": "Seller's Market"
      }
    }

    Do not include any explanations, apologies, or additional text outside of the JSON structure. The response must be valid JSON that can be parsed by JSON.parse().`;

    // Create the user message with the address and property details
    let userMessage = `Please analyze comparable properties for this address and recommend a listing price:

    Subject Property:
    - Address: ${address}`;

    // Add property details if available
    if (propertyDetails) {
      if (propertyDetails.squareFeet) userMessage += `\n- Sq Ft: ${propertyDetails.squareFeet}`;
      if (propertyDetails.bedrooms && propertyDetails.bathrooms) userMessage += `\n- Beds/Baths: ${propertyDetails.bedrooms}/${propertyDetails.bathrooms}`;
      if (propertyDetails.lotSize) userMessage += `\n- Lot Size: ${propertyDetails.lotSize} acres`;
      if (propertyDetails.yearBuilt) userMessage += `\n- Year Built: ${propertyDetails.yearBuilt}`;
      if (propertyDetails.condition) userMessage += `\n- Condition: ${propertyDetails.condition}`;
      if (propertyDetails.uniqueFeatures && propertyDetails.uniqueFeatures.length > 0) {
        userMessage += `\n- Unique Features: ${propertyDetails.uniqueFeatures.join(', ')}`;
      }
    }

    // Add market trends if available
    if (marketTrends) {
      userMessage += `\n\nLocal Market Trends:`;
      if (marketTrends.averagePricePerSqFt) userMessage += `\n- Average Price Per Sq Ft: ${marketTrends.averagePricePerSqFt}`;
      if (marketTrends.medianSalePrice) userMessage += `\n- Median Sale Price: ${marketTrends.medianSalePrice}`;
      if (marketTrends.averageDaysOnMarket) userMessage += `\n- Average Days on Market: ${marketTrends.averageDaysOnMarket}`;
      if (marketTrends.priceChangeLastYear) userMessage += `\n- Price Change Last Year: ${marketTrends.priceChangeLastYear}`;
      if (marketTrends.inventoryLevel) userMessage += `\n- Inventory Level: ${marketTrends.inventoryLevel}`;
      if (marketTrends.marketType) userMessage += `\n- Market Type: ${marketTrends.marketType}`;
    }

    userMessage += `\n\nPlease focus on finding properties sold within ${searchRadius} miles of the subject property and within the last 6 months. ${
      filterMode === 'strict'
        ? `Use strict filtering criteria: only include comps that have EXACT same number of bedrooms and bathrooms, within ±10% sqft, within 5 years of construction, and within ${searchRadius} miles.`
        : `Use soft filtering with similarity scores: rank properties by how closely they match the subject property, even if they fall outside the strict criteria.`
    } Make adjustments based on differences between the subject property and comps, including size, condition, age, and features. Explicitly exclude comps that are too far (>${searchRadius} miles) or too old (>6 months).`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1500,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: userMessage }
          ]
        }
      ]
    });

    // Extract the analysis
    const content = response.content[0];
    if (content.type === 'text') {
      try {
        // Log the raw response for debugging
        console.log('Raw Claude response:', content.text.substring(0, 100) + '...');

        // Try to extract JSON from the response
        let jsonText = content.text;

        // If the response starts with text before the JSON, try to find the JSON part
        if (jsonText.trim().charAt(0) !== '{') {
          const jsonStartIndex = jsonText.indexOf('{');
          if (jsonStartIndex !== -1) {
            jsonText = jsonText.substring(jsonStartIndex);
            console.log('Extracted JSON part starting with:', jsonText.substring(0, 50) + '...');
          }
        }

        // If the response has text after the JSON, try to find the end of the JSON
        const lastBraceIndex = jsonText.lastIndexOf('}');
        if (lastBraceIndex !== -1 && lastBraceIndex < jsonText.length - 1) {
          jsonText = jsonText.substring(0, lastBraceIndex + 1);
          console.log('Trimmed JSON part ending with:', '...' + jsonText.substring(jsonText.length - 50));
        }

        // Parse the JSON
        const analysisData = JSON.parse(jsonText);

        // Validate the required fields
        if (!analysisData.recommendedPrice || !analysisData.comparableProperties || !Array.isArray(analysisData.comparableProperties)) {
          throw new Error('Missing required fields in the response');
        }

        // Mark properties as included/excluded based on similarity score if not already marked
        analysisData.comparableProperties = analysisData.comparableProperties.map(comp => {
          if (comp.included === undefined) {
            // If similarity score is available, use it to determine inclusion
            if (comp.similarityScore !== undefined) {
              return {
                ...comp,
                included: comp.similarityScore >= 70
              };
            }
            // Default to included if no similarity score
            return {
              ...comp,
              included: true
            };
          }
          return comp;
        });

        // Ensure keyCompsUsed and excludedCompsAndWhy fields are present
        if (!analysisData.keyCompsUsed || !Array.isArray(analysisData.keyCompsUsed) || analysisData.keyCompsUsed.length === 0) {
          // Extract key comps from the comparable properties
          analysisData.keyCompsUsed = analysisData.comparableProperties
            .filter(comp => comp.included === true || (comp.included === undefined && comp.similarityScore >= 80))
            .map(comp => `${comp.address} (${comp.price}, ${comp.bedrooms}bd/${comp.bathrooms}ba, ${comp.squareFeet} sqft)`);

          // If still empty, include at least one property with highest similarity score
          if (analysisData.keyCompsUsed.length === 0 && analysisData.comparableProperties.length > 0) {
            const sortedComps = [...analysisData.comparableProperties].sort((a, b) =>
              (b.similarityScore || 0) - (a.similarityScore || 0)
            );
            const topComp = sortedComps[0];
            analysisData.keyCompsUsed = [`${topComp.address} (${topComp.price}, ${topComp.bedrooms}bd/${topComp.bathrooms}ba, ${topComp.squareFeet} sqft)`];
          }
        }

        if (!analysisData.excludedCompsAndWhy || !Array.isArray(analysisData.excludedCompsAndWhy) || analysisData.excludedCompsAndWhy.length === 0) {
          // Extract excluded comps from the comparable properties
          analysisData.excludedCompsAndWhy = analysisData.comparableProperties
            .filter(comp => comp.included === false || (comp.similarityScore && comp.similarityScore < 70))
            .map(comp => `${comp.address}: ${comp.exclusionReason || 'Low similarity score or distant from subject property'}`);

          // If no excluded comps but we have properties, add a generic one
          if (analysisData.excludedCompsAndWhy.length === 0 && analysisData.comparableProperties.length > 0) {
            const leastSimilarComp = [...analysisData.comparableProperties]
              .sort((a, b) => (a.similarityScore || 0) - (b.similarityScore || 0))[0];

            if (leastSimilarComp) {
              // Add exclusion reason if not present
              if (!leastSimilarComp.exclusionReason) {
                leastSimilarComp.exclusionReason = 'Lower similarity to subject property';
              }

              // Mark as excluded if not already
              if (leastSimilarComp.included !== false) {
                leastSimilarComp.included = false;
              }

              analysisData.excludedCompsAndWhy = [
                `${leastSimilarComp.address}: ${leastSimilarComp.exclusionReason}`
              ];
            }
          }
        }

        return res.status(200).json(analysisData);
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);

        // Return an error instead of fallback mock data to prevent mixing real and fake data
        console.log('Claude response parsing failed - returning error instead of mock data');
        return res.status(500).json({
          error: 'Failed to parse Claude response. Please try again.',
          details: 'Claude analysis could not be processed properly'
        });
      }
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error analyzing comps with Claude:', error);
    return res.status(500).json({ error: 'Failed to analyze comps with Claude. Please try again.' });
  }
});

// Endpoint to extract appointment data from natural language with Claude
app.post('/api/claude/extract-appointment-data', async (req, res) => {
  try {
    const { naturalLanguageInput } = req.body;

    if (!naturalLanguageInput) {
      return res.status(400).json({ error: 'No input provided' });
    }

    // Get the current date for reference in relative date calculations
    const currentDate = new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    // Create the system message with instructions for the AI
    const systemMessage = `You are an AI assistant for real estate agents. Your task is to extract appointment details from the user's natural language input.

    For the time parameter, please format it as a standard time with AM/PM indicator (e.g., "10:00 AM", "2:30 PM").

    For the date parameter, when the user mentions relative dates like "today", "tomorrow", or "next Monday", calculate the actual calendar date based on the current date (${currentDate}). Always provide a full date with month, day, and year (e.g., "June 15, 2023").

    If the user doesn't specify a complete detail, make a reasonable assumption based on context.

    Extract the following fields:
    - appointmentId: Generate a unique ID
    - clientName: The name of the client
    - clientEmail: The email address of the client
    - clientPhone: The phone number of the client (if provided)
    - date: The appointment date (formatted as "Month Day, Year")
    - time: The appointment time (formatted as "H:MM AM/PM")
    - propertyAddress: The address of the property
    - propertyType: The type of property (House, Condo, Apartment, etc.)
    - status: The status of the appointment ("confirmed", "pending", or "cancelled")
    - notes: Any additional notes about the appointment

    IMPORTANT: Return ONLY a valid JSON object with no additional text, explanations, or formatting. The response should be pure JSON that can be parsed directly.`;

    // Make the API request to Claude 4.0
    const response = await anthropic.messages.create({
      model: "claude-opus-4-20250514",
      max_tokens: 1000,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: naturalLanguageInput }
          ]
        }
      ]
    });

    // Extract the structured data
    const content = response.content[0];
    if (content.type === 'text') {
      try {
        console.log('Raw Claude response:', content.text.substring(0, 200) + '...');

        // Try to extract JSON from the response
        let jsonContent = content.text;

        // Look for JSON in code blocks first
        const codeBlockMatch = content.text.match(/```(?:json)?\s*([\s\S]*?)```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
          jsonContent = codeBlockMatch[1].trim();
          console.log('Extracted JSON from code block');
        } else if (content.text.includes('{') && content.text.includes('}')) {
          // Try to extract JSON between curly braces if no code block is found
          const jsonMatch = content.text.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonContent = jsonMatch[0];
            console.log('Extracted JSON between curly braces');
          }
        }

        const extractedData = JSON.parse(jsonContent);
        console.log('Successfully parsed appointment data from Claude');
        return res.status(200).json({ extractedData });
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        console.error('Raw response:', content.text);

        // Return a fallback response with basic structure
        const fallbackData = {
          appointmentId: `appt-${Date.now()}`,
          clientName: '',
          clientEmail: '',
          clientPhone: '',
          date: '',
          time: '',
          propertyAddress: '',
          propertyType: 'House',
          status: 'pending',
          notes: 'Failed to parse appointment data from Claude response'
        };

        console.log('Using fallback appointment data due to parsing error');
        return res.status(200).json({ extractedData: fallbackData, warning: 'Partial data extraction due to parsing error' });
      }
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error extracting appointment data with Claude:', error);
    return res.status(500).json({ error: 'Failed to extract appointment data with Claude. Please try again.' });
  }
});

// Endpoint to extract appointment data from natural language with OpenAI
app.post('/api/openai/extract-appointment-data', async (req, res) => {
  try {
    const { naturalLanguageInput } = req.body;
    const axios = require('axios');

    if (!naturalLanguageInput) {
      return res.status(400).json({ error: 'No input provided' });
    }

    console.log('Received request to extract appointment data with OpenAI');
    console.log('Input:', naturalLanguageInput);

    // Get the current date for reference in relative date calculations
    const currentDate = new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    // Create the system message with instructions for the AI
    const systemMessage = `You are an AI assistant for real estate agents. Your task is to extract appointment details from the user's natural language input.

    For the time parameter, please format it as a standard time with AM/PM indicator (e.g., "10:00 AM", "2:30 PM").

    For the date parameter, when the user mentions relative dates like "today", "tomorrow", or "next Monday", calculate the actual calendar date based on the current date (${currentDate}). Always provide a full date with month, day, and year (e.g., "June 15, 2023").

    If the user doesn't specify a complete detail, make a reasonable assumption based on context.

    Extract the following fields:
    - appointmentId: Generate a unique ID
    - clientName: The name of the client
    - clientEmail: The email address of the client
    - clientPhone: The phone number of the client (if provided)
    - date: The appointment date (formatted as "Month Day, Year")
    - time: The appointment time (formatted as "H:MM AM/PM")
    - propertyAddress: The address of the property
    - propertyType: The type of property (House, Condo, Apartment, etc.)
    - status: The status of the appointment ("confirmed", "pending", or "cancelled")
    - notes: Any additional notes about the appointment

    Return the data in a structured JSON format.`;

    console.log('Using OpenAI API Key:', OPENAI_API_KEY.substring(0, 10) + '...');

    try {
      // Make the API request to OpenAI
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: "gpt-4",
          messages: [
            { role: "system", content: systemMessage },
            { role: "user", content: naturalLanguageInput }
          ],
          max_tokens: 1000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${OPENAI_API_KEY}`
          }
        }
      );

      console.log('Received response from OpenAI');

      // Extract the structured data
      const content = response.data.choices[0].message.content;
      console.log('Raw response from OpenAI:', content.substring(0, 100) + '...');

      try {
        // Clean the response text to handle markdown formatting
        const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)```/;
        const match = content.match(jsonBlockRegex);

        let jsonText = content;
        if (match && match[1]) {
          // Return the content inside the code block
          jsonText = match[1].trim();
          console.log('Extracted JSON from code block');
        }

        // Try to parse the JSON response
        const extractedData = JSON.parse(jsonText);
        console.log('Successfully parsed JSON response');
        return res.status(200).json({ extractedData });
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        console.log('Raw response:', content);

        // Fallback to using the Claude endpoint
        console.log('Falling back to Claude endpoint');

        // Create a new request to the Claude endpoint
        const claudeResponse = await anthropic.messages.create({
          model: "claude-3-opus-20240229",
          max_tokens: 1000,
          system: systemMessage,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: naturalLanguageInput }
              ]
            }
          ]
        });

        // Extract the structured data from Claude
        const claudeContent = claudeResponse.content[0];
        if (claudeContent.type === 'text') {
          try {
            const extractedData = JSON.parse(claudeContent.text);
            console.log('Successfully parsed JSON response from Claude fallback');
            return res.status(200).json({ extractedData, provider: 'claude-fallback' });
          } catch (claudeParseError) {
            console.error('Error parsing JSON response from Claude fallback:', claudeParseError);
            return res.status(500).json({ error: 'Failed to parse structured data from both OpenAI and Claude' });
          }
        } else {
          console.error('Unexpected response format from Claude fallback');
          return res.status(500).json({ error: 'Unexpected response format from Claude fallback' });
        }
      }
    } catch (apiError) {
      console.error('Error calling OpenAI API:', apiError.message);
      console.log('Falling back to Claude endpoint due to OpenAI API error');

      // Create a new request to the Claude endpoint
      const claudeResponse = await anthropic.messages.create({
        model: "claude-3-opus-20240229",
        max_tokens: 1000,
        system: systemMessage,
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: naturalLanguageInput }
            ]
          }
        ]
      });

      // Extract the structured data from Claude
      const claudeContent = claudeResponse.content[0];
      if (claudeContent.type === 'text') {
        try {
          const extractedData = JSON.parse(claudeContent.text);
          console.log('Successfully parsed JSON response from Claude fallback');
          return res.status(200).json({ extractedData, provider: 'claude-fallback' });
        } catch (claudeParseError) {
          console.error('Error parsing JSON response from Claude fallback:', claudeParseError);
          return res.status(500).json({ error: 'Failed to parse structured data from Claude fallback' });
        }
      } else {
        console.error('Unexpected response format from Claude fallback');
        return res.status(500).json({ error: 'Unexpected response format from Claude fallback' });
      }
    }
  } catch (error) {
    console.error('Error extracting appointment data with OpenAI:', error);
    return res.status(500).json({ error: 'Failed to extract appointment data with OpenAI. Please try again.' });
  }
});

// Endpoint to forward email requests to AWS API Gateway
app.post('/api/send-email', async (req, res) => {
  try {
    const { name, email, phone, subject, message } = req.body;

    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields. Please provide name, email, and message.'
      });
    }

    // Construct the message content
    const messageContent = `
Name: ${name}
Email: ${email}
${phone ? `Phone: ${phone}` : ''}
Subject: ${subject || 'N/A'}

Message:
${message}
    `;

    // Prepare the payload for the AWS API
    const payload = {
      toEmail: '<EMAIL>',
      subject: 'New Contact Us Message',
      message: messageContent
    };

    console.log('Forwarding email request to AWS API Gateway');

    // Forward the request to AWS API Gateway
    const response = await axios.post(
      'https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com/prod/send-email',
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('Email sent successfully via AWS API Gateway');

    // Return the response from AWS API Gateway
    return res.status(200).json({
      success: true,
      message: 'Email sent successfully',
      data: response.data
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to send email',
      error: error.message
    });
  }
});

// Client Appointments endpoints (for local appointment management)
// These endpoints handle appointments without requiring authentication
// and store data in a simple format for the appointment scheduler

// In-memory storage for client appointments (in production, this would use a database)
let clientAppointments = [];

// Create a new client appointment
app.post('/api/client-appointments', (req, res) => {
  try {
    console.log('Creating client appointment:', req.body);

    const appointmentData = {
      id: `appt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    clientAppointments.push(appointmentData);
    console.log('Client appointment created:', appointmentData.id);

    res.status(201).json(appointmentData);
  } catch (error) {
    console.error('Error creating client appointment:', error);
    res.status(500).json({ error: 'Failed to create appointment' });
  }
});

// Get all client appointments
app.get('/api/client-appointments', (req, res) => {
  try {
    console.log('Getting all client appointments, count:', clientAppointments.length);
    res.json(clientAppointments);
  } catch (error) {
    console.error('Error getting client appointments:', error);
    res.status(500).json({ error: 'Failed to get appointments' });
  }
});

// Get client appointments for a specific user
app.get('/api/client-appointments/user/:userId', (req, res) => {
  try {
    const { userId } = req.params;
    console.log('Getting client appointments for user:', userId);

    const userAppointments = clientAppointments.filter(apt => apt.userId === userId);
    console.log('Found appointments for user:', userAppointments.length);

    res.json(userAppointments);
  } catch (error) {
    console.error('Error getting user appointments:', error);
    res.status(500).json({ error: 'Failed to get user appointments' });
  }
});

// Get client appointment by ID
app.get('/api/client-appointments/:id', (req, res) => {
  try {
    const { id } = req.params;
    console.log('Getting client appointment by ID:', id);

    const appointment = clientAppointments.find(apt => apt.id === id);

    if (!appointment) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    res.json(appointment);
  } catch (error) {
    console.error('Error getting appointment by ID:', error);
    res.status(500).json({ error: 'Failed to get appointment' });
  }
});

// Update a client appointment
app.put('/api/client-appointments/:id', (req, res) => {
  try {
    const { id } = req.params;
    console.log('Updating client appointment:', id);

    const appointmentIndex = clientAppointments.findIndex(apt => apt.id === id);

    if (appointmentIndex === -1) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    clientAppointments[appointmentIndex] = {
      ...clientAppointments[appointmentIndex],
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    console.log('Client appointment updated:', id);
    res.json(clientAppointments[appointmentIndex]);
  } catch (error) {
    console.error('Error updating appointment:', error);
    res.status(500).json({ error: 'Failed to update appointment' });
  }
});

// Delete a client appointment
app.delete('/api/client-appointments/:id', (req, res) => {
  try {
    const { id } = req.params;
    console.log('Deleting client appointment:', id);

    const appointmentIndex = clientAppointments.findIndex(apt => apt.id === id);

    if (appointmentIndex === -1) {
      return res.status(404).json({ error: 'Appointment not found' });
    }

    clientAppointments.splice(appointmentIndex, 1);
    console.log('Client appointment deleted:', id);

    res.json({ message: 'Appointment deleted successfully' });
  } catch (error) {
    console.error('Error deleting appointment:', error);
    res.status(500).json({ error: 'Failed to delete appointment' });
  }
});

// Start server
console.log('Starting server...');

// Add OPTIONS handler for the neighborhood-compliance endpoint
app.options('/api/tools/neighborhood-compliance/generate', cors(corsOptions));

// Add a specific CORS middleware for the neighborhood-compliance endpoint
app.use('/api/tools/neighborhood-compliance/generate', (req, res, next) => {
  // Check if the origin is in our allowed origins list
  const origin = req.headers.origin;
  if (corsOptions.origin.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (process.env.NODE_ENV !== 'production') {
    // In development, allow all origins
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle OPTIONS requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// Endpoint for Neighborhood & Compliance Overview
app.post('/api/tools/neighborhood-compliance/generate', async (req, res) => {
  try {
    console.log('Received request for neighborhood & compliance report');
    console.log('Request body:', req.body);

    const { propertyAddress, zipCode, llm_provider } = req.body;

    // Validate required fields
    if (!propertyAddress) {
      console.log('Error: Property address is required');
      return res.status(400).json({ error: 'Property address is required' });
    }

    // Validate LLM provider
    if (llm_provider !== 'openai' && llm_provider !== 'claude') {
      console.log('Error: Invalid LLM provider:', llm_provider);
      return res.status(400).json({ error: 'Invalid LLM provider. Must be "openai" or "claude"' });
    }

    console.log(`Using ${llm_provider} to generate report for address: ${propertyAddress}`);
    console.log('Zip code:', zipCode || 'Not provided');

    // Create the system message with instructions for the AI
    const systemMessage = `You are a real estate data analyst specializing in neighborhood analysis and property compliance.
    Generate a comprehensive report for the provided property address that includes:

    1. Neighborhood data:
       - School information (ratings, top schools in the area)
       - Crime rate statistics
       - Local amenities (parks, restaurants, shopping, etc.)
       - Walkability score

    2. Compliance information:
       - Zoning type
       - HOA rules (if applicable)
       - Required permits for common modifications
       - Any compliance alerts or issues

    Return the data in this JSON format:
    {
      "neighborhoodSummary": {
        "schools": {
          "rating": "9/10",
          "topSchools": ["Lincoln High", "Washington Elementary"]
        },
        "crimeRate": "Low",
        "amenities": ["Parks", "Restaurants", "Public Transit"],
        "walkScore": 78
      },
      "complianceSummary": {
        "zoningType": "Residential R3",
        "hoaRules": ["No exterior modifications"],
        "permitsRequired": ["Driveway expansion"],
        "alerts": ["Flood zone designation"]
      },
      "generatedAt": "2024-04-24T16:00:00Z"
    }

    Make reasonable inferences based on the location. Be specific and provide actionable insights.`;

    // Create the user message with the property details
    const userMessage = `Please generate a neighborhood and compliance report for this property:

    Property Address: ${propertyAddress}
    Zip Code: ${zipCode || 'Not provided'}

    Include detailed information about schools, crime rates, amenities, walkability, zoning, HOA rules, required permits, and any compliance alerts.`;

    let response;

    if (llm_provider === 'claude') {
      console.log('Making API request to Claude...');
      try {
        // Make the API request to Claude
        response = await anthropic.messages.create({
          model: "claude-3-5-sonnet-20241022",
          max_tokens: 1500,
          system: systemMessage,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: userMessage }
              ]
            }
          ]
        });
        console.log('Claude API request successful');
      } catch (claudeError) {
        console.error('Error calling Claude API:', claudeError);
        throw claudeError;
      }

      // Extract the generated report
      const content = response.content[0];
      if (content.type === 'text') {
        try {
          // Check if the content contains a JSON code block
          let jsonContent = content.text;

          // Look for JSON in code blocks
          const match = content.text.match(/```(?:json)?\n([\s\S]*?)\n```/);
          if (match && match[1]) {
            jsonContent = match[1];
            console.log('Extracted JSON from code block:', jsonContent.substring(0, 100) + '...');
          } else if (content.text.includes('{') && content.text.includes('}')) {
            // Try to extract JSON between curly braces if no code block is found
            const jsonMatch = content.text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              jsonContent = jsonMatch[0];
              console.log('Extracted JSON between curly braces:', jsonContent.substring(0, 100) + '...');
            }
          }

          const reportData = JSON.parse(jsonContent);
          // Add generatedAt if not present
          if (!reportData.generatedAt) {
            reportData.generatedAt = new Date().toISOString();
          }
          return res.status(200).json(reportData);
        } catch (parseError) {
          console.error('Error parsing JSON response from Claude:', parseError);
          console.error('Raw content:', content.text);
          return res.status(500).json({ error: 'Failed to parse structured data from Claude' });
        }
      } else {
        console.error('Unexpected response format from Claude');
        return res.status(500).json({ error: 'Unexpected response format from Claude' });
      }
    } else {
      // Make the API request to OpenAI
      console.log('Making API request to OpenAI...');
      console.log('OpenAI API Key:', OPENAI_API_KEY ? `${OPENAI_API_KEY.substring(0, 10)}...` : 'Not provided');

      try {
        const openaiResponse = await axios.post(
          'https://api.openai.com/v1/chat/completions',
          {
            model: "gpt-4o",
            messages: [
              {
                role: "system",
                content: systemMessage
              },
              {
                role: "user",
                content: userMessage
              }
            ],
            max_tokens: 1500
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${OPENAI_API_KEY}`
            }
          }
        );

        console.log('OpenAI API request successful');

        // Extract the result
        const content = openaiResponse.data.choices[0].message.content;

        try {
          // Check if the content contains a JSON code block
          let jsonContent = content;

          // Look for JSON in code blocks
          const match = content.match(/```(?:json)?\n([\s\S]*?)\n```/);
          if (match && match[1]) {
            jsonContent = match[1];
            console.log('Extracted JSON from code block:', jsonContent.substring(0, 100) + '...');
          } else if (content.includes('{') && content.includes('}')) {
            // Try to extract JSON between curly braces if no code block is found
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              jsonContent = jsonMatch[0];
              console.log('Extracted JSON between curly braces:', jsonContent.substring(0, 100) + '...');
            }
          }

          const reportData = JSON.parse(jsonContent);
          // Add generatedAt if not present
          if (!reportData.generatedAt) {
            reportData.generatedAt = new Date().toISOString();
          }
          return res.status(200).json(reportData);
        } catch (parseError) {
          console.error('Error parsing JSON response from OpenAI:', parseError);
          console.error('Raw content:', content);
          return res.status(500).json({ error: 'Failed to parse structured data from OpenAI' });
        }
      } catch (openaiError) {
        console.error('Error calling OpenAI API:', openaiError);
        return res.status(500).json({ error: 'Failed to generate report with OpenAI. Please try again.' });
      }
    }
  } catch (error) {
    console.error('Error generating neighborhood & compliance report:', error);

    // Return an error response instead of a mock report
    return res.status(500).json({
      error: 'Failed to generate neighborhood & compliance report',
      details: error.message || 'Unknown error'
    });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // Use the correct hostname based on the environment
  const hostname = process.env.NODE_ENV === 'production'
    ? 'digital-realtor-api.onrender.com'
    : 'localhost';

  console.log(`API Base URL: ${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${hostname}${process.env.NODE_ENV === 'production' ? '' : ':' + PORT}/api`);
  console.log(`Health check: ${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${hostname}${process.env.NODE_ENV === 'production' ? '' : ':' + PORT}/health`);
});
