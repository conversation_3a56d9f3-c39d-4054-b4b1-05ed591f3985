// Simple rate limiting for API calls
let lastCallTime = 0;
const minTimeBetweenCalls = 1000; // 1 second minimum between calls

export async function rateLimitedFetch(url: string, options: RequestInit): Promise<Response> {
  const now = Date.now();
  const timeSinceLastCall = now - lastCallTime;
  
  if (timeSinceLastCall < minTimeBetweenCalls) {
    // Wait for the remaining time before making the call
    const waitTime = minTimeBetweenCalls - timeSinceLastCall;
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  // Update the last call time
  lastCallTime = Date.now();
  
  // Make the API call with exponential backoff for retries
  let retries = 0;
  const maxRetries = 1; // Limit to one retry
  
  while (true) {
    try {
      const response = await fetch(url, options);
      
      // If we get a rate limit error (429), wait and retry
      if (response.status === 429 && retries < maxRetries) {
        retries++;
        const retryAfter = parseInt(response.headers.get('Retry-After') || '1', 10);
        const waitTime = retryAfter * 1000 || Math.pow(2, retries) * 1000; // Exponential backoff
        
        console.log(`Rate limited. Retrying after ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }
      
      return response;
    } catch (error) {
      if (retries < maxRetries) {
        retries++;
        const waitTime = Math.pow(2, retries) * 1000; // Exponential backoff
        
        console.log(`API call failed. Retrying after ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }
      
      throw error;
    }
  }
}
