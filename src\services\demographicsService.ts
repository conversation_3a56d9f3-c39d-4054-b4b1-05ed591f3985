// Demographics Service
// This service handles fetching demographic data from the US Census Bureau API

// Constants
const PROXY_ENDPOINT = 'https://digital-realtor-api.onrender.com/api/proxy/census';
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours

// Simple in-memory cache
const demographicsCache = new Map<string, { timestamp: number; data: any }>();

// Types
export interface DemographicsData {
  population: number;
  median_age: number;
  median_household_income?: number;
  median_home_value?: number;
}

export interface DemographicsResponse {
  demographics: DemographicsData | string;
}

/**
 * Fetches demographic data for a given ZIP code
 * @param zipCode ZIP code (e.g., '60614')
 * @returns Promise with demographic data or error message
 */
export const fetchDemographics = async (zipCode: string): Promise<DemographicsResponse> => {
  try {
    // Validate input
    if (!zipCode || !/^\d{5}(-\d{4})?$/.test(zipCode)) {
      console.warn(`Invalid ZIP code: "${zipCode}"`);
      return { demographics: 'Data not available for this ZIP code' };
    }

    // Extract the 5-digit ZIP code if it includes the +4 extension
    const fiveDigitZip = zipCode.split('-')[0];

    // Check cache first
    const cacheKey = fiveDigitZip;
    const cachedEntry = demographicsCache.get(cacheKey);

    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
      console.log('Using cached demographics data');
      return { demographics: cachedEntry.data };
    }

    // Log request in development mode
    console.log('%c[DEV] Fetching demographics data', 'color: #f59e0b; font-weight: bold');
    console.log('ZIP Code:', fiveDigitZip);

    // Build the API URL with query parameters
    const apiUrl = `${PROXY_ENDPOINT}?zipCode=${fiveDigitZip}`;
    console.log('Census API proxy URL:', apiUrl);

    // Make the API request with a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(apiUrl, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('Census API response status:', response.status);

      if (!response.ok) {
        console.error('Census API response not OK:', response.status);

        // Try to get more error details
        try {
          const errorText = await response.text();
          console.error('Census API error details:', errorText);
        } catch (textError) {
          console.error('Could not read error response text');
        }

        return { demographics: 'Data not available for this ZIP code' };
      }

      const data = await response.json();
      console.log('Census API response data:', data);

      // Check if the response has the expected format
      if (!Array.isArray(data) || data.length < 2) {
        console.warn('Census API returned unexpected data format');
        return { demographics: 'Data not available for this ZIP code' };
      }

      // Parse the response
      // data[0] is the header row: ["NAME", "B01002_001E", "B01003_001E", "B19013_001E", "B25077_001E", "zip code tabulation area"]
      // data[1] is the data row: ["ZCTA5 60614", "35.2", "68000", "82000", "450000", "60614"]
      const medianAge = parseFloat(data[1][1]);
      const population = parseInt(data[1][2], 10);
      const medianHouseholdIncome = parseInt(data[1][3], 10);
      const medianHomeValue = parseInt(data[1][4], 10);

      // Validate the parsed data
      if (isNaN(medianAge) || isNaN(population)) {
        console.warn('Census API returned invalid numeric data for required fields');
        return { demographics: 'Data not available for this ZIP code' };
      }

      // Create the demographics data object
      const demographicsData: DemographicsData = {
        median_age: medianAge,
        population: population
      };

      // Add optional fields if they are valid
      // Census API returns -666666666 for missing data
      if (!isNaN(medianHouseholdIncome) && medianHouseholdIncome > 0) {
        demographicsData.median_household_income = medianHouseholdIncome;
      }

      if (!isNaN(medianHomeValue) && medianHomeValue > 0) {
        demographicsData.median_home_value = medianHomeValue;
      }

      console.log('Parsed demographics data:', demographicsData);

      // Cache the result
      demographicsCache.set(cacheKey, {
        timestamp: Date.now(),
        data: demographicsData
      });

      return { demographics: demographicsData };
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError; // Re-throw to be caught by the outer try/catch
    }
  } catch (error) {
    console.error('Error fetching demographics data:', error);

    // Check if it's an abort error (timeout)
    if (error.name === 'AbortError') {
      console.warn('Census API request timed out');
    }

    // Return a fallback response
    return { demographics: 'Data not available for this ZIP code' };
  }
};

/**
 * Extracts a ZIP code from an address string
 * @param address Full address string
 * @returns ZIP code or empty string
 */
export const extractZipCodeFromAddress = (address: string): string => {
  if (!address) return '';

  // Look for a 5-digit ZIP code, optionally followed by a hyphen and 4 more digits
  const zipMatch = address.match(/\b(\d{5}(?:-\d{4})?)\b/);
  return zipMatch ? zipMatch[1] : '';
};
