@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Primary Colors - Electric Blue */
  --primary-50: #e3f8ff;
  --primary-100: #b3e7ff;
  --primary-200: #80d5ff;
  --primary-300: #4dc3ff;
  --primary-400: #26b3ff;
  --primary-500: #00a0ff;
  --primary-600: #0084ff;
  --primary-700: #0068ff;
  --primary-800: #0052cc;
  --primary-900: #003f99;

  /* Secondary Colors - Neon Purple */
  --secondary-50: #f5f0ff;
  --secondary-100: #ead9ff;
  --secondary-200: #d8b9ff;
  --secondary-300: #c194ff;
  --secondary-400: #aa6eff;
  --secondary-500: #9333ff;
  --secondary-600: #8520ff;
  --secondary-700: #7000ff;
  --secondary-800: #5c00d2;
  --secondary-900: #4800a5;

  /* Neutral Colors - Dark Tech */
  --dark-50: #f5f9ff;
  --dark-100: #e6f1ff;
  --dark-200: #d1e3ff;
  --dark-300: #b3d1ff;
  --dark-400: #8ab4ff;
  --dark-500: #5a7cbe;
  --dark-600: #3d5a8c;
  --dark-700: #2a3f66;
  --dark-800: #192440;
  --dark-900: #0d1526;

  /* Accent Colors - Futuristic */
  --success: #00f2c3;
  --warning: #ff8d00;
  --error: #ff3366;
  --info: #00d4ff;
  --accent-cyan: #00ffff;
  --accent-magenta: #ff00ff;
  --accent-lime: #ccff00;

  /* Animation Timing */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-neon: 0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5);

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  background-color: var(--dark-900);
  color: var(--primary-100);
  line-height: 1.5;
  transition: background-color var(--transition-normal) ease, color var(--transition-normal) ease;
}

/* Basic styles */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Layout */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-6 > * + * {
  margin-left: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.min-h-screen {
  min-height: 100vh;
}

.h-full {
  height: 100%;
}

.w-full {
  width: 100%;
}

.max-w-xs {
  max-width: 20rem;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.flex-grow {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-1 {
  flex: 1 1 0%;
}

/* Grid */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 640px) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:flex-row {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }
}

/* Background */
.bg-transparent {
  background-color: transparent;
}

.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: var(--dark-50);
}

.bg-gray-100 {
  background-color: var(--dark-100);
}

.bg-gray-200 {
  background-color: var(--dark-200);
}

.bg-gray-800 {
  background-color: var(--dark-800);
}

.bg-gray-900 {
  background-color: var(--dark-900);
}

.bg-blue-50 {
  background-color: var(--primary-50);
}

.bg-blue-100 {
  background-color: var(--primary-100);
}

.bg-blue-500 {
  background-color: var(--primary-500);
}

.bg-blue-600 {
  background-color: var(--primary-600);
}

.bg-blue-700 {
  background-color: var(--primary-700);
}

.bg-purple-500 {
  background-color: var(--secondary-500);
}

.bg-purple-600 {
  background-color: var(--secondary-600);
}

.bg-gradient-primary {
  background-image: linear-gradient(to right, var(--primary-500), var(--primary-600));
}

.bg-gradient-secondary {
  background-image: linear-gradient(to right, var(--secondary-500), var(--secondary-600));
}

.bg-gradient-blue-purple {
  background-image: linear-gradient(to right, var(--primary-500), var(--secondary-500));
}

.bg-gradient-cyber {
  background: linear-gradient(135deg, var(--dark-800) 0%, var(--primary-900) 100%);
}

.bg-gradient-neon {
  background: linear-gradient(135deg, var(--primary-900) 0%, var(--secondary-900) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient-neon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(0, 212, 255, 0.1) 0%,
    rgba(147, 51, 255, 0.1) 100%);
  z-index: 1;
}

.bg-gradient-tech {
  background: radial-gradient(circle at top right, var(--primary-500) 0%, transparent 50%),
              radial-gradient(circle at bottom left, var(--secondary-500) 0%, var(--dark-900) 70%);
}

.hover\:bg-blue-700:hover {
  background-color: var(--primary-700);
}

.hover\:bg-gray-100:hover {
  background-color: var(--dark-100);
}

.hover\:bg-opacity-90:hover {
  background-opacity: 0.9;
}

/* Text */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-white {
  color: white;
}

.text-black {
  color: black;
}

.text-gray-400 {
  color: var(--dark-400);
}

.text-gray-500 {
  color: var(--dark-500);
}

.text-gray-600 {
  color: var(--dark-600);
}

.text-gray-700 {
  color: var(--dark-700);
}

.text-gray-800 {
  color: var(--dark-800);
}

.text-gray-900 {
  color: var(--dark-900);
}

.text-blue-500 {
  color: var(--primary-500);
}

.text-blue-600 {
  color: var(--primary-600);
}

.text-blue-700 {
  color: var(--primary-700);
}

.text-purple-500 {
  color: var(--secondary-500);
}

.text-purple-600 {
  color: var(--secondary-600);
}

.text-dark-400 {
  color: var(--dark-400);
}

.text-dark-500 {
  color: var(--dark-500);
}

.text-dark-600 {
  color: var(--dark-600);
}

.text-dark-700 {
  color: var(--dark-700);
}

.text-dark-800 {
  color: var(--dark-800);
}

.text-dark-900 {
  color: var(--dark-900);
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.leading-none {
  line-height: 1;
}

.leading-tight {
  line-height: 1.25;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.hover\:text-blue-600:hover {
  color: var(--primary-600);
}

.hover\:text-blue-700:hover {
  color: var(--primary-700);
}

.hover\:text-white:hover {
  color: white;
}

.hover\:underline:hover {
  text-decoration: underline;
}

/* Spacing */
.p-0 {
  padding: 0;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-12 {
  padding: 3rem;
}

.p-16 {
  padding: 4rem;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pl-0 {
  padding-left: 0;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pr-0 {
  padding-right: 0;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-4 {
  padding-right: 1rem;
}

.m-0 {
  margin: 0;
}

.m-1 {
  margin: 0.25rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-3 {
  margin: 0.75rem;
}

.m-4 {
  margin: 1rem;
}

.m-auto {
  margin: auto;
}

.mx-0 {
  margin-left: 0;
  margin-right: 0;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

/* Borders */
.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-md {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.rounded-2xl {
  border-radius: var(--radius-2xl);
}

.rounded-full {
  border-radius: var(--radius-full);
}

.rounded-t-lg {
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}

.rounded-b-lg {
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.border {
  border-width: 1px;
  border-style: solid;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-t {
  border-top-width: 1px;
  border-top-style: solid;
}

.border-b {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}

.border-l {
  border-left-width: 1px;
  border-left-style: solid;
}

.border-r {
  border-right-width: 1px;
  border-right-style: solid;
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  border-color: white;
}

.border-gray-100 {
  border-color: var(--dark-100);
}

.border-gray-200 {
  border-color: var(--dark-200);
}

.border-gray-300 {
  border-color: var(--dark-300);
}

.border-gray-400 {
  border-color: var(--dark-400);
}

.border-blue-500 {
  border-color: var(--primary-500);
}

.border-blue-600 {
  border-color: var(--primary-600);
}

.hover\:border-blue-600:hover {
  border-color: var(--primary-600);
}

/* Lists */
.list-none {
  list-style-type: none;
}

.list-disc {
  list-style-type: disc;
}

.list-decimal {
  list-style-type: decimal;
}

.pl-6 {
  padding-left: 1.5rem;
}

/* Shadows */
.shadow-none {
  box-shadow: none;
}

.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

.hover\:shadow-lg:hover {
  box-shadow: var(--shadow-lg);
}

.hover\:shadow-xl:hover {
  box-shadow: var(--shadow-xl);
}

/* Animations and Transitions */
.transition {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-normal);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-normal);
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-normal);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-normal);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-normal);
}

.duration-75 {
  transition-duration: 75ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.delay-100 {
  transition-delay: 100ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-500 {
  transition-delay: 500ms;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(49, 151, 149, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(49, 151, 149, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(49, 151, 149, 0);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-10px);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.animate-fade-out {
  animation: fadeOut var(--transition-normal) ease-in-out;
}

.animate-slide-in-up {
  animation: slideInUp var(--transition-normal) ease-out;
}

.animate-slide-in-down {
  animation: slideInDown var(--transition-normal) ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft var(--transition-normal) ease-out;
}

.animate-slide-in-right {
  animation: slideInRight var(--transition-normal) ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-delay-150 {
  animation-delay: 150ms;
}

/* Transform */
.scale-0 {
  transform: scale(0);
}

.scale-50 {
  transform: scale(0.5);
}

.scale-75 {
  transform: scale(0.75);
}

.scale-90 {
  transform: scale(0.9);
}

.scale-95 {
  transform: scale(0.95);
}

.scale-100 {
  transform: scale(1);
}

.scale-105 {
  transform: scale(1.05);
}

.scale-110 {
  transform: scale(1.1);
}

.scale-125 {
  transform: scale(1.25);
}

.scale-150 {
  transform: scale(1.5);
}

.rotate-0 {
  transform: rotate(0deg);
}

.rotate-45 {
  transform: rotate(45deg);
}

.rotate-90 {
  transform: rotate(90deg);
}

.rotate-180 {
  transform: rotate(180deg);
}

.translate-x-0 {
  transform: translateX(0);
}

.translate-x-1 {
  transform: translateX(0.25rem);
}

.translate-x-2 {
  transform: translateX(0.5rem);
}

.translate-x-4 {
  transform: translateX(1rem);
}

.-translate-x-1 {
  transform: translateX(-0.25rem);
}

.-translate-x-2 {
  transform: translateX(-0.5rem);
}

.-translate-x-4 {
  transform: translateX(-1rem);
}

.translate-y-0 {
  transform: translateY(0);
}

.translate-y-1 {
  transform: translateY(0.25rem);
}

.translate-y-2 {
  transform: translateY(0.5rem);
}

.translate-y-4 {
  transform: translateY(1rem);
}

.-translate-y-1 {
  transform: translateY(-0.25rem);
}

.-translate-y-2 {
  transform: translateY(-0.5rem);
}

.-translate-y-4 {
  transform: translateY(-1rem);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:scale-110:hover {
  transform: scale(1.1);
}

.hover\:-translate-y-1:hover {
  transform: translateY(-0.25rem);
}

.hover\:-translate-y-2:hover {
  transform: translateY(-0.5rem);
}

.hover\:translate-x-1:hover {
  transform: translateX(0.25rem);
}

.hover\:translate-x-2:hover {
  transform: translateX(0.5rem);
}

/* Custom Link Effects */
.link-hover-effect {
  position: relative;
  text-decoration: none;
  transition: all 0.3s ease;
}

.link-hover-effect::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--accent-cyan);
  box-shadow: 0 0 5px var(--accent-cyan), 0 0 10px var(--accent-cyan);
  transition: width 0.3s ease;
}

.link-hover-effect:hover::after {
  width: 100%;
}

.link-hover-slide {
  position: relative;
  overflow: hidden;
  display: inline-block;
  transition: all 0.3s ease;
}

.link-hover-slide::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background-color: var(--primary-500);
  transition: left 0.3s ease;
}

.link-hover-slide:hover::before {
  left: 0;
}

.button-hover-effect {
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
}

.button-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
  transition: left 0.7s ease;
  z-index: -1;
}

.button-hover-effect:hover::before {
  left: 100%;
}

/* Futuristic Effects */
.shimmer {
  background: linear-gradient(90deg,
    var(--primary-300) 0%,
    var(--primary-400) 25%,
    var(--accent-cyan) 50%,
    var(--primary-400) 75%,
    var(--primary-300) 100%);
  background-size: 200% 100%;
  animation: shimmer 3s infinite linear;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.neon-glow {
  box-shadow: 0 0 5px var(--accent-cyan),
              0 0 10px var(--accent-cyan),
              0 0 15px var(--accent-cyan),
              0 0 20px var(--accent-cyan);
}

.neon-text {
  text-shadow: 0 0 5px var(--accent-cyan),
               0 0 10px var(--accent-cyan),
               0 0 15px var(--accent-cyan),
               0 0 20px var(--accent-cyan);
}

.cyber-border {
  position: relative;
  border: 1px solid var(--accent-cyan);
  overflow: hidden;
  transition: all 0.3s ease;
}

.cyber-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-cyan));
  animation: cyber-border-animation 3s infinite linear;
}

@keyframes cyber-border-animation {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

.futuristic-gradient {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
}

.futuristic-gradient-animated {
  background: linear-gradient(135deg,
    var(--primary-600) 0%,
    var(--secondary-600) 50%,
    var(--primary-600) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Futuristic Gradients */
.bg-gradient-neon {
  background: linear-gradient(135deg, var(--dark-800) 0%, var(--primary-900) 50%, var(--dark-800) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient-neon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, var(--accent-cyan) 0%, transparent 50%);
  opacity: 0.15;
  z-index: 0;
}

.bg-gradient-neon > * {
  position: relative;
  z-index: 1;
}

.bg-gradient-tech {
  background: linear-gradient(135deg, var(--dark-900) 0%, var(--primary-900) 100%);
  position: relative;
  overflow: hidden;
}

.bg-gradient-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0, 255, 255, 0.03) 10px, rgba(0, 255, 255, 0.03) 20px);
  z-index: 0;
}

.bg-gradient-tech > * {
  position: relative;
  z-index: 1;
}

/* Footer Link Hover Effect */
.relative span.relative {
  position: relative;
}

.relative span.relative::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--accent-cyan);
  box-shadow: 0 0 5px var(--accent-cyan), 0 0 10px var(--accent-cyan);
  transition: width 0.3s ease;
}

.relative:hover span.relative::after {
  width: 100%;
}

/* Shadow Glow Effects */
.hover\:shadow-glow:hover {
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.4);
}

.shadow-glow-sm {
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.3), 0 0 10px rgba(0, 255, 255, 0.2);
}

/* Futuristic Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-800);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-500), var(--secondary-500));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--primary-400), var(--secondary-400));
  box-shadow: 0 0 5px var(--primary-400);
}

/* Dark mode styles - Cyberpunk Theme */
.dark {
  background-color: var(--dark-900);
  color: var(--primary-100);
}

.dark ::-webkit-scrollbar-track {
  background: var(--dark-900);
  border: 1px solid var(--primary-900);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-700), var(--secondary-700));
  border: 1px solid var(--primary-500);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--primary-600), var(--secondary-600));
  box-shadow: 0 0 8px var(--primary-500);
}

.dark .neon-text {
  text-shadow: 0 0 5px var(--accent-cyan),
               0 0 10px var(--accent-cyan),
               0 0 15px var(--accent-cyan),
               0 0 20px var(--accent-cyan),
               0 0 30px var(--accent-cyan);
}

/* Enhanced Analysis Results Styles */
.analysis-loading {
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analysis-metadata {
  display: flex;
  gap: 2rem;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.confidence-score, .data-quality {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.score.high, .quality.high {
  color: #4CAF50;
  font-weight: bold;
}

.score.medium, .quality.medium {
  color: #FF9800;
  font-weight: bold;
}

.score.low, .quality.low {
  color: #F44336;
  font-weight: bold;
}

.data-issues {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #FFC107;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.data-issues h4 {
  color: #FF8F00;
  margin-top: 0;
}

.analysis-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 2rem;
  margin: 1rem 0;
}

.enhanced-analysis-results {
  margin-top: 2rem;
}

.analysis-header h2 {
  color: var(--primary-100);
  margin-bottom: 1rem;
}

.analysis-footer {
  text-align: center;
  margin-top: 1rem;
  color: var(--gray-400);
}

.loading-subtext {
  color: var(--gray-400);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.analysis-error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #F44336;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.analysis-error h3 {
  color: #F44336;
  margin-top: 0;
}

.analysis-error .primary-button {
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .analysis-metadata {
    flex-direction: column;
    gap: 1rem;
  }
}
