# Render Environment Variables Setup

To ensure the proxy endpoints for Walk Score and SchoolDigger APIs work correctly, you need to add the following environment variables to your Render service:

## Required Environment Variables

1. **WALKSCORE_API_KEY**
   - Value: `********************************`
   - Description: API key for the Walk Score API

2. **RAPIDAPI_SCHOOLDIGGER_KEY**
   - Value: `**************************************************`
   - Description: API key for the SchoolDigger API via RapidAPI

## How to Add Environment Variables on Render

1. Log in to your Render dashboard
2. Select your Digital Realtor backend service
3. Go to the **Environment** tab
4. Add both environment variables under the "Environment Variables" section
5. Click **Save Changes**
6. Ren<PERSON> will automatically redeploy your application with the new environment variables

## Verifying the Setup

After deployment, you can verify that the proxy endpoints are working correctly by making requests to:

- Walk Score Proxy: `https://digital-realtor-api.onrender.com/api/proxy/walkscore?address=YOUR_ADDRESS`
- SchoolDigger Proxy: `https://digital-realtor-api.onrender.com/api/proxy/schooldigger?st=IL&q=Chicago`

These endpoints should return data from the respective APIs without any CORS errors.
