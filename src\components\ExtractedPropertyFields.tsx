import React from 'react';
import { FaCheck, FaHome, FaBed, FaBath, FaRulerCombined, FaCalendarAlt,
  FaTree, FaCar, FaUtensils, FaDollarSign, FaIdCard, FaCheckSquare,
  FaSquare, FaExclamationTriangle, FaInfoCircle, FaFileInvoiceDollar } from 'react-icons/fa';

interface PropertyField {
  id: string;
  label: string;
  value: string;
  icon: React.ReactNode;
}

interface ExtractedPropertyFieldsProps {
  propertyData: any;
  selectedFields: string[];
  onToggleField: (fieldId: string) => void;
  onApplySelected: () => void;
  onSelectAll?: () => void;
  onDeselectAll?: () => void;
  isFallbackData?: boolean;
  isIncompleteData?: boolean;
  validationIssue?: string;
}

const ExtractedPropertyFields: React.FC<ExtractedPropertyFieldsProps> = ({
  propertyData,
  selectedFields,
  onToggleField,
  onApplySelected,
  onSelectAll,
  onDeselectAll,
  isFallbackData = false,
  isIncompleteData = false,
  validationIssue = ''
}) => {
  // Map property data to fields with icons
  const extractFields = (): PropertyField[] => {
    const fields: PropertyField[] = [];

    if (propertyData) {
      // Use the mapped details if available, otherwise fall back to raw data
      const mappedDetails = propertyData._mappedDetails;
      const sourceData = mappedDetails || propertyData;

      console.log('ExtractedPropertyFields - Using data source:', {
        hasMappedDetails: !!mappedDetails,
        mappedDetails: mappedDetails,
        rawData: propertyData,
        sourceData: sourceData
      });

      // Property Type
      if (sourceData.propertyType || sourceData.homeType) {
        fields.push({
          id: 'propertyType',
          label: 'Property Type',
          value: sourceData.propertyType || sourceData.homeType || '',
          icon: <FaHome className="text-blue-400" />
        });
      }

      // Bedrooms
      if (sourceData.bedrooms !== null && sourceData.bedrooms !== undefined) {
        fields.push({
          id: 'bedrooms',
          label: 'Bedrooms',
          value: sourceData.bedrooms.toString(),
          icon: <FaBed className="text-blue-400" />
        });
      }

      // Bathrooms
      if (sourceData.bathrooms !== null && sourceData.bathrooms !== undefined) {
        fields.push({
          id: 'bathrooms',
          label: 'Bathrooms',
          value: sourceData.bathrooms.toString(),
          icon: <FaBath className="text-blue-400" />
        });
      }

      // Square Feet
      if (sourceData.squareFeet !== null && sourceData.squareFeet !== undefined) {
        fields.push({
          id: 'squareFeet',
          label: 'Square Feet',
          value: sourceData.squareFeet.toString(),
          icon: <FaRulerCombined className="text-blue-400" />
        });
      } else if (sourceData.livingArea || sourceData.livingAreaSqFt) {
        // Fallback to raw data fields
        fields.push({
          id: 'squareFeet',
          label: 'Square Feet',
          value: (sourceData.livingArea || sourceData.livingAreaSqFt).toString(),
          icon: <FaRulerCombined className="text-blue-400" />
        });
      }

      // Year Built
      if (sourceData.yearBuilt !== null && sourceData.yearBuilt !== undefined) {
        fields.push({
          id: 'yearBuilt',
          label: 'Year Built',
          value: sourceData.yearBuilt.toString(),
          icon: <FaCalendarAlt className="text-blue-400" />
        });
      }

      // Lot Size
      if (sourceData.lotSize !== null && sourceData.lotSize !== undefined) {
        fields.push({
          id: 'lotSize',
          label: 'Lot Size',
          value: sourceData.lotSize.toString(),
          icon: <FaTree className="text-blue-400" />
        });
      } else if (sourceData.lotSizeSqFt) {
        // Fallback to raw data fields
        fields.push({
          id: 'lotSize',
          label: 'Lot Size',
          value: sourceData.lotSizeSqFt.toString(),
          icon: <FaTree className="text-blue-400" />
        });
      }

      // Parking
      if (sourceData.parking && sourceData.parking !== 'N/A') {
        let displayValue;
        if (Array.isArray(sourceData.parking)) {
          displayValue = sourceData.parking.length > 2
            ? `${sourceData.parking.slice(0, 2).join(', ')} +${sourceData.parking.length - 2} more`
            : sourceData.parking.join(', ');
        } else {
          // Truncate very long parking descriptions
          const parkingStr = sourceData.parking.toString();
          displayValue = parkingStr.length > 50
            ? `${parkingStr.substring(0, 47)}...`
            : parkingStr;
        }

        fields.push({
          id: 'parking',
          label: 'Parking',
          value: displayValue,
          icon: <FaCar className="text-blue-400" />
        });
      } else if (sourceData.parkingFeatures) {
        // Fallback to raw data fields
        let displayValue;
        if (Array.isArray(sourceData.parkingFeatures)) {
          displayValue = sourceData.parkingFeatures.length > 2
            ? `${sourceData.parkingFeatures.slice(0, 2).join(', ')} +${sourceData.parkingFeatures.length - 2} more`
            : sourceData.parkingFeatures.join(', ');
        } else {
          const parkingStr = sourceData.parkingFeatures.toString();
          displayValue = parkingStr.length > 50
            ? `${parkingStr.substring(0, 47)}...`
            : parkingStr;
        }

        fields.push({
          id: 'parking',
          label: 'Parking',
          value: displayValue,
          icon: <FaCar className="text-blue-400" />
        });
      }

      // Appliances/Home Features
      if (sourceData.appliances && Array.isArray(sourceData.appliances) && sourceData.appliances.length > 0) {
        // Show more items before truncating since we have more space now
        const appliancesList = sourceData.appliances;
        const displayValue = appliancesList.length > 6
          ? `${appliancesList.slice(0, 6).join(', ')} +${appliancesList.length - 6} more`
          : appliancesList.join(', ');

        fields.push({
          id: 'appliances',
          label: 'Appliances/Features',
          value: displayValue,
          icon: <FaUtensils className="text-blue-400" />
        });
      } else if (sourceData.homeFeatures) {
        // Fallback to raw data fields
        const featuresArray = Array.isArray(sourceData.homeFeatures)
          ? sourceData.homeFeatures
          : sourceData.homeFeatures.toString().split(',').map(f => f.trim());

        const displayValue = featuresArray.length > 6
          ? `${featuresArray.slice(0, 6).join(', ')} +${featuresArray.length - 6} more`
          : featuresArray.join(', ');

        fields.push({
          id: 'appliances',
          label: 'Appliances/Features',
          value: displayValue,
          icon: <FaUtensils className="text-blue-400" />
        });
      }

      // Price Per Sq Ft
      if (sourceData.pricePerSqFt !== null && sourceData.pricePerSqFt !== undefined) {
        fields.push({
          id: 'pricePerSqFt',
          label: 'Price Per Sq Ft',
          value: `$${sourceData.pricePerSqFt}`,
          icon: <FaDollarSign className="text-blue-400" />
        });
      }

      // MLS ID
      if (sourceData.mlsId || sourceData.mlsNumber) {
        fields.push({
          id: 'mlsId',
          label: 'MLS ID',
          value: sourceData.mlsId || sourceData.mlsNumber || '',
          icon: <FaIdCard className="text-blue-400" />
        });
      }

      // Price
      if (sourceData.price !== null && sourceData.price !== undefined && sourceData.price > 0) {
        fields.push({
          id: 'price',
          label: 'Price',
          value: `$${sourceData.price.toLocaleString()}`,
          icon: <FaDollarSign className="text-blue-400" />
        });
      } else if (sourceData.zestimate) {
        // Fallback to zestimate
        fields.push({
          id: 'price',
          label: 'Price (Zestimate)',
          value: `$${sourceData.zestimate.toLocaleString()}`,
          icon: <FaDollarSign className="text-blue-400" />
        });
      }

      // HOA Fees
      if (sourceData.hoaFee !== null && sourceData.hoaFee !== undefined) {
        fields.push({
          id: 'hoaFees',
          label: 'HOA Fees',
          value: typeof sourceData.hoaFee === 'number'
            ? `$${sourceData.hoaFee}/month`
            : sourceData.hoaFee.toString(),
          icon: <FaDollarSign className="text-blue-400" />
        });
      }

      // Tax Information (Last 2 Years)
      if (sourceData.taxSummary) {
        fields.push({
          id: 'taxHistory',
          label: 'Property Taxes (Last 2 Years)',
          value: sourceData.taxSummary,
          icon: <FaFileInvoiceDollar className="text-blue-400" />
        });
      } else if (sourceData.taxHistory && Array.isArray(sourceData.taxHistory) && sourceData.taxHistory.length > 0) {
        // Fallback to raw tax history if summary is not available
        const taxSummary = sourceData.taxHistory
          .slice(0, 2) // Only show last 2 years
          .map((tax: any) => `${tax.year}: $${tax.amount.toLocaleString()}`)
          .join(' | ');

        fields.push({
          id: 'taxHistory',
          label: 'Property Taxes (Last 2 Years)',
          value: taxSummary,
          icon: <FaFileInvoiceDollar className="text-blue-400" />
        });
      }
    }

    console.log('ExtractedPropertyFields - Generated fields:', fields);
    return fields;
  };

  const fields = extractFields();

  if (fields.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 mb-6 bg-dark-800/80 p-6 rounded-lg border border-blue-500/30 max-w-none">
      <div className="flex flex-wrap items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-blue-400 flex items-center">
          <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Extracted Property Fields

          {/* Data source indicator */}
          {isFallbackData && (
            <span className="ml-2 text-xs text-yellow-300 bg-yellow-900/30 px-2 py-0.5 rounded-full flex items-center">
              <FaExclamationTriangle className="mr-1" size={10} />
              Estimated Data
            </span>
          )}
          {isIncompleteData && (
            <span className="ml-2 text-xs text-red-300 bg-red-900/30 px-2 py-0.5 rounded-full flex items-center">
              <FaExclamationTriangle className="mr-1" size={10} />
              Incomplete Data
            </span>
          )}
        </h3>

        {/* Select/Deselect buttons */}
        <div className="flex space-x-2 mt-2 sm:mt-0">
          {onSelectAll && (
            <button
              type="button"
              onClick={onSelectAll}
              className="flex items-center justify-center gap-2 px-3 py-1.5 rounded-md bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition text-xs"
            >
              <FaCheckSquare className="w-3.5 h-3.5" />
              <span>Select All</span>
            </button>
          )}

          {onDeselectAll && (
            <button
              type="button"
              onClick={onDeselectAll}
              className="flex items-center justify-center gap-2 px-3 py-1.5 rounded-md bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition text-xs"
            >
              <FaSquare className="w-3.5 h-3.5" />
              <span>Deselect All</span>
            </button>
          )}
        </div>
      </div>

      {/* Info message */}
      <div className="mb-3 text-xs text-blue-300 bg-blue-900/20 p-2 rounded border border-blue-900/30 flex items-start">
        <FaInfoCircle className="text-blue-300 mr-2 mt-0.5 flex-shrink-0" />
        <span>
          Select the property fields you want to use in your listing. Click "Apply Selected Fields" to add them to the form.
        </span>
      </div>

      {/* Warning for incomplete data */}
      {isIncompleteData && (
        <div className="mb-3 text-xs text-red-300 bg-red-900/20 p-2 rounded border border-red-900/30 flex items-start">
          <FaExclamationTriangle className="text-red-300 mr-2 mt-0.5 flex-shrink-0" />
          <span>
            {validationIssue || 'Some property data is missing or incomplete. Please review and enter missing details manually.'}
          </span>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
        {fields.map((field) => (
          <div
            key={field.id}
            className={`relative p-4 rounded-lg cursor-pointer transition-all duration-200 min-h-[100px] ${
              selectedFields.includes(field.id)
                ? 'bg-blue-900/40 border border-blue-500/50 shadow-md'
                : 'bg-dark-700/60 border border-dark-600 hover:border-blue-500/30 hover:bg-dark-700/80'
            }`}
            onClick={() => onToggleField(field.id)}
          >
            {/* Selection indicator */}
            <div className={`absolute top-2 right-2 w-5 h-5 rounded-full flex items-center justify-center transition-colors ${
              selectedFields.includes(field.id)
                ? 'bg-blue-500 text-white'
                : 'bg-dark-600 border border-dark-500'
            }`}>
              {selectedFields.includes(field.id) && <FaCheck className="text-xs" />}
            </div>

            {/* Content */}
            <div className="flex flex-col h-full pr-8">
              <div className="flex items-start mb-2">
                <div className="flex-shrink-0 mr-3 mt-0.5">
                  {field.icon}
                </div>
                <div className="flex-grow min-w-0">
                  <div className="text-xs font-medium text-primary-400 mb-1">{field.label}</div>
                </div>
              </div>
              <div className="flex-grow">
                <div className="text-sm text-primary-100 leading-relaxed break-words overflow-wrap-anywhere">
                  {field.value}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-between items-center">
        <div className="text-xs text-primary-400">
          {selectedFields.length} of {fields.length} fields selected
        </div>
        <button
          type="button"
          onClick={onApplySelected}
          className="flex items-center justify-center gap-2 px-5 py-2 rounded-md bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition"
          disabled={selectedFields.length === 0}
        >
          <FaCheck className="w-5 h-5" />
          <span>Apply Selected Fields</span>
        </button>
      </div>
    </div>
  );
};

export default ExtractedPropertyFields;
