import React from 'react';
import { FaCheck, FaHome, FaBed, FaBath, FaRulerCombined, FaCalendarAlt,
  FaTree, FaCar, FaUtensils, FaDollarSign, FaIdCard, FaCheckSquare,
  FaSquare, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';

interface PropertyField {
  id: string;
  label: string;
  value: string;
  icon: React.ReactNode;
}

interface ExtractedPropertyFieldsProps {
  propertyData: any;
  selectedFields: string[];
  onToggleField: (fieldId: string) => void;
  onApplySelected: () => void;
  onSelectAll?: () => void;
  onDeselectAll?: () => void;
  isFallbackData?: boolean;
  isIncompleteData?: boolean;
  validationIssue?: string;
}

const ExtractedPropertyFields: React.FC<ExtractedPropertyFieldsProps> = ({
  propertyData,
  selectedFields,
  onToggleField,
  onApplySelected,
  onSelectAll,
  onDeselectAll,
  isFallbackData = false,
  isIncompleteData = false,
  validationIssue = ''
}) => {
  // Map property data to fields with icons
  const extractFields = (): PropertyField[] => {
    const fields: PropertyField[] = [];

    if (propertyData) {
      // Property Type
      if (propertyData.homeType || propertyData.propertyType) {
        fields.push({
          id: 'propertyType',
          label: 'Property Type',
          value: propertyData.homeType || propertyData.propertyType || '',
          icon: <FaHome className="text-blue-400" />
        });
      }

      // Bedrooms
      if (propertyData.bedrooms) {
        fields.push({
          id: 'bedrooms',
          label: 'Bedrooms',
          value: propertyData.bedrooms.toString(),
          icon: <FaBed className="text-blue-400" />
        });
      }

      // Bathrooms
      if (propertyData.bathrooms) {
        fields.push({
          id: 'bathrooms',
          label: 'Bathrooms',
          value: propertyData.bathrooms.toString(),
          icon: <FaBath className="text-blue-400" />
        });
      }

      // Square Feet
      if (propertyData.livingArea || propertyData.livingAreaSqFt) {
        fields.push({
          id: 'squareFeet',
          label: 'Square Feet',
          value: (propertyData.livingArea || propertyData.livingAreaSqFt).toString(),
          icon: <FaRulerCombined className="text-blue-400" />
        });
      }

      // Year Built
      if (propertyData.yearBuilt) {
        fields.push({
          id: 'yearBuilt',
          label: 'Year Built',
          value: propertyData.yearBuilt.toString(),
          icon: <FaCalendarAlt className="text-blue-400" />
        });
      }

      // Lot Size
      if (propertyData.lotSize || propertyData.lotSizeSqFt) {
        fields.push({
          id: 'lotSize',
          label: 'Lot Size',
          value: propertyData.lotSize?.toString() || propertyData.lotSizeSqFt?.toString() || '',
          icon: <FaTree className="text-blue-400" />
        });
      }

      // Parking
      if (Array.isArray(propertyData.parking) ? propertyData.parking.length > 0 : propertyData.parkingFeatures) {
        fields.push({
          id: 'parking',
          label: 'Parking',
          value: Array.isArray(propertyData.parking)
            ? propertyData.parking.join(', ')
            : (propertyData.parkingFeatures || ''),
          icon: <FaCar className="text-blue-400" />
        });
      }

      // Appliances/Home Features
      if (Array.isArray(propertyData.appliances) ? propertyData.appliances.length > 0 : propertyData.homeFeatures) {
        fields.push({
          id: 'appliances',
          label: 'Appliances/Features',
          value: Array.isArray(propertyData.appliances)
            ? propertyData.appliances.join(', ')
            : (propertyData.homeFeatures || ''),
          icon: <FaUtensils className="text-blue-400" />
        });
      }

      // Price Per Sq Ft
      if (propertyData.pricePerSqFt) {
        fields.push({
          id: 'pricePerSqFt',
          label: 'Price Per Sq Ft',
          value: propertyData.pricePerSqFt.toString(),
          icon: <FaDollarSign className="text-blue-400" />
        });
      }

      // MLS ID
      if (propertyData.mlsId || propertyData.mlsNumber) {
        fields.push({
          id: 'mlsId',
          label: 'MLS ID',
          value: propertyData.mlsId || propertyData.mlsNumber || '',
          icon: <FaIdCard className="text-blue-400" />
        });
      }

      // Price
      if (propertyData.price || propertyData.zestimate) {
        fields.push({
          id: 'price',
          label: 'Price',
          value: (propertyData.price || propertyData.zestimate).toString(),
          icon: <FaDollarSign className="text-blue-400" />
        });
      }
    }

    return fields;
  };

  const fields = extractFields();

  if (fields.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 mb-6 bg-dark-800/80 p-4 rounded-lg border border-blue-500/30">
      <div className="flex flex-wrap items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-blue-400 flex items-center">
          <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Extracted Property Fields

          {/* Data source indicator */}
          {isFallbackData && (
            <span className="ml-2 text-xs text-yellow-300 bg-yellow-900/30 px-2 py-0.5 rounded-full flex items-center">
              <FaExclamationTriangle className="mr-1" size={10} />
              Estimated Data
            </span>
          )}
          {isIncompleteData && (
            <span className="ml-2 text-xs text-red-300 bg-red-900/30 px-2 py-0.5 rounded-full flex items-center">
              <FaExclamationTriangle className="mr-1" size={10} />
              Incomplete Data
            </span>
          )}
        </h3>

        {/* Select/Deselect buttons */}
        <div className="flex space-x-2 mt-2 sm:mt-0">
          {onSelectAll && (
            <button
              type="button"
              onClick={onSelectAll}
              className="flex items-center justify-center gap-2 px-3 py-1.5 rounded-md bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition text-xs"
            >
              <FaCheckSquare className="w-3.5 h-3.5" />
              <span>Select All</span>
            </button>
          )}

          {onDeselectAll && (
            <button
              type="button"
              onClick={onDeselectAll}
              className="flex items-center justify-center gap-2 px-3 py-1.5 rounded-md bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition text-xs"
            >
              <FaSquare className="w-3.5 h-3.5" />
              <span>Deselect All</span>
            </button>
          )}
        </div>
      </div>

      {/* Info message */}
      <div className="mb-3 text-xs text-blue-300 bg-blue-900/20 p-2 rounded border border-blue-900/30 flex items-start">
        <FaInfoCircle className="text-blue-300 mr-2 mt-0.5 flex-shrink-0" />
        <span>
          Select the property fields you want to use in your listing. Click "Apply Selected Fields" to add them to the form.
        </span>
      </div>

      {/* Warning for incomplete data */}
      {isIncompleteData && (
        <div className="mb-3 text-xs text-red-300 bg-red-900/20 p-2 rounded border border-red-900/30 flex items-start">
          <FaExclamationTriangle className="text-red-300 mr-2 mt-0.5 flex-shrink-0" />
          <span>
            {validationIssue || 'Some property data is missing or incomplete. Please review and enter missing details manually.'}
          </span>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mb-4">
        {fields.map((field) => (
          <div
            key={field.id}
            className={`flex items-start p-2 rounded cursor-pointer transition-colors ${
              selectedFields.includes(field.id)
                ? 'bg-blue-900/40 border border-blue-500/50'
                : 'bg-dark-700/60 border border-dark-600 hover:border-blue-500/30'
            }`}
            onClick={() => onToggleField(field.id)}
          >
            <div className="flex-shrink-0 mt-0.5 mr-2">
              {field.icon}
            </div>
            <div className="flex-grow min-w-0">
              <div className="text-xs text-primary-400">{field.label}</div>
              <div className="text-sm text-primary-100 truncate">{field.value}</div>
            </div>
            <div className={`flex-shrink-0 ml-2 w-5 h-5 rounded-full flex items-center justify-center ${
              selectedFields.includes(field.id)
                ? 'bg-blue-500 text-white'
                : 'bg-dark-600'
            }`}>
              {selectedFields.includes(field.id) && <FaCheck className="text-xs" />}
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-between items-center">
        <div className="text-xs text-primary-400">
          {selectedFields.length} of {fields.length} fields selected
        </div>
        <button
          type="button"
          onClick={onApplySelected}
          className="flex items-center justify-center gap-2 px-5 py-2 rounded-md bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition"
          disabled={selectedFields.length === 0}
        >
          <FaCheck className="w-5 h-5" />
          <span>Apply Selected Fields</span>
        </button>
      </div>
    </div>
  );
};

export default ExtractedPropertyFields;
