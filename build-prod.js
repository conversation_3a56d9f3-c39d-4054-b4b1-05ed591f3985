import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

console.log(`${colors.bright}${colors.cyan}=== Digital Realtor Production Build ====${colors.reset}\n`);

try {
  // Step 1: Clean previous build
  console.log(`${colors.yellow}Cleaning previous build...${colors.reset}`);
  if (fs.existsSync(path.join(__dirname, 'build'))) {
    fs.rmSync(path.join(__dirname, 'build'), { recursive: true, force: true });
  }
  console.log(`${colors.green}✓ Previous build cleaned${colors.reset}\n`);

  // Step 2: Install dependencies
  console.log(`${colors.yellow}Installing dependencies...${colors.reset}`);
  execSync('npm install', { stdio: 'inherit' });
  console.log(`${colors.green}✓ Dependencies installed${colors.reset}\n`);

  // Step 3: Run linting (optional)
  console.log(`${colors.yellow}Running linting...${colors.reset}`);
  try {
    execSync('npm run lint', { stdio: 'inherit' });
    console.log(`${colors.green}✓ Linting passed${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.yellow}Linting found issues, but continuing with build...${colors.reset}\n`);
  }

  // Step 4: Build the application
  console.log(`${colors.yellow}Building application...${colors.reset}`);
  // Use cross-platform way to set NODE_ENV
  const buildCmd = process.platform === 'win32'
    ? 'set NODE_ENV=production && npm run build'
    : 'NODE_ENV=production npm run build';
  execSync(buildCmd, { stdio: 'inherit' });
  console.log(`${colors.green}✓ Build completed${colors.reset}\n`);

  // Step 5: Copy server files to build directory
  console.log(`${colors.yellow}Preparing server files...${colors.reset}`);
  if (!fs.existsSync(path.join(__dirname, 'build', 'server'))) {
    fs.mkdirSync(path.join(__dirname, 'build', 'server'), { recursive: true });
  }

  // Copy server directory
  const copyDir = (src, dest) => {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const entries = fs.readdirSync(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        copyDir(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  };

  copyDir(path.join(__dirname, 'server'), path.join(__dirname, 'build', 'server'));

  // Copy package.json and .env.production
  fs.copyFileSync(
    path.join(__dirname, 'package.json'),
    path.join(__dirname, 'build', 'package.json')
  );

  fs.copyFileSync(
    path.join(__dirname, '.env.production'),
    path.join(__dirname, 'build', '.env')
  );

  console.log(`${colors.green}✓ Server files prepared${colors.reset}\n`);

  console.log(`${colors.bright}${colors.green}Production build completed successfully!${colors.reset}`);
  console.log(`${colors.cyan}The build is available in the 'build' directory.${colors.reset}`);
  console.log(`${colors.cyan}To start the production server:${colors.reset}`);
  console.log(`${colors.yellow}  cd build${colors.reset}`);
  console.log(`${colors.yellow}  npm install --production${colors.reset}`);
  console.log(`${colors.yellow}  npm start${colors.reset}`);

} catch (error) {
  console.error(`${colors.red}Build failed:${colors.reset}`, error);
  process.exit(1);
}
