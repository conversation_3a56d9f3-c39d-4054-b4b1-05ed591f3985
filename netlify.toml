[build]
  command = "npm install --include=dev && npm run build && echo '/* /index.html 200' > dist/_redirects"
  publish = "dist"
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF ./src/ ./public/"

[build.environment]
  NODE_VERSION = "20.11.1"
  NPM_VERSION = "10.8.2"
  NPM_CONFIG_PRODUCTION = "false"
  NODE_ENV = "production"
  NETLIFY_BUILD = "true"
  VITE_BACKEND_URL = "https://digital-realtor-api.onrender.com"
  VITE_FRONTEND_URL = "https://aidigitalrealtor.com"

# Handle SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"

# Cache fonts
[[headers]]
  for = "/assets/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Security headers for HTML
[[headers]]
  for = "/*"
  [headers.values]
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.clarity.ms https://www.googletagmanager.com https://maps.googleapis.com https://apis.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https://www.clarity.ms https://c.clarity.ms https://*.clarity.ms https://c.bing.com https://maps.googleapis.com https://*.blob.core.windows.net https://photos.zillowstatic.com https://*.zillowstatic.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://api.openai.com https://api.anthropic.com https://maps.googleapis.com https://apis.google.com https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com https://digital-realtor-api.onrender.com https://l.clarity.ms https://*.clarity.ms https://schooldigger-k-12-school-data-api.p.rapidapi.com https://api.walkscore.com https://photos.zillowstatic.com https://*.zillowstatic.com; frame-src 'self';"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self), interest-cohort=()"

# Enable Brotli compression if available
[build.processing]
  skip_processing = false
[build.processing.css]
  bundle = true
  minify = true
[build.processing.js]
  bundle = true
  minify = true
[build.processing.html]
  pretty_urls = true
[build.processing.images]
  compress = true
