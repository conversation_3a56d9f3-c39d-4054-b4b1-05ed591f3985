import express from 'express';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();

// Get OpenAI API key from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';

// Endpoint for generating staging suggestions using GPT-4 Vision
router.post('/suggestions', async (req, res) => {
  try {
    const { image, style } = req.body;

    if (!image) {
      return res.status(400).json({ error: 'No image provided' });
    }

    if (!style) {
      return res.status(400).json({ error: 'No style provided' });
    }

    // Log request in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log('[DEV] Received request for staging suggestions');
      console.log('Style:', style);
      console.log('Image length:', image.length);
    }

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional interior designer providing virtual staging suggestions.
    Analyze the empty room in the image and provide detailed staging recommendations in the ${style} style.
    Focus on furniture placement, color schemes, decor items, and overall design elements.
    Be specific about what furniture to place where, what colors to use, and what decor items would enhance the space.
    Format your response as a bulleted list of recommendations.`;

    // Make the API request to OpenAI
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'system',
            content: systemMessage
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analyze this empty room and suggest furniture placement, interior design style (${style}), color schemes, and decor enhancements.`
              },
              {
                type: 'image_url',
                image_url: {
                  url: image
                }
              }
            ]
          }
        ],
        max_tokens: 500
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        }
      }
    );

    // Extract the suggestions from the response
    const suggestions = response.data.choices[0].message.content;

    // Log response in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log('[DEV] Received response from OpenAI');
      console.log('Response status:', response.status);
      console.log('Suggestions length:', suggestions.length);
    }

    return res.status(200).json({ suggestions });
  } catch (error) {
    console.error('Error generating staging suggestions:', error);
    
    // Handle different types of errors
    if (axios.isAxiosError(error) && error.response) {
      return res.status(error.response.status).json({
        error: `OpenAI API error: ${error.response.data.error?.message || error.message}`
      });
    }
    
    return res.status(500).json({
      error: 'Failed to generate staging suggestions. Please try again.'
    });
  }
});

// Endpoint for generating staging images using DALL-E
router.post('/image', async (req, res) => {
  try {
    const { prompt, image } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'No prompt provided' });
    }

    // Log request in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log('[DEV] Received request for staging image');
      console.log('Prompt:', prompt);
    }

    // Make the API request to OpenAI DALL-E
    const response = await axios.post(
      'https://api.openai.com/v1/images/generations',
      {
        model: 'dall-e-3',
        prompt: prompt,
        n: 1,
        size: '1024x1024'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        }
      }
    );

    // Extract the image URL from the response
    const imageUrl = response.data.data[0].url;

    // Log response in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log('[DEV] Received response from DALL-E');
      console.log('Response status:', response.status);
      console.log('Image URL:', imageUrl);
    }

    return res.status(200).json({ imageUrl });
  } catch (error) {
    console.error('Error generating staging image:', error);
    
    // Handle different types of errors
    if (axios.isAxiosError(error) && error.response) {
      return res.status(error.response.status).json({
        error: `OpenAI API error: ${error.response.data.error?.message || error.message}`
      });
    }
    
    return res.status(500).json({
      error: 'Failed to generate staging image. Please try again.'
    });
  }
});

export default router;
