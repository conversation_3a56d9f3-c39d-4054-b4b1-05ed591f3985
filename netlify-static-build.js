// Static build script for Netlify
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('=== Digital Realtor Netlify Static Build ====\n');

try {
  // Step 1: Clean previous build
  console.log('Cleaning previous build...');
  if (fs.existsSync(path.join(__dirname, 'prod-build'))) {
    fs.rmSync(path.join(__dirname, 'prod-build'), { recursive: true, force: true });
  }
  fs.mkdirSync(path.join(__dirname, 'prod-build'), { recursive: true });
  console.log('✓ Previous build cleaned\n');

  // Step 2: Create a basic index.html file
  console.log('Creating basic index.html...');
  const indexHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Digital Realtor</title>
    <meta name="description" content="AI-powered tools for real estate professionals" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" />
    <script src="https://clarity.microsoft.com/s/r7yjq54tfv"></script>
    <style>
      body {
        font-family: 'Inter', sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f8fafc;
        color: #334155;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }
      .logo {
        max-width: 200px;
        margin-bottom: 2rem;
      }
      h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #0f172a;
      }
      p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        line-height: 1.6;
      }
      .button {
        display: inline-block;
        background-color: #3b82f6;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        text-decoration: none;
        font-weight: 500;
        transition: background-color 0.2s;
      }
      .button:hover {
        background-color: #2563eb;
      }
      .features {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 2rem;
        margin-top: 3rem;
      }
      .feature {
        flex: 1;
        min-width: 250px;
        max-width: 350px;
        padding: 1.5rem;
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      .feature h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #0f172a;
      }
      .feature p {
        font-size: 1rem;
        margin-bottom: 0;
      }
      .maintenance {
        margin-top: 3rem;
        padding: 1.5rem;
        background-color: #eff6ff;
        border-radius: 0.5rem;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img src="/images/logo.png" alt="AI Digital Realtor Logo" class="logo" />
      <h1>AI Digital Realtor</h1>
      <p>Transforming real estate with AI-powered tools for modern professionals</p>
      <div class="maintenance">
        <h2>Site Maintenance</h2>
        <p>We're currently updating our website to serve you better. Please check back soon!</p>
        <p>For immediate assistance, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>
      <div class="features">
        <div class="feature">
          <h2>AI Listing Generator</h2>
          <p>Create compelling property listings with AI-generated descriptions based on property details and images.</p>
        </div>
        <div class="feature">
          <h2>Comp Analyzer</h2>
          <p>Get accurate property valuations with our AI-powered comparative market analysis tool.</p>
        </div>
        <div class="feature">
          <h2>Inspection Report Generator</h2>
          <p>Generate detailed inspection reports with AI analysis of property issues and recommendations.</p>
        </div>
      </div>
    </div>
  </body>
</html>`;

  fs.writeFileSync(path.join(__dirname, 'prod-build', 'index.html'), indexHtml);
  console.log('✓ Basic index.html created\n');

  // Step 3: Create Netlify configuration files
  console.log('Creating Netlify configuration files...');

  // Create _redirects file for client-side routing
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', '_redirects'),
    '/* /index.html 200'
  );

  // Create a robots.txt file
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'robots.txt'),
    'User-agent: *\nAllow: /'
  );

  console.log('✓ Netlify configuration files created\n');

  // Step 4: Create images directory and copy logo
  console.log('Creating images directory...');
  fs.mkdirSync(path.join(__dirname, 'prod-build', 'images'), { recursive: true });
  
  // Create a simple placeholder logo if the original doesn't exist
  if (fs.existsSync(path.join(__dirname, 'public', 'images', 'logo.png'))) {
    fs.copyFileSync(
      path.join(__dirname, 'public', 'images', 'logo.png'),
      path.join(__dirname, 'prod-build', 'images', 'logo.png')
    );
  } else {
    // We'll just create a text file noting that the logo is missing
    fs.writeFileSync(
      path.join(__dirname, 'prod-build', 'images', 'logo-missing.txt'),
      'Logo image was not found in the source directory.'
    );
  }
  
  console.log('✓ Images directory created\n');

  // Step 5: Copy favicon if it exists
  console.log('Copying favicon...');
  if (fs.existsSync(path.join(__dirname, 'public', 'favicon.ico'))) {
    fs.copyFileSync(
      path.join(__dirname, 'public', 'favicon.ico'),
      path.join(__dirname, 'prod-build', 'favicon.ico')
    );
    console.log('✓ Favicon copied');
  } else {
    console.log('Warning: favicon.ico not found');
  }

  console.log('\nNetlify static build completed successfully!');
  console.log('The build is available in the prod-build directory.');

} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}
