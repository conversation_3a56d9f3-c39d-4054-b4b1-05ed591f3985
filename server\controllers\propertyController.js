import asyncHandler from 'express-async-handler';
import Property from '../models/propertyModel.js';

// @desc    Fetch all properties
// @route   GET /api/properties
// @access  Public
const getProperties = asyncHandler(async (req, res) => {
  const pageSize = 10;
  const page = Number(req.query.pageNumber) || 1;

  const keyword = req.query.keyword
    ? {
        $or: [
          { title: { $regex: req.query.keyword, $options: 'i' } },
          { 'address.city': { $regex: req.query.keyword, $options: 'i' } },
          { 'address.state': { $regex: req.query.keyword, $options: 'i' } },
          { 'address.zipCode': { $regex: req.query.keyword, $options: 'i' } },
        ],
      }
    : {};

  const count = await Property.countDocuments({ ...keyword });
  const properties = await Property.find({ ...keyword })
    .limit(pageSize)
    .skip(pageSize * (page - 1));

  res.json({ properties, page, pages: Math.ceil(count / pageSize) });
});

// @desc    Fetch single property
// @route   GET /api/properties/:id
// @access  Public
const getPropertyById = asyncHandler(async (req, res) => {
  const property = await Property.findById(req.params.id);

  if (property) {
    res.json(property);
  } else {
    res.status(404);
    throw new Error('Property not found');
  }
});

// @desc    Delete a property
// @route   DELETE /api/properties/:id
// @access  Private/Admin/Realtor
const deleteProperty = asyncHandler(async (req, res) => {
  const property = await Property.findById(req.params.id);

  if (property) {
    // Check if user is admin or the realtor who created the property
    if (req.user.isAdmin || (property.user.toString() === req.user._id.toString() && req.user.isRealtor)) {
      await property.remove();
      res.json({ message: 'Property removed' });
    } else {
      res.status(401);
      throw new Error('Not authorized to delete this property');
    }
  } else {
    res.status(404);
    throw new Error('Property not found');
  }
});

// @desc    Create a property
// @route   POST /api/properties
// @access  Private/Realtor
const createProperty = asyncHandler(async (req, res) => {
  const {
    title,
    address,
    description,
    price,
    images,
    propertyType,
    bedrooms,
    bathrooms,
    squareFeet,
    lotSize,
    yearBuilt,
    features,
    status,
  } = req.body;

  const property = new Property({
    user: req.user._id,
    title,
    address,
    description,
    price,
    images: images || [],
    propertyType,
    bedrooms,
    bathrooms,
    squareFeet,
    lotSize,
    yearBuilt,
    features: features || [],
    rating: 0,
    numReviews: 0,
    status,
  });

  const createdProperty = await property.save();
  res.status(201).json(createdProperty);
});

// @desc    Update a property
// @route   PUT /api/properties/:id
// @access  Private/Realtor
const updateProperty = asyncHandler(async (req, res) => {
  const {
    title,
    address,
    description,
    price,
    images,
    propertyType,
    bedrooms,
    bathrooms,
    squareFeet,
    lotSize,
    yearBuilt,
    features,
    status,
  } = req.body;

  const property = await Property.findById(req.params.id);

  if (property) {
    // Check if user is admin or the realtor who created the property
    if (req.user.isAdmin || (property.user.toString() === req.user._id.toString() && req.user.isRealtor)) {
      property.title = title || property.title;
      property.address = address || property.address;
      property.description = description || property.description;
      property.price = price || property.price;
      property.images = images || property.images;
      property.propertyType = propertyType || property.propertyType;
      property.bedrooms = bedrooms || property.bedrooms;
      property.bathrooms = bathrooms || property.bathrooms;
      property.squareFeet = squareFeet || property.squareFeet;
      property.lotSize = lotSize || property.lotSize;
      property.yearBuilt = yearBuilt || property.yearBuilt;
      property.features = features || property.features;
      property.status = status || property.status;

      const updatedProperty = await property.save();
      res.json(updatedProperty);
    } else {
      res.status(401);
      throw new Error('Not authorized to update this property');
    }
  } else {
    res.status(404);
    throw new Error('Property not found');
  }
});

// @desc    Create new review
// @route   POST /api/properties/:id/reviews
// @access  Private
const createPropertyReview = asyncHandler(async (req, res) => {
  const { rating, comment } = req.body;

  const property = await Property.findById(req.params.id);

  if (property) {
    const alreadyReviewed = property.reviews.find(
      (r) => r.user.toString() === req.user._id.toString()
    );

    if (alreadyReviewed) {
      res.status(400);
      throw new Error('Property already reviewed');
    }

    const review = {
      name: req.user.name,
      rating: Number(rating),
      comment,
      user: req.user._id,
    };

    property.reviews.push(review);

    property.numReviews = property.reviews.length;

    property.rating =
      property.reviews.reduce((acc, item) => item.rating + acc, 0) /
      property.reviews.length;

    await property.save();
    res.status(201).json({ message: 'Review added' });
  } else {
    res.status(404);
    throw new Error('Property not found');
  }
});

// @desc    Get top rated properties
// @route   GET /api/properties/top
// @access  Public
const getTopProperties = asyncHandler(async (req, res) => {
  const properties = await Property.find({}).sort({ rating: -1 }).limit(3);

  res.json(properties);
});

// @desc    Get featured properties
// @route   GET /api/properties/featured
// @access  Public
const getFeaturedProperties = asyncHandler(async (req, res) => {
  const properties = await Property.find({ isFeatured: true }).limit(6);

  res.json(properties);
});

export {
  getProperties,
  getPropertyById,
  deleteProperty,
  createProperty,
  updateProperty,
  createPropertyReview,
  getTopProperties,
  getFeaturedProperties,
};
