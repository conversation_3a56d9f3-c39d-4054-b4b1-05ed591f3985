// Constants
const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';
const OPENAI_API_URL = 'https://api.openai.com/v1';
const MIN_REQUEST_INTERVAL = 1000; // 1 second between requests
const CACHE_TTL = 30 * 60 * 1000; // 30 minutes

// Simple in-memory cache
const reportCache = new Map<string, { timestamp: number; data: any }>();

// Last request timestamp for rate limiting
let lastRequestTime = 0;

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create a cache key
const createCacheKey = (address: string, zipCode: string, llmProvider: string) => {
  return `${address.trim().toLowerCase()}_${zipCode}_${llmProvider}`;
};

// Function to generate a neighborhood and compliance report
export const generateNeighborhoodComplianceReport = async (
  address: string,
  zipCode: string,
  llmProvider: 'openai' | 'claude',
  apiKey: string
): Promise<any> => {
  // Check cache first
  const cacheKey = createCacheKey(address, zipCode, llmProvider);
  const cachedEntry = reportCache.get(cacheKey);

  if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
    console.log('Using cached neighborhood & compliance report');
    return cachedEntry.data;
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  lastRequestTime = Date.now();

  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log(`%c[DEV] Generating neighborhood & compliance report with ${llmProvider}`,
        `color: ${llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
      console.log('Address:', address);
      console.log('Zip Code:', zipCode);
      console.log('API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'Not provided');
      console.log('API URL:', `${API_BASE_URL}/api/tools/neighborhood-compliance/generate`);
    }

    // Prepare the request payload
    const payload = {
      propertyAddress: address,
      zipCode: zipCode || extractZipCode(address),
      llm_provider: llmProvider
    };

    // Make the API request
    const response = await fetch(`${API_BASE_URL}/api/tools/neighborhood-compliance/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(payload)
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('API response not OK:', response.status, data);
      throw new Error(data.error || `API request failed with status ${response.status}`);
    }

    // Check if the response is a mock report (for debugging purposes)
    if (import.meta.env.DEV &&
        data.neighborhoodSummary?.schools?.topSchools?.includes('Lincoln High School') &&
        data.neighborhoodSummary?.schools?.topSchools?.includes('Washington Elementary') &&
        data.neighborhoodSummary?.schools?.topSchools?.includes('Jefferson Middle School')) {
      console.warn('%c[DEV] Received mock report from backend. This is not a real AI-generated report.', 'color: orange; font-weight: bold');
    } else {
      console.log('%c[DEV] Received real AI-generated report from backend.', 'color: green; font-weight: bold');
    }

    // Cache the result
    reportCache.set(cacheKey, {
      timestamp: Date.now(),
      data
    });

    return data;
  } catch (error) {
    console.error(`Error generating neighborhood & compliance report with ${llmProvider}:`, error);

    // In development mode, we'll still return a mock report to allow for UI testing
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Generating mock report for development due to API error', 'color: orange; font-weight: bold');
      const mockReport = generateMockReport(address, zipCode);

      // Cache the mock result
      reportCache.set(cacheKey, {
        timestamp: Date.now(),
        data: mockReport
      });

      return mockReport;
    }

    throw error;
  }
};

// Helper function to extract zip code from address
const extractZipCode = (address: string): string => {
  // Simple regex to extract US zip code (5 digits, optionally followed by dash and 4 more digits)
  const zipCodeMatch = address.match(/\b\d{5}(?:-\d{4})?\b/);
  return zipCodeMatch ? zipCodeMatch[0] : '';
};

// Function to generate a mock report for development purposes
const generateMockReport = (address: string, zipCode: string) => {
  return {
    neighborhoodSummary: {
      schools: {
        rating: "8/10",
        topSchools: [
          "Lincoln High School",
          "Washington Elementary",
          "Jefferson Middle School"
        ]
      },
      crimeRate: "Low",
      amenities: [
        "Parks",
        "Restaurants",
        "Shopping Centers",
        "Public Transit",
        "Medical Facilities"
      ],
      walkScore: 78
    },
    complianceSummary: {
      zoningType: "Residential R3",
      hoaRules: [
        "No exterior modifications without approval",
        "Quiet hours from 10 PM to 7 AM",
        "No short-term rentals (less than 30 days)"
      ],
      permitsRequired: [
        "Building permit for structural changes",
        "Permit required for fence installation",
        "Special permit for home business operation"
      ],
      alerts: [
        "Property is in a flood zone designation",
        "Historical district restrictions apply to exterior changes"
      ]
    },
    generatedAt: new Date().toISOString()
  };
};
