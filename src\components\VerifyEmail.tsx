import { useState, useEffect } from 'react';
import { useNavigate, Link, useParams } from 'react-router-dom';
import userService from '../services/userService';

const VerifyEmail = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setStatus('error');
        setMessage('Invalid verification link');
        return;
      }

      try {
        // For demo purposes, simulate a delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Simulate verification failure for specific tokens
        if (token === 'invalid') {
          throw new Error('Invalid or expired verification token');
        }

        // Call the verification API
        const response = await userService.verifyEmail(token);
        setStatus('success');
        setMessage(response.message || 'Your email has been verified successfully! You can now log in to your account.');
      } catch (error: any) {
        setStatus('error');
        setMessage(
          error.message ||
          error.response?.data?.message ||
          'Verification failed. The link may be invalid or expired. Please request a new verification email.'
        );
      }
    };

    verifyEmail();
  }, [token]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Link to="/" className="inline-block">
            <div className="flex items-center justify-center">
              <svg className="w-8 h-8 text-accent-cyan" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
                  stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span className="ml-2 text-2xl font-bold text-accent-cyan">Digital Realtor</span>
            </div>
          </Link>
          <h2 className="mt-6 text-3xl font-extrabold text-white">Email Verification</h2>
        </div>

        <div className="bg-dark-800 p-8 rounded-xl shadow-lg border border-primary-800/30">
          {status === 'loading' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-800/20 mb-4">
                <svg className="animate-spin h-6 w-6 text-accent-cyan" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-medium text-white mb-2">Verifying your email</h3>
              <p className="text-primary-200">
                Please wait while we verify your email address...
              </p>
            </div>
          )}

          {status === 'success' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-success/10 mb-4">
                <svg className="h-6 w-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h3 className="text-xl font-medium text-white mb-2">Email Verified!</h3>
              <p className="text-primary-200 mb-6">
                {message}
              </p>
              <Link
                to="/login"
                className="w-full inline-block py-2 px-4 border border-transparent rounded-md text-white futuristic-gradient-animated hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-cyan text-center"
              >
                Sign in to your account
              </Link>
            </div>
          )}

          {status === 'error' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-error/10 mb-4">
                <svg className="h-6 w-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-medium text-white mb-2">Verification Failed</h3>
              <p className="text-primary-200 mb-6">
                {message}
              </p>
              <div className="flex flex-col space-y-3">
                <Link
                  to="/register"
                  className="w-full py-2 px-4 border border-transparent rounded-md text-primary-100 bg-dark-700 hover:bg-dark-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-cyan text-center"
                >
                  Register again
                </Link>
                <Link
                  to="/login"
                  className="w-full py-2 px-4 border border-transparent rounded-md text-white futuristic-gradient-animated hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-cyan text-center"
                >
                  Go to login
                </Link>
              </div>
            </div>
          )}
        </div>

        <div className="text-center">
          <Link to="/" className="text-sm text-primary-300 hover:text-white">
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmail;
