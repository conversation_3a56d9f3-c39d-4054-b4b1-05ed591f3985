const express = require('express');
const router = express.Router();
const axios = require('axios');
const { diagnosticLog } = require('../utils/diagnosticLog');

console.log('[DEBUG] Zillow routes module loaded');

// Simple test route to verify routing is working
router.get('/test-polygon', (req, res) => {
  console.log('[DEBUG] Test polygon route hit!');
  res.json({ message: 'Polygon test route working!', timestamp: new Date().toISOString() });
});

const path = require('path');
const {
  parseZillowPropertyDetails,
  zillowDetailsToFormFields,
  zillowDetailsToAiContext
} = require(path.join(__dirname, '..', 'utils', 'zillowFieldMapping.js'));

// Get RapidAPI key from environment variables
const RAPIDAPI_KEY = process.env.RAPIDAPI_ZILLOW_KEY;

// Log the API key (safely) for debugging
diagnosticLog('Zillow API Key Check', 'RAPIDAPI_ZILLOW_KEY', RAPIDAPI_KEY);

// Verify that the API key is set
if (!RAPIDAPI_KEY) {
  console.error('ERROR: RAPIDAPI_ZILLOW_KEY environment variable is not set!');
}

// Helper function to validate required parameters
const validateRequiredParams = (params, requiredParams) => {
  const missingParams = requiredParams.filter(param => !params[param]);
  if (missingParams.length > 0) {
    return {
      valid: false,
      message: `Missing required parameters: ${missingParams.join(', ')}`
    };
  }
  return { valid: true };
};

/**
 * Fetch property details from Zillow API via RapidAPI using coordinates
 * @route GET /api/zillow/property-by-coordinates
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {number} d - Search radius in miles (optional, default: 0.1)
 * @param {boolean} includeSold - Whether to include sold properties (optional, default: true)
 * @returns {Object} Property details from Zillow
 */
router.get('/property-by-coordinates', async (req, res) => {
  const { lat, lng, d = 0.1, includeSold = true, originalAddress } = req.query;

  if (!lat || !lng) {
    return res.status(400).json({ error: 'Missing lat or lng query parameters' });
  }

  try {
    // Log request details for debugging (without exposing the full API key)
    console.log(`[DEBUG] Zillow API Request: propertyByCoordinates with lat=${lat}, lng=${lng}, d=${d}, includeSold=${includeSold}`);
    console.log('🚀 BACKEND ZILLOW API REQUEST (Coordinates):', {
      lat,
      lng,
      searchRadius: d,
      includeSold,
      originalAddress,
      fullUrl: `https://zillow-com1.p.rapidapi.com/propertyByCoordinates?lat=${lat}&long=${lng}&d=${d}&includeSold=${includeSold}`,
      timestamp: new Date().toISOString()
    });
    diagnosticLog('Zillow API Request Headers', 'X-RapidAPI-Key', RAPIDAPI_KEY);

    // Verify API key is available before making the request
    if (!RAPIDAPI_KEY) {
      throw new Error('RAPIDAPI_ZILLOW_KEY is not configured. Please check your environment variables.');
    }

    const response = await axios.get('https://zillow-com1.p.rapidapi.com/propertyByCoordinates', {
      params: {
        lat,
        long: lng,
        d,
        includeSold
      },
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      }
    });

    // Log response details for debugging
    console.log(`[DEBUG] Zillow API Response: Status ${response.status}`);
    console.log(`[DEBUG] Zillow API Response Data Type: ${typeof response.data}`);
    console.log(`[DEBUG] Zillow API Response Data:`, JSON.stringify(response.data, null, 2));

    // Check if the response contains valid property data
    if (!response.data || response.data.length === 0) {
      console.log('[DEBUG] No properties found in response');
      return res.status(404).json({
        error: 'No properties found',
        message: 'No properties found at this location. Please try adjusting the search radius or entering details manually.'
      });
    }

    // Find the best property from the results
    const properties = response.data;
    console.log(`[DEBUG] Found ${properties.length} properties, selecting best match...`);

    // Extract apartment/unit number from the original address if provided
    let requestedApartment = null;
    if (originalAddress) {
      const aptMatch = originalAddress.match(/(?:apt|apartment|unit|#)\s*(\w+)/i);
      if (aptMatch) {
        requestedApartment = aptMatch[1].toUpperCase();
        console.log(`[DEBUG] Detected requested apartment: ${requestedApartment} from original address: ${originalAddress}`);
      }
    }

    // Filter and rank properties to find the best match
    let bestProperty = null;
    let bestScore = -1;

    for (const item of properties) {
      const property = item.property || item; // Handle nested structure

      // Skip properties with no useful data
      if (!property.address || !property.address.streetAddress) {
        continue;
      }

      // Calculate a score based on data completeness and relevance
      let score = 0;

      // HIGHEST PRIORITY: Exact apartment number match
      if (requestedApartment) {
        const propertyAddress = property.address.streetAddress.toUpperCase();
        const propertyAptMatch = propertyAddress.match(/(?:APT|APARTMENT|UNIT|#)\s*(\w+)/i);
        if (propertyAptMatch && propertyAptMatch[1].toUpperCase() === requestedApartment) {
          score += 50; // Very high boost for exact apartment match
          console.log(`[DEBUG] EXACT APARTMENT MATCH: ${property.address.streetAddress} matches requested ${requestedApartment}`);
        }
      }

      // Prefer properties with price data
      if (property.price && property.price > 0) score += 10;

      // Prefer properties with living area data
      if (property.livingArea && property.livingArea > 0) score += 8;

      // Prefer properties with bedroom/bathroom data
      if (property.bedrooms && property.bedrooms > 0) score += 5;
      if (property.bathrooms && property.bathrooms > 0) score += 5;

      // Prefer active listings over sold/other
      if (property.homeStatus === 'FOR_SALE') score += 15;
      else if (property.homeStatus === 'RECENTLY_SOLD') score += 10;
      else if (property.homeStatus === 'OTHER') score += 3;

      // Prefer condos/apartments over multi-family for unit searches
      if (property.homeType === 'CONDO' || property.homeType === 'APARTMENT') score += 3;

      console.log(`[DEBUG] Property ${property.address.streetAddress}: score=${score}, price=${property.price}, livingArea=${property.livingArea}, status=${property.homeStatus}`);

      if (score > bestScore) {
        bestScore = score;
        bestProperty = item;
      }
    }

    // If no good property found, use the first one
    if (!bestProperty) {
      console.log('[DEBUG] No high-quality property found, using first available');
      bestProperty = properties[0];
    }

    // Flatten the property structure for easier processing
    const propertyData = bestProperty.property || bestProperty;
    const flattenedProperty = {
      ...propertyData,
      // Ensure address components are at the top level for easier mapping
      streetAddress: propertyData.address?.streetAddress,
      city: propertyData.address?.city,
      state: propertyData.address?.state,
      zipcode: propertyData.address?.zipcode,
      // Keep the original nested structure as well
      address: propertyData.address
    };

    console.log(`[DEBUG] Selected property:`, JSON.stringify(flattenedProperty.address, null, 2));
    console.log(`[DEBUG] Flattened property keys:`, Object.keys(flattenedProperty));

    // Process the data through the mapping functions to create the expected structure
    try {
      // Parse the property details
      const mappedDetails = parseZillowPropertyDetails(flattenedProperty);
      console.log(`[DEBUG] Mapped details:`, mappedDetails);

      // Convert to form fields
      const formFields = zillowDetailsToFormFields(mappedDetails);
      console.log(`[DEBUG] Form fields:`, formFields);

      // Create AI context
      const aiContext = zillowDetailsToAiContext(mappedDetails);
      console.log(`[DEBUG] AI context:`, aiContext);

      // Create the response with all the processed data
      const response = {
        ...flattenedProperty,
        _mappedDetails: mappedDetails,
        _formFields: formFields,
        _aiContext: aiContext,
        _processed: true
      };

      console.log(`[DEBUG] Final response keys:`, Object.keys(response));
      res.json(response);
    } catch (mappingError) {
      console.error('[ERROR] Failed to process property data through mapping functions:', mappingError);

      // Fall back to returning just the flattened property with a flag
      const response = {
        ...flattenedProperty,
        _processed: false,
        _error: 'Failed to process property data through mapping functions'
      };

      res.json(response);
    }
  } catch (error) {
    console.error('Zillow API Error:', error.response?.data || error.message);

    // Return a more detailed error response
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch property data',
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      code: error.code
    });
  }
});

/**
 * Fetch property details from Zillow API via RapidAPI (legacy endpoint)
 * @route GET /api/zillow/property
 * @param {string} address - The property address
 * @param {string} citystatezip - The city, state, and zip code
 * @returns {Object} Property details from Zillow
 */
router.get('/property', async (req, res) => {
  const { address, citystatezip } = req.query;

  if (!address) {
    return res.status(400).json({ error: 'Missing address query parameter' });
  }

  try {
    // Log request details for debugging (without exposing the full API key)
    console.log(`[DEBUG] Zillow API Request: property with address=${address}, citystatezip=${citystatezip || ''}`);
    diagnosticLog('Zillow API Request Headers', 'X-RapidAPI-Key', RAPIDAPI_KEY);

    // Verify API key is available before making the request
    if (!RAPIDAPI_KEY) {
      throw new Error('RAPIDAPI_ZILLOW_KEY is not configured. Please check your environment variables.');
    }

    const response = await axios.get('https://zillow-com1.p.rapidapi.com/property', {
      params: {
        address,
        citystatezip: citystatezip || ''
      },
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      }
    });

    // Check if the response contains valid property data
    if (!response.data || response.data.error) {
      return res.status(404).json({
        error: 'Property not found',
        message: response.data.error || 'No property data available for this address'
      });
    }

    res.json(response.data);
  } catch (error) {
    console.error('Zillow API Error:', error.response?.data || error.message);

    // Return a more detailed error response
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch property data',
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      code: error.code
    });
  }
});

/**
 * Generate AI-suggested address corrections when Zillow API fails
 * @route POST /api/zillow/suggest-corrections
 * @param {string} address - The failed address
 * @returns {Array} Array of suggested address corrections
 */
router.post('/suggest-corrections', async (req, res) => {
  const { address, provider = 'openai' } = req.body;

  if (!address) {
    return res.status(400).json({ error: 'Missing address in request body' });
  }

  try {
    let suggestions = [];

    if (provider === 'claude') {
      // Use Claude for address correction suggestions
      const { Anthropic } = require('@anthropic-ai/sdk');
      const CLAUDE_API_KEY = process.env.ANTHROPIC_API_KEY;
      const anthropic = new Anthropic({ apiKey: CLAUDE_API_KEY });

      const prompt = `You are helping resolve a property address that failed to return results from Zillow.
The user selected this address: "${address}"
Suggest 1 to 3 similar corrected or more specific variations of this address that might work better with Zillow. Consider:
- Adding unit/apartment numbers
- Alternate street spellings (St. vs Street, N vs North)
- Adjusting ZIP code if needed
- Including or removing suite/building identifiers
Do not explain — just return a JSON list of corrected addresses.`;

      const response = await anthropic.messages.create({
        model: "claude-3-opus-20240229",
        max_tokens: 300,
        messages: [
          { role: "user", content: prompt }
        ]
      });

      // Extract the suggestions from Claude's response
      const content = response.content[0].text;
      try {
        // Try to parse the JSON response
        suggestions = JSON.parse(content);
      } catch (parseError) {
        // If parsing fails, extract array-like content
        const matches = content.match(/\[.*\]/s);
        if (matches) {
          try {
            suggestions = JSON.parse(matches[0]);
          } catch (e) {
            // If still fails, extract quoted strings
            const stringMatches = content.match(/"([^"]*)"/g);
            if (stringMatches) {
              suggestions = stringMatches.map(m => m.replace(/"/g, ''));
            }
          }
        }
      }
    } else {
      // Use OpenAI for address correction suggestions
      const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: "gpt-4o",
          messages: [
            {
              role: "system",
              content: "You are helping resolve a property address that failed to return results from Zillow. Suggest 1 to 3 similar corrected or more specific variations of the address that might work better with Zillow. Consider adding unit/apartment numbers, alternate street spellings (St. vs Street, N vs North), adjusting ZIP code, or including/removing suite/building identifiers. Return ONLY a JSON array of strings with no explanation."
            },
            {
              role: "user",
              content: `The user selected this address: "${address}"`
            }
          ],
          temperature: 0.7,
          max_tokens: 300
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${OPENAI_API_KEY}`
          }
        }
      );

      // Extract the suggestions from OpenAI's response
      const content = response.data.choices[0].message.content;
      try {
        // Try to parse the JSON response
        suggestions = JSON.parse(content);
      } catch (parseError) {
        // If parsing fails, extract array-like content
        const matches = content.match(/\[.*\]/s);
        if (matches) {
          try {
            suggestions = JSON.parse(matches[0]);
          } catch (e) {
            // If still fails, extract quoted strings
            const stringMatches = content.match(/"([^"]*)"/g);
            if (stringMatches) {
              suggestions = stringMatches.map(m => m.replace(/"/g, ''));
            }
          }
        }
      }
    }

    // Ensure suggestions is an array and limit to 3 items
    if (!Array.isArray(suggestions)) {
      suggestions = [suggestions].filter(Boolean);
    }

    suggestions = suggestions.slice(0, 3);

    res.json({ suggestions });
  } catch (error) {
    console.error('Error generating address corrections:', error);
    res.status(500).json({
      error: 'Failed to generate address corrections',
      message: error.message
    });
  }
});

/**
 * Fetch property details from Zillow API via RapidAPI using address
 * @route GET /api/zillow/property-by-address
 * @param {string} address - The property address (street number and name)
 * @param {string} citystatezip - The city, state, and zip code
 * @returns {Object} Property details from Zillow
 */
router.get('/property-by-address', async (req, res) => {
  // Get parameters from query string
  const { address, citystatezip } = req.query;

  // Validate required parameters
  const validation = validateRequiredParams(req.query, ['address', 'citystatezip']);
  if (!validation.valid) {
    return res.status(400).json({
      success: false,
      error: validation.message
    });
  }

  // Verify API key is available
  if (!RAPIDAPI_KEY) {
    return res.status(500).json({
      success: false,
      error: 'RAPIDAPI_ZILLOW_KEY is not configured. Please check your environment variables.'
    });
  }

  // Log request details for debugging
  console.log(`[DEBUG] Zillow API Request: property with address=${address}, citystatezip=${citystatezip}`);
  console.log('🚀 BACKEND ZILLOW API REQUEST:', {
    address,
    citystatezip,
    encodedAddress: encodeURIComponent(address),
    encodedCityStateZip: encodeURIComponent(citystatezip),
    fullUrl: `https://zillow-com1.p.rapidapi.com/property?address=${encodeURIComponent(address)}&citystatezip=${encodeURIComponent(citystatezip)}`,
    timestamp: new Date().toISOString()
  });
  diagnosticLog('Zillow API Request Headers', 'X-RapidAPI-Key', RAPIDAPI_KEY);

  try {
    const response = await axios.get('https://zillow-com1.p.rapidapi.com/property', {
      params: {
        address: encodeURIComponent(address),
        citystatezip: encodeURIComponent(citystatezip)
      },
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      }
    });

    // Log response summary
    console.log(`[DEBUG] Zillow API Response: Status ${response.status}, Data keys: ${Object.keys(response.data).join(', ')}`);

    // Return the response data
    return res.json(response.data);
  } catch (error) {
    console.error('Zillow API Error:', error.response?.data || error.message);

    // Return a detailed error response
    return res.status(error.response?.status || 500).json({
      success: false,
      error: 'Failed to fetch property data from Zillow',
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      code: error.code
    });
  }
});

/**
 * Get properties by polygon coordinates with filters
 * @route GET /api/zillow/property-by-polygon
 * @returns {Object} Properties within polygon with applied filters
 */
router.get('/property-by-polygon', async (req, res) => {
  console.log('[DEBUG] Polygon endpoint hit!', req.query);
  try {
    const {
      polygon,
      bedroomsMin,
      bedroomsMax,
      bathroomsMin,
      bathroomsMax,
      sqftMin,
      sqftMax,
      homeType,
      status_type
    } = req.query;

    if (!polygon) {
      return res.status(400).json({ error: 'Missing polygon coordinates' });
    }

    // Parse polygon coordinates
    let polygonCoords;
    try {
      polygonCoords = JSON.parse(polygon);
    } catch (error) {
      return res.status(400).json({ error: 'Invalid polygon format' });
    }

    // Validate polygon coordinates
    if (!Array.isArray(polygonCoords) || polygonCoords.length < 3) {
      return res.status(400).json({ error: 'Polygon must have at least 3 coordinates' });
    }

    // Log the request for debugging
    console.log('[DEBUG] Zillow polygon search request:', {
      polygonPoints: polygonCoords.length,
      filters: { bedroomsMin, bedroomsMax, bathroomsMin, bathroomsMax, sqftMin, sqftMax, homeType, status_type }
    });

    // Prepare API parameters
    const apiParams = {
      polygon: JSON.stringify(polygonCoords)
    };

    // Add filters if provided
    if (bedroomsMin) apiParams.bedroomsMin = bedroomsMin;
    if (bedroomsMax) apiParams.bedroomsMax = bedroomsMax;
    if (bathroomsMin) apiParams.bathroomsMin = bathroomsMin;
    if (bathroomsMax) apiParams.bathroomsMax = bathroomsMax;
    if (sqftMin) apiParams.sqftMin = sqftMin;
    if (sqftMax) apiParams.sqftMax = sqftMax;

    // Since Zillow API doesn't have a direct polygon endpoint,
    // we'll use the center point of the polygon with coordinate-based search
    // and then filter results based on the polygon and other criteria

    // Calculate center point of polygon
    const centerLat = polygonCoords.reduce((sum, coord) => sum + coord[0], 0) / polygonCoords.length;
    const centerLng = polygonCoords.reduce((sum, coord) => sum + coord[1], 0) / polygonCoords.length;

    // Calculate approximate radius from polygon bounds
    const latRange = Math.max(...polygonCoords.map(c => c[0])) - Math.min(...polygonCoords.map(c => c[0]));
    const lngRange = Math.max(...polygonCoords.map(c => c[1])) - Math.min(...polygonCoords.map(c => c[1]));
    const radius = Math.max(latRange, lngRange) * 69; // Convert degrees to miles (approximate)

    console.log('[DEBUG] Using center point search:', { centerLat, centerLng, radius });

    // Prepare coordinate API parameters
    const coordinateParams = {
      lat: centerLat,
      long: centerLng,
      d: Math.min(radius, 2.0), // Cap radius at 2 miles to avoid too many results
      includeSold: true
    };

    // Add homeType filter if provided
    if (homeType) {
      coordinateParams.homeType = homeType;
    }

    // Add status_type filter if provided
    if (status_type) {
      coordinateParams.status_type = status_type;
    }

    // Make request to Zillow API using coordinate-based search
    const response = await axios.get('https://zillow-com1.p.rapidapi.com/propertyByCoordinates', {
      params: coordinateParams,
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      },
      timeout: 30000
    });

    console.log('[DEBUG] Zillow coordinate API response received');

    // Filter results based on polygon bounds and property filters
    let filteredResults = response.data || [];

    if (Array.isArray(filteredResults)) {
      filteredResults = filteredResults.filter(item => {
        const property = item.property || item;

        // Apply bedroom filters
        if (bedroomsMin && property.bedrooms < parseInt(bedroomsMin)) return false;
        if (bedroomsMax && property.bedrooms > parseInt(bedroomsMax)) return false;

        // Apply bathroom filters
        if (bathroomsMin && property.bathrooms < parseFloat(bathroomsMin)) return false;
        if (bathroomsMax && property.bathrooms > parseFloat(bathroomsMax)) return false;

        // Apply square footage filters
        if (sqftMin && property.livingArea < parseInt(sqftMin)) return false;
        if (sqftMax && property.livingArea > parseInt(sqftMax)) return false;

        return true;
      });
    }

    console.log(`[DEBUG] Filtered ${filteredResults.length} properties based on criteria`);

    // Return filtered results in the expected format
    res.json({ results: filteredResults });

  } catch (error) {
    console.error('[ERROR] Zillow polygon API error:', error.message);

    if (error.response) {
      console.error('[ERROR] Response status:', error.response.status);
      console.error('[ERROR] Response data:', error.response.data);

      // Handle rate limiting
      if (error.response.status === 429) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests to Zillow API. Please try again later.',
          retryAfter: error.response.headers['retry-after'] || 60
        });
      }
    }

    res.status(500).json({
      error: 'Failed to fetch properties by polygon',
      message: error.message,
      details: error.response?.data || 'No additional details'
    });
  }
});

/**
 * Test endpoint to verify Zillow API key is working
 * @route GET /api/zillow/test-key
 * @returns {Object} Status of the API key
 */
router.get('/test-key', async (req, res) => {
  try {
    // Log that we're testing the API key
    console.log('[DEBUG] Testing Zillow API key...');
    diagnosticLog('Zillow API Key Test', 'RAPIDAPI_ZILLOW_KEY', RAPIDAPI_KEY);

    // Verify API key is available
    if (!RAPIDAPI_KEY) {
      return res.status(500).json({
        success: false,
        error: 'API key not configured',
        message: 'RAPIDAPI_ZILLOW_KEY environment variable is not set'
      });
    }

    // Test coordinates for a known location (example: Los Angeles, CA)
    const testLat = 34.052235;
    const testLng = -118.243683;

    // Make a minimal request to test the API key
    const response = await axios.get('https://zillow-com1.p.rapidapi.com/propertyByCoordinates', {
      params: {
        lat: testLat,
        long: testLng,
        d: 0.1,
        includeSold: true
      },
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      }
    });

    // If we get here, the request was successful
    return res.status(200).json({
      success: true,
      message: 'Zillow API key is valid and working',
      keyStatus: 'Valid',
      testLocation: 'Los Angeles, CA',
      resultsCount: Array.isArray(response.data) ? response.data.length : 'N/A'
    });
  } catch (error) {
    console.error('Zillow API Key Test Error:', error.response?.data || error.message);

    // Return a detailed error response
    return res.status(error.response?.status || 500).json({
      success: false,
      error: 'API key test failed',
      message: error.response?.data?.message || error.message,
      status: error.response?.status,
      code: error.code
    });
  }
});

module.exports = router;
