/**
 * Test script for Zillow API
 * Run with: node scripts/test-zillow-api.js
 */

const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const axios = require('axios');
const { diagnosticLog } = require('../utils/diagnosticLog');

// Get RapidAPI key from environment variables
const RAPIDAPI_KEY = process.env.RAPIDAPI_ZILLOW_KEY;

// Log the API key (safely) for debugging
diagnosticLog('Zillow API Key Check', 'RAPIDAPI_ZILLOW_KEY', RAPIDAPI_KEY);

// Verify that the API key is set
if (!RAPIDAPI_KEY) {
  console.error('ERROR: RAPIDAPI_ZILLOW_KEY environment variable is not set!');
  process.exit(1);
}

// Test coordinates for a known location (example: Los Angeles, CA)
const testLat = 34.052235;
const testLng = -118.243683;

async function testZillowApi() {
  console.log('Testing Zillow API with propertyByCoordinates endpoint...');
  console.log(`Test location: Los Angeles, CA (${testLat}, ${testLng})`);

  try {
    const response = await axios.get('https://zillow-com1.p.rapidapi.com/propertyByCoordinates', {
      params: {
        lat: testLat,
        long: testLng,
        d: 0.1,
        includeSold: true
      },
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      }
    });

    console.log('✅ API request successful!');
    console.log(`Results count: ${Array.isArray(response.data) ? response.data.length : 'N/A'}`);

    if (Array.isArray(response.data) && response.data.length > 0) {
      const firstProperty = response.data[0];
      console.log('\nSample property data:');
      console.log('---------------------');
      console.log(`Address: ${firstProperty.address || 'N/A'}`);
      console.log(`Price: ${firstProperty.price || firstProperty.zestimate || 'N/A'}`);
      console.log(`Bedrooms: ${firstProperty.bedrooms || 'N/A'}`);
      console.log(`Bathrooms: ${firstProperty.bathrooms || 'N/A'}`);
      console.log(`Living Area: ${firstProperty.livingArea || firstProperty.livingAreaSqFt || 'N/A'} sqft`);
    } else {
      console.log('No properties found in the test location.');
    }

    console.log('\nCURL equivalent for manual testing:');
    console.log('----------------------------------');
    console.log(`curl --request GET \\
  --url 'https://zillow-com1.p.rapidapi.com/propertyByCoordinates?lat=${testLat}&long=${testLng}&d=0.1&includeSold=true' \\
  --header 'x-rapidapi-host: zillow-com1.p.rapidapi.com' \\
  --header 'x-rapidapi-key: ${RAPIDAPI_KEY.slice(0, 4)}...${RAPIDAPI_KEY.slice(-4)}'`);

  } catch (error) {
    console.error('❌ API request failed!');
    console.error('Error details:', error.response?.data || error.message);
    console.error('Status code:', error.response?.status || 'N/A');

    if (error.response?.status === 401 || error.response?.status === 403) {
      console.error('\n⚠️ Authentication error! Your API key may be invalid or expired.');
      console.error('Please check your RAPIDAPI_ZILLOW_KEY environment variable.');
    }
  }
}

// Run the test
testZillowApi();
