const express = require('express');
const router = express.Router();
const { diagnosticLog } = require('../utils/diagnosticLog');

/**
 * Check environment variables
 * @route GET /api/env-check
 * @returns {Object} Status of environment variables
 */
router.get('/', async (req, res) => {
  try {
    // Create a list of environment variables to check
    const envVars = [
      'RAPIDAPI_ZILLOW_KEY',
      'RAPIDAPI_SCHOOLDIGGER_KEY',
      'GOOGLE_MAPS_API_KEY',
      'ANTHROPIC_API_KEY',
      'OPENAI_API_KEY'
    ];
    
    // Check each environment variable
    const results = {};
    let allConfigured = true;
    
    envVars.forEach(varName => {
      const value = process.env[varName];
      const isConfigured = !!value;
      
      // Log the status (safely)
      diagnosticLog('Environment Variable Check', varName, value || 'Not set');
      
      // Add to results
      results[varName] = {
        configured: isConfigured,
        // Only include masked value if it exists
        value: isConfigured ? `${value.slice(0, 4)}...${value.slice(-4)}` : null
      };
      
      // Update overall status
      if (!isConfigured) {
        allConfigured = false;
      }
    });
    
    // Return the results
    return res.status(200).json({
      success: allConfigured,
      message: allConfigured 
        ? 'All environment variables are configured' 
        : 'Some environment variables are missing',
      results
    });
  } catch (error) {
    console.error('Environment Check Error:', error.message);
    
    // Return an error response
    return res.status(500).json({
      success: false,
      error: 'Environment check failed',
      message: error.message
    });
  }
});

module.exports = router;
