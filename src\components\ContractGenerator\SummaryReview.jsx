import React from 'react';
import { FaArrowLeft, FaFileDownload, FaSpinner } from 'react-icons/fa';
import './ContractGenerator.css';

const SummaryReview = ({ formData, onPrevious, onSubmit, isGenerating }) => {
  // Format currency values
  const formatCurrency = (value) => {
    if (!value) return 'Not specified';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };
  
  // Format date values
  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };
  
  return (
    <div className="summary-review">
      <h2 className="summary-title">Review Your Contract Details</h2>
      <p className="summary-subtitle">Please review all information before generating your contract.</p>
      
      <div className="summary-section">
        <h3>Buyer & Seller Information</h3>
        <div className="summary-grid">
          <div className="summary-item">
            <span className="summary-label">Buyer:</span>
            <span className="summary-value">{formData.buyer.fullName || 'Not specified'}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Buyer Email:</span>
            <span className="summary-value">{formData.buyer.email || 'Not specified'}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Seller:</span>
            <span className="summary-value">{formData.seller.fullName || 'Not specified'}</span>
          </div>
        </div>
      </div>
      
      <div className="summary-section">
        <h3>Property Details</h3>
        <div className="summary-grid">
          <div className="summary-item">
            <span className="summary-label">Address:</span>
            <span className="summary-value">{formData.property.address || 'Not specified'}</span>
          </div>
          {formData.property.unit && (
            <div className="summary-item">
              <span className="summary-label">Unit:</span>
              <span className="summary-value">{formData.property.unit}</span>
            </div>
          )}
          <div className="summary-item">
            <span className="summary-label">Parking Included:</span>
            <span className="summary-value">{formData.property.parkingIncluded ? 'Yes' : 'No'}</span>
          </div>
          {formData.property.parkingIncluded && formData.property.parkingDetails && (
            <div className="summary-item">
              <span className="summary-label">Parking Details:</span>
              <span className="summary-value">{formData.property.parkingDetails}</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="summary-section">
        <h3>Financial Details</h3>
        <div className="summary-grid">
          <div className="summary-item">
            <span className="summary-label">Purchase Price:</span>
            <span className="summary-value">{formatCurrency(formData.financials.purchasePrice)}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Earnest Money:</span>
            <span className="summary-value">{formatCurrency(formData.financials.earnestMoney)}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Closing Date:</span>
            <span className="summary-value">{formatDate(formData.financials.closingDate)}</span>
          </div>
        </div>
      </div>
      
      {formData.associations.hoa && (
        <div className="summary-section">
          <h3>Association Details</h3>
          <div className="summary-grid">
            <div className="summary-item">
              <span className="summary-label">HOA:</span>
              <span className="summary-value">Yes</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Monthly Fees:</span>
              <span className="summary-value">{formatCurrency(formData.associations.monthlyFees)}</span>
            </div>
          </div>
        </div>
      )}
      
      {formData.fixturesIncluded.length > 0 && (
        <div className="summary-section">
          <h3>Included Fixtures</h3>
          <div className="summary-list">
            {formData.fixturesIncluded.map((fixture, index) => (
              <div key={index} className="summary-list-item">{fixture}</div>
            ))}
          </div>
        </div>
      )}
      
      {formData.additionalTerms && (
        <div className="summary-section">
          <h3>Additional Terms</h3>
          <div className="summary-text">
            {formData.additionalTerms}
          </div>
        </div>
      )}
      
      <div className="summary-buttons">
        <button 
          type="button" 
          className="btn-previous" 
          onClick={onPrevious}
          disabled={isGenerating}
        >
          <FaArrowLeft /> Back
        </button>
        
        <button 
          type="button" 
          className="btn-generate" 
          onClick={onSubmit}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <>
              <FaSpinner className="spinner" /> Generating...
            </>
          ) : (
            <>
              <FaFileDownload /> Generate My Contract
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default SummaryReview;
