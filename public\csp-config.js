// This script dynamically sets the Content Security Policy based on the environment
(function() {
  // Check if we're in a development environment
  const isDevelopment =
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1';

  // Base CSP that's common to both environments
  const baseCSP = {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://www.clarity.ms", "https://www.googletagmanager.com", "https://maps.googleapis.com", "https://apis.google.com"],
    'style-src': ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    'img-src': ["'self'", "data:", "blob:", "https://www.clarity.ms", "https://c.clarity.ms", "https://*.clarity.ms", "https://c.bing.com", "https://maps.googleapis.com", "https://*.blob.core.windows.net", "https://photos.zillowstatic.com", "https://*.zillowstatic.com"],
    'font-src': ["'self'", "https://fonts.gstatic.com"],
    'connect-src': [
      "'self'",
      "https://api.openai.com",
      "https://api.anthropic.com",
      "https://maps.googleapis.com",
      "https://apis.google.com",
      "https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com",
      "https://digital-realtor-api.onrender.com",
      "http://localhost:3001",
      "http://localhost:5000",
      "https://l.clarity.ms",
      "https://*.clarity.ms",
      "https://schooldigger-k-12-school-data-api.p.rapidapi.com",
      "https://api.walkscore.com",
      "https://photos.zillowstatic.com",
      "https://*.zillowstatic.com"
    ],
    'frame-src': ["'self'"]
  };

  // Add development-specific CSP rules
  if (isDevelopment) {
    // Add WebSocket connections for Vite's hot module replacement
    const devPorts = ['5173', '5174', '5175', '5176', '5177', '5178', '5179', '5180'];

    devPorts.forEach(port => {
      baseCSP['connect-src'].push(`http://localhost:${port}`);
      baseCSP['connect-src'].push(`ws://localhost:${port}`);
      baseCSP['connect-src'].push(`wss://localhost:${port}`);
    });

    // Add WebSocket connections for development tools (browser extensions, etc.)
    const devToolPorts = ['24678', '24679', '24680'];
    devToolPorts.forEach(port => {
      baseCSP['connect-src'].push(`ws://localhost:${port}`);
      baseCSP['connect-src'].push(`wss://localhost:${port}`);
    });
  }

  // Convert the CSP object to a string
  const cspString = Object.entries(baseCSP)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');

  // Remove any existing CSP meta tags first
  const existingCSP = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
  existingCSP.forEach(tag => tag.remove());

  // Create a meta element for the CSP
  const meta = document.createElement('meta');
  meta.httpEquiv = 'Content-Security-Policy';
  meta.content = cspString;

  // Add it to the head of the document
  document.head.appendChild(meta);

  // Log the CSP for debugging
  console.log('CSP applied:', cspString);
})();
