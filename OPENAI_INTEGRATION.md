# OpenAI API Integration for AI Listing Generator

This document provides information on how to set up and use the OpenAI API integration for the AI Listing Generator feature.

## Overview

The AI Listing Generator now uses OpenAI's GPT-4o model to generate professional, compelling property listings based on user input. This agentic AI capability analyzes property details and creates optimized content that highlights key selling points.

## Setup Instructions

1. **Get an OpenAI API Key**:
   - Sign up for an account at [OpenAI](https://platform.openai.com/)
   - Navigate to the API section and create a new API key
   - Copy the API key for the next step

2. **Configure the API Key**:
   - Open the `.env` file in the root directory of the project
   - Update the `VITE_OPENAI_API_KEY` variable with your OpenAI API key:
     ```
     VITE_OPENAI_API_KEY=your_openai_api_key_here
     ```
   - Save the file

3. **Restart the Development Server**:
   - If the development server is running, stop it and restart it to load the new environment variables

## Usage

1. Navigate to the AI Listing Generator page
2. Fill in the property details form
3. Click the "Generate Listing" button
4. The application will use the OpenAI API to generate a professional property listing
5. The generated listing will be displayed in the "Generated Listing" section

## Fallback Mechanism

If no OpenAI API key is provided or if there's an error with the API call, the application will fall back to using a local generation function. This ensures that the feature will still work even without an API key, although the quality of the generated listings will be lower.

## Technical Details

- The OpenAI integration uses the GPT-4o model, which is optimized for generating high-quality text
- The API call is configured with a temperature of 0.7 to balance creativity and coherence
- The maximum token limit is set to 1000 to allow for detailed listings
- The system prompt instructs the model to act as an expert real estate agent with years of experience writing compelling property listings
- The generated listing is formatted with Markdown for easy styling and readability

## Troubleshooting

If you encounter issues with the OpenAI API integration:

1. **Check your API key**: Make sure your API key is correctly set in the `.env` file
2. **Check your API quota**: OpenAI has usage limits that may affect your ability to generate listings
3. **Check the console for errors**: Open the browser's developer console to see any error messages
4. **Try the fallback mechanism**: If the API is unavailable, the application will use local generation

## Cost Considerations

Using the OpenAI API incurs costs based on the number of tokens used. The AI Listing Generator is designed to be efficient with token usage, but be aware that frequent use will consume your API quota.

## Privacy and Data Handling

Property details submitted to the OpenAI API are subject to OpenAI's privacy policy. No personal information should be included in the property details to maintain privacy.
