# Digital Realtor Backend

## Environment Variables

The following environment variables need to be set for the backend to function properly:

### API Keys
- `RAPIDAPI_ZILLOW_KEY` - RapidAPI key for Zillow API
- `RAPIDAPI_SCHOOLDIGGER_KEY` - RapidAPI key for SchoolDigger API
- `GOO<PERSON>LE_MAPS_API_KEY` - Google Maps API key
- `GOOGLE_DISTANCE_MATRIX_API_KEY` - Google Distance Matrix API key
- `ANTHROPIC_API_KEY` - Anthropic API key for Claude
- `OPENAI_API_KEY` - OpenAI API key
- `WALKSCORE_API_KEY` - WalkScore API key
- `CENSUS_API_KEY` - Census Bureau API key

### Server Configuration
- `PORT` - Port number for the server (default: 3001)
- `NODE_ENV` - Environment (development, production)

## Deployment on Render

When deploying to Render, make sure to set all the environment variables in the Render dashboard:

1. Go to the Render dashboard
2. Select your service
3. Click on "Environment" tab
4. Add all the required environment variables
5. Click "Save Changes"

## Verifying Environment Variables

You can verify that the environment variables are set correctly by making a GET request to:

```
GET /api/env-check
```

This will return a JSON response with the status of each environment variable.

## Testing Zillow API

You can test the Zillow API by making a GET request to:

```
GET /api/zillow/test-key
```

This will attempt to make a request to the Zillow API and return the results.

## Troubleshooting

If you encounter a 401 Unauthorized error when making requests to the Zillow API, check that:

1. The `RAPIDAPI_ZILLOW_KEY` environment variable is set correctly
2. The API key is valid and has not expired
3. You have subscribed to the Zillow API on RapidAPI

You can use the following curl command to test the API key manually:

```bash
curl --request GET \
  --url 'https://zillow-com1.p.rapidapi.com/propertyByCoordinates?lat=34.052235&long=-118.243683&d=0.1&includeSold=true' \
  --header 'x-rapidapi-host: zillow-com1.p.rapidapi.com' \
  --header 'x-rapidapi-key: YOUR_API_KEY_HERE'
```

Replace `YOUR_API_KEY_HERE` with your actual RapidAPI key.
