import React, { useState, useEffect, useRef } from 'react';
import { FaMapMarkerAlt } from 'react-icons/fa';

interface AddressAutocompleteProps {
  initialValue?: string;
  onSelect: (address: string, placeDetails?: any) => void;
  placeholder?: string;
  inputClassName?: string;
  dropdownClassName?: string;
}

const AddressAutocomplete: React.FC<AddressAutocompleteProps> = ({
  initialValue = '',
  onSelect,
  placeholder = 'Enter an address',
  inputClassName = '',
  dropdownClassName = ''
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const [suggestions, setSuggestions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const sessionToken = useRef<google.maps.places.AutocompleteSessionToken | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Initialize Google Places Autocomplete service
  useEffect(() => {
    if (window.google && window.google.maps && window.google.maps.places) {
      autocompleteService.current = new window.google.maps.places.AutocompleteService();
      sessionToken.current = new window.google.maps.places.AutocompleteSessionToken();
    } else {
      console.error('Google Maps Places API not loaded');
    }
  }, []);

  // Handle clicks outside the component to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch suggestions when input changes
  const fetchSuggestions = (input: string) => {
    if (!autocompleteService.current || input.length < 3) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);

    const request: google.maps.places.AutocompletionRequest = {
      input,
      sessionToken: sessionToken.current,
      componentRestrictions: { country: 'us' },
      types: ['address']
    };

    autocompleteService.current.getPlacePredictions(
      request,
      (predictions: google.maps.places.AutocompletePrediction[] | null, status: google.maps.places.PlacesServiceStatus) => {
        setIsLoading(false);

        if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
          setSuggestions(predictions);
          setShowSuggestions(true);
        } else {
          setSuggestions([]);
        }
      }
    );
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.length >= 3) {
      fetchSuggestions(value);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle suggestion selection
  const handleSelectSuggestion = (suggestion: google.maps.places.AutocompletePrediction) => {
    setInputValue(suggestion.description);
    setSuggestions([]);
    setShowSuggestions(false);

    // Create a new session token for the next request
    sessionToken.current = new google.maps.places.AutocompleteSessionToken();

    // Get place details to access coordinates
    if (window.google && window.google.maps && window.google.maps.places) {
      const placesService = new window.google.maps.places.PlacesService(
        document.createElement('div')
      );

      placesService.getDetails(
        {
          placeId: suggestion.place_id,
          fields: ['geometry', 'address_components', 'formatted_address'],
          sessionToken: sessionToken.current
        },
        (placeResult, status) => {
          if (status === window.google.maps.places.PlacesServiceStatus.OK && placeResult) {
            // Pass both the address and the place details to the parent component
            onSelect(suggestion.description, placeResult);
          } else {
            // If place details request fails, just pass the address
            onSelect(suggestion.description);
            console.error('Error fetching place details:', status);
          }
        }
      );
    } else {
      // If Google Places API is not available, just pass the address
      onSelect(suggestion.description);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (inputValue.length >= 3) {
      fetchSuggestions(inputValue);
    }
  };

  // Default input class if none provided
  const defaultInputClass = 'w-full px-4 py-3 rounded-md text-base';
  const inputClasses = inputClassName || defaultInputClass;

  // Default dropdown class if none provided
  const defaultDropdownClass = 'absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto';
  const dropdownClasses = dropdownClassName || defaultDropdownClass;

  return (
    <div className="relative w-full">
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        placeholder={placeholder}
        className={inputClasses}
        aria-label="Address autocomplete"
      />

      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin h-5 w-5 border-2 border-blue-500 rounded-full border-t-transparent"></div>
        </div>
      )}

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className={dropdownClasses}
        >
          <ul>
            {suggestions.map((suggestion) => (
              <li
                key={suggestion.place_id}
                onClick={() => handleSelectSuggestion(suggestion)}
                className="px-4 py-2 hover:bg-blue-100 cursor-pointer flex items-center transition-colors duration-150"
              >
                <FaMapMarkerAlt className="text-blue-500 mr-2 flex-shrink-0" />
                <span className="truncate">{suggestion.description}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default AddressAutocomplete;
