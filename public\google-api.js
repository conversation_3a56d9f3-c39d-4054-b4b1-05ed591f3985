// Load the Google API client
function loadGoogleApi() {
  // Check if the script is already loaded
  if (document.querySelector('script[src="https://apis.google.com/js/api.js"]')) {
    console.log('Google API script already loaded');
    return;
  }

  // Create a global variable to track the loading state
  window.googleApiLoading = true;

  const script = document.createElement('script');
  script.src = 'https://apis.google.com/js/api.js';
  script.async = true;
  script.defer = true;

  script.onload = () => {
    console.log('Google API script loaded');
    // Load the auth2 library
    if (window.gapi) {
      window.gapi.load('client:auth2', () => {
        console.log('Google API client and auth2 libraries loaded');
        window.googleApiLoading = false;
        window.googleApiLoaded = true;

        // Dispatch an event that the app can listen for
        const event = new CustomEvent('google-api-loaded');
        window.dispatchEvent(event);
      });
    }
  };

  script.onerror = (error) => {
    console.error('Error loading Google API script:', error);
    window.googleApiLoading = false;
    window.googleApiLoadError = true;

    // Dispatch an event that the app can listen for
    const event = new CustomEvent('google-api-load-error');
    window.dispatchEvent(event);
  };

  document.head.appendChild(script);
}

// Load the Google API client when the page loads
window.addEventListener('load', loadGoogleApi);

// Add a helper function to check if the API is loaded
window.isGoogleApiLoaded = function() {
  return window.googleApiLoaded === true;
};

// Add a helper function to check if the API is loading
window.isGoogleApiLoading = function() {
  return window.googleApiLoading === true;
};

// Add a helper function to check if there was an error loading the API
window.hasGoogleApiLoadError = function() {
  return window.googleApiLoadError === true;
};
