// Neighborhood Data Service
// This service integrates data from multiple sources to provide a comprehensive neighborhood overview

import { fetchSchoolRatings, extractStateFromAddress, extractCityFromAddress, SchoolRatingsResponse } from './schoolRatingsService';
import { fetchWalkabilityData, WalkabilityResponse } from './walkabilityService';
import { fetchDemographics, extractZipCodeFromAddress, DemographicsResponse } from './demographicsService';
// Temporarily commented out
// import { fetchCommuteAnalysis, CommuteAnalysisResponse } from './commuteAnalysisService';

// Types
export interface NeighborhoodData {
  neighborhood: {
    school_ratings: SchoolRatingsResponse['school_ratings'];
    walkability: WalkabilityResponse['walkability'];
    demographics: DemographicsResponse['demographics'];
    // Temporarily commented out
    // commute_analysis: CommuteAnalysisResponse['commute_analysis'];
  };
}

/**
 * Fetches comprehensive neighborhood data from multiple sources
 * @param address Full address string
 * @param latitude Optional latitude coordinate
 * @param longitude Optional longitude coordinate
 * @returns Promise with combined neighborhood data
 */
export const fetchNeighborhoodData = async (
  address: string,
  latitude?: number,
  longitude?: number
): Promise<NeighborhoodData> => {
  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Fetching comprehensive neighborhood data', 'color: #805ad5; font-weight: bold');
      console.log('Address:', address);
    }

    // Extract state, city, and ZIP code from address
    const state = extractStateFromAddress(address);
    const city = extractCityFromAddress(address);
    const zipCode = extractZipCodeFromAddress(address);

    // Fetch data from multiple sources in parallel with a timeout
    const fetchWithTimeout = async (promiseFactory, fallback, name) => {
      try {
        // Create a timeout promise that rejects after 8 seconds
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`${name} request timed out`)), 8000);
        });

        // Execute the promise factory to get the actual promise
        const dataPromise = promiseFactory();

        // Race the original promise against the timeout
        return await Promise.race([dataPromise, timeoutPromise]);
      } catch (error) {
        console.warn(`Error fetching ${name} data:`, error);

        // Log more details in development mode
        if (import.meta.env.DEV) {
          console.log(`%c[DEV] Using fallback data for ${name} due to error: ${error.message}`, 'color: orange;');
        }

        return fallback;
      }
    };

    // Define fallback responses
    const schoolRatingsFallback = { school_ratings: 'Data not available for this area' };
    const walkabilityFallback = { walkability: 'Data not available for this address' };
    const demographicsFallback = { demographics: 'Data not available for this ZIP code' };
    // Temporarily commented out
    // const commuteAnalysisFallback = { commute_analysis: 'Commute data not available for this address' };

    // Fetch all data with timeouts and fallbacks
    const [schoolRatingsResponse, walkabilityResponse, demographicsResponse] = await Promise.all([
      fetchWithTimeout(() => fetchSchoolRatings(state, city), schoolRatingsFallback, 'School Ratings'),
      fetchWithTimeout(() => fetchWalkabilityData(address, latitude, longitude), walkabilityFallback, 'Walkability'),
      fetchWithTimeout(() => fetchDemographics(zipCode), demographicsFallback, 'Demographics'),
      // Temporarily commented out
      // fetchWithTimeout(() => fetchCommuteAnalysis(address), commuteAnalysisFallback, 'Commute Analysis')
    ]);

    // Combine the data
    return {
      neighborhood: {
        school_ratings: schoolRatingsResponse.school_ratings,
        walkability: walkabilityResponse.walkability,
        demographics: demographicsResponse.demographics,
        // Temporarily commented out
        // commute_analysis: commuteAnalysisResponse.commute_analysis
      }
    };
  } catch (error) {
    console.error('Error fetching neighborhood data:', error);

    // Return fallback data
    return {
      neighborhood: {
        school_ratings: 'Data not available for this area',
        walkability: 'Data not available for this address',
        demographics: 'Data not available for this ZIP code',
        // Temporarily commented out
        // commute_analysis: 'Commute data not available for this address'
      }
    };
  }
};

/**
 * Merges the AI-generated report with real-time neighborhood data
 * @param aiReport The report generated by the AI
 * @param neighborhoodData Real-time neighborhood data
 * @returns Merged report with both AI and real-time data
 */
export const mergeReportWithRealTimeData = (aiReport: any, neighborhoodData: NeighborhoodData): any => {
  // Create a deep copy of the AI report
  const mergedReport = JSON.parse(JSON.stringify(aiReport));

  // Add the real-time neighborhood data
  mergedReport.realTimeData = neighborhoodData;

  return mergedReport;
};
