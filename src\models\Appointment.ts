// Appointment model definition

export interface Appointment {
  id: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  propertyAddress: string;
  propertyType: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes?: string;
  createdAt: string;
  updatedAt: string;
  userId: string; // Reference to the user who created the appointment
}

// Type for creating a new appointment
export interface CreateAppointmentData {
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  propertyAddress: string;
  propertyType: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes?: string;
  userId: string;
}
