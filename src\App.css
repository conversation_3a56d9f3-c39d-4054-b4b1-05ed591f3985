/* Base styles */
:root {
  --primary-color: #4a6bff;
  --secondary-color: #6e3adc;
  --accent-color: #00f7ff;
  --dark-color: #0a1128;
  --dark-900: rgba(10, 12, 26, 1);
  --light-color: #f0f4ff;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
  color: var(--light-color);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

#root {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  position: relative;
}

/* Layout styles */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.min-h-screen {
  min-height: 100vh;
}

/* Background styles */
.bg-gray-50 {
  background-color: rgba(249, 250, 251, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(74, 107, 255, 0.1);
  border-radius: 8px;
}

.bg-white {
  background-color: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(74, 107, 255, 0.1);
  border-radius: 8px;
}

.glass-card {
  background: rgba(10, 12, 26, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 247, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Text styles */
.text-center {
  text-align: center;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-bold {
  font-weight: 700;
}

/* Spacing styles */
.p-4 {
  padding: 1rem;
}

.p-8 {
  padding: 2rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

/* Border styles */
.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

/* Shadow styles */
.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 247, 255, 0.1);
}

.shadow-card {
  box-shadow: 0 4px 28px 0 rgba(0, 0, 0, 0.2), 0 0 15px rgba(0, 247, 255, 0.1);
}

.shadow-hover {
  box-shadow: 0 10px 40px 0 rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 247, 255, 0.15);
  transition: all 0.3s ease;
}

.drop-shadow-glow {
  filter: drop-shadow(0 0 8px rgba(0, 247, 255, 0.5));
}

.text-glow {
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);
}

.border-glow {
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.2);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(0, 247, 255, 0.3); }
  50% { box-shadow: 0 0 20px rgba(0, 247, 255, 0.6); }
  100% { box-shadow: 0 0 5px rgba(0, 247, 255, 0.3); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 3s infinite ease-in-out;
}

.animate-float {
  animation: float 6s infinite ease-in-out;
}

.animate-glow {
  animation: glow 3s infinite ease-in-out;
}
