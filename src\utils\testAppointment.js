// This file adds a test appointment to localStorage for debugging purposes

// Function to add a test appointment
export function addTestAppointment(userId = 'guest') {
  // Get existing appointments
  let appointments = [];
  try {
    const storedAppointments = localStorage.getItem('localAppointments');
    if (storedAppointments) {
      appointments = JSON.parse(storedAppointments);
    }
  } catch (error) {
    console.error('Error loading appointments from localStorage:', error);
  }

  // Create a test appointment
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  // Format date as "Month Day, Year" (e.g., "June 15, 2023")
  const formattedDate = tomorrow.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
  
  // Format time as "HH:MM AM/PM" (e.g., "10:00 AM")
  const formattedTime = tomorrow.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const testAppointment = {
    id: `test-appt-${Date.now()}`,
    clientName: 'Test Client',
    clientEmail: '<EMAIL>',
    clientPhone: '************',
    propertyAddress: '123 Test Street, Test City',
    propertyType: 'House',
    date: formattedDate,
    time: formattedTime,
    status: 'confirmed',
    notes: 'This is a test appointment added for debugging purposes.',
    userId: userId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Add the test appointment to the array
  appointments.push(testAppointment);

  // Save back to localStorage
  try {
    localStorage.setItem('localAppointments', JSON.stringify(appointments));
    console.log('Test appointment added:', testAppointment);
    console.log('Total appointments:', appointments.length);
    return testAppointment;
  } catch (error) {
    console.error('Error saving test appointment to localStorage:', error);
    return null;
  }
}

// Function to clear all appointments
export function clearAllAppointments() {
  try {
    localStorage.removeItem('localAppointments');
    console.log('All appointments cleared from localStorage');
    return true;
  } catch (error) {
    console.error('Error clearing appointments from localStorage:', error);
    return false;
  }
}
