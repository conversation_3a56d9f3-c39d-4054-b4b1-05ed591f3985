import express from 'express';
const router = express.Router();
import {
  createAppointment,
  getUserAppointments,
  getRealtorAppointments,
  updateAppointmentStatus,
  getAppointmentById,
  deleteAppointment,
} from '../controllers/appointmentController.js';
import { protect, realtor } from '../middleware/authMiddleware.js';

router.route('/').post(protect, createAppointment).get(protect, getUserAppointments);
router.route('/realtor').get(protect, realtor, getRealtorAppointments);
router
  .route('/:id')
  .get(protect, getAppointmentById)
  .delete(protect, deleteAppointment);
router.route('/:id/status').put(protect, realtor, updateAppointmentStatus);

export default router;
