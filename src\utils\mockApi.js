/**
 * Mock API implementations for when the backend is not available
 * This is used in production when the backend API is not deployed or is down
 * It can also be used in development when the backend server is not running
 */

// Mock data for contract generation
const mockContractData = {
  status: 'success',
  message: 'Contract generated successfully.',
  downloadUrl: '/sample-contract.pdf',
  generatedAt: new Date().toISOString()
};

/**
 * Mock implementation of the contract generator API
 * @param {Object} formData - The form data for the contract
 * @returns {Promise<Object>} - A promise that resolves to the mock contract data
 */
export const mockGenerateContract = async (formData) => {
  console.log('Using mock contract generator API');
  console.log('Form data:', formData);

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Return mock data
  return {
    ...mockContractData,
    formData: formData // Include the form data in the response for debugging
  };
};

/**
 * Mock implementation of the virtual staging API
 * @param {Object} data - The data for virtual staging
 * @returns {Promise<Object>} - A promise that resolves to the mock virtual staging data
 */
export const mockVirtualStaging = async (data) => {
  console.log('Using mock virtual staging API');
  console.log('Data:', data);

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Return mock data
  return {
    status: 'success',
    message: 'Virtual staging suggestions generated successfully.',
    suggestions: [
      {
        room: 'Living Room',
        style: 'Modern',
        suggestions: [
          'Add a minimalist sofa in light gray',
          'Include a glass coffee table',
          'Add floor lamps with warm lighting',
          'Consider a large area rug to define the space'
        ]
      },
      {
        room: 'Kitchen',
        style: 'Contemporary',
        suggestions: [
          'Add bar stools at the kitchen island',
          'Include pendant lighting above the island',
          'Consider adding plants for a touch of greenery',
          'Keep countertops clear for a clean look'
        ]
      }
    ]
  };
};

export default {
  mockGenerateContract,
  mockVirtualStaging
};
