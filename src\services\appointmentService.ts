import api from './api';
import { Property } from './propertyService';
import { User } from './userService';
import { Appointment as LocalAppointment, CreateAppointmentData } from '../models/Appointment';
import { LocalAppointmentService } from './LocalAppointmentService';
import axios from 'axios';

// Types
export interface Appointment {
  _id?: string;
  user?: string | User;
  realtor: string | User;
  property: string | Property;
  date: string;
  time: string;
  status: 'Pending' | 'Confirmed' | 'Cancelled' | 'Completed';
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AppointmentCreate {
  realtorId: string;
  propertyId: string;
  date: string;
  time: string;
  notes?: string;
}

export interface StatusUpdate {
  status: 'Pending' | 'Confirmed' | 'Cancelled' | 'Completed';
}

// Flag to determine if we should use the database or local storage
const USE_DATABASE = true;

// Base URL for the API
const API_BASE_URL = import.meta.env.VITE_API_URL || import.meta.env.VITE_BACKEND_URL ? `${import.meta.env.VITE_BACKEND_URL}/api` : 'http://localhost:5000/api';

// Appointment API calls
const appointmentService = {
  // Create new appointment
  createAppointment: async (appointmentData: AppointmentCreate): Promise<Appointment> => {
    const { data } = await api.post('/appointments', appointmentData);
    return data;
  },

  // Get user appointments
  getUserAppointments: async (): Promise<Appointment[]> => {
    const { data } = await api.get('/appointments');
    return data;
  },

  // Get realtor appointments (realtor only)
  getRealtorAppointments: async (): Promise<Appointment[]> => {
    const { data } = await api.get('/appointments/realtor');
    return data;
  },

  // Update appointment status (realtor only)
  updateAppointmentStatus: async (
    id: string,
    statusData: StatusUpdate
  ): Promise<Appointment> => {
    const { data } = await api.put(`/appointments/${id}/status`, statusData);
    return data;
  },

  // Get appointment by ID
  getAppointmentById: async (id: string): Promise<Appointment> => {
    const { data } = await api.get(`/appointments/${id}`);
    return data;
  },

  // Delete appointment
  deleteAppointment: async (id: string): Promise<{ message: string }> => {
    const { data } = await api.delete(`/appointments/${id}`);
    return data;
  },

  // New methods for the local/database appointment system

  // Create a new appointment in the database
  createClientAppointment: async (data: CreateAppointmentData): Promise<LocalAppointment> => {
    if (USE_DATABASE) {
      try {
        console.log('Creating appointment in database for user:', data.userId);
        const response = await axios.post(`${API_BASE_URL}/client-appointments`, data, {
          headers: {
            'Content-Type': 'application/json',
          },
        });

        console.log('Appointment created in database:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error creating appointment in database:', error);
        // Fallback to local storage
        return LocalAppointmentService.createAppointment(data);
      }
    } else {
      return LocalAppointmentService.createAppointment(data);
    }
  },

  // Get all client appointments
  getAllClientAppointments: async (): Promise<LocalAppointment[]> => {
    if (USE_DATABASE) {
      try {
        const response = await axios.get(`${API_BASE_URL}/client-appointments`);
        console.log('Retrieved all appointments from database:', response.data.length);
        return response.data;
      } catch (error) {
        console.error('Error getting all appointments from database:', error);
        // Fallback to local storage
        return LocalAppointmentService.getAllAppointments();
      }
    } else {
      return LocalAppointmentService.getAllAppointments();
    }
  },

  // Get client appointments for a specific user
  getClientAppointmentsForUser: async (userId: string): Promise<LocalAppointment[]> => {
    if (USE_DATABASE) {
      try {
        console.log(`Retrieving appointments for user ${userId} from database`);
        const response = await axios.get(`${API_BASE_URL}/client-appointments/user/${userId}`);
        console.log(`Retrieved ${response.data.length} appointments for user ${userId} from database`);
        return response.data;
      } catch (error) {
        console.error(`Error getting appointments for user ${userId} from database:`, error);
        // Fallback to local storage
        return LocalAppointmentService.getUserAppointments(userId);
      }
    } else {
      return LocalAppointmentService.getUserAppointments(userId);
    }
  },

  // Get client appointment by ID
  getClientAppointmentById: async (id: string): Promise<LocalAppointment | undefined> => {
    if (USE_DATABASE) {
      try {
        const response = await axios.get(`${API_BASE_URL}/client-appointments/${id}`);
        return response.data;
      } catch (error) {
        console.error(`Error getting appointment ${id} from database:`, error);
        // Fallback to local storage
        return LocalAppointmentService.getAppointmentById(id);
      }
    } else {
      return LocalAppointmentService.getAppointmentById(id);
    }
  },

  // Update a client appointment
  updateClientAppointment: async (id: string, data: Partial<LocalAppointment>): Promise<LocalAppointment | null> => {
    if (USE_DATABASE) {
      try {
        const response = await axios.put(`${API_BASE_URL}/client-appointments/${id}`, data, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
        console.log(`Updated appointment ${id} in database`);
        return response.data;
      } catch (error) {
        console.error(`Error updating appointment ${id} in database:`, error);
        // Fallback to local storage
        return LocalAppointmentService.updateAppointment(id, data);
      }
    } else {
      return LocalAppointmentService.updateAppointment(id, data);
    }
  },

  // Delete a client appointment
  deleteClientAppointment: async (id: string): Promise<boolean> => {
    if (USE_DATABASE) {
      try {
        await axios.delete(`${API_BASE_URL}/client-appointments/${id}`);
        console.log(`Deleted appointment ${id} from database`);
        return true;
      } catch (error) {
        console.error(`Error deleting appointment ${id} from database:`, error);
        // Fallback to local storage
        return LocalAppointmentService.deleteAppointment(id);
      }
    } else {
      return LocalAppointmentService.deleteAppointment(id);
    }
  },
};

export default appointmentService;
