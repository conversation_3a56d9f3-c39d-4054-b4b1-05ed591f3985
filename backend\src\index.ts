import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import claudeRoutes from './routes/claude';
import neighborhoodComplianceRoutes from './routes/neighborhoodCompliance';
import virtualStagingRoutes from './routes/virtualStaging';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Routes
app.use('/api/claude', claudeRoutes);
app.use('/api/tools/neighborhood-compliance', neighborhoodComplianceRoutes);
app.use('/api/virtual-staging', virtualStagingRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
