/**
 * API Configuration Utility
 *
 * This utility provides the correct API URLs based on the current environment.
 * It helps ensure that API requests are sent to the right server in both development and production.
 */

// Determine if we're in a production environment
const isProduction =
  window.location.hostname === 'aidigitalrealtor.com' ||
  window.location.hostname === 'www.aidigitalrealtor.com';

// Base API URLs
const API_BASE_URL = isProduction
  ? 'https://digital-realtor-api.onrender.com' // Production API URL
  : ''; // Empty string for development (uses relative paths)

// Flag to indicate if we should use mock implementations
// Set this to true if the backend API is not available
export const USE_MOCK_IN_PRODUCTION = true;

// Flag to indicate if we should use mock implementations in development
// Set this to true if the backend server is not running
export const USE_MOCK_IN_DEVELOPMENT = true;

/**
 * Get the full API URL for a specific endpoint
 * @param {string} endpoint - The API endpoint (e.g., '/api/contract-generator')
 * @returns {string} The full API URL
 */
export const getApiUrl = (endpoint) => {
  // Make sure the endpoint starts with a slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  return `${API_BASE_URL}${formattedEndpoint}`;
};

/**
 * Common API endpoints used in the application
 */
export const API_ENDPOINTS = {
  CONTRACT_GENERATOR: '/api/contract-generator',
  USERS: '/api/users',
  PROPERTIES: '/api/properties',
  APPOINTMENTS: '/api/appointments',
  VIRTUAL_STAGING: '/api/virtual-staging',
  // Add more endpoints as needed
};

export default {
  getApiUrl,
  API_ENDPOINTS,
  isProduction
};
