import { Link, useParams } from 'react-router-dom';
import { FaArrowLeft, FaHome } from 'react-icons/fa';

const PropertyDetail = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <nav className="text-sm text-gray-500">
            <ol className="list-none p-0 inline-flex">
              <li className="flex items-center">
                <Link to="/" className="hover:text-primary-600">
                  Home
                </Link>
                <span className="mx-2">/</span>
              </li>
              <li className="flex items-center">
                <Link to="/properties" className="hover:text-primary-600">
                  Properties
                </Link>
                <span className="mx-2">/</span>
              </li>
              <li className="text-gray-700">Property {id}</li>
            </ol>
          </nav>
        </div>

        {/* Back Button */}
        <Link
          to="/properties"
          className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-6"
        >
          <FaArrowLeft className="mr-2" />
          Back to Properties
        </Link>

        {/* Property Detail - Placeholder */}
        <div className="bg-white rounded-xl shadow-card p-8 text-center">
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white mx-auto mb-6">
            <FaHome className="w-8 h-8" />
          </div>
          <h2 className="text-2xl font-bold text-dark-800 mb-4">Property Details Coming Soon</h2>
          <p className="text-dark-500 max-w-lg mx-auto mb-6">
            Detailed information for property #{id} is currently being prepared. Check back soon to see all the features and amenities of this property.
          </p>
          <Link
            to="/properties"
            className="inline-block bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
          >
            Browse Other Properties
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetail;
