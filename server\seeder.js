import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { users, properties } from './data/sampleData.js';
import User from './models/userModel.js';
import Property from './models/propertyModel.js';
import Appointment from './models/appointmentModel.js';
import connectDB from './config/db.js';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

connectDB();

const importData = async () => {
  try {
    await User.deleteMany();
    await Property.deleteMany();
    await Appointment.deleteMany();

    const createdUsers = await User.insertMany(users);

    const adminUser = createdUsers[0]._id;
    const realtorUser = createdUsers[1]._id;

    const sampleProperties = properties.map((property) => {
      return { ...property, user: property.isFeatured ? adminUser : realtorUser };
    });

    await Property.insertMany(sampleProperties);

    console.log('Data Imported!');
    process.exit();
  } catch (error) {
    console.error(`${error}`);
    process.exit(1);
  }
};

const destroyData = async () => {
  try {
    await User.deleteMany();
    await Property.deleteMany();
    await Appointment.deleteMany();

    console.log('Data Destroyed!');
    process.exit();
  } catch (error) {
    console.error(`${error}`);
    process.exit(1);
  }
};

if (process.argv[2] === '-d') {
  destroyData();
} else {
  importData();
};
