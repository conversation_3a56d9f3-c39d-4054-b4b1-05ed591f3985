// OpenAI API integration
import { getOpenAIClient } from './openaiConfig';

// Sample questionnaire data
const sampleQuestionnaire = {
  "steps": [
    {
      "id": "step1",
      "title": "Buyer Information",
      "questions": [
        {
          "id": "buyerName",
          "text": "What is the buyer's full name?",
          "field": "buyer.fullName",
          "type": "text",
          "required": true,
          "defaultValue": "",
          "conditionalOn": null
        },
        {
          "id": "buyerEmail",
          "text": "What is the buyer's email address?",
          "field": "buyer.email",
          "type": "text",
          "required": true,
          "defaultValue": "",
          "conditionalOn": null
        }
      ]
    },
    {
      "id": "step2",
      "title": "Seller Information",
      "questions": [
        {
          "id": "sellerName",
          "text": "What is the seller's full name?",
          "field": "seller.fullName",
          "type": "text",
          "required": true,
          "defaultValue": "",
          "conditionalOn": null
        }
      ]
    },
    {
      "id": "step3",
      "title": "Property Details",
      "questions": [
        {
          "id": "propertyAddress",
          "text": "What is the property address?",
          "field": "property.address",
          "type": "textarea",
          "required": true,
          "defaultValue": "",
          "conditionalOn": null
        },
        {
          "id": "propertyUnit",
          "text": "What is the unit number (if applicable)?",
          "field": "property.unit",
          "type": "text",
          "required": false,
          "defaultValue": "",
          "conditionalOn": null
        }
      ]
    }
  ]
};

/**
 * Generate a questionnaire from the schema using OpenAI
 * @param {Object} schema - The contract schema
 * @returns {Promise<Object>} - The generated questionnaire
 */
export async function generateQuestionnaireFromSchema(schema) {
  try {
    console.log('Generating questionnaire from schema:', schema);

    // Get the OpenAI client
    const openai = getOpenAIClient();

    if (!openai) {
      console.warn('OpenAI client not available, using mock implementation');
      throw new Error('OpenAI client not available');
    }

    const prompt = `
You are a contract generation assistant. Below is a JSON schema for a real estate contract form.
Your task is to:
- Create step-by-step friendly questions (TurboTax-style) to collect all necessary information
- Group related questions into logical steps
- Provide clear, conversational question text
- Suggest default values where appropriate
- Include conditional logic (e.g., only ask about parking details if parking is included)
- Output all of this in a JSON format

Schema:
${JSON.stringify(schema, null, 2)}

Please respond with a JSON object only, no additional text. The JSON should have this structure:
{
  "steps": [
    {
      "id": "step1",
      "title": "Step Title",
      "questions": [
        {
          "id": "question1",
          "text": "What is the buyer's full name?",
          "field": "buyer.fullName",
          "type": "text",
          "required": true,
          "defaultValue": "",
          "conditionalOn": null
        },
        ...
      ]
    },
    ...
  ]
}
`;

    // Call the OpenAI API
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        { role: "system", content: "You are a helpful assistant that generates questionnaires for contract generation." },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    // Parse the response
    const questionnaireText = response.choices[0].message.content.trim();

    // Extract JSON from the response (in case there's any additional text)
    const jsonMatch = questionnaireText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Could not extract JSON from the response');
    }

    const questionnaire = JSON.parse(jsonMatch[0]);
    return questionnaire;
  } catch (error) {
    console.error('Error generating questionnaire:', error);

    // Return the sample questionnaire as a fallback
    console.log('Returning sample questionnaire as fallback');
    return sampleQuestionnaire;
  }
}

/**
 * Save the questionnaire to a file
 * @param {Object} questionnaire - The generated questionnaire
 */
export function saveQuestionnaire(questionnaire) {
  // In a browser environment, we can't save to a file
  // Instead, we'll log the questionnaire to the console
  console.log('Questionnaire generated:', questionnaire);

  // In a real implementation, this would save the questionnaire to a file or database
  return questionnaire;
}

/**
 * Main function to generate and save the questionnaire
 */
export async function generateAndSaveQuestionnaire() {
  try {
    // In a browser environment, we can't load from a file
    // Instead, we'll use a sample schema
    const sampleSchema = {
      "buyer": {
        "fullName": { "infer": false },
        "email": { "infer": false }
      },
      "seller": {
        "fullName": { "infer": false }
      },
      "property": {
        "address": { "infer": false },
        "unit": { "infer": false }
      }
    };

    // Generate the questionnaire
    const questionnaire = await generateQuestionnaireFromSchema(sampleSchema);

    // Save the questionnaire
    return saveQuestionnaire(questionnaire);
  } catch (error) {
    console.error('Error generating and saving questionnaire:', error);
    // Return the sample questionnaire as a fallback
    return sampleQuestionnaire;
  }
}

export default {
  generateQuestionnaireFromSchema,
  saveQuestionnaire,
  generateAndSaveQuestionnaire
};
