import { createContext, useContext, useState, ReactNode } from 'react';

// Define the state types
interface User {
  isAuthenticated: boolean;
  name: string;
  email: string;
  role: string;
  isRealtor?: boolean;
  id?: string;
}

interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
}

interface AppState {
  user: User;
  ui: UIState;
  openAiApiKey: string;
  claudeApiKey: string;
}

// Define the context type
interface AppContextType {
  state: AppState;
  login: (name: string, email: string, role: string) => void;
  logout: () => void;
  toggleTheme: () => void;
  toggleSidebar: () => void;
}

// Create the context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Create a provider component
export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<AppState>({
    user: {
      isAuthenticated: false,
      name: '',
      email: '',
      role: '',
    },
    ui: {
      theme: 'light',
      sidebarOpen: false,
    },
    openAiApiKey: '********************************************************************************************************************************************************************',
    claudeApiKey: '************************************************************************************************************',
  });

  const login = (name: string, email: string, role: string) => {
    const isRealtor = role === 'agent';
    // Generate a simple user ID based on email
    const id = `user-${email.split('@')[0]}-${Date.now()}`;

    setState((prevState) => ({
      ...prevState,
      user: {
        isAuthenticated: true,
        name,
        email,
        role,
        isRealtor,
        id,
      },
    }));
  };

  const logout = () => {
    setState((prevState) => ({
      ...prevState,
      user: {
        isAuthenticated: false,
        name: '',
        email: '',
        role: '',
      },
    }));
  };

  const toggleTheme = () => {
    setState((prevState) => ({
      ...prevState,
      ui: {
        ...prevState.ui,
        theme: prevState.ui.theme === 'light' ? 'dark' : 'light',
      },
    }));
  };

  const toggleSidebar = () => {
    setState((prevState) => ({
      ...prevState,
      ui: {
        ...prevState.ui,
        sidebarOpen: !prevState.ui.sidebarOpen,
      },
    }));
  };

  return (
    <AppContext.Provider
      value={{
        state,
        login,
        logout,
        toggleTheme,
        toggleSidebar,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

// Create a hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
