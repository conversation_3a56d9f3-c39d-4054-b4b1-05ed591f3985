import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/digitalrealtordb');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

const testConnection = async () => {
  const conn = await connectDB();
  console.log('Connection successful!');
  console.log('Database name:', conn.connection.name);
  console.log('Connection state:', conn.connection.readyState);
  
  // Close the connection after testing
  await mongoose.connection.close();
  console.log('Connection closed');
  process.exit(0);
};

// Run the test
testConnection();
