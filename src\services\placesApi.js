/**
 * Places API Service
 * This service handles fetching address suggestions and geocoding from the Google Places API via our backend proxy
 */

// Get backend URL from environment (check both Vite env and window env)
const getBackendUrl = () => {
  // Force localhost:5000 in development mode
  const isDevelopment =
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1';

  if (isDevelopment) {
    const devPort = import.meta.env.VITE_PORT || 5000;
    console.log(`%c[DEV] Places API: Using localhost:${devPort} for development`, 'color: #4285F4; font-weight: bold');
    return `http://localhost:${devPort}`;
  }

  // First check Vite environment
  if (import.meta.env.VITE_BACKEND_URL) {
    return import.meta.env.VITE_BACKEND_URL;
  }

  // Then check window environment (for dynamic configuration)
  if (typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_BACKEND_URL) {
    return window.__ENV__.VITE_BACKEND_URL;
  }

  // Fallback to environment-configured port for development
  const fallbackPort = import.meta.env.VITE_PORT || 5000;
  return `http://localhost:${fallbackPort}`;
};

// Define the API base URL
const backendUrl = getBackendUrl();

/**
 * Fetches address suggestions from the Google Places Autocomplete API
 * @param {string} input - The user's input to search for
 * @returns {Promise<Array<string>>} - Array of address suggestions
 */
export async function fetchPlaceSuggestions(input) {
  if (!input || input.length < 3) return [];

  try {
    const url = `${backendUrl}/api/places/autocomplete?input=${encodeURIComponent(input)}`;
    console.log('Making autocomplete request to:', url);

    const response = await fetch(url);

    if (!response.ok) {
      console.error('Autocomplete API response not OK:', response.status, response.statusText);
      throw new Error(`Autocomplete API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Autocomplete API response:', data);

    if (data.predictions && Array.isArray(data.predictions)) {
      const suggestions = data.predictions.map(pred => pred.description);
      console.log('Extracted suggestions:', suggestions);
      return suggestions;
    } else {
      console.warn('No predictions in response:', data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching address suggestions:', error);
    return [];
  }
}

/**
 * Fetches latitude and longitude coordinates for a given address
 * @param {string} address - The full address to geocode
 * @returns {Promise<Object|null>} - Object with lat, lng, and formatted_address or null on error
 */
export async function fetchLatLngForAddress(address) {
  if (!address) return null;

  try {
    const response = await fetch(
      `${backendUrl}/api/places/geocode?address=${encodeURIComponent(address)}`
    );

    if (!response.ok) {
      throw new Error(`Geocode API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error geocoding address:', error);
    return null;
  }
}
