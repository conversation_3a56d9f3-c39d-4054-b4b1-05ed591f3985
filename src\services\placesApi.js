/**
 * Places API Service
 * This service handles fetching address suggestions and geocoding from the Google Places API via our backend proxy
 */

// Define the API base URL
const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';

/**
 * Fetches address suggestions from the Google Places Autocomplete API
 * @param {string} input - The user's input to search for
 * @returns {Promise<Array<string>>} - Array of address suggestions
 */
export async function fetchPlaceSuggestions(input) {
  if (!input || input.length < 3) return [];

  try {
    const url = `${backendUrl}/api/places/autocomplete?input=${encodeURIComponent(input)}`;
    console.log('Making autocomplete request to:', url);

    const response = await fetch(url);

    if (!response.ok) {
      console.error('Autocomplete API response not OK:', response.status, response.statusText);
      throw new Error(`Autocomplete API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Autocomplete API response:', data);

    if (data.predictions && Array.isArray(data.predictions)) {
      const suggestions = data.predictions.map(pred => pred.description);
      console.log('Extracted suggestions:', suggestions);
      return suggestions;
    } else {
      console.warn('No predictions in response:', data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching address suggestions:', error);
    return [];
  }
}

/**
 * Fetches latitude and longitude coordinates for a given address
 * @param {string} address - The full address to geocode
 * @returns {Promise<Object|null>} - Object with lat, lng, and formatted_address or null on error
 */
export async function fetchLatLngForAddress(address) {
  if (!address) return null;

  try {
    const response = await fetch(
      `${backendUrl}/api/places/geocode?address=${encodeURIComponent(address)}`
    );

    if (!response.ok) {
      throw new Error(`Geocode API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error geocoding address:', error);
    return null;
  }
}
