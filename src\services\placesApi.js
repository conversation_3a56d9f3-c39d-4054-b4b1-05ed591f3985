/**
 * Places API Service
 * This service handles fetching address suggestions and geocoding from the Google Places API via our backend proxy
 */

// Define the API base URL
const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';

/**
 * Fetches address suggestions from the Google Places Autocomplete API
 * @param {string} input - The user's input to search for
 * @returns {Promise<Array<string>>} - Array of address suggestions
 */
export async function fetchPlaceSuggestions(input) {
  if (!input || input.length < 3) return [];

  try {
    const response = await fetch(
      `${backendUrl}/api/places/autocomplete?input=${encodeURIComponent(input)}`
    );

    if (!response.ok) {
      throw new Error(`Autocomplete API error: ${response.status}`);
    }

    const data = await response.json();
    return data.predictions.map(pred => pred.description);
  } catch (error) {
    console.error('Error fetching address suggestions:', error);
    return [];
  }
}

/**
 * Fetches latitude and longitude coordinates for a given address
 * @param {string} address - The full address to geocode
 * @returns {Promise<Object|null>} - Object with lat, lng, and formatted_address or null on error
 */
export async function fetchLatLngForAddress(address) {
  if (!address) return null;

  try {
    const response = await fetch(
      `${backendUrl}/api/places/geocode?address=${encodeURIComponent(address)}`
    );

    if (!response.ok) {
      throw new Error(`Geocode API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error geocoding address:', error);
    return null;
  }
}
