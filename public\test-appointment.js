// This script adds a test appointment directly to localStorage
// Run this in the browser console to add a test appointment

function addTestAppointment() {
  // Get existing appointments
  let appointments = [];
  try {
    const storedAppointments = localStorage.getItem('localAppointments');
    if (storedAppointments) {
      appointments = JSON.parse(storedAppointments);
    }
  } catch (error) {
    console.error('Error loading appointments from localStorage:', error);
  }

  // Create a test appointment
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  // Format date as "Month Day, Year" (e.g., "June 15, 2023")
  const formattedDate = tomorrow.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
  
  // Format time as "HH:MM AM/PM" (e.g., "10:00 AM")
  const formattedTime = tomorrow.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const userId = prompt('Enter user ID (or leave blank for "guest")') || 'guest';

  const testAppointment = {
    id: `test-appt-${Date.now()}`,
    clientName: 'Test Client',
    clientEmail: '<EMAIL>',
    clientPhone: '************',
    propertyAddress: '123 Test Street, Test City',
    propertyType: 'House',
    date: formattedDate,
    time: formattedTime,
    status: 'confirmed',
    notes: 'This is a test appointment added for debugging purposes.',
    userId: userId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Add the test appointment to the array
  appointments.push(testAppointment);

  // Save back to localStorage
  try {
    localStorage.setItem('localAppointments', JSON.stringify(appointments));
    console.log('Test appointment added:', testAppointment);
    console.log('Total appointments:', appointments.length);
    alert('Test appointment added successfully! Refresh the page to see it.');
    return testAppointment;
  } catch (error) {
    console.error('Error saving test appointment to localStorage:', error);
    alert('Error adding test appointment: ' + error.message);
    return null;
  }
}

function clearAllAppointments() {
  if (confirm('Are you sure you want to clear all appointments? This cannot be undone.')) {
    try {
      localStorage.removeItem('localAppointments');
      console.log('All appointments cleared from localStorage');
      alert('All appointments cleared successfully! Refresh the page to see the changes.');
      return true;
    } catch (error) {
      console.error('Error clearing appointments from localStorage:', error);
      alert('Error clearing appointments: ' + error.message);
      return false;
    }
  }
}

// Add these functions to the window object so they can be called from the console
window.addTestAppointment = addTestAppointment;
window.clearAllAppointments = clearAllAppointments;

console.log('Test appointment functions loaded. Call window.addTestAppointment() or window.clearAllAppointments() to use them.');
