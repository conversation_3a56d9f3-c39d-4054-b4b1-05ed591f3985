// Full static build script for Netlify
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('=== Digital Realtor Netlify Full Static Build ====\n');

try {
  // Step 1: Clean previous build
  console.log('Cleaning previous build...');
  if (fs.existsSync(path.join(__dirname, 'prod-build'))) {
    fs.rmSync(path.join(__dirname, 'prod-build'), { recursive: true, force: true });
  }
  fs.mkdirSync(path.join(__dirname, 'prod-build'), { recursive: true });
  console.log('✓ Previous build cleaned\n');

  // Step 2: Install ALL dependencies including dev dependencies
  console.log('Installing ALL dependencies including dev dependencies...');
  execSync('npm install --include=dev', { stdio: 'inherit' });
  console.log('✓ All dependencies installed\n');

  // Step 3: Create a pre-built dist directory
  console.log('Creating pre-built distribution...');
  
  // Copy the src directory to the prod-build directory
  if (fs.existsSync(path.join(__dirname, 'src'))) {
    // Create the destination directory
    fs.mkdirSync(path.join(__dirname, 'prod-build', 'src'), { recursive: true });
    
    // Copy all files from src to prod-build/src
    const copyDir = (src, dest) => {
      const entries = fs.readdirSync(src, { withFileTypes: true });
      for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        if (entry.isDirectory()) {
          fs.mkdirSync(destPath, { recursive: true });
          copyDir(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      }
    };
    
    copyDir(path.join(__dirname, 'src'), path.join(__dirname, 'prod-build', 'src'));
    console.log('✓ Source files copied');
  } else {
    console.log('Warning: src directory not found');
  }

  // Step 4: Copy public directory
  console.log('Copying public directory...');
  if (fs.existsSync(path.join(__dirname, 'public'))) {
    // Create the destination directory
    fs.mkdirSync(path.join(__dirname, 'prod-build', 'public'), { recursive: true });
    
    // Copy all files from public to prod-build/public
    const copyDir = (src, dest) => {
      const entries = fs.readdirSync(src, { withFileTypes: true });
      for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        if (entry.isDirectory()) {
          fs.mkdirSync(destPath, { recursive: true });
          copyDir(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      }
    };
    
    copyDir(path.join(__dirname, 'public'), path.join(__dirname, 'prod-build'));
    console.log('✓ Public files copied');
  } else {
    console.log('Warning: public directory not found');
  }

  // Step 5: Create index.html
  console.log('Creating index.html...');
  const indexHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Digital Realtor</title>
    <meta name="description" content="AI-powered tools for real estate professionals" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" />
    <script src="https://clarity.microsoft.com/s/r7yjq54tfv"></script>
    <link rel="stylesheet" href="/assets/index.css" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/assets/index.js"></script>
  </body>
</html>`;

  fs.writeFileSync(path.join(__dirname, 'prod-build', 'index.html'), indexHtml);
  console.log('✓ Index.html created\n');

  // Step 6: Create assets directory
  console.log('Creating assets directory...');
  fs.mkdirSync(path.join(__dirname, 'prod-build', 'assets'), { recursive: true });
  
  // Create a basic CSS file
  const cssContent = `
/* Base styles */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --text-color: #334155;
  --heading-color: #0f172a;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --border-color: #e2e8f0;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header */
header {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  height: 40px;
}

nav ul {
  display: flex;
  list-style: none;
  gap: 1.5rem;
}

nav a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
}

nav a:hover {
  color: var(--primary-color);
}

/* Hero section */
.hero {
  padding: 4rem 0;
  text-align: center;
  background-color: var(--primary-color);
  color: white;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.25rem;
  max-width: 800px;
  margin: 0 auto 2rem;
}

.button {
  display: inline-block;
  background-color: white;
  color: var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
}

.button:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

/* Features section */
.features {
  padding: 4rem 0;
}

.features h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 3rem;
  color: var(--heading-color);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: var(--card-background);
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

/* Footer */
footer {
  background-color: var(--heading-color);
  color: white;
  padding: 3rem 0;
  margin-top: auto;
}

.footer-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.footer-column h3 {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
}

.footer-column ul {
  list-style: none;
}

.footer-column li {
  margin-bottom: 0.75rem;
}

.footer-column a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
}

.footer-column a:hover {
  color: white;
}

.copyright {
  text-align: center;
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
}
`;

  fs.writeFileSync(path.join(__dirname, 'prod-build', 'assets', 'index.css'), cssContent);
  
  // Create a basic JavaScript file
  const jsContent = `
document.addEventListener('DOMContentLoaded', function() {
  // Create the app structure
  const root = document.getElementById('root');
  
  // Create header
  const header = document.createElement('header');
  const headerContainer = document.createElement('div');
  headerContainer.className = 'header-container container';
  
  // Logo
  const logoLink = document.createElement('a');
  logoLink.href = '/';
  const logo = document.createElement('img');
  logo.src = '/images/logo.png';
  logo.alt = 'AI Digital Realtor Logo';
  logo.className = 'logo';
  logoLink.appendChild(logo);
  
  // Navigation
  const nav = document.createElement('nav');
  const navList = document.createElement('ul');
  
  const navItems = [
    { text: 'Home', url: '/' },
    { text: 'Features', url: '/features' },
    { text: 'About Us', url: '/about' },
    { text: 'Contact', url: '/contact' }
  ];
  
  navItems.forEach(item => {
    const li = document.createElement('li');
    const a = document.createElement('a');
    a.href = item.url;
    a.textContent = item.text;
    li.appendChild(a);
    navList.appendChild(li);
  });
  
  nav.appendChild(navList);
  headerContainer.appendChild(logoLink);
  headerContainer.appendChild(nav);
  header.appendChild(headerContainer);
  
  // Create hero section
  const hero = document.createElement('section');
  hero.className = 'hero';
  const heroContainer = document.createElement('div');
  heroContainer.className = 'container';
  
  const heroTitle = document.createElement('h1');
  heroTitle.textContent = 'AI Digital Realtor';
  
  const heroText = document.createElement('p');
  heroText.textContent = 'Transforming real estate with AI-powered tools for modern professionals';
  
  const heroButton = document.createElement('a');
  heroButton.href = '/features';
  heroButton.className = 'button';
  heroButton.textContent = 'Explore Features';
  
  heroContainer.appendChild(heroTitle);
  heroContainer.appendChild(heroText);
  heroContainer.appendChild(heroButton);
  hero.appendChild(heroContainer);
  
  // Create features section
  const features = document.createElement('section');
  features.className = 'features';
  const featuresContainer = document.createElement('div');
  featuresContainer.className = 'container';
  
  const featuresTitle = document.createElement('h2');
  featuresTitle.textContent = 'Our AI-Powered Tools';
  
  const featuresGrid = document.createElement('div');
  featuresGrid.className = 'features-grid';
  
  const featuresList = [
    {
      title: 'AI Listing Generator',
      description: 'Create compelling property listings with AI-generated descriptions based on property details and images.'
    },
    {
      title: 'Comp Analyzer',
      description: 'Get accurate property valuations with our AI-powered comparative market analysis tool.'
    },
    {
      title: 'Inspection Report Generator',
      description: 'Generate detailed inspection reports with AI analysis of property issues and recommendations.'
    },
    {
      title: 'Appointment Scheduler',
      description: 'Streamline your scheduling process with our AI appointment scheduler that integrates with your calendar.'
    },
    {
      title: 'Neighborhood & Compliance Overview',
      description: 'Get comprehensive neighborhood data and compliance information for any property.'
    }
  ];
  
  featuresList.forEach(feature => {
    const card = document.createElement('div');
    card.className = 'feature-card';
    
    const title = document.createElement('h3');
    title.textContent = feature.title;
    
    const description = document.createElement('p');
    description.textContent = feature.description;
    
    card.appendChild(title);
    card.appendChild(description);
    featuresGrid.appendChild(card);
  });
  
  featuresContainer.appendChild(featuresTitle);
  featuresContainer.appendChild(featuresGrid);
  features.appendChild(featuresContainer);
  
  // Create footer
  const footer = document.createElement('footer');
  const footerContainer = document.createElement('div');
  footerContainer.className = 'footer-container container';
  
  const columns = [
    {
      title: 'Features',
      links: [
        { text: 'AI Listing Generator', url: '/listing-generator' },
        { text: 'Comp Analyzer', url: '/comp-analyzer' },
        { text: 'Inspection Report Generator', url: '/inspection-report' },
        { text: 'Appointment Scheduler', url: '/appointment-scheduler' },
        { text: 'Neighborhood & Compliance', url: '/neighborhood-compliance' }
      ]
    },
    {
      title: 'Company',
      links: [
        { text: 'About Us', url: '/about' },
        { text: 'Contact', url: '/contact' }
      ]
    },
    {
      title: 'Contact Us',
      links: [
        { text: '<EMAIL>', url: 'mailto:<EMAIL>' }
      ]
    }
  ];
  
  columns.forEach(column => {
    const div = document.createElement('div');
    div.className = 'footer-column';
    
    const title = document.createElement('h3');
    title.textContent = column.title;
    
    const list = document.createElement('ul');
    
    column.links.forEach(link => {
      const li = document.createElement('li');
      const a = document.createElement('a');
      a.href = link.url;
      a.textContent = link.text;
      li.appendChild(a);
      list.appendChild(li);
    });
    
    div.appendChild(title);
    div.appendChild(list);
    footerContainer.appendChild(div);
  });
  
  const copyright = document.createElement('div');
  copyright.className = 'copyright';
  copyright.textContent = '© ' + new Date().getFullYear() + ' AI Digital Realtor. All rights reserved.';
  
  footer.appendChild(footerContainer);
  footer.appendChild(copyright);
  
  // Add all sections to the root
  root.appendChild(header);
  root.appendChild(hero);
  root.appendChild(features);
  root.appendChild(footer);
  
  // Add event listeners for navigation
  document.querySelectorAll('nav a, .button, .feature-card').forEach(element => {
    element.addEventListener('click', function(e) {
      // For now, prevent default navigation and show an alert
      e.preventDefault();
      alert('This is a static preview. The full application is coming soon!');
    });
  });
});
`;

  fs.writeFileSync(path.join(__dirname, 'prod-build', 'assets', 'index.js'), jsContent);
  console.log('✓ Assets created\n');

  // Step 7: Create Netlify configuration files
  console.log('Creating Netlify configuration files...');

  // Create _redirects file for client-side routing
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', '_redirects'),
    '/* /index.html 200'
  );

  // Create a robots.txt file
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'robots.txt'),
    'User-agent: *\nAllow: /'
  );

  console.log('✓ Netlify configuration files created\n');

  // Step 8: Create images directory if it doesn't exist
  console.log('Ensuring images directory exists...');
  fs.mkdirSync(path.join(__dirname, 'prod-build', 'images'), { recursive: true });
  console.log('✓ Images directory created\n');

  console.log('\nNetlify full static build completed successfully!');
  console.log('The build is available in the prod-build directory.');

} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}
