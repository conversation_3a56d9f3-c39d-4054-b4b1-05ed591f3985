import { Link } from 'react-router-dom';
import { useEffect, useState } from 'react';

const SimpleAbout = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <div className="bg-dark-900">
      {/* Hero Section */}
      <section className="py-8 bg-gradient-neon text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-900/50 to-dark-900/50"></div>

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full bg-accent-cyan/20 animate-float"
              style={{
                width: `${Math.random() * 8 + 4}px`,
                height: `${Math.random() * 8 + 4}px`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animationDuration: `${Math.random() * 8 + 8}s`,
                animationDelay: `${Math.random() * 3}s`
              }}
            ></div>
          ))}
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8 animate-fade-in">
            <div className="md:w-2/3 text-left">
              <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-3 py-1 rounded-full border border-accent-cyan/30">
                <span className="text-accent-cyan text-sm font-medium">Our Story</span>
              </div>

              <h1 className="text-4xl md:text-5xl font-bold mb-4 neon-text bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">
                About Digital Realtor
              </h1>

              <p className="text-lg text-white/90 max-w-2xl leading-relaxed">
                We're revolutionizing the real estate industry with <span className="text-accent-cyan font-semibold">AI technology</span> that makes finding your dream home easier than ever before.
              </p>
            </div>

            <div className="md:w-1/3 flex justify-center">
              <div className="relative w-48 h-48 md:w-56 md:h-56">
                <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/20 to-primary-500/20 rounded-full blur-xl animate-pulse-slow"></div>
                <div className="absolute inset-0 border-2 border-accent-cyan/30 rounded-full"></div>
                <div className="absolute inset-4 border border-accent-cyan/20 rounded-full"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <svg className="w-24 h-24 text-accent-cyan/80" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={0.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission */}
      <section className="py-10">
        <div className="container mx-auto px-4">
          <div className="glass-card p-10 mb-8 animate-slide-in-up border-glow relative overflow-hidden">
            <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/5 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <div className="max-w-3xl mx-auto">
                <div className="text-center">
                  <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-3 py-1 rounded-full border border-accent-cyan/30">
                    <span className="text-accent-cyan text-sm font-medium">Our Purpose</span>
                  </div>
                  <h2 className="text-3xl md:text-5xl font-bold mb-6 neon-text">
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Our Mission</span>
                  </h2>
                </div>
                <div className="space-y-6 text-center">
                  <p className="text-primary-100 leading-relaxed text-lg">
                    At Digital Realtor, our mission is to transform the way people find and purchase homes by leveraging the power of <span className="text-accent-cyan font-medium">artificial intelligence</span>. We believe that finding your perfect home should be an exciting and stress-free experience.
                  </p>
                  <p className="text-primary-100 leading-relaxed text-lg">
                    Our <span className="text-accent-cyan font-medium">AI assistant</span> is designed to understand your unique preferences, lifestyle needs, and budget constraints to provide personalized property recommendations that truly match what you're looking for.
                  </p>
                  <p className="text-primary-100 leading-relaxed text-lg">
                    We're committed to transparency, innovation, and exceptional customer service at every step of your home buying journey.
                  </p>
                </div>

                <div className="mt-8 flex items-center">
                  <div className="h-px bg-gradient-to-r from-accent-cyan/50 to-transparent flex-grow"></div>
                  <span className="px-4 text-accent-cyan text-sm">Powered by AI</span>
                  <div className="h-px bg-gradient-to-l from-accent-cyan/50 to-transparent flex-grow"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Technology */}
      <section className="py-14 bg-gradient-tech relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-accent-cyan/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12 animate-fade-in">
            <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">Cutting-Edge Solutions</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-4 neon-text">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Our Technology</span>
            </h2>
            <p className="text-lg text-primary-100 max-w-2xl mx-auto leading-relaxed">
              Powered by advanced algorithms and machine learning, our platform delivers a seamless and intelligent real estate experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="glass-card p-6 text-center transition-all duration-500 hover:shadow-lg hover:-translate-y-2 animate-fade-in border-glow group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-secondary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-secondary-900 to-secondary-700 text-secondary-400 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 neon-glow transform transition-all duration-500 group-hover:scale-110 relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-secondary-400 mb-3 relative z-10">AI Assistant</h3>
              <p className="text-primary-200 relative z-10 leading-relaxed">
                Our conversational AI understands your needs and preferences to provide personalized recommendations.
              </p>

              <div className="mt-4 h-px w-16 bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent mx-auto"></div>
            </div>

            <div className="glass-card p-6 text-center transition-all duration-500 hover:shadow-lg hover:-translate-y-2 animate-fade-in delay-100 border-glow group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-primary-900 to-primary-700 text-accent-cyan w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 neon-glow transform transition-all duration-500 group-hover:scale-110 relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-accent-cyan mb-3 relative z-10">Smart Search</h3>
              <p className="text-primary-200 relative z-10 leading-relaxed">
                Advanced algorithms that go beyond basic filters to find properties that match your lifestyle.
              </p>

              <div className="mt-4 h-px w-16 bg-gradient-to-r from-transparent via-accent-cyan/30 to-transparent mx-auto"></div>
            </div>

            <div className="glass-card p-6 text-center transition-all duration-500 hover:shadow-lg hover:-translate-y-2 animate-fade-in delay-200 border-glow group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-secondary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-secondary-900 to-secondary-700 text-secondary-400 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 neon-glow transform transition-all duration-500 group-hover:scale-110 relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-secondary-400 mb-3 relative z-10">Virtual Tours</h3>
              <p className="text-primary-200 relative z-10 leading-relaxed">
                Explore properties from the comfort of your home with our immersive virtual tour technology.
              </p>

              <div className="mt-4 h-px w-16 bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent mx-auto"></div>
            </div>

            <div className="glass-card p-6 text-center transition-all duration-500 hover:shadow-lg hover:-translate-y-2 animate-fade-in delay-300 border-glow group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-primary-900 to-primary-700 text-accent-cyan w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 neon-glow transform transition-all duration-500 group-hover:scale-110 relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-accent-cyan mb-3 relative z-10">Market Insights</h3>
              <p className="text-primary-200 relative z-10 leading-relaxed">
                Data-driven insights on property values, neighborhood trends, and investment potential.
              </p>

              <div className="mt-4 h-px w-16 bg-gradient-to-r from-transparent via-accent-cyan/30 to-transparent mx-auto"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-14 bg-dark-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12 animate-fade-in">
            <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">Meet Our Experts</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-4 neon-text">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Our Team</span>
            </h2>
            <p className="text-lg text-primary-100 max-w-2xl mx-auto leading-relaxed">
              A diverse group of experts dedicated to revolutionizing the real estate industry through technology and innovation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="glass-card overflow-hidden transition-all duration-500 hover:shadow-lg animate-fade-in border-glow group relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&q=80"
                  alt="Team Member"
                  className="w-full h-56 object-cover object-center transform transition-all duration-500 group-hover:scale-105 brightness-90"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-900/90 via-dark-900/50 to-transparent"></div>
                <div className="absolute top-4 right-4">
                  <div className="bg-dark-800/80 backdrop-blur-sm px-3 py-1 rounded-full border border-accent-cyan/30">
                    <span className="text-accent-cyan text-xs font-medium">Leadership</span>
                  </div>
                </div>
              </div>

              <div className="p-6 relative z-10">
                <h3 className="text-xl font-bold text-accent-cyan mb-2">Sarah Johnson</h3>
                <p className="text-secondary-400 mb-4 flex items-center">
                  <span className="bg-accent-cyan/10 px-2 py-0.5 rounded text-sm">CEO & Founder</span>
                </p>
                <p className="text-primary-200 leading-relaxed">
                  With over 15 years of experience in real estate and <span className="text-accent-cyan font-medium">technology</span>, Sarah leads our mission to transform the industry.
                </p>

                <div className="mt-4 pt-4 border-t border-primary-800/30 flex justify-between items-center">
                  <div className="flex space-x-2">
                    <div className="w-6 h-6 rounded-full bg-dark-800 border border-accent-cyan/30 flex items-center justify-center text-accent-cyan hover:bg-accent-cyan/10 transition-colors cursor-pointer">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.162 5.656a8.384 8.384 0 0 1-2.402.658A4.196 4.196 0 0 0 21.6 4c-.82.488-1.719.83-2.656 1.015a4.182 4.182 0 0 0-7.126 3.814 11.874 11.874 0 0 1-8.62-4.37 4.168 4.168 0 0 0-.566 2.103c0 1.45.738 2.731 1.86 3.481a4.168 4.168 0 0 1-1.894-.523v.052a4.185 4.185 0 0 0 3.355 4.101 4.21 4.21 0 0 1-1.89.072A4.185 4.185 0 0 0 7.97 16.65a8.394 8.394 0 0 1-6.191 1.732 11.83 11.83 0 0 0 6.41 1.88c7.693 0 11.9-6.373 11.9-11.9 0-.18-.005-.362-.013-.54a8.496 8.496 0 0 0 2.087-2.165z"/>
                      </svg>
                    </div>
                    <div className="w-6 h-6 rounded-full bg-dark-800 border border-accent-cyan/30 flex items-center justify-center text-accent-cyan hover:bg-accent-cyan/10 transition-colors cursor-pointer">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 0 1-2.063-2.065 2.064 2.064 0 1 1 2.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </div>
                  </div>
                  <span className="text-xs text-primary-400">Joined 2018</span>
                </div>
              </div>
            </div>

            <div className="glass-card overflow-hidden transition-all duration-500 hover:shadow-lg animate-fade-in delay-100 border-glow group relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-secondary-400/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&q=80"
                  alt="Team Member"
                  className="w-full h-56 object-cover object-center transform transition-all duration-500 group-hover:scale-105 brightness-90"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-900/90 via-dark-900/50 to-transparent"></div>
                <div className="absolute top-4 right-4">
                  <div className="bg-dark-800/80 backdrop-blur-sm px-3 py-1 rounded-full border border-secondary-400/30">
                    <span className="text-secondary-400 text-xs font-medium">Technology</span>
                  </div>
                </div>
              </div>

              <div className="p-6 relative z-10">
                <h3 className="text-xl font-bold text-secondary-400 mb-2">Michael Chen</h3>
                <p className="text-accent-cyan mb-4 flex items-center">
                  <span className="bg-secondary-400/10 px-2 py-0.5 rounded text-sm">CTO</span>
                </p>
                <p className="text-primary-200 leading-relaxed">
                  Michael brings expertise in <span className="text-secondary-400 font-medium">AI and machine learning</span> to create our innovative property matching algorithms.
                </p>

                <div className="mt-4 pt-4 border-t border-primary-800/30 flex justify-between items-center">
                  <div className="flex space-x-2">
                    <div className="w-6 h-6 rounded-full bg-dark-800 border border-secondary-400/30 flex items-center justify-center text-secondary-400 hover:bg-secondary-400/10 transition-colors cursor-pointer">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"/>
                      </svg>
                    </div>
                    <div className="w-6 h-6 rounded-full bg-dark-800 border border-secondary-400/30 flex items-center justify-center text-secondary-400 hover:bg-secondary-400/10 transition-colors cursor-pointer">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 0 1-2.063-2.065 2.064 2.064 0 1 1 2.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </div>
                  </div>
                  <span className="text-xs text-primary-400">Joined 2019</span>
                </div>
              </div>
            </div>

            <div className="glass-card overflow-hidden transition-all duration-500 hover:shadow-lg animate-fade-in delay-200 border-glow group relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&q=80"
                  alt="Team Member"
                  className="w-full h-56 object-cover object-center transform transition-all duration-500 group-hover:scale-105 brightness-90"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-900/90 via-dark-900/50 to-transparent"></div>
                <div className="absolute top-4 right-4">
                  <div className="bg-dark-800/80 backdrop-blur-sm px-3 py-1 rounded-full border border-accent-cyan/30">
                    <span className="text-accent-cyan text-xs font-medium">Real Estate</span>
                  </div>
                </div>
              </div>

              <div className="p-6 relative z-10">
                <h3 className="text-xl font-bold text-accent-cyan mb-2">Jessica Rodriguez</h3>
                <p className="text-secondary-400 mb-4 flex items-center">
                  <span className="bg-accent-cyan/10 px-2 py-0.5 rounded text-sm">Head of Real Estate</span>
                </p>
                <p className="text-primary-200 leading-relaxed">
                  Jessica's deep knowledge of the real estate market ensures our <span className="text-accent-cyan font-medium">technology</span> delivers practical, valuable results.
                </p>

                <div className="mt-4 pt-4 border-t border-primary-800/30 flex justify-between items-center">
                  <div className="flex space-x-2">
                    <div className="w-6 h-6 rounded-full bg-dark-800 border border-accent-cyan/30 flex items-center justify-center text-accent-cyan hover:bg-accent-cyan/10 transition-colors cursor-pointer">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.162 5.656a8.384 8.384 0 0 1-2.402.658A4.196 4.196 0 0 0 21.6 4c-.82.488-1.719.83-2.656 1.015a4.182 4.182 0 0 0-7.126 3.814 11.874 11.874 0 0 1-8.62-4.37 4.168 4.168 0 0 0-.566 2.103c0 1.45.738 2.731 1.86 3.481a4.168 4.168 0 0 1-1.894-.523v.052a4.185 4.185 0 0 0 3.355 4.101 4.21 4.21 0 0 1-1.89.072A4.185 4.185 0 0 0 7.97 16.65a8.394 8.394 0 0 1-6.191 1.732 11.83 11.83 0 0 0 6.41 1.88c7.693 0 11.9-6.373 11.9-11.9 0-.18-.005-.362-.013-.54a8.496 8.496 0 0 0 2.087-2.165z"/>
                      </svg>
                    </div>
                    <div className="w-6 h-6 rounded-full bg-dark-800 border border-accent-cyan/30 flex items-center justify-center text-accent-cyan hover:bg-accent-cyan/10 transition-colors cursor-pointer">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 0 1-2.063-2.065 2.064 2.064 0 1 1 2.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </div>
                  </div>
                  <span className="text-xs text-primary-400">Joined 2020</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-14 bg-dark-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="glass-card p-10 text-center text-white animate-fade-in border-glow relative overflow-hidden">
            <div className="absolute top-0 right-0 w-96 h-96 bg-accent-cyan/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
                <span className="text-accent-cyan text-sm font-medium">Get Started Today</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold mb-6 neon-text">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Ready to Find Your Dream Home?</span>
              </h2>

              <p className="text-xl mb-8 max-w-2xl mx-auto leading-relaxed">
                Let our <span className="text-accent-cyan font-medium">AI assistant</span> help you discover the perfect property tailored to your unique preferences and needs.
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link
                  to="/contact"
                  className="primary-button button-lg group"
                >
                  <svg className="w-5 h-5 mr-2 group-hover:animate-pulse" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Contact Us Today
                </Link>

                <Link
                  to="/properties"
                  className="secondary-button button-lg group"
                >
                  <svg className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
                  </svg>
                  Browse Properties
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SimpleAbout;
