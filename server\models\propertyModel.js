import mongoose from 'mongoose';

const reviewSchema = mongoose.Schema(
  {
    name: { type: String, required: true },
    rating: { type: Number, required: true },
    comment: { type: String, required: true },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

const propertySchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    title: {
      type: String,
      required: true,
    },
    address: {
      street: { type: String, required: true },
      city: { type: String, required: true },
      state: { type: String, required: true },
      zipCode: { type: String, required: true },
      country: { type: String, required: true, default: 'USA' },
    },
    description: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
      default: 0,
    },
    images: [
      {
        type: String,
      },
    ],
    propertyType: {
      type: String,
      required: true,
      enum: ['House', 'Apartment', 'Condo', 'Townhouse', 'Land', 'Commercial'],
    },
    bedrooms: {
      type: Number,
      required: true,
      default: 0,
    },
    bathrooms: {
      type: Number,
      required: true,
      default: 0,
    },
    squareFeet: {
      type: Number,
      required: true,
      default: 0,
    },
    lotSize: {
      type: Number,
      default: 0,
    },
    yearBuilt: {
      type: Number,
    },
    features: [
      {
        type: String,
      },
    ],
    reviews: [reviewSchema],
    rating: {
      type: Number,
      required: true,
      default: 0,
    },
    numReviews: {
      type: Number,
      required: true,
      default: 0,
    },
    isFeatured: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      required: true,
      enum: ['For Sale', 'For Rent', 'Sold', 'Pending'],
      default: 'For Sale',
    },
  },
  {
    timestamps: true,
  }
);

const Property = mongoose.model('Property', propertySchema);

export default Property;
