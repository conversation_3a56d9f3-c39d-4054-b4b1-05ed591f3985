// Calendar Utilities
// This file contains utility functions for calendar operations

import { Appointment } from '../models/Appointment';

// Generate a unique ID for calendar events
export const generateUid = (): string => {
  return `appointment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Format date and time for Google Calendar URL
export const formatDateTimeForGoogleCalendar = (
  date: string,
  time: string
): { startDate: string; endDate: string } => {
  try {
    // Parse the date (e.g., "June 15, 2023")
    const dateParts = new Date(date);
    
    // Parse the time (e.g., "10:00 AM")
    const timeParts = time.match(/(\d+):(\d+)\s*(AM|PM)/i);
    
    if (!timeParts) {
      throw new Error('Invalid time format');
    }
    
    let hours = parseInt(timeParts[1], 10);
    const minutes = parseInt(timeParts[2], 10);
    const period = timeParts[3].toUpperCase();
    
    // Convert to 24-hour format
    if (period === 'PM' && hours < 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }
    
    // Create start date
    const startDate = new Date(dateParts);
    startDate.setHours(hours, minutes, 0, 0);
    
    // Create end date (1 hour after start)
    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + 1);
    
    // Format for Google Calendar URL
    const formatForUrl = (date: Date): string => {
      return date.toISOString().replace(/-|:|\.\d+/g, '');
    };
    
    return {
      startDate: formatForUrl(startDate),
      endDate: formatForUrl(endDate)
    };
  } catch (error) {
    console.error('Error formatting date and time:', error);
    throw error;
  }
};

// Generate Google Calendar URL
export const generateGoogleCalendarUrl = (appointment: Appointment): string => {
  try {
    const { date, time, clientName, propertyAddress, notes, id } = appointment;
    
    // Validate required data
    if (!date || !time) {
      throw new Error('Date and time are required for calendar integration');
    }
    
    // Format date and time
    const { startDate, endDate } = formatDateTimeForGoogleCalendar(date, time);
    
    // Create event details
    const eventTitle = encodeURIComponent(`Property Viewing: ${propertyAddress}`);
    const eventDescription = encodeURIComponent(
      `Appointment with ${clientName}\nProperty Address: ${propertyAddress}\nStatus: ${appointment.status}\nAppointment ID: ${id}${notes ? `\nNotes: ${notes}` : ''}`
    );
    const eventLocation = encodeURIComponent(propertyAddress);
    
    // Generate Google Calendar URL
    return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${eventTitle}&dates=${startDate}/${endDate}&details=${eventDescription}&location=${eventLocation}&sf=true&output=xml`;
  } catch (error) {
    console.error('Error generating Google Calendar URL:', error);
    throw error;
  }
};

// Generate ICS file content
export const generateIcsFileContent = (appointment: Appointment): string => {
  try {
    const { date, time, clientName, propertyAddress, notes, id } = appointment;
    
    // Validate required data
    if (!date || !time) {
      throw new Error('Date and time are required for calendar integration');
    }
    
    // Format date and time
    const { startDate, endDate } = formatDateTimeForGoogleCalendar(date, time);
    
    // Format for ICS file (remove 'Z' at the end and add it separately)
    const startDateTime = startDate.replace(/Z$/, '') + 'Z';
    const endDateTime = endDate.replace(/Z$/, '') + 'Z';
    
    // Generate a unique ID for the event
    const uid = generateUid();
    
    // Create the ICS content
    return [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Digital Realtor//Appointment Scheduler//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:PUBLISH',
      'BEGIN:VEVENT',
      `UID:${uid}`,
      `DTSTAMP:${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}Z`,
      `DTSTART:${startDateTime}`,
      `DTEND:${endDateTime}`,
      `SUMMARY:Property Viewing: ${propertyAddress}`,
      `DESCRIPTION:Appointment with ${clientName}\\nProperty Address: ${propertyAddress}\\nStatus: ${appointment.status}\\nAppointment ID: ${id}${notes ? `\\nNotes: ${notes}` : ''}`,
      `LOCATION:${propertyAddress}`,
      'STATUS:CONFIRMED',
      'SEQUENCE:0',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');
  } catch (error) {
    console.error('Error generating ICS file content:', error);
    throw error;
  }
};

// Download ICS file
export const downloadIcsFile = (appointment: Appointment): void => {
  try {
    // Generate ICS content
    const icsContent = generateIcsFileContent(appointment);
    
    // Create a Blob with the ICS content
    const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
    
    // Create a download link
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `appointment-${appointment.id}.ics`;
    
    // Trigger the download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('Generated ICS file for appointment:', appointment.id);
  } catch (error) {
    console.error('Error downloading ICS file:', error);
    throw error;
  }
};
