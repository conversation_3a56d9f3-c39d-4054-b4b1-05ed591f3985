const express = require('express');
const axios = require('axios');
const router = express.Router();

// Proxy endpoint for Walk Score API
router.get('/walkscore', async (req, res) => {
  try {
    // Get parameters from query string
    const { address, lat, lon } = req.query;

    // Log the request parameters
    console.log('Walk Score API request parameters:', { address, lat, lon });

    if (!address) {
      return res.status(400).json({ error: 'Address is required' });
    }

    // Get API key from environment variables
    const apiKey = process.env.WALKSCORE_API_KEY || '********************************';

    // Build the API URL
    let apiUrl = `https://api.walkscore.com/score?format=json&address=${encodeURIComponent(address)}&wsapikey=${apiKey}`;

    // Add coordinates if available
    if (lat && lon) {
      apiUrl += `&lat=${lat}&lon=${lon}&transit=1&bike=1`;
    }

    console.log('Walk Score API URL (redacted):', apiUrl.replace(apiKey, 'API_KEY_REDACTED'));

    // Make the request to the Walk Score API
    const response = await axios.get(apiUrl, {
      timeout: 10000, // 10 second timeout
      headers: {
        'User-Agent': 'Digital Realtor/1.0'
      }
    });

    // Log the response status and data
    console.log('Walk Score API response status:', response.status);
    console.log('Walk Score API response data:', response.data);

    // Check if the response contains an error
    if (response.data && response.data.status !== 1) {
      console.warn('Walk Score API returned an error:', response.data.message || 'Unknown error');

      // Return the error with a 400 status code
      return res.status(400).json({
        error: 'Walk Score API error',
        message: response.data.message || 'Unknown error',
        status: response.data.status
      });
    }

    // Return the response data
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Error proxying request to Walk Score API:', error);

    // Check if it's an Axios error with a response
    if (error.response) {
      console.error('Walk Score API error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });

      // Return the error response from the API
      return res.status(error.response.status).json({
        error: 'Walk Score API error',
        details: error.response.data
      });
    }

    // Check for timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        error: 'Walk Score API timeout',
        message: 'The request to the Walk Score API timed out'
      });
    }

    // Return a more detailed error message in development
    if (process.env.NODE_ENV !== 'production') {
      return res.status(500).json({
        error: 'Failed to fetch data from Walk Score API',
        details: error.message,
        stack: error.stack
      });
    }

    // Return a generic error message in production
    return res.status(500).json({ error: 'Failed to fetch data from Walk Score API' });
  }
});

// Proxy endpoint for SchoolDigger API
router.get('/schooldigger', async (req, res) => {
  try {
    // Get parameters from query string
    const { st, q } = req.query;

    // Log the request parameters
    console.log('SchoolDigger API request parameters:', { st, q });

    if (!st || !q) {
      return res.status(400).json({ error: 'State (st) and query (q) parameters are required' });
    }

    // Validate state code (must be 2 letters)
    if (!/^[A-Za-z]{2}$/.test(st)) {
      return res.status(400).json({ error: 'State code (st) must be a 2-letter code' });
    }

    // Get API key from environment variables
    const apiKey = process.env.RAPIDAPI_SCHOOLDIGGER_KEY || '**************************************************';

    // Build the API URL
    const apiUrl = `https://schooldigger-k-12-school-data-api.p.rapidapi.com/v2.0/autocomplete/schools?st=${st.toUpperCase()}&q=${encodeURIComponent(q)}`;
    console.log('SchoolDigger API URL:', apiUrl);

    // Make the request to the SchoolDigger API
    const response = await axios.get(
      apiUrl,
      {
        headers: {
          'x-rapidapi-host': 'schooldigger-k-12-school-data-api.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }
      }
    );

    // Log the response status and data
    console.log('SchoolDigger API response status:', response.status);
    console.log('SchoolDigger API response data:', response.data);

    // Return the response data
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Error proxying request to SchoolDigger API:', error);

    // Check if it's an Axios error with a response
    if (error.response) {
      console.error('SchoolDigger API error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });

      // Return the error response from the API
      return res.status(error.response.status).json({
        error: 'SchoolDigger API error',
        details: error.response.data
      });
    }

    // Return a more detailed error message in development
    if (process.env.NODE_ENV !== 'production') {
      return res.status(500).json({
        error: 'Failed to fetch data from SchoolDigger API',
        details: error.message,
        stack: error.stack
      });
    }

    // Return a generic error message in production
    return res.status(500).json({ error: 'Failed to fetch data from SchoolDigger API' });
  }
});

// Proxy endpoint for Census API
router.get('/census', async (req, res) => {
  try {
    // Get parameters from query string
    const { zipCode } = req.query;

    // Log the request parameters
    console.log('Census API request parameters:', { zipCode });

    if (!zipCode) {
      return res.status(400).json({ error: 'ZIP code is required' });
    }

    // Validate ZIP code (must be 5 digits, optionally followed by a hyphen and 4 more digits)
    if (!/^\d{5}(-\d{4})?$/.test(zipCode)) {
      return res.status(400).json({ error: 'ZIP code must be in the format 12345 or 12345-6789' });
    }

    // Extract the 5-digit ZIP code if it includes the +4 extension
    const fiveDigitZip = zipCode.split('-')[0];

    // Get API key from environment variables
    const apiKey = process.env.CENSUS_API_KEY || '15027d6cf60090ffdbaae0fc98a53b92fc0a1b65';

    // Build the API URL with additional fields for median household income and median home value
    const apiUrl = `https://api.census.gov/data/2020/acs/acs5?get=NAME,B01002_001E,B01003_001E,B19013_001E,B25077_001E&for=zip%20code%20tabulation%20area:${fiveDigitZip}&key=${apiKey}`;
    console.log('Census API URL (redacted):', apiUrl.replace(apiKey, 'API_KEY_REDACTED'));

    // Make the request to the Census API
    const response = await axios.get(apiUrl, {
      timeout: 10000, // 10 second timeout
      headers: {
        'User-Agent': 'Digital Realtor/1.0'
      }
    });

    // Log the response status and data
    console.log('Census API response status:', response.status);
    console.log('Census API response data:', response.data);

    // Check if the response has the expected format
    if (!Array.isArray(response.data) || response.data.length < 2) {
      console.warn('Census API returned unexpected data format');
      return res.status(400).json({
        error: 'Census API error',
        message: 'Unexpected data format returned by the Census API'
      });
    }

    // Return the response data
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Error proxying request to Census API:', error);

    // Check if it's an Axios error with a response
    if (error.response) {
      console.error('Census API error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });

      // Return the error response from the API
      return res.status(error.response.status).json({
        error: 'Census API error',
        details: error.response.data
      });
    }

    // Check for timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        error: 'Census API timeout',
        message: 'The request to the Census API timed out'
      });
    }

    // Return a more detailed error message in development
    if (process.env.NODE_ENV !== 'production') {
      return res.status(500).json({
        error: 'Failed to fetch data from Census API',
        details: error.message,
        stack: error.stack
      });
    }

    // Return a generic error message in production
    return res.status(500).json({ error: 'Failed to fetch data from Census API' });
  }
});

// Proxy endpoint for Google Distance Matrix API
router.get('/distancematrix', async (req, res) => {
  // Set CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  try {
    // Get parameters from query string
    const { origins, destinations, mode } = req.query;

    // Log the request parameters
    console.log('Google Distance Matrix API request parameters:', { origins, destinations, mode });

    if (!origins || !destinations) {
      return res.status(400).json({ error: 'Origins and destinations are required' });
    }

    // Get API key from environment variables
    // Using a different API key without referer restrictions for the Distance Matrix API
    const apiKey = process.env.GOOGLE_DISTANCE_MATRIX_API_KEY || 'AIzaSyBhkMKaomxQvBYMvQnYRV4jlJ1JQZQkuTM';

    // Build the API URL with all necessary parameters
    let apiUrl = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${encodeURIComponent(origins)}&destinations=${encodeURIComponent(destinations)}&mode=${mode || 'driving'}&key=${apiKey}`;

    // Add optional parameters if they exist in the request
    if (req.query.language) {
      apiUrl += `&language=${encodeURIComponent(req.query.language)}`;
    }

    if (req.query.units) {
      apiUrl += `&units=${encodeURIComponent(req.query.units)}`;
    }

    if (req.query.avoid) {
      apiUrl += `&avoid=${encodeURIComponent(req.query.avoid)}`;
    }

    if (req.query.departure_time) {
      apiUrl += `&departure_time=${encodeURIComponent(req.query.departure_time)}`;
    }

    if (req.query.arrival_time) {
      apiUrl += `&arrival_time=${encodeURIComponent(req.query.arrival_time)}`;
    }

    console.log('Google Distance Matrix API URL (redacted):', apiUrl.replace(apiKey, 'API_KEY_REDACTED'));

    // Log the full URL for debugging (with API key redacted)
    console.log('Full Google Distance Matrix API URL (redacted):', apiUrl.replace(apiKey, 'API_KEY_REDACTED'));

    try {
      // Make the request to the Google Distance Matrix API
      const response = await axios.get(apiUrl, {
        timeout: 10000, // 10 second timeout
        headers: {
          'User-Agent': 'Digital Realtor/1.0'
        }
      });

      // Log the response status and data
      console.log('Google Distance Matrix API response status:', response.status);
      console.log('Google Distance Matrix API response data:', response.data);

      // Log the full response for debugging
      console.log('Full Google Distance Matrix API response:', JSON.stringify(response.data, null, 2));

      return response;
    } catch (apiError) {
      console.error('Error in Google Distance Matrix API request:', apiError.message);

      if (apiError.response) {
        console.error('Google Distance Matrix API error response:', {
          status: apiError.response.status,
          statusText: apiError.response.statusText,
          data: apiError.response.data
        });

        throw apiError;
      }

      throw apiError;
    }

    // Log the response status and data
    console.log('Google Distance Matrix API response status:', response.status);

    // Check if the response contains data
    if (!response.data) {
      console.warn('Google Distance Matrix API returned empty response');
      return res.status(400).json({
        error: 'Google Distance Matrix API error',
        message: 'Empty response from the Google Distance Matrix API'
      });
    }

    // Log the response data
    console.log('Google Distance Matrix API response data:', response.data);

    // Log the full response for debugging
    console.log('Full Google Distance Matrix API response:', JSON.stringify(response.data, null, 2));

    // This check is now redundant since we already checked for !response.data above

    // Check if the response contains an error status
    if (response.data.status && response.data.status !== 'OK') {
      console.warn(`Google Distance Matrix API returned error status: ${response.data.status}`);
      return res.status(400).json({
        error: 'Google Distance Matrix API error',
        message: `API returned status: ${response.data.status}`,
        details: response.data.error_message || 'No additional error details provided'
      });
    }

    // Check if the response has the expected format
    if (!response.data.rows || !Array.isArray(response.data.rows) || response.data.rows.length === 0) {
      console.warn('Google Distance Matrix API returned no rows');
      return res.status(400).json({
        error: 'Google Distance Matrix API error',
        message: 'No rows returned from the Google Distance Matrix API'
      });
    }

    // Check if the first row has elements
    if (!response.data.rows[0].elements || !Array.isArray(response.data.rows[0].elements) || response.data.rows[0].elements.length === 0) {
      console.warn('Google Distance Matrix API returned no elements');
      return res.status(400).json({
        error: 'Google Distance Matrix API error',
        message: 'No elements returned from the Google Distance Matrix API'
      });
    }

    // Return the response data
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Error proxying request to Google Distance Matrix API:', error);

    // Check if it's an Axios error with a response
    if (error.response) {
      console.error('Google Distance Matrix API error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });

      // Return the error response from the API
      return res.status(error.response.status).json({
        error: 'Google Distance Matrix API error',
        details: error.response.data
      });
    }

    // Check for timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        error: 'Google Distance Matrix API timeout',
        message: 'The request to the Google Distance Matrix API timed out'
      });
    }

    // Return a more detailed error message in development
    if (process.env.NODE_ENV !== 'production') {
      return res.status(500).json({
        error: 'Failed to fetch data from Google Distance Matrix API',
        details: error.message,
        stack: error.stack
      });
    }

    // Return a generic error message in production
    return res.status(500).json({ error: 'Failed to fetch data from Google Distance Matrix API' });
  }
});

module.exports = router;
