{"buyer": {"fullName": {"pdfField": "Buyer_Name", "label": "Buyer Name", "type": "text", "infer": false}, "email": {"pdfField": "Buyer_Email", "label": "Buyer Email", "type": "text", "infer": false}}, "seller": {"fullName": {"pdfField": "Seller_Name", "label": "Seller Name", "type": "text", "infer": false}}, "property": {"address": {"pdfField": "Property_Address", "label": "Property Address", "type": "textarea", "infer": false}, "unit": {"pdfField": "Unit_Number", "label": "Unit # (Optional)", "type": "text", "infer": false}, "parkingIncluded": {"pdfField": "Parking_Included", "label": "Parking Included?", "type": "checkbox", "infer": false}, "parkingDetails": {"pdfField": "Parking_Details", "label": "Parking Details", "type": "text", "infer": true}}, "financials": {"purchasePrice": {"pdfField": "Purchase_Price", "label": "Purchase Price", "type": "number", "infer": false}, "earnestMoney": {"pdfField": "Earnest_Money", "label": "<PERSON><PERSON>nest Money", "type": "number", "infer": true}, "closingDate": {"pdfField": "Closing_Date", "label": "Closing Date", "type": "date", "infer": true}}, "associations": {"hoa": {"pdfField": "HOA_Exists", "label": "Is there an HOA?", "type": "checkbox", "infer": false}, "monthlyFees": {"pdfField": "HOA_Fees", "label": "Monthly HOA Fees", "type": "number", "infer": true}}, "fixturesIncluded": {"pdfField": "Fixtures_Included", "label": "Included Fixtures", "type": "textarea", "infer": true}, "additionalTerms": {"pdfField": "Additional_Terms", "label": "Additional Terms", "type": "textarea", "infer": false}, "metadata": {"contractDate": {"pdfField": "Contract_Date", "label": "Contract Date", "type": "date", "infer": true}}}