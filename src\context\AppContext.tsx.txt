// src/context/AppContext.tsx
import React, { createContext, useContext, useState, ReactNode } from "react";

type AgenticTask =
  | "listingGenerator"
  | "appointmentScheduler"
  | "compAnalysis"
  | "inspectionReport"
  | "leaseAnalysis";

interface TaskStatus {
  inProgress: boolean;
  result?: any;
  error?: string;
}

interface AppContextProps {
  activeTask: AgenticTask | null;
  setActiveTask: (task: AgenticTask | null) => void;
  taskStatus: Record<AgenticTask, TaskStatus>;
  updateTaskStatus: (task: AgenticTask, status: TaskStatus) => void;
  userToken?: string;
  setUserToken: (token: string) => void;
}

const AppContext = createContext<AppContextProps | undefined>(undefined);

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [activeTask, setActiveTask] = useState<AgenticTask | null>(null);
  const [taskStatus, setTaskStatus] = useState<Record<AgenticTask, TaskStatus>>({
    listingGenerator: { inProgress: false },
    appointmentScheduler: { inProgress: false },
    compAnalysis: { inProgress: false },
    inspectionReport: { inProgress: false },
    leaseAnalysis: { inProgress: false },
  });
  const [userToken, setUserToken] = useState<string>();

  const updateTaskStatus = (task: AgenticTask, status: TaskStatus) => {
    setTaskStatus((prev) => ({ ...prev, [task]: status }));
  };

  return (
    <AppContext.Provider
      value={{
        activeTask,
        setActiveTask,
        taskStatus,
        updateTaskStatus,
        userToken,
        setUserToken,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = (): AppContextProps => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};