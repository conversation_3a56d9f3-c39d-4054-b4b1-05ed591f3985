import React from 'react';
import ReactMarkdown from 'react-markdown';

interface AnalysisResult {
  analysis: string;
  confidence: number;
  dataQuality: {
    quality: string;
    issues: string[];
  };
  timestamp: string;
}

interface EnhancedAnalysisResultsProps {
  analysisResult?: AnalysisResult;
  loading: boolean;
  error?: string;
  onRetry?: () => void;
}

// Enhanced results display that shows confidence and data quality
const EnhancedAnalysisResults: React.FC<EnhancedAnalysisResultsProps> = ({ 
  analysisResult, 
  loading, 
  error, 
  onRetry 
}) => {
  if (loading) {
    return (
      <div className="analysis-loading">
        <div className="loading-spinner"></div>
        <p>Generating enhanced market analysis...</p>
        <p className="loading-subtext">Processing Zillow data and comparable properties...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="analysis-error">
        <h3>Analysis Error</h3>
        <p>{error}</p>
        {onRetry && (
          <button onClick={onRetry} className="primary-button">
            Retry Analysis
          </button>
        )}
      </div>
    );
  }
  
  if (!analysisResult) return null;
  
  return (
    <div className="enhanced-analysis-results">
      <div className="analysis-header">
        <h2>Market Analysis Results</h2>
        <div className="analysis-metadata">
          <div className="confidence-score">
            <span className="label">Confidence Score:</span>
            <span className={`score ${getConfidenceClass(analysisResult.confidence)}`}>
              {analysisResult.confidence}%
            </span>
          </div>
          <div className="data-quality">
            <span className="label">Data Quality:</span>
            <span className={`quality ${analysisResult.dataQuality.quality.toLowerCase()}`}>
              {analysisResult.dataQuality.quality}
            </span>
          </div>
        </div>
      </div>
      
      {analysisResult.dataQuality.issues.length > 0 && (
        <div className="data-issues">
          <h4>Data Limitations:</h4>
          <ul>
            {analysisResult.dataQuality.issues.map((issue, index) => (
              <li key={index}>{issue}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="analysis-content">
        <ReactMarkdown>{analysisResult.analysis}</ReactMarkdown>
      </div>
      
      <div className="analysis-footer">
        <small>Generated: {new Date(analysisResult.timestamp).toLocaleString()}</small>
      </div>
    </div>
  );
};

const getConfidenceClass = (score: number) => {
  if (score >= 80) return 'high';
  if (score >= 60) return 'medium';
  return 'low';
};

export default EnhancedAnalysisResults;
