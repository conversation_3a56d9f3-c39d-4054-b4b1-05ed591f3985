import { Link } from 'react-router-dom';
import { FaFileAlt, FaCalendarAlt, FaChartLine, FaClipboardCheck, FaFileContract, FaArrowRight, FaMapMarkerAlt } from 'react-icons/fa';
import { useApp } from '../context/AppContext';

const AgentDashboard = () => {
  const { state } = useApp();

  const tools = [
    {
      id: 'listing',
      title: 'Listing Generator',
      description: 'Create compelling property listings with AI-generated descriptions and marketing tips.',
      icon: <FaFileAlt className="text-3xl" />,
      path: '/agent/tools/listing',
      color: 'from-primary-500 to-primary-600',
    },
    {
      id: 'appointment',
      title: 'Appointment Scheduler',
      description: 'Get AI recommendations for scheduling client appointments and viewing preparations.',
      icon: <FaCalendarAlt className="text-3xl" />,
      path: '/agent/tools/appointment',
      color: 'from-secondary-500 to-secondary-600',
    },
    {
      id: 'analysis',
      title: 'Competitive Analysis',
      description: 'Generate detailed market analysis and competitive property comparisons.',
      icon: <FaChartLine className="text-3xl" />,
      path: '/agent/tools/analysis',
      color: 'from-primary-500 to-secondary-500',
    },
    {
      id: 'inspection',
      title: 'Inspection Report Generator',
      description: 'Analyze inspection findings and generate client-friendly summary reports.',
      icon: <FaClipboardCheck className="text-3xl" />,
      path: '/agent/tools/inspection',
      color: 'from-secondary-500 to-primary-500',
    },
    {
      id: 'lease',
      title: 'Lease Analysis',
      description: 'Review lease agreements and identify potential concerns or negotiation points.',
      icon: <FaFileContract className="text-3xl" />,
      path: '/agent/tools/lease',
      color: 'from-primary-600 to-secondary-600',
    },
    {
      id: 'neighborhood',
      title: 'Neighborhood & Compliance',
      description: 'Generate comprehensive reports on neighborhoods and property compliance requirements.',
      icon: <FaMapMarkerAlt className="text-3xl" />,
      path: '/neighborhood-compliance',
      color: 'from-secondary-600 to-primary-600',
    },
  ];

  return (
    <div className="py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-dark-800 mb-4">Agent AI Tools</h1>
          <p className="text-dark-500 max-w-3xl">
            Leverage the power of AI to streamline your real estate business. Our suite of AI-powered tools helps you save time,
            create professional content, and provide better service to your clients.
          </p>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {tools.map((tool) => (
            <Link
              key={tool.id}
              to={tool.path}
              className="bg-white rounded-xl shadow-card hover:shadow-hover transition-all duration-300 overflow-hidden group"
            >
              <div className="p-6">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${tool.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform`}>
                  {tool.icon}
                </div>
                <h3 className="text-xl font-bold text-dark-800 mb-2 group-hover:text-primary-600 transition-colors">{tool.title}</h3>
                <p className="text-dark-500 mb-4">{tool.description}</p>
                <div className="flex items-center text-primary-600 font-medium group-hover:translate-x-1 transition-transform">
                  <span>Get started</span>
                  <FaArrowRight className="ml-2" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl p-8 text-white">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="col-span-1 md:col-span-1">
              <h2 className="text-2xl font-bold mb-4">Why Use AI Tools?</h2>
              <p className="text-white/90">
                Our AI-powered tools help real estate professionals save time, increase productivity, and deliver exceptional client service.
              </p>
            </div>
            <div className="col-span-1 md:col-span-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Save Time</h3>
                  <p className="text-white/80">
                    Automate routine tasks and generate content in seconds instead of hours.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Increase Consistency</h3>
                  <p className="text-white/80">
                    Maintain professional quality across all client communications and materials.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Enhance Client Service</h3>
                  <p className="text-white/80">
                    Provide detailed information and insights that impress clients and build trust.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Stay Competitive</h3>
                  <p className="text-white/80">
                    Leverage cutting-edge technology to differentiate your services in the market.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDashboard;
