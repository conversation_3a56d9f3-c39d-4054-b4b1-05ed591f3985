{"name": "backend", "version": "1.0.0", "description": "Backend server for Digital Realtor", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.30", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.4.3"}}