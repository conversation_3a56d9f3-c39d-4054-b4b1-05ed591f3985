import { Link } from 'react-router-dom';

const PrivacyPolicy = () => {
  return (
    <div className="container mx-auto px-6 py-12 max-w-4xl">
      <div className="mb-8">
        <Link to="/" className="text-accent-cyan hover:underline flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Home
        </Link>
      </div>

      <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">Privacy Policy</h1>
      
      <div className="space-y-6 text-primary-100">
        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">Introduction</h2>
          <p>
            Digital Realtor ("we", "our", or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and share information about you when you use our website and services.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">Information We Collect</h2>
          <p className="mb-3">We collect information in the following ways:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong className="text-white">Information you provide:</strong> When you register for an account, we collect your name, email address, and other contact information.
            </li>
            <li>
              <strong className="text-white">Usage information:</strong> We collect information about how you use our website, including pages visited, features used, and actions taken.
            </li>
            <li>
              <strong className="text-white">Device information:</strong> We collect information about the device you use to access our website, including device type, operating system, and browser type.
            </li>
            <li>
              <strong className="text-white">Cookies and similar technologies:</strong> We use cookies and similar technologies to collect information about your browsing behavior and preferences.
            </li>
          </ul>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">How We Use Your Information</h2>
          <p className="mb-3">We use the information we collect to:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Provide, maintain, and improve our services</li>
            <li>Process transactions and send related information</li>
            <li>Send you technical notices, updates, security alerts, and support messages</li>
            <li>Respond to your comments, questions, and requests</li>
            <li>Develop new products and services</li>
            <li>Monitor and analyze trends, usage, and activities</li>
            <li>Personalize your experience</li>
            <li>Detect, investigate, and prevent fraudulent transactions and other illegal activities</li>
          </ul>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">Analytics and Tracking</h2>
          <p className="mb-3">
            We use Microsoft Clarity, an analytics service provided by Microsoft Corporation, to help us understand how users interact with our website. Microsoft Clarity collects information such as:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Pages visited and links clicked</li>
            <li>Mouse movements, clicks, and scrolling</li>
            <li>Session recordings (anonymized)</li>
            <li>Heatmaps of user interactions</li>
          </ul>
          <p className="mt-3">
            This information helps us improve our website and provide a better user experience. Microsoft Clarity may use cookies or similar technologies to collect this information. You can learn more about Microsoft Clarity's privacy practices by visiting <a href="https://clarity.microsoft.com/terms" target="_blank" rel="noopener noreferrer" className="text-accent-cyan hover:underline">Microsoft Clarity's Terms of Service</a>.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">Your Choices</h2>
          <p className="mb-3">You have several choices regarding the information we collect and how it is used:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong className="text-white">Account Information:</strong> You can update your account information by logging into your account settings.
            </li>
            <li>
              <strong className="text-white">Cookies:</strong> Most web browsers are set to accept cookies by default. You can usually set your browser to remove or reject cookies, but this may affect certain features of our website.
            </li>
            <li>
              <strong className="text-white">Marketing Communications:</strong> You can opt out of receiving promotional emails from us by following the instructions in those emails.
            </li>
          </ul>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">Contact Us</h2>
          <p>
            If you have any questions about this Privacy Policy, please contact us at <a href="mailto:<EMAIL>" className="text-accent-cyan hover:underline"><EMAIL></a>.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-white mb-3">Changes to This Privacy Policy</h2>
          <p>
            We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.
          </p>
          <p className="mt-3">
            Last Updated: December 1, 2024
          </p>
        </section>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
