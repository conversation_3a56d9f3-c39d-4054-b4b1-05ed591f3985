// Image Analysis Service
// This service handles the analysis of property images using OpenAI or Claude

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL ? `${import.meta.env.VITE_BACKEND_URL}/api` : 'http://localhost:5000/api';

// Define the interface for the image analysis result
export interface ImageAnalysisResult {
  description: string;
  features: string[];
  imageId?: string;
}

// Function to analyze property images
export const analyzePropertyImages = async (
  images: string[],
  provider: 'openai' | 'claude',
  propertyType?: string,
  targetAudience?: string,
  priceRange?: string,
  locationHints?: string
): Promise<ImageAnalysisResult> => {
  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log(`%c[DEV] Sending request to analyze property images with ${provider}`, 'color: #00b9ff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/analyze-property-images`);
      console.log('Number of images:', images.length);
    }

    const response = await fetch(`${API_BASE_URL}/analyze-property-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        images,
        provider,
        propertyType,
        targetAudience,
        priceRange,
        locationHints
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log(`%c[DEV] Received response from ${provider} image analysis`, 'color: #00b9ff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Raw response data:', data);
      console.log('Description length:', data.description?.length || 0);
      console.log('Features:', data.features);

      // Additional debugging for features
      if (data.features) {
        console.log('Features type:', typeof data.features);
        console.log('Is features an array?', Array.isArray(data.features));
        console.log('Features length:', Array.isArray(data.features) ? data.features.length : 'N/A');
      } else {
        console.log('Features is undefined or null');
      }
    }

    // Ensure we have a valid result object
    const result: ImageAnalysisResult = {
      description: data.description || '',
      features: Array.isArray(data.features) ? data.features : []
    };

    return result;
  } catch (error) {
    console.error(`Error analyzing property images with ${provider}:`, error);
    throw error;
  }
};
