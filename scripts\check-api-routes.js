import fetch from 'node-fetch';

// API endpoints to check
const endpoints = [
  { url: 'https://digital-realtor-api.onrender.com/api', method: 'GET', name: 'Health Check' },
  { url: 'https://digital-realtor-api.onrender.com/api/users', method: 'GET', name: 'Users API' },
  { url: 'https://digital-realtor-api.onrender.com/api/properties', method: 'GET', name: 'Properties API' },
  { url: 'https://digital-realtor-api.onrender.com/api/appointments', method: 'GET', name: 'Appointments API' },
  { url: 'https://digital-realtor-api.onrender.com/api/virtual-staging', method: 'GET', name: 'Virtual Staging API' },
  { url: 'https://digital-realtor-api.onrender.com/api/contract-generator', method: 'GET', name: 'Contract Generator API' }
];

// Function to check if an endpoint is available
async function checkEndpoint(endpoint) {
  try {
    console.log(`Checking ${endpoint.name} at ${endpoint.url}...`);
    const response = await fetch(endpoint.url, { method: endpoint.method });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    // Try to parse the response as JSON
    try {
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('Response:', JSON.stringify(data, null, 2));
      } else {
        const text = await response.text();
        console.log('Response:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));
      }
    } catch (error) {
      console.log('Error parsing response:', error.message);
    }
    
    console.log('-----------------------------------');
    
    return {
      name: endpoint.name,
      url: endpoint.url,
      status: response.status,
      success: response.status >= 200 && response.status < 400
    };
  } catch (error) {
    console.log(`Error checking ${endpoint.name}:`, error.message);
    console.log('-----------------------------------');
    
    return {
      name: endpoint.name,
      url: endpoint.url,
      status: 'Error',
      success: false,
      error: error.message
    };
  }
}

// Check all endpoints
async function checkAllEndpoints() {
  console.log('Checking API endpoints...');
  console.log('===================================');
  
  const results = await Promise.all(endpoints.map(checkEndpoint));
  
  console.log('===================================');
  console.log('Summary:');
  
  results.forEach(result => {
    console.log(`${result.name}: ${result.success ? '✅ Available' : '❌ Not Available'} (${result.status})`);
  });
  
  const availableCount = results.filter(result => result.success).length;
  console.log(`===================================`);
  console.log(`${availableCount}/${endpoints.length} endpoints available`);
}

// Run the checks
checkAllEndpoints();
