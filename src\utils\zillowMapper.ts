/**
 * Utility functions for mapping Zillow API response data to form fields
 */

/**
 * Maps Zillow API response data to form fields
 * @param zillowData - The data returned from the Zillow API
 * @returns An object with mapped form fields
 */
export function mapZillowToFormFields(zillowData: any) {
  if (!zillowData || typeof zillowData !== 'object') {
    return {};
  }

  // Extract resoFacts for nested properties
  const reso = zillowData.resoFacts || {};

  // Log the structure for debugging
  console.log('Mapping Zillow data:', {
    topLevelKeys: Object.keys(zillowData),
    hasResoFacts: !!zillowData.resoFacts,
    resoFactsKeys: Object.keys(reso),
    address: zillowData.address,
    streetAddress: zillowData.streetAddress,
    bedrooms: zillowData.bedrooms,
    resoBedrooms: reso.bedrooms
  });

  return {
    // Address information
    address: zillowData.address?.streetAddress || zillowData.streetAddress || '',
    city: zillowData.address?.city || zillowData.city || '',
    state: zillowData.address?.state || zillowData.state || '',
    zip: zillowData.address?.zipcode || zillowData.zipcode || '',
    
    // Basic property details
    bedrooms: zillowData.bedrooms || reso.bedrooms || '',
    bathrooms: zillowData.bathrooms || reso.bathrooms || '',
    squareFeet: zillowData.livingArea || reso.livingArea || '',
    lotSize: reso.lotSize || zillowData.lotSize || '',
    yearBuilt: zillowData.yearBuilt || reso.yearBuilt || '',
    propertyType: zillowData.homeType || zillowData.propertyTypeDimension || reso.propertyType || '',
    
    // Additional features
    parking: Array.isArray(reso.parkingFeatures) 
      ? reso.parkingFeatures.join(', ') 
      : (reso.parkingFeatures || zillowData.parking || ''),
    appliances: Array.isArray(reso.appliances) 
      ? reso.appliances 
      : (Array.isArray(zillowData.appliances) ? zillowData.appliances : []),
    
    // Financial information
    hoaFees: reso.associationFee || zillowData.monthlyHoaFee || '',
    price: zillowData.price || '',
    pricePerSqFt: zillowData.pricePerSquareFoot || zillowData.pricePerSqFt || '',
    
    // Additional data
    mlsId: zillowData.mlsid || zillowData.mlsId || '',
    subdivision: reso.subdivisionName || '',
    taxHistory: zillowData.taxHistory || {},
    priceHistory: zillowData.priceHistory || [],
    
    // Images
    images: [zillowData.imgSrc, ...(zillowData.imageGallery || [])].filter(Boolean),
    
    // Additional details for AI context
    description: zillowData.description || '',
    features: Array.isArray(reso.features) 
      ? reso.features.join(', ') 
      : (reso.features || zillowData.features || '')
  };
}

/**
 * Creates a context object for AI prompts from Zillow data
 * @param zillowData - The mapped Zillow data
 * @returns An object suitable for AI context
 */
export function createAiContext(zillowData: any) {
  const mappedData = typeof zillowData.bedrooms !== 'undefined' 
    ? zillowData // Already mapped
    : mapZillowToFormFields(zillowData);
  
  // Create a simplified context object for AI
  return {
    address: mappedData.address,
    city: mappedData.city,
    state: mappedData.state,
    zip: mappedData.zip,
    bedrooms: mappedData.bedrooms,
    bathrooms: mappedData.bathrooms,
    sqft: mappedData.squareFeet,
    lotSize: mappedData.lotSize,
    yearBuilt: mappedData.yearBuilt,
    propertyType: mappedData.propertyType,
    parking: mappedData.parking,
    appliances: mappedData.appliances,
    hoaFees: mappedData.hoaFees,
    price: mappedData.price,
    pricePerSqFt: mappedData.pricePerSqFt,
    features: mappedData.features,
    description: mappedData.description
  };
}
