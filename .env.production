PORT=5000
MONGO_URI=mongodb+srv://davidfeld78:<EMAIL>/digitalrealtordb?retryWrites=true&w=majority
JWT_SECRET=digital_realtor_secure_jwt_secret_key_2024
NODE_ENV=production

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=Digital Realtor <<EMAIL>>

# Frontend Environment Variables for Netlify
VITE_API_URL=https://digital-realtor-api.onrender.com/api
VITE_BACKEND_URL=https://digital-realtor-api.onrender.com

# OpenAI API Key
VITE_OPENAI_API_KEY=********************************************************************************************************************************************************************

# Claude API Key
VITE_CLAUDE_API_KEY=************************************************************************************************************

# Microsoft Clarity ID
VITE_CLARITY_ID=r7yjq54tfv

# Build configuration
VITE_BUILD_MODE=production
VITE_BUILD_TIME=${new Date().toISOString()}

# Contact form endpoint
VITE_CONTACT_FORM_ENDPOINT=https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com/prod/send-email
VITE_CONTACT_EMAIL=<EMAIL>

# Google API Keys
# Note: You need to replace this with your actual Google Maps API key
VITE_GOOGLE_API_KEY=
VITE_GOOGLE_CLIENT_ID=

# Neighborhood Data API Keys
VITE_WALKSCORE_API_KEY=********************************
VITE_RAPIDAPI_SCHOOLDIGGER_KEY=**************************************************
