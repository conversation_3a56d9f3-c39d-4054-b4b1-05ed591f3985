/**
 * Toast Utility Functions
 *
 * This file provides utility functions for displaying toast notifications
 * using react-toastify. It centralizes toast configuration and provides
 * a consistent interface for showing different types of toasts.
 */

import { toast, ToastOptions, TypeOptions } from 'react-toastify';
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationTriangle, FaInfoCircle, FaTimes, FaSpinner, FaRobot } from 'react-icons/fa';

// Default toast configuration
const defaultOptions: ToastOptions = {
  position: 'top-right',
  autoClose: 4000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  theme: 'dark',
};

/**
 * Show a toast notification
 *
 * @param type - The type of toast (success, error, info, warning)
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showToast = (
  type: TypeOptions,
  message: string,
  options: ToastOptions = {}
) => {
  const mergedOptions = { ...defaultOptions, ...options };

  switch (type) {
    case 'success':
      toast.success(message, mergedOptions);
      break;
    case 'error':
      toast.error(message, mergedOptions);
      break;
    case 'info':
      toast.info(message, mergedOptions);
      break;
    case 'warning':
      toast.warning(message, mergedOptions);
      break;
    default:
      toast(message, mergedOptions);
  }
};

/**
 * Show a success toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showSuccess = (message: string, options: ToastOptions = {}) => {
  showToast('success', message, options);
};

/**
 * Show an error toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showError = (message: string, options: ToastOptions = {}) => {
  showToast('error', message, options);
};

/**
 * Show an info toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showInfo = (message: string, options: ToastOptions = {}) => {
  showToast('info', message, options);
};

/**
 * Show a warning toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showWarning = (message: string, options: ToastOptions = {}) => {
  showToast('warning', message, options);
};

/**
 * Custom Toast Components for consistent styling
 */

interface CustomToastProps {
  title: string;
  message?: string;
  icon?: React.ReactNode;
}

const CustomToastContent: React.FC<CustomToastProps & { type: 'success' | 'error' | 'info' | 'warning' }> = ({
  title,
  message,
  icon,
  type
}) => (
  <div className="custom-toast-content">
    <div className={`custom-toast-icon custom-toast-icon--${type}`}>
      {icon}
    </div>
    <div className="custom-toast-text">
      <div className="custom-toast-title">{title}</div>
      {message && <div className="custom-toast-message">{message}</div>}
    </div>
  </div>
);

/**
 * Show a custom success toast with consistent styling
 */
export const showAppointmentSuccess = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = (
    <CustomToastContent
      type="success"
      title={title}
      message={message}
      icon={<FaCheck className="text-green-400" />}
    />
  );

  toast.success(content, {
    ...defaultOptions,
    ...options,
    className: 'custom-toast custom-toast--success',
  });
};

/**
 * Show a custom processing toast with AI provider indicator
 */
export const showProcessingToast = (provider: 'openai' | 'claude', message?: string, options: ToastOptions = {}) => {
  const providerName = provider === 'openai' ? 'OpenAI' : 'Claude 4.0';
  const providerColor = provider === 'openai' ? '#10a37f' : '#a27aff';

  const content = (
    <CustomToastContent
      type="info"
      title={`Processing with ${providerName}...`}
      message={message}
      icon={<FaSpinner className="animate-spin" style={{ color: providerColor }} />}
    />
  );

  return toast.info(content, {
    ...defaultOptions,
    ...options,
    autoClose: false, // Don't auto-close processing toasts
    className: 'custom-toast custom-toast--info',
  });
};

/**
 * Show a custom error toast
 */
export const showAppointmentError = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = (
    <CustomToastContent
      type="error"
      title={title}
      message={message}
      icon={<FaTimes className="text-red-400" />}
    />
  );

  toast.error(content, {
    ...defaultOptions,
    ...options,
    className: 'custom-toast custom-toast--error',
  });
};

/**
 * Show a custom info toast
 */
export const showAppointmentInfo = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = (
    <CustomToastContent
      type="info"
      title={title}
      message={message}
      icon={<FaInfoCircle className="text-blue-400" />}
    />
  );

  toast.info(content, {
    ...defaultOptions,
    ...options,
    className: 'custom-toast custom-toast--info',
  });
};

/**
 * Show a custom warning toast
 */
export const showAppointmentWarning = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = (
    <CustomToastContent
      type="warning"
      title={title}
      message={message}
      icon={<FaExclamationTriangle className="text-yellow-400" />}
    />
  );

  toast.warning(content, {
    ...defaultOptions,
    ...options,
    className: 'custom-toast custom-toast--warning',
  });
};

// Export the toast object for direct access if needed
export { toast };
