/**
 * Toast Utility Functions
 *
 * This file provides utility functions for displaying toast notifications
 * using react-toastify. It centralizes toast configuration and provides
 * a consistent interface for showing different types of toasts.
 */

import { toast, ToastOptions, TypeOptions } from 'react-toastify';

// Default toast configuration
const defaultOptions: ToastOptions = {
  position: 'top-right',
  autoClose: 4000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  theme: 'dark',
};

/**
 * Show a toast notification
 *
 * @param type - The type of toast (success, error, info, warning)
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showToast = (
  type: TypeOptions,
  message: string,
  options: ToastOptions = {}
) => {
  const mergedOptions = { ...defaultOptions, ...options };

  switch (type) {
    case 'success':
      toast.success(message, mergedOptions);
      break;
    case 'error':
      toast.error(message, mergedOptions);
      break;
    case 'info':
      toast.info(message, mergedOptions);
      break;
    case 'warning':
      toast.warning(message, mergedOptions);
      break;
    default:
      toast(message, mergedOptions);
  }
};

/**
 * Show a success toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showSuccess = (message: string, options: ToastOptions = {}) => {
  showToast('success', message, options);
};

/**
 * Show an error toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showError = (message: string, options: ToastOptions = {}) => {
  showToast('error', message, options);
};

/**
 * Show an info toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showInfo = (message: string, options: ToastOptions = {}) => {
  showToast('info', message, options);
};

/**
 * Show a warning toast
 *
 * @param message - The message to display
 * @param options - Optional configuration options
 */
export const showWarning = (message: string, options: ToastOptions = {}) => {
  showToast('warning', message, options);
};

/**
 * Show a custom success toast with consistent styling
 */
export const showAppointmentSuccess = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = message ? `${title}\n${message}` : title;

  toast.success(content, {
    ...defaultOptions,
    ...options,
  });
};

/**
 * Show a custom processing toast with AI provider indicator
 */
export const showProcessingToast = (provider: 'openai' | 'claude', message?: string, options: ToastOptions = {}) => {
  const providerName = provider === 'openai' ? 'OpenAI' : 'Claude 4.0';
  const content = message ? `Processing with ${providerName}...\n${message}` : `Processing with ${providerName}...`;

  return toast.info(content, {
    ...defaultOptions,
    ...options,
    autoClose: false, // Don't auto-close processing toasts
  });
};

/**
 * Show a custom error toast
 */
export const showAppointmentError = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = message ? `${title}\n${message}` : title;

  toast.error(content, {
    ...defaultOptions,
    ...options,
  });
};

/**
 * Show a custom info toast
 */
export const showAppointmentInfo = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = message ? `${title}\n${message}` : title;

  toast.info(content, {
    ...defaultOptions,
    ...options,
  });
};

/**
 * Show a custom warning toast
 */
export const showAppointmentWarning = (title: string, message?: string, options: ToastOptions = {}) => {
  const content = message ? `${title}\n${message}` : title;

  toast.warning(content, {
    ...defaultOptions,
    ...options,
  });
};

// Export the toast object for direct access if needed
export { toast };
