<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/jpeg" href="/AiDA-Logo.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- CSP will be dynamically added by csp-config.js -->
    <title>Digital Realtor | AI-Powered Real Estate</title>
    <!-- Dynamic CSP configuration -->
    <script src="/csp-config.js"></script>
    <!-- Environment variables configuration -->
    <script src="/env-config.js"></script>
    <!-- Google API script -->
    <script src="/google-api.js"></script>
    <!-- Test appointment script (for development only) -->
    <script src="/test-appointment.js"></script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
