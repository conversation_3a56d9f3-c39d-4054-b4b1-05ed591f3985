<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/jpeg" href="/AiDA-Logo.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Static CSP for development -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.clarity.ms https://www.googletagmanager.com https://maps.googleapis.com https://apis.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https://www.clarity.ms https://c.clarity.ms https://*.clarity.ms https://c.bing.com https://maps.googleapis.com https://*.blob.core.windows.net https://photos.zillowstatic.com https://*.zillowstatic.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://api.openai.com https://api.anthropic.com https://maps.googleapis.com https://apis.google.com https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com https://digital-realtor-api.onrender.com http://localhost:3001 http://localhost:5000 http://localhost:5173 http://localhost:5174 http://localhost:5175 http://localhost:5176 http://localhost:5177 http://localhost:5178 http://localhost:5179 http://localhost:5180 ws://localhost:5173 ws://localhost:5174 ws://localhost:5175 ws://localhost:5176 ws://localhost:5177 ws://localhost:5178 ws://localhost:5179 ws://localhost:5180 wss://localhost:5173 wss://localhost:5174 wss://localhost:5175 wss://localhost:5176 wss://localhost:5177 wss://localhost:5178 wss://localhost:5179 wss://localhost:5180 https://l.clarity.ms https://*.clarity.ms https://schooldigger-k-12-school-data-api.p.rapidapi.com https://api.walkscore.com https://photos.zillowstatic.com https://*.zillowstatic.com; frame-src 'self';" />
    <title>Digital Realtor | AI-Powered Real Estate</title>
    <!-- Dynamic CSP configuration -->
    <script src="/csp-config.js"></script>
    <!-- Environment variables configuration -->
    <script src="/env-config.js"></script>
    <!-- Google API script -->
    <script src="/google-api.js"></script>
    <!-- Test appointment script (for development only) -->
    <script src="/test-appointment.js"></script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
