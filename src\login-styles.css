/* Login Page Specific Styles */

/* Input field icon styles */
.relative svg {
  display: inline-block;
  vertical-align: middle;
}

/* Ensure proper icon sizing */
.h-4.w-4 {
  height: 1rem;
  width: 1rem;
}

.h-8.w-8 {
  height: 2rem;
  width: 2rem;
}

.bg-dark-900 {
  background-color: var(--dark-900);
}

.bg-dark-800 {
  background-color: var(--dark-800);
}

.bg-dark-700 {
  background-color: var(--dark-700);
}

.border-primary-800\/30 {
  border-color: rgba(0, 82, 204, 0.3);
}

.border-primary-700 {
  border-color: var(--primary-700);
}

.text-primary-100 {
  color: var(--primary-100);
}

.text-primary-200 {
  color: var(--primary-200);
}

.text-primary-300 {
  color: var(--primary-300);
}

.text-primary-400 {
  color: var(--primary-400);
}

.text-accent-cyan {
  color: var(--accent-cyan);
}

.bg-error\/20 {
  background-color: rgba(255, 51, 102, 0.2);
}

.text-error {
  color: var(--error);
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

.space-y-10 > * + * {
  margin-top: 2.5rem;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.futuristic-gradient-animated {
  background: linear-gradient(135deg,
    var(--primary-600) 0%,
    var(--secondary-600) 50%,
    var(--primary-600) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
