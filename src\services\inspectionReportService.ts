import axios, { AxiosError } from 'axios';

// Define the OpenAI API base URL
const OPENAI_API_URL = 'https://api.openai.com/v1';

// Define the interface for the inspection report data
export interface InspectionReportData {
  propertyAddress: string;
  inspectionDate: string;
  inspectorName: string;
  clientName: string;
  propertyType: string;
  yearBuilt: string;
  squareFootage: string;
  generalNotes: string;
  images?: string[]; // Base64 encoded images
  imageAnalysis?: string; // Analysis of uploaded images
}

// Define the interface for the OpenAI API request
interface OpenAIRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string | Array<{
      type: 'text' | 'image_url';
      text?: string;
      image_url?: {
        url: string;
      };
    }>;
  }>;
  temperature?: number;
  max_tokens?: number;
}

// Define the interface for the OpenAI API response
interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Simple in-memory cache for reports
interface CacheEntry {
  timestamp: number;
  report: string;
}

const reportCache = new Map<string, CacheEntry>();
const CACHE_TTL = 1000 * 60 * 60; // 1 hour in milliseconds

// Rate limiting variables
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 3000; // 3 seconds between requests

// Helper function to create a cache key from inspection data
const createCacheKey = (inspectionData: InspectionReportData): string => {
  return JSON.stringify(inspectionData);
};

// Helper function to implement delay
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Function to analyze images using OpenAI's vision capabilities
export const analyzeImages = async (
  apiKey: string,
  images: string[]
): Promise<string> => {
  if (!images || images.length === 0) {
    return '';
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  // Update last request time
  lastRequestTime = Date.now();

  // Retry logic with exponential backoff
  let retries = 0;
  const MAX_RETRIES = 1; // Only retry once

  while (retries <= MAX_RETRIES) {
    try {
      // Create the system message with instructions for the AI
      const systemMessage = `You are a professional property inspector analyzing images of a property.
      Identify any visible issues, defects, or concerns in the provided images that would be relevant for a property inspection report.
      Focus on structural issues, water damage, electrical problems, HVAC issues, plumbing problems, safety hazards, and other significant defects.

      For each issue identified, provide the following information in a structured format:
      1. Item/Area: The specific item or area visible in the photo (e.g., "Bathroom Ceiling", "Kitchen Sink", "Basement Wall")
      2. Issue: A clear description of the problem
      3. Analysis: The potential implications or risks, including severity
      4. Recommendation: Specific actions to address the issue

      Format your response as a structured list where each issue is clearly separated and includes all four elements above. This will be used to create a table in the final report.`;

      // Prepare the messages array with the system message and user content
      const messages = [
        { role: 'system' as const, content: systemMessage },
        {
          role: 'user' as const,
          content: [
            { type: 'text' as const, text: 'Please analyze these property images and identify any issues or defects. For each issue, provide the Item/Area, Issue description, Analysis, and Recommendation in a structured format that can be easily converted to a table:' },
            ...images.map(image => ({
              type: 'image_url' as const,
              image_url: {
                url: image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}`
              }
            }))
          ]
        }
      ];

      // Make the API request
      const response = await axios.post<OpenAIResponse>(
        `${OPENAI_API_URL}/chat/completions`,
        {
          model: 'gpt-4o',
          messages,
          temperature: 0.7,
          max_tokens: 1000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      // Extract the analysis
      const analysis = response.data.choices[0].message.content;
      return analysis;

    } catch (error) {
      const axiosError = error as AxiosError;

      // Check if it's a rate limit error (429)
      if (axiosError.response?.status === 429) {
        console.log(`Rate limit error (429) encountered. Will try one more time.`);

        // Get retry-after header if available
        const retryAfter = axiosError.response.headers['retry-after'];

        if (retryAfter && !isNaN(parseInt(retryAfter as string))) {
          // If server specifies retry-after, use that value (in seconds)
          const waitTime = parseInt(retryAfter as string) * 1000;
          console.log(`Server requested retry after ${waitTime/1000} seconds`);
          await delay(waitTime);
        } else {
          // Use a fixed 10-second delay for the single retry
          console.log('Using a 10-second delay before retrying');
          await delay(10000);
        }

        if (retries < MAX_RETRIES) {
          retries++;
          continue; // Retry the request once
        } else {
          console.error('Single retry attempt failed');
          throw new Error('OpenAI API rate limit reached. Please try again later.');
        }
      }

      // For other errors or if max retries reached
      console.error('Error analyzing images:', error);
      throw new Error('Failed to analyze images. Please try again.');
    }
  }

  // This should never be reached due to the throw in the catch block
  throw new Error('Failed to analyze images after multiple attempts.');
};

// Create a function to generate an inspection report using OpenAI
export const generateInspectionReport = async (
  apiKey: string,
  inspectionData: InspectionReportData
): Promise<string> => {
  // Check cache first
  const cacheKey = createCacheKey(inspectionData);
  const cachedEntry = reportCache.get(cacheKey);

  if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
    console.log('Using cached inspection report');
    return cachedEntry.report;
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  // Update last request time
  lastRequestTime = Date.now();

  // Retry logic with exponential backoff
  let retries = 0;
  const MAX_RETRIES = 1; // Only retry once

  while (retries <= MAX_RETRIES) {
    try {
      // Create the system message with instructions for the AI
      const systemMessage = `You are a professional property inspector. Create a detailed inspection report based on the provided information.
      The report should include:

      1. A professional header with property details
      2. A summary of findings
      3. Detailed analysis of issues found - THIS MUST BE FORMATTED AS A MARKDOWN TABLE with the following columns:
         - Item: The specific item or area with the issue
         - Issue: A clear description of the problem
         - Analysis: Your professional analysis of the issue, its severity, and potential implications
         - Recommendation: Specific actions to address the issue
      4. If image analysis is provided, include a section titled "Issues Identified from Photos" with the findings
         - THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns as above:
         - Item: The specific item or area visible in the photo
         - Issue: A clear description of the problem identified in the photo
         - Analysis: Your professional analysis of the issue, its severity, and potential implications
         - Recommendation: Specific actions to address the issue
      5. A conclusion

      Format the report using Markdown with appropriate headers, bullet points, and sections. BOTH the detailed analysis section AND the issues identified from photos section MUST be formatted as markdown tables with the columns specified above. Be specific, professional, and thorough.`;

      // Create the user message with the inspection details
      const userMessage = `Property Inspection Details:
      - Property Address: ${inspectionData.propertyAddress}
      - Inspection Date: ${new Date(inspectionData.inspectionDate).toLocaleDateString()}
      - Inspector: ${inspectionData.inspectorName}
      - Client: ${inspectionData.clientName}
      - Property Type: ${inspectionData.propertyType || 'Not specified'}
      - Year Built: ${inspectionData.yearBuilt || 'Not specified'}
      - Square Footage: ${inspectionData.squareFootage || 'Not specified'}

      General Notes and Issues Found:
      ${inspectionData.generalNotes}

      ${inspectionData.imageAnalysis ? `
      Image Analysis Results:
      ${inspectionData.imageAnalysis}
      ` : ''}

      Please analyze these notes${inspectionData.imageAnalysis ? ' and image analysis results' : ''}, identify all issues mentioned, and provide professional recommendations for addressing each issue. Format the report professionally with clear sections.

      IMPORTANT: The "Detailed Analysis of Issues" section MUST be formatted as a markdown table with these exact columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.
      ${inspectionData.imageAnalysis ? 'Include a separate section titled "Issues Identified from Photos" that summarizes the findings from the image analysis. THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.' : ''}`;

      // Create the request payload
      const requestData: OpenAIRequest = {
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage }
        ],
        temperature: 0.7,
        max_tokens: 1500 // Increased token limit for detailed reports
      };

      // Make the API request
      const response = await axios.post<OpenAIResponse>(
        `${OPENAI_API_URL}/chat/completions`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      // Extract the generated report
      const report = response.data.choices[0].message.content;

      // Cache the result
      reportCache.set(cacheKey, {
        timestamp: Date.now(),
        report
      });

      // Return the generated report
      return report;

    } catch (error) {
      const axiosError = error as AxiosError;

      // Check if it's a rate limit error (429)
      if (axiosError.response?.status === 429) {
        console.log(`Rate limit error (429) encountered. Will try one more time.`);

        // Get retry-after header if available
        const retryAfter = axiosError.response.headers['retry-after'];

        if (retryAfter && !isNaN(parseInt(retryAfter as string))) {
          // If server specifies retry-after, use that value (in seconds)
          const waitTime = parseInt(retryAfter as string) * 1000;
          console.log(`Server requested retry after ${waitTime/1000} seconds`);
          await delay(waitTime);
        } else {
          // Use a fixed 10-second delay for the single retry
          console.log('Using a 10-second delay before retrying');
          await delay(10000);
        }

        if (retries < MAX_RETRIES) {
          retries++;
          continue; // Retry the request once
        } else {
          console.error('Single retry attempt failed');
          throw new Error('OpenAI API rate limit reached. Please try again later.');
        }
      }

      // For other errors or if max retries reached
      console.error('Error generating inspection report:', error);
      throw new Error('Failed to generate inspection report. Please try again.');
    }
  }

  // This should never be reached due to the throw in the catch block
  throw new Error('Failed to generate inspection report after multiple attempts.');
};
