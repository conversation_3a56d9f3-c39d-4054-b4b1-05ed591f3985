import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the contract template PDF
const pdfPath = path.join(__dirname, '..', 'contract-template.pdf.pdf');

// Check if the PDF exists
if (fs.existsSync(pdfPath)) {
  console.log(`PDF exists at: ${pdfPath}`);

  // Get file stats
  const stats = fs.statSync(pdfPath);
  console.log(`File size: ${stats.size} bytes`);
  console.log(`Last modified: ${stats.mtime}`);
} else {
  console.error(`PDF not found at: ${pdfPath}`);

  // List files in the directory
  const dirPath = path.join(__dirname, '..');
  console.log(`Files in ${dirPath}:`);
  const files = fs.readdirSync(dirPath);
  files.forEach(file => {
    console.log(`- ${file}`);
  });
}
