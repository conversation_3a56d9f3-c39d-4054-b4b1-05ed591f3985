/* Custom Calendar Styles */
.custom-calendar {
  width: 100%;
  max-width: 350px;
  background-color: #1a1a2e !important;
  border: none !important;
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-family: inherit;
}

.custom-calendar .react-calendar__navigation {
  margin-bottom: 0.5rem;
}

.custom-calendar .react-calendar__navigation button {
  color: #e2e8f0;
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.custom-calendar .react-calendar__navigation button:hover,
.custom-calendar .react-calendar__navigation button:focus {
  background-color: rgba(99, 102, 241, 0.2);
}

.custom-calendar .react-calendar__navigation button:disabled {
  opacity: 0.5;
}

.custom-calendar .react-calendar__month-view__weekdays {
  text-align: center;
  text-transform: uppercase;
  font-size: 0.75rem;
  font-weight: bold;
  color: #9ca3af;
}

.custom-calendar .react-calendar__month-view__weekdays__weekday {
  padding: 0.5rem;
}

.custom-calendar .react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
}

.custom-calendar .react-calendar__tile {
  background: none;
  color: #e2e8f0;
  border: none;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.custom-calendar .react-calendar__tile:hover,
.custom-calendar .react-calendar__tile:focus {
  background-color: rgba(99, 102, 241, 0.2);
}

.custom-calendar .react-calendar__tile--active {
  background-color: #6366f1 !important;
  color: white;
}

.custom-calendar .react-calendar__tile--now {
  background-color: rgba(99, 102, 241, 0.1);
  font-weight: bold;
}

.custom-calendar .react-calendar__tile:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.custom-calendar .react-calendar__month-view__days__day--neighboringMonth {
  color: #6b7280;
}

/* Print styles for appointment recommendations */
@media print {
  body {
    background-color: white !important;
    color: black !important;
  }
  
  .container {
    max-width: 100% !important;
    padding: 0 !important;
  }
  
  .glass-card {
    background-color: white !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: none !important;
    padding: 1rem !important;
  }
  
  h1, h2, h3, h4 {
    color: black !important;
  }
  
  p, li, span {
    color: #1f2937 !important;
  }
  
  button, .form-group, nav, header, footer {
    display: none !important;
  }
  
  .bg-dark-700\/50, .bg-primary-800\/30, .bg-secondary-500\/20 {
    background-color: white !important;
    border: 1px solid #e2e8f0 !important;
  }
}
