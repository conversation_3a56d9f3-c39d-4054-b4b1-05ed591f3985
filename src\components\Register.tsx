import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import userService from '../services/userService';
import EmailVerification from './EmailVerification';

const Register = () => {
  const navigate = useNavigate();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [isDevelopment, setIsDevelopment] = useState(false);
  const [isClearingUsers, setIsClearingUsers] = useState(false);

  const validateForm = () => {
    if (!name || !email || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return false;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Request verification code instead of registering immediately
      const response = await userService.requestVerificationCode(email);

      // For development, show a message with the verification code
      if (process.env.NODE_ENV !== 'production' && response.verificationCode) {
        setError(`For development: Your verification code is ${response.verificationCode}`);
      }

      // Show verification form
      setIsVerifying(true);
    } catch (err: any) {
      console.error('Verification request error:', err);
      setError(err.response?.data?.message || 'Failed to send verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationComplete = async (code: string) => {
    setVerificationCode(code);
    setIsLoading(true);

    try {
      // Register the user with the verification code
      await userService.register({
        name,
        email,
        password,
        verificationCode: code
      });

      // Show success message
      setVerificationSent(true);
      setIsVerifying(false);

      // Clear form
      setName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setVerificationCode('');
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.response?.data?.message || 'Registration failed. Please try again.');
      setIsVerifying(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelVerification = () => {
    setIsVerifying(false);
  };

  // Check if we're in development mode
  useEffect(() => {
    // In a real app, this would check process.env.NODE_ENV
    // For this demo, we'll check the URL for a development indicator
    const hostname = window.location.hostname;
    setIsDevelopment(hostname === 'localhost' || hostname === '127.0.0.1');
  }, []);

  // Handle clearing all users
  const handleClearAllUsers = async () => {
    if (!window.confirm('Are you sure you want to clear all user accounts? This will allow previously used email addresses to be registered again. This action cannot be undone.')) {
      return;
    }

    setIsClearingUsers(true);
    setError('');

    try {
      // Use fetch directly for better debugging
      const url = 'http://localhost:5000/api/users/clear-all-test';
      console.log(`Connecting to: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      const data = await response.json();

      // Format the cleared emails for display
      const emailsList = data.emailsCleared && data.emailsCleared.length > 0
        ? data.emailsCleared.join('\n- ')
        : 'No email addresses were cleared';

      // Show a more detailed message
      alert(`${data.message}\n\nThe following email addresses can now be used again:\n- ${emailsList}`);
    } catch (err: any) {
      console.error('Error clearing users:', err);
      alert(`Error: ${err.message}\n\nPlease check the console for more details.`);
      setError('Failed to clear users. Please check the console for details.');
    } finally {
      setIsClearingUsers(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Link to="/" className="inline-block">
            <div className="flex items-center justify-center">
              <svg className="w-8 h-8 text-accent-cyan" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
                  stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span className="ml-2 text-2xl font-bold text-accent-cyan">Digital Realtor</span>
            </div>
          </Link>
          <h2 className="mt-6 text-3xl font-extrabold text-white">Create your account</h2>
          <p className="mt-2 text-sm text-primary-200">
            Already have an account?{' '}
            <Link to="/login" className="font-medium text-accent-cyan hover:text-accent-cyan/80">
              Sign in
            </Link>
          </p>
        </div>

        {isVerifying ? (
          <EmailVerification
            email={email}
            onVerified={handleVerificationComplete}
            onCancel={handleCancelVerification}
          />
        ) : verificationSent ? (
          <div className="glass-card p-8 rounded-xl shadow-lg border-glow">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-br from-green-400 to-green-600 mb-4">
                <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-600">Account Created Successfully!</span>
              </h3>
              <p className="text-primary-200 mb-6">
                Your account has been created and verified successfully. You can now log in with your credentials.
              </p>
              <p className="text-primary-300 text-sm mb-6">
                Please use your email and password to log in to your new account.
              </p>
              <div className="flex flex-col space-y-4">
                <Link
                  to="/login"
                  style={{
                    background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                    color: 'white',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                >
                  <span className="inline-flex items-center justify-center whitespace-nowrap">
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Go to Login
                  </span>
                </Link>
                <button
                  onClick={() => setVerificationSent(false)}
                  style={{
                    background: 'white',
                    color: '#00b9ff',
                    padding: '0.75rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                >
                  <span className="inline-flex items-center justify-center whitespace-nowrap">
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Register a New Account
                  </span>
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-dark-800 p-6 md:p-12 rounded-xl shadow-lg border border-primary-800/30 flex flex-col items-center">
            {error && (
              <div className="mb-6 p-3 bg-error/20 text-error rounded-lg w-full max-w-sm mx-auto">
                {error}
              </div>
            )}

            <form className="space-y-6 w-full max-w-sm mx-auto" onSubmit={handleSubmit}>
              {/* Name field */}
              <div>
                <label htmlFor="name" className="flex items-center text-sm font-medium text-primary-100 mb-1">
                  <svg className="h-4 w-4 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Full Name
                </label>
                <div className="mt-1 relative rounded-md shadow-sm w-full">

                  <input
                    id="name"
                    name="name"
                    type="text"
                    autoComplete="name"
                    required
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 text-primary-100 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan focus:border-accent-cyan text-base"
                    placeholder="John Doe"
                  />
                </div>
              </div>

              {/* Email field */}
              <div>
                <label htmlFor="email" className="flex items-center text-sm font-medium text-primary-100 mb-1">
                  <svg className="h-4 w-4 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Email Address
                </label>
                <div className="mt-1 relative rounded-md shadow-sm w-full">

                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 text-primary-100 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan focus:border-accent-cyan text-base"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Password field */}
              <div>
                <label htmlFor="password" className="flex items-center text-sm font-medium text-primary-100 mb-1">
                  <svg className="h-4 w-4 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Password
                </label>
                <div className="mt-1 relative rounded-md shadow-sm w-full">

                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 text-primary-100 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan focus:border-accent-cyan text-base"
                    placeholder="••••••••"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-primary-400 hover:text-white focus:outline-none"
                    >
                      {showPassword ? (
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                      ) : (
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
                <p className="mt-1 text-xs text-primary-300">
                  Must be at least 6 characters
                </p>
              </div>

              {/* Confirm Password field */}
              <div>
                <label htmlFor="confirmPassword" className="flex items-center text-sm font-medium text-primary-100 mb-1">
                  <svg className="h-4 w-4 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Confirm Password
                </label>
                <div className="mt-1 relative rounded-md shadow-sm w-full">

                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? "text" : "password"}
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 text-primary-100 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan focus:border-accent-cyan text-base"
                    placeholder="••••••••"
                  />
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="group relative w-full max-w-sm flex justify-center py-3 px-6 border border-transparent rounded-md text-white futuristic-gradient-animated hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-cyan"
                >
                  {isLoading ? (
                    <span className="inline-flex items-center justify-center whitespace-nowrap">
                      <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating account...
                    </span>
                  ) : (
                    <span className="inline-flex items-center justify-center whitespace-nowrap">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                      </svg>
                      Create Account
                    </span>
                  )}
                </button>
              </div>
            </form>

            <div className="mt-6 pt-6 border-t border-primary-800/30">
              <p className="text-xs text-primary-300 text-center">
                By creating an account, you agree to our{' '}
                <a href="#" className="text-accent-cyan hover:text-accent-cyan/80">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="text-accent-cyan hover:text-accent-cyan/80">
                  Privacy Policy
                </a>
              </p>
            </div>
          </div>
        )}

        <div className="text-center space-y-4">
          <Link to="/" className="text-sm text-primary-300 hover:text-white">
            ← Back to Home
          </Link>

          {isDevelopment && (
            <div className="pt-4 border-t border-primary-800/30 mt-4">
              <div className="text-xs text-primary-400 mb-2">
                <span className="px-2 py-0.5 bg-red-500/20 text-red-300 rounded-full text-xs font-mono">DEV MODE</span>
              </div>
              <div className="space-y-2">
                <button
                  onClick={handleClearAllUsers}
                  disabled={isClearingUsers}
                  className="text-xs bg-red-500/20 text-red-300 hover:bg-red-500/30 px-3 py-1.5 rounded-md transition-colors flex items-center justify-center mx-auto"
                >
                {isClearingUsers ? (
                  <span className="inline-flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Clearing...
                  </span>
                ) : (
                  <span className="inline-flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Clear All User Accounts & Emails
                  </span>
                )}
                </button>

                <button
                  onClick={() => alert('The development-only button is visible!')}
                  className="text-xs bg-green-500/20 text-green-300 hover:bg-green-500/30 px-3 py-1.5 rounded-md transition-colors flex items-center justify-center mx-auto"
                >
                  <span className="inline-flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
                    </svg>
                    Test Button Visibility
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Register;
