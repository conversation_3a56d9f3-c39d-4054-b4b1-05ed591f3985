const express = require('express');
const router = express.Router();
const axios = require('axios');

// Route to forward email requests to AWS API Gateway
router.post('/send-email', async (req, res) => {
  try {
    const { name, email, phone, subject, message } = req.body;
    
    // Construct the message content
    const messageContent = `
Name: ${name}
Email: ${email}
${phone ? `Phone: ${phone}` : ''}
Subject: ${subject}

Message:
${message}
    `;
    
    // Prepare the payload for the AWS API
    const payload = {
      toEmail: '<EMAIL>',
      subject: 'New Contact Us Message',
      message: messageContent
    };
    
    // Forward the request to AWS API Gateway
    const response = await axios.post(
      'https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com/prod/send-email',
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    // Return the response from AWS API Gateway
    return res.status(200).json({
      success: true,
      message: 'Email sent successfully',
      data: response.data
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to send email',
      error: error.message
    });
  }
});

module.exports = router;
