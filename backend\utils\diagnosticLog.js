/**
 * Utility function to safely log sensitive information like API keys
 * Only shows first and last few characters of the value
 * 
 * @param {string} label - Description label for the log
 * @param {string} variableName - Name of the variable being logged
 * @param {any} value - The value to log (will be masked if it's a string)
 * @param {boolean} mask - Whether to mask the value (default: true)
 */
function diagnosticLog(label, variableName, value, mask = true) {
  const shortVal = mask && typeof value === 'string' && value.length > 10
    ? `${value.slice(0, 4)}...${value.slice(-4)}`
    : value;
  
  console.log(`[DEBUG] ${label}: ${variableName} = ${shortVal || '⚠️ Not set'}`);
}

module.exports = { diagnosticLog };
