import { Link } from 'react-router-dom';
import { FaHome, FaClipboardCheck, FaMapMarkerAlt, FaFileAlt, FaCalendarAlt, FaCouch } from 'react-icons/fa';
import styles from './ModernHome.module.css';

const ModernHome = () => {
  return (
    <div className="bg-gray-900 text-white min-h-screen">
      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroBackground}>
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900"></div>
          <div className={`absolute inset-0 ${styles.gridPattern}`}></div>
          <div className="absolute top-20 left-1/4 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-1/4 w-72 h-72 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className={styles.heroContent}>
          <h1 className={styles.heroTitle}>
            Find Your Dream Home
            <span className={styles.heroTitleHighlight}>With AI Assistance</span>
          </h1>
          <p className={styles.heroSubtitle}>
            Our advanced AI helps you discover the perfect property tailored to your unique needs and preferences.
          </p>
          {/* CTA Button removed as requested */}
        </div>
      </section>

      {/* Features Section */}
      <section className={styles.featuresSection}>
        <div className="container mx-auto">
          <h2 className={`${styles.sectionTitle} text-center mb-8`}>AI-Powered Tools</h2>
          <div className={styles.featuresGrid}>
            <Link to="/direct-inspection" className={styles.featureCard}>
              <h3 className={styles.featureTitle}>
                <FaClipboardCheck className="inline-block mr-2 text-blue-400" />
                Inspection Report
              </h3>
              <p className={styles.featureDescription}>
                Generate comprehensive inspection reports with AI analysis.
              </p>
            </Link>

            <Link to="/neighborhood-compliance" className={styles.featureCard}>
              <h3 className={styles.featureTitle}>
                <FaMapMarkerAlt className="inline-block mr-2 text-blue-400" />
                Neighborhood & Compliance
              </h3>
              <p className={styles.featureDescription}>
                Access detailed neighborhood data and compliance checks.
              </p>
            </Link>

            <Link to="/listing-generator" className={styles.featureCard}>
              <h3 className={styles.featureTitle}>
                <FaFileAlt className="inline-block mr-2 text-blue-400" />
                AI Listing Generator
              </h3>
              <p className={styles.featureDescription}>
                Create compelling listings with AI-tailored descriptions.
              </p>
            </Link>

            <Link to="/comp-analyzer" className={styles.featureCard}>
              <h3 className={styles.featureTitle}>
                <FaHome className="inline-block mr-2 text-blue-400" />
                Comp Analyzer
              </h3>
              <p className={styles.featureDescription}>
                Analyze market value comparisons based on data.
              </p>
            </Link>

            <Link to="/appointment-scheduler" className={styles.featureCard}>
              <h3 className={styles.featureTitle}>
                <FaCalendarAlt className="inline-block mr-2 text-blue-400" />
                Appointment Scheduler
              </h3>
              <p className={styles.featureDescription}>
                Book tours effortlessly with intelligent scheduling.
              </p>
            </Link>

            <Link to="/virtual-staging" className={styles.featureCard}>
              <h3 className={styles.featureTitle}>
                <FaCouch className="inline-block mr-2 text-blue-400" />
                Virtual Staging
              </h3>
              <p className={styles.featureDescription}>
                Visualize properties with virtual furniture and decor.
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Technology Section */}
      <section className={styles.technologySection}>
        <div className="container mx-auto">
          <h2 className={styles.sectionTitle}>Cutting-Edge Technology</h2>
          <p className={styles.sectionDescription}>
            Our platform combines AI technology with real estate expertise to provide an unparalleled experience.
          </p>
        </div>
      </section>
    </div>
  );
};

export default ModernHome;
