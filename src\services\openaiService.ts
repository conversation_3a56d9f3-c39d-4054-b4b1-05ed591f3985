import axios, { AxiosError } from 'axios';

// Define the OpenAI API base URL
const OPENAI_API_URL = 'https://api.openai.com/v1';

// Define the interface for the OpenAI API request
interface OpenAIRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  max_tokens?: number;
}

// Define the interface for the OpenAI API response
interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Simple in-memory cache for listings and comp analysis
interface CacheEntry {
  timestamp: number;
  data: string | CompAnalysisResult;
}

// Interface for comp analysis result
export interface CompAnalysisResult {
  recommendedPrice: string;
  recommendedListingPrice?: string; // New field for the enhanced prompt
  priceRange?: string;
  confidenceScore?: number;
  confidenceExplanation?: string;
  priceJustification?: string; // New field for the enhanced prompt
  keyCompsUsed?: string[]; // New field for the enhanced prompt
  excludedCompsAndWhy?: string[]; // New field for the enhanced prompt
  comparableProperties: Array<{
    address: string;
    price: string;
    bedrooms?: number;
    bathrooms?: number;
    squareFeet?: number;
    yearBuilt?: number;
    distanceFromTarget?: string;
    soldDate?: string; // New field for the enhanced prompt
    similarityScore?: number;
    adjustments?: string;
    zillowLink?: string;
    source?: string;
    included?: boolean; // New field for the enhanced prompt
    exclusionReason?: string | null; // New field for the enhanced prompt
  }>;
  marketTrends?: {
    averagePricePerSqFt: string;
    medianSalePrice: string;
    averageDaysOnMarket: number;
    priceChangeLastYear: string;
    inventoryLevel?: string;
    marketType?: string;
  };
}

const listingCache = new Map<string, CacheEntry>();
const compAnalysisCache = new Map<string, CacheEntry>();
const CACHE_TTL = 1000 * 60 * 60; // 1 hour in milliseconds

// Rate limiting variables
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 3000; // 3 seconds between requests (increased from 1 second)

// Helper function to create a cache key from property data
const createCacheKey = (propertyData: any): string => {
  return JSON.stringify(propertyData);
};

// Helper function to implement delay
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Helper function for fixed delay retry
const fixedDelayRetry = async (seconds: number): Promise<void> => {
  console.log(`Rate limit hit. Retrying in ${seconds} seconds...`);
  await delay(seconds * 1000);
};

// Helper function to clean JSON response from markdown formatting
const cleanJsonResponse = (text: string): string => {
  // Check if the response is wrapped in markdown code blocks
  const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)```/;
  const match = text.match(jsonBlockRegex);

  if (match && match[1]) {
    // Return the content inside the code block
    return match[1].trim();
  }

  // If no markdown code blocks found, try to find JSON object directly
  const jsonObjectRegex = /\{[\s\S]*\}/;
  const objectMatch = text.match(jsonObjectRegex);

  if (objectMatch) {
    return objectMatch[0];
  }

  // If no JSON object found, return the original text
  return text;
};

// Create a function to generate a property listing using OpenAI
export const generatePropertyListing = async (
  apiKey: string,
  propertyData: {
    propertyType: string;
    bedrooms: string;
    bathrooms: string;
    squareFeet: string;
    location: string;
    price: string;
    yearBuilt?: string;
    features?: string;
    description?: string;
    listingType?: string; // 'sale' or 'rent'
  }
): Promise<string> => {
  // Check cache first
  const cacheKey = createCacheKey(propertyData);
  const cachedEntry = listingCache.get(cacheKey);

  if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
    console.log('Using cached listing');
    return cachedEntry.data as string;
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  // Update last request time
  lastRequestTime = Date.now();

  // Retry logic with exponential backoff
  let retries = 0;
  const MAX_RETRIES = 1; // Changed to only retry once

  while (retries <= MAX_RETRIES) {
    try {
      // Create the system message with instructions for the AI (optimized for token usage)
      const listingType = propertyData.listingType || 'sale';

      const systemMessage = listingType === 'sale'
        ? `As a top-tier real estate marketing expert, create a compelling and enticing property listing that will make potential buyers eager to view the property. Your listing should be emotionally engaging, highlight unique selling points, and create a sense of urgency.

        Create a concise, engaging property listing with these sections:
        1. Title (catchy and appealing)
        2. Overview (compelling narrative that creates desire)
        3. Location (emphasize benefits and lifestyle)
        4. Features (bullets highlighting the most attractive aspects)
        5. Closing (create urgency and call to action)

        Use vivid, descriptive language that helps buyers envision themselves living in the home. Focus on benefits rather than just features. Use Markdown formatting. Emphasize ownership benefits, investment value, and long-term advantages.`

        : `As a top-tier real estate marketing expert, create a compelling and persuasive rental listing that will attract quality tenants. Your listing should be emotionally engaging, highlight lifestyle benefits, and emphasize the property's convenience and comfort.

        Create a concise, engaging rental listing with these sections:
        1. Title (catchy and appealing to renters)
        2. Overview (compelling narrative that creates desire to live there)
        3. Location (emphasize convenience, nearby amenities, and lifestyle)
        4. Features (bullets highlighting the most attractive aspects for tenants)
        5. Closing (mention lease terms if provided and create a call to action)

        Use vivid, descriptive language that helps potential tenants envision themselves living in the space. Focus on livability, convenience, and comfort. Use phrases like "available now," "monthly rent," and "ideal for tenants." Avoid ownership language like "asking price" or "purchase opportunity." Use Markdown formatting.`;

      // Create the user message with the property details (optimized for token usage)
      const priceLabel = listingType === 'sale' ? 'Price' : 'Monthly Rent';

      const userMessage = `Property details:
      - Type: ${propertyData.propertyType}
      - Bed: ${propertyData.bedrooms}
      - Bath: ${propertyData.bathrooms}
      - SqFt: ${propertyData.squareFeet}
      - Location: ${propertyData.location}
      - ${priceLabel}: $${propertyData.price}
      - Listing Type: ${listingType === 'sale' ? 'For Sale' : 'For Rent'}
      ${propertyData.yearBuilt ? `- Year: ${propertyData.yearBuilt}` : ''}
      ${propertyData.features ? `- Features: ${propertyData.features}` : ''}
      ${propertyData.description ? `- Details: ${propertyData.description}` : ''}`;

      // Create the request payload
      const requestData: OpenAIRequest = {
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage }
        ],
        temperature: 0.7,
        max_tokens: 800 // Reduced from 1000 to optimize token usage
      };

      // Make the API request
      const response = await axios.post<OpenAIResponse>(
        `${OPENAI_API_URL}/chat/completions`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      // Extract the generated listing
      const listing = response.data.choices[0].message.content;

      // Cache the result
      listingCache.set(cacheKey, {
        timestamp: Date.now(),
        data: listing
      });

      // Return the generated listing
      return listing;

    } catch (error) {
      const axiosError = error as AxiosError;

      // Check if it's an authentication error (401)
      if (axiosError.response?.status === 401) {
        console.error('Authentication error: Invalid API key');
        throw new Error('Authentication failed. Please check your OpenAI API key.');
      }
      // Check if it's a rate limit error (429)
      else if (axiosError.response?.status === 429) {
        console.log(`Rate limit error (429) encountered. Will try one more time.`);

        // Get retry-after header if available
        const retryAfter = axiosError.response.headers['retry-after'];

        if (retryAfter && !isNaN(parseInt(retryAfter))) {
          // If server specifies retry-after, use that value (in seconds)
          const waitTime = parseInt(retryAfter) * 1000;
          console.log(`Server requested retry after ${waitTime/1000} seconds`);
          await delay(waitTime);
        } else {
          // Use a fixed 10-second delay for the single retry
          console.log('Using a 10-second delay before retrying');
          await delay(10000);
        }

        if (retries < MAX_RETRIES) {
          retries++;
          continue; // Retry the request once
        } else {
          console.error('Single retry attempt failed');
          throw new Error('OpenAI API rate limit reached. Please try again later.');
        }
      }

      // For other errors or if max retries reached
      console.error('Error generating property listing:', error);
      throw new Error('Failed to generate property listing. Please try again.');
    }
  }

  // This should never be reached due to the throw in the catch block
  throw new Error('Failed to generate property listing after multiple attempts.');
};

// Function to analyze comparable properties and recommend a listing price
export const analyzeComps = async (
  apiKey: string,
  address: string,
  propertyDetails?: {
    squareFeet?: number;
    bedrooms?: number;
    bathrooms?: number;
    lotSize?: number;
    yearBuilt?: number;
    condition?: string;
    uniqueFeatures?: string[];
    searchRadius?: number;
    filterMode?: 'strict' | 'soft';
  },
  marketTrends?: {
    averagePricePerSqFt?: string;
    medianSalePrice?: string;
    averageDaysOnMarket?: number;
    priceChangeLastYear?: string;
    inventoryLevel?: string;
    marketType?: string;
  }
): Promise<CompAnalysisResult> => {
  // Check cache first
  const cacheKey = address.trim().toLowerCase();
  const cachedEntry = compAnalysisCache.get(cacheKey);

  if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
    console.log('Using cached comp analysis');
    return cachedEntry.data as CompAnalysisResult;
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  // Update last request time
  lastRequestTime = Date.now();

  // Retry logic with exponential backoff
  let retries = 0;
  const MAX_RETRIES = 1;

  while (retries <= MAX_RETRIES) {
    try {
      // Create the system message with instructions for the AI
      const searchRadius = propertyDetails?.searchRadius || 1.5;
      const filterMode = propertyDetails?.filterMode || 'strict';

      const systemMessage = `You are a highly analytical real estate valuation expert with 20+ years of experience in comparative market analysis.
      Given a subject property and market data, you will determine a precise, data-driven competitive listing price using advanced valuation methodologies.

      The analysis should include:

      1. A recommended listing price with a well-justified price range (low to high)
      2. A list of comparable properties with the following details for each:
         - Address
         - Price
         - Bedrooms
         - Bathrooms
         - Square Feet
         - Year Built
         - Distance from target property (in miles)
         - Date sold
         - Similarity score (as a percentage, calculated with precision)
         - Zillow link (formatted as a search URL)
         - Detailed adjustments made for differences between this property and the subject property
         - Whether the comp was included or excluded from analysis, with specific reason if excluded

      3. Comprehensive market trends including:
         - Average price per square foot in the area (calculated with precision)
         - Median sale price
         - Average days on market
         - Year-over-year price change percentage
         - Current inventory level with specific metrics
         - Market classification (buyer's, seller's, or balanced) with supporting evidence

      4. A confidence score (1-10) with detailed justification based on:
         - Quality and quantity of comparable properties
         - Recency of sales data
         - Consistency of pricing across comparable properties
         - Market stability indicators
         - Seasonal factors
         - Neighborhood-specific trends

      5. Two detailed lists:
         - keyCompsUsed: An array of strings describing the key comparable properties used in your analysis with specific reasons why each was weighted heavily
         - excludedCompsAndWhy: An array of strings explaining which properties were excluded with precise reasoning

      💡 Advanced Valuation Instructions:
      - Apply a weighted scoring system for comps: 40% for location proximity, 30% for property similarity, 20% for recency, 10% for market conditions
      - Only give significant weight to comps within ${searchRadius} miles and sold within the last 6 months
      - ${filterMode === 'strict'
          ? 'Apply strict filtering criteria: only include comps that have EXACT same number of bedrooms and bathrooms, within ±10% sqft, within 5 years of construction, and within the specified search radius'
          : 'Apply sophisticated similarity scoring: rank properties using a weighted algorithm that considers beds (15%), baths (15%), sqft (25%), year built (15%), distance (20%), and condition/features (10%)'}
      - Make precise dollar adjustments for each difference: $X per sqft difference, $Y per bedroom/bathroom difference, $Z per decade of age difference
      - Apply neighborhood-specific multipliers based on school districts, crime rates, and amenity access
      - Factor in current interest rates and their impact on buyer purchasing power
      - Consider seasonal market fluctuations and their impact on the current valuation
      - For each comp, provide a detailed similarity score calculation and adjustment methodology

      IMPORTANT: Your response MUST be a valid JSON object containing all the fields described above. Do not include any text before or after the JSON object.

      Return the data in this JSON format:
      {
        "recommendedListingPrice": "$X,XXX,XXX",
        "priceRange": "$X,XXX,XXX - $X,XXX,XXX",
        "confidenceScore": 8,
        "priceJustification": "Short explanation of how you arrived at this price",
        "keyCompsUsed": ["123 Main St, City, State", "456 Oak Ave, City, State"],
        "excludedCompsAndWhy": ["789 Pine St: too far (2.3 miles)", "321 Elm St: sold 14 months ago"],
        "comparableProperties": [
          {
            "address": "123 Main St, City, State",
            "price": "$XXX,XXX",
            "bedrooms": 3,
            "bathrooms": 2,
            "squareFeet": 1500,
            "yearBuilt": 2005,
            "distanceFromTarget": "0.5 miles",
            "soldDate": "March 15, 2023",
            "similarityScore": 85,
            "adjustments": "+$10,000 for extra bathroom, -$5,000 for smaller lot",
            "zillowLink": "https://www.zillow.com/homes/123-Main-St,-City,-State_rb/",
            "included": true,
            "exclusionReason": null
          },
          {
            "address": "789 Pine St, City, State",
            "price": "$XXX,XXX",
            "bedrooms": 3,
            "bathrooms": 2,
            "squareFeet": 1600,
            "yearBuilt": 2008,
            "distanceFromTarget": "2.3 miles",
            "soldDate": "April 10, 2023",
            "similarityScore": 70,
            "adjustments": "-$15,000 for distance",
            "zillowLink": "https://www.zillow.com/homes/789-Pine-St,-City,-State_rb/",
            "included": false,
            "exclusionReason": "Property is too far (> 1.5 miles)"
          }
        ],
        "marketTrends": {
          "averagePricePerSqFt": "$XXX",
          "medianSalePrice": "$XXX,XXX",
          "averageDaysOnMarket": 30,
          "priceChangeLastYear": "+5%",
          "inventoryLevel": "Low",
          "marketType": "Seller's Market"
        }
      }

      Do not include any explanations, markdown formatting, or code blocks in your response. Return only the JSON object.`;

      // Create the user message with the address and property details
      let userMessage = `Please analyze comparable properties for this address and recommend a listing price:

      Subject Property:
      - Address: ${address}`;

      // Add property details if available
      if (propertyDetails) {
        if (propertyDetails.squareFeet) userMessage += `\n- Sq Ft: ${propertyDetails.squareFeet}`;
        if (propertyDetails.bedrooms && propertyDetails.bathrooms) userMessage += `\n- Beds/Baths: ${propertyDetails.bedrooms}/${propertyDetails.bathrooms}`;
        if (propertyDetails.lotSize) userMessage += `\n- Lot Size: ${propertyDetails.lotSize} acres`;
        if (propertyDetails.yearBuilt) userMessage += `\n- Year Built: ${propertyDetails.yearBuilt}`;
        if (propertyDetails.condition) userMessage += `\n- Condition: ${propertyDetails.condition}`;
        if (propertyDetails.uniqueFeatures && propertyDetails.uniqueFeatures.length > 0) {
          userMessage += `\n- Unique Features: ${propertyDetails.uniqueFeatures.join(', ')}`;
        }
      }

      // Add market trends if available
      if (marketTrends) {
        userMessage += `\n\nLocal Market Trends:`;
        if (marketTrends.averagePricePerSqFt) userMessage += `\n- Average Price Per Sq Ft: ${marketTrends.averagePricePerSqFt}`;
        if (marketTrends.medianSalePrice) userMessage += `\n- Median Sale Price: ${marketTrends.medianSalePrice}`;
        if (marketTrends.averageDaysOnMarket) userMessage += `\n- Average Days on Market: ${marketTrends.averageDaysOnMarket}`;
        if (marketTrends.priceChangeLastYear) userMessage += `\n- Price Change Last Year: ${marketTrends.priceChangeLastYear}`;
        if (marketTrends.inventoryLevel) userMessage += `\n- Inventory Level: ${marketTrends.inventoryLevel}`;
        if (marketTrends.marketType) userMessage += `\n- Market Type: ${marketTrends.marketType}`;
      }

      userMessage += `\n\nPlease focus on finding properties sold within ${searchRadius} miles of the subject property and within the last 6 months. ${
        filterMode === 'strict'
          ? `Use strict filtering criteria: only include comps that have EXACT same number of bedrooms and bathrooms, within ±10% sqft, within 5 years of construction, and within ${searchRadius} miles.`
          : `Use soft filtering with similarity scores: rank properties by how closely they match the subject property, even if they fall outside the strict criteria.`
      } Make adjustments based on differences between the subject property and comps, including size, condition, age, and features. Explicitly exclude comps that are too far (>${searchRadius} miles) or too old (>6 months).`;

      // Create the request payload
      const requestData: OpenAIRequest = {
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage }
        ],
        temperature: 0.5,
        max_tokens: 1000
      };

      // Make the API request
      const response = await axios.post<OpenAIResponse>(
        `${OPENAI_API_URL}/chat/completions`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      // Extract and parse the generated analysis
      const analysisText = response.data.choices[0].message.content;
      let analysisResult: CompAnalysisResult;

      try {
        // Clean the response text to handle markdown formatting
        const cleanedText = cleanJsonResponse(analysisText);

        // Try to parse the JSON response
        analysisResult = JSON.parse(cleanedText);
      } catch (parseError) {
        console.error('Error parsing OpenAI response as JSON:', parseError);
        console.log('Raw response:', analysisText);

        // Fallback: Create a structured result with a note about the parsing error
        analysisResult = {
          recommendedPrice: 'Unable to determine',
          recommendedListingPrice: 'Unable to determine',
          priceJustification: 'Error parsing response',
          keyCompsUsed: [],
          excludedCompsAndWhy: ['Error parsing response'],
          comparableProperties: [{
            address: 'Error parsing OpenAI response',
            price: 'N/A',
            included: false,
            exclusionReason: 'Error parsing response'
          }]
        };
      }

      // Cache the result
      compAnalysisCache.set(cacheKey, {
        timestamp: Date.now(),
        data: analysisResult
      });

      // Return the analysis result
      return analysisResult;

    } catch (error) {
      const axiosError = error as AxiosError;

      // Check if it's an authentication error (401)
      if (axiosError.response?.status === 401) {
        console.error('Authentication error: Invalid API key');
        throw new Error('Authentication failed. Please check your OpenAI API key.');
      }
      // Check if it's a rate limit error (429)
      else if (axiosError.response?.status === 429) {
        console.log(`Rate limit error (429) encountered. Will try one more time.`);

        // Get retry-after header if available
        const retryAfter = axiosError.response.headers['retry-after'];

        if (retryAfter && !isNaN(parseInt(retryAfter))) {
          // If server specifies retry-after, use that value (in seconds)
          const waitTime = parseInt(retryAfter) * 1000;
          console.log(`Server requested retry after ${waitTime/1000} seconds`);
          await delay(waitTime);
        } else {
          // Use a fixed 10-second delay for the single retry
          console.log('Using a 10-second delay before retrying');
          await delay(10000);
        }

        if (retries < MAX_RETRIES) {
          retries++;
          continue; // Retry the request once
        } else {
          console.error('Single retry attempt failed');
          throw new Error('OpenAI API rate limit reached. Please try again later.');
        }
      }

      // For other errors or if max retries reached
      console.error('Error analyzing comparable properties:', error);
      throw new Error('Failed to analyze comparable properties. Please try again.');
    }
  }

  // This should never be reached due to the throw in the catch block
  throw new Error('Failed to analyze comparable properties after multiple attempts.');
};

// Enhanced LLM service that works with both OpenAI and Claude
export const generateEnhancedCMA = async (
  targetProperty: any,
  comparableProperties: any[],
  marketMetrics?: any,
  llmProvider: 'openai' | 'claude' = 'openai',
  apiKey?: string
) => {
  // Get OpenAI API key from environment variable
  // First try to get it from import.meta.env
  let apiKey = import.meta.env.VITE_OPENAI_API_KEY;

  // If not available, try to get it from window.__ENV__ (which we'll set up)
  if (!apiKey && typeof window !== 'undefined' && (window as any).__ENV__ && (window as any).__ENV__.VITE_OPENAI_API_KEY) {
    apiKey = (window as any).__ENV__.VITE_OPENAI_API_KEY;
  }

  // For development/testing, you can hardcode the key here (but remove before committing)
  // This is a fallback for testing only
  if (!apiKey) {
    apiKey = '********************************************************************************************************************************************************************';
  }

  if (!apiKey) {
    throw new Error('OpenAI API key not found. Please check your environment configuration.');
  }

  // Process and validate the Zillow data before sending to LLM
  console.log('Raw target property data:', targetProperty);
  console.log('Raw comparable properties data:', comparableProperties);
  console.log('Raw market metrics:', marketMetrics);

  const processedData = {
    targetProperty: {
      address: targetProperty.address || 'Address not available',
      price: targetProperty.price || targetProperty.zestimate || 0,
      bedrooms: targetProperty.bedrooms || 0,
      bathrooms: targetProperty.bathrooms || 0,
      sqft: targetProperty.livingArea || targetProperty.sqft || 0,
      yearBuilt: targetProperty.yearBuilt || 'Unknown',
      propertyType: targetProperty.propertyType || targetProperty.homeType || 'Unknown',
      lotSize: targetProperty.lotSize || 'Unknown',
      pricePerSqft: targetProperty.price && targetProperty.sqft ?
        (targetProperty.price / targetProperty.sqft) : 0,
      zestimate: targetProperty.zestimate || 0,
      rentEstimate: targetProperty.rentZestimate || 0,
      taxHistory: targetProperty.taxHistory || [],
      daysOnMarket: targetProperty.daysOnZillow || 0
    },
    comparables: comparableProperties.map(comp => ({
      address: comp.address || 'Address not available',
      price: comp.price || comp.zestimate || 0,
      bedrooms: comp.bedrooms || 0,
      bathrooms: comp.bathrooms || 0,
      sqft: comp.livingArea || comp.squareFeet || comp.sqft || 0,
      yearBuilt: comp.yearBuilt || 'Unknown',
      pricePerSqft: comp.price && (comp.squareFeet || comp.sqft) ?
        (comp.price / (comp.squareFeet || comp.sqft)) : 0,
      daysOnMarket: comp.daysOnZillow || comp.daysOnMarket || 0,
      homeStatus: comp.homeStatus || comp.listingStatus || 'Unknown',
      lastSoldDate: comp.lastSoldDate || comp.dateSold || 'Unknown',
      lastSoldPrice: comp.lastSoldPrice || comp.price || 0
    })),
    marketMetrics: marketMetrics || calculateMarketMetrics(comparableProperties)
  };

  console.log('Processed data for Enhanced Analysis:', processedData);

  const prompt = createDetailedCMAPrompt(processedData);

  try {
    let analysisContent: string;

    if (llmProvider === 'claude') {
      // Use Claude via backend API
      const { analyzeCompsWithClaude } = await import('./claudeBackendService');

      const claudeResult = await analyzeCompsWithClaude(
        targetProperty.address,
        {
          squareFeet: targetProperty.sqft,
          bedrooms: targetProperty.bedrooms,
          bathrooms: targetProperty.bathrooms,
          yearBuilt: targetProperty.yearBuilt,
          searchRadius: 1.5,
          filterMode: 'strict'
        },
        marketMetrics ? {
          averagePricePerSqFt: `$${marketMetrics.avgPricePerSqft || 0}`,
          medianSalePrice: `$${marketMetrics.avgPrice || 0}`,
          averageDaysOnMarket: marketMetrics.avgDaysOnMarket || 0
        } : undefined
      );

      // Extract analysis content from Claude response
      analysisContent = claudeResult.analysis || claudeResult.marketAnalysis || 'Analysis completed successfully.';
    } else {
      // Use OpenAI
      const completion = await axios.post(
        `${OPENAI_API_URL}/chat/completions`,
        {
          model: "gpt-4o",
          messages: [
            {
              role: "system",
              content: `You are a senior real estate analyst with 20+ years of experience.
              You specialize in Chicago market analysis and provide data-driven insights.
              Always include specific numbers, percentages, and actionable recommendations.
              Format your response with clear markdown sections.
              Focus on practical insights that help with pricing and marketing decisions.`
            },
            {
              role: "user",
              content: prompt
            }
          ],
          temperature: 0.2, // Lower for more consistent analysis
          max_tokens: 3000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      analysisContent = completion.data.choices[0].message.content;
    }

    return {
      analysis: analysisContent,
      confidence: calculateConfidenceScore(processedData),
      dataQuality: assessDataQuality(processedData),
      timestamp: new Date().toISOString(),
      provider: llmProvider
    };
  } catch (error) {
    console.error(`Enhanced CMA Generation Error (${llmProvider}):`, error);
    throw new Error(`Failed to generate analysis with ${llmProvider}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

const createDetailedCMAPrompt = (data: any) => {
  const { targetProperty, comparables, marketMetrics } = data;

  return `
COMPREHENSIVE MARKET ANALYSIS REQUEST

**TARGET PROPERTY DETAILS:**
- Address: ${targetProperty.address}
- Current List Price: ${targetProperty.price?.toLocaleString() || 'Not Listed'}
- Zillow Estimate: ${targetProperty.zestimate?.toLocaleString() || 'N/A'}
- Property Specs: ${targetProperty.bedrooms}BR/${targetProperty.bathrooms}BA, ${targetProperty.sqft?.toLocaleString() || 'Unknown'} sq ft
- Year Built: ${targetProperty.yearBuilt}
- Property Type: ${targetProperty.propertyType}
- Current Price/Sq Ft: ${targetProperty.pricePerSqft?.toFixed(2) || 'N/A'}
- Days on Market: ${targetProperty.daysOnMarket} days

**COMPARABLE PROPERTIES ANALYSIS (${comparables.length} properties):**
${comparables.slice(0, 6).map((comp: any, index: number) => `
${index + 1}. ${comp.address}
   • Sale/List Price: ${comp.price?.toLocaleString() || 'N/A'}
   • Specs: ${comp.bedrooms}BR/${comp.bathrooms}BA, ${comp.sqft?.toLocaleString() || 'N/A'} sq ft
   • Price/Sq Ft: ${comp.pricePerSqft?.toFixed(2) || 'N/A'}
   • Year Built: ${comp.yearBuilt}
   • Status: ${comp.homeStatus}
   • Days on Market: ${comp.daysOnMarket} days
   • Last Sold: ${comp.lastSoldDate} for ${comp.lastSoldPrice?.toLocaleString() || 'N/A'}
`).join('')}

**MARKET METRICS SUMMARY:**
- Average Sale Price: ${marketMetrics.avgPrice?.toLocaleString() || 'N/A'}
- Average Price/Sq Ft: ${marketMetrics.avgPricePerSqft?.toFixed(2) || 'N/A'}
- Price Range: ${marketMetrics.priceRange?.min?.toLocaleString() || 'N/A'} - ${marketMetrics.priceRange?.max?.toLocaleString() || 'N/A'}
- Average Days on Market: ${marketMetrics.avgDaysOnMarket?.toFixed(0) || 'N/A'} days
- Total Comparable Properties: ${comparables.length}

**REQUIRED ANALYSIS SECTIONS:**

## Executive Summary
Provide a 3-sentence summary of this property's market position and key findings.

## Pricing Analysis & Recommendations
- Compare target property price to market averages
- Identify if property is overpriced, underpriced, or fairly priced
- Provide specific recommended price range with justification
- Calculate percentage difference from market average

## Competitive Positioning
- How does this property compare to similar homes?
- What are the key differentiators (positive and negative)?
- Which comparable properties are the strongest competition?

## Market Conditions Assessment
- Current market trends based on days on market
- Pricing trends from comparable sales
- Inventory levels and buyer/seller market conditions

## Strategic Recommendations
- Specific pricing strategy
- Marketing positioning advice
- Timing recommendations
- Potential concerns or red flags

## Value Drivers & Concerns
- Top 3 features that add value
- Top 3 potential issues or drawbacks
- Recommendations for improvements

Provide specific dollar amounts, percentages, and actionable insights throughout your analysis.
  `.trim();
};

const calculateMarketMetrics = (comparables: any[]) => {
  if (!comparables || comparables.length === 0) {
    return {
      avgPrice: 0,
      avgPricePerSqft: 0,
      priceRange: { min: 0, max: 0 },
      avgDaysOnMarket: 0
    };
  }

  const validPrices = comparables.filter(c => c.price > 0).map(c => c.price);
  const validPricePerSqft = comparables.filter(c => c.pricePerSqft > 0).map(c => c.pricePerSqft);
  const validDaysOnMarket = comparables.filter(c => c.daysOnMarket >= 0).map(c => c.daysOnMarket);

  return {
    avgPrice: validPrices.length ? validPrices.reduce((sum, price) => sum + price, 0) / validPrices.length : 0,
    avgPricePerSqft: validPricePerSqft.length ? validPricePerSqft.reduce((sum, price) => sum + price, 0) / validPricePerSqft.length : 0,
    priceRange: {
      min: validPrices.length ? Math.min(...validPrices) : 0,
      max: validPrices.length ? Math.max(...validPrices) : 0
    },
    avgDaysOnMarket: validDaysOnMarket.length ? validDaysOnMarket.reduce((sum, days) => sum + days, 0) / validDaysOnMarket.length : 0
  };
};

const calculateConfidenceScore = (data: any) => {
  let score = 0;
  let maxScore = 100;

  // Data completeness (40 points)
  if (data.targetProperty.price > 0) score += 10;
  if (data.targetProperty.sqft > 0) score += 10;
  if (data.targetProperty.bedrooms > 0) score += 5;
  if (data.targetProperty.bathrooms > 0) score += 5;
  if (data.targetProperty.yearBuilt && data.targetProperty.yearBuilt !== 'Unknown') score += 10;

  // Comparable properties quality (40 points)
  const validComps = data.comparables.filter((c: any) => c.price > 0 && c.sqft > 0);
  if (validComps.length >= 3) score += 15;
  if (validComps.length >= 6) score += 10;
  if (validComps.some((c: any) => c.daysOnMarket > 0)) score += 15;

  // Market data availability (20 points)
  if (data.marketMetrics.avgPrice > 0) score += 10;
  if (data.marketMetrics.avgDaysOnMarket > 0) score += 10;

  return Math.min(Math.round(score), maxScore);
};

const assessDataQuality = (data: any) => {
  const issues = [];

  if (!data.targetProperty.price || data.targetProperty.price === 0) {
    issues.push('No listing price available');
  }
  if (!data.targetProperty.sqft || data.targetProperty.sqft === 0) {
    issues.push('Square footage not available');
  }
  if (data.comparables.length < 3) {
    issues.push('Limited comparable properties found');
  }

  const validComps = data.comparables.filter((c: any) => c.price > 0);
  if (validComps.length < 2) {
    issues.push('Insufficient pricing data from comparables');
  }

  return {
    quality: issues.length === 0 ? 'High' : issues.length <= 2 ? 'Medium' : 'Low',
    issues: issues
  };
};

// Add this function to validate Zillow data before analysis
export const validateZillowData = (propertyData: any, comparables: any[]) => {
  const errors = [];

  if (!propertyData) {
    errors.push('No target property data available');
    return { isValid: false, errors };
  }

  if (!propertyData.address) {
    errors.push('Property address is missing');
  }

  if (!propertyData.price && !propertyData.zestimate) {
    errors.push('No pricing information available for target property');
  }

  if (!comparables || comparables.length === 0) {
    errors.push('No comparable properties found');
  } else if (comparables.length < 3) {
    errors.push('Insufficient comparable properties (minimum 3 required)');
  }

  const validComps = comparables.filter(c => c.price > 0 || c.zestimate > 0);
  if (validComps.length < 2) {
    errors.push('Insufficient pricing data from comparable properties');
  }

  return {
    isValid: errors.length === 0,
    errors: errors,
    warnings: errors.length > 0 && errors.length <= 2 ? ['Analysis quality may be reduced'] : []
  };
};
