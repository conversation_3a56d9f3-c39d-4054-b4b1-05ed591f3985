// Global type declarations

interface Window {
  __ENV__?: {
    VITE_OPENAI_API_KEY?: string;
    VITE_CLAUDE_API_KEY?: string;
    VITE_GOOGLE_API_KEY?: string;
    VITE_GOOGLE_CLIENT_ID?: string;
    [key: string]: any;
  };
  gapi?: any;
  googleApiLoaded?: boolean;
  googleApiLoading?: boolean;
  googleApiLoadError?: boolean;
  isGoogleApiLoaded?: () => boolean;
  isGoogleApiLoading?: () => boolean;
  hasGoogleApiLoadError?: () => boolean;

  // Microsoft Clarity
  clarity?: (command: string, ...args: any[]) => void;
}
