import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaClipboardCheck, FaArrowLeft, FaFilePdf, FaPrint, FaCheck, FaTimes, FaExclamationTriangle } from 'react-icons/fa';
import { useApp } from '../../context/SimpleAppContext';

type InspectionItem = {
  id: string;
  category: string;
  item: string;
  condition: 'good' | 'fair' | 'poor' | 'n/a';
  notes: string;
};

type InspectionData = {
  propertyAddress: string;
  inspectionDate: string;
  inspectorName: string;
  clientName: string;
  propertyType: string;
  yearBuilt: string;
  squareFootage: string;
  generalNotes: string;
  items: InspectionItem[];
};

const defaultCategories = [
  'Exterior',
  'Roof',
  'Structure',
  'Electrical',
  'Plumbing',
  'HVAC',
  'Interior',
  'Kitchen',
  'Bathroom',
  'Basement',
  'Attic',
  'Garage',
];

const defaultItems: Record<string, string[]> = {
  'Exterior': ['Siding/Walls', 'Windows', 'Doors', 'Foundation', 'Grading/Drainage', 'Driveway', 'Walkways', 'Landscaping', 'Deck/Patio'],
  'Roof': ['Covering', 'Flashing', 'Gutters', 'Downspouts', 'Chimneys', 'Skylights'],
  'Structure': ['Foundation', 'Framing', 'Floors', 'Walls', 'Ceilings', 'Stairs'],
  'Electrical': ['Service Entrance', 'Main Panel', 'Branch Circuits', 'Outlets/Switches', 'Fixtures', 'GFCI Protection'],
  'Plumbing': ['Water Supply', 'Drain/Waste/Vent', 'Water Heater', 'Fixtures', 'Sump Pump'],
  'HVAC': ['Heating System', 'Cooling System', 'Ductwork', 'Thermostat', 'Fireplace/Stove'],
  'Interior': ['Walls', 'Ceilings', 'Floors', 'Windows', 'Doors', 'Stairs', 'Smoke Detectors'],
  'Kitchen': ['Cabinets', 'Countertops', 'Sink', 'Disposal', 'Dishwasher', 'Range/Oven', 'Refrigerator', 'Ventilation'],
  'Bathroom': ['Sink', 'Toilet', 'Tub/Shower', 'Ventilation', 'GFCI Protection'],
  'Basement': ['Walls', 'Floor', 'Stairs', 'Windows', 'Moisture'],
  'Attic': ['Access', 'Framing', 'Insulation', 'Ventilation'],
  'Garage': ['Door', 'Opener', 'Floor', 'Walls', 'Ceiling', 'Windows'],
};

const initialInspectionData: InspectionData = {
  propertyAddress: '',
  inspectionDate: new Date().toISOString().split('T')[0],
  inspectorName: '',
  clientName: '',
  propertyType: '',
  yearBuilt: '',
  squareFootage: '',
  generalNotes: '',
  items: [],
};

// Initialize with default items
defaultCategories.forEach(category => {
  defaultItems[category]?.forEach(item => {
    initialInspectionData.items.push({
      id: `${category}-${item}`.replace(/\s+/g, '-').toLowerCase(),
      category,
      item,
      condition: 'n/a',
      notes: '',
    });
  });
});

const InspectionReportGenerator = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const [inspectionData, setInspectionData] = useState<InspectionData>(initialInspectionData);
  const [activeTab, setActiveTab] = useState<string>('general');
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const reportRef = useRef<HTMLDivElement>(null);

  const handleGeneralInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setInspectionData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleItemChange = (id: string, field: 'condition' | 'notes', value: string) => {
    setInspectionData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === id ? { ...item, [field]: value } : item
      ),
    }));
  };

  const handleGenerateReport = () => {
    setShowPreview(true);
  };

  const handleDownloadPDF = () => {
    if (reportRef.current) {
      // Use browser's print functionality with PDF option
      window.print();
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'good': return 'text-green-500';
      case 'fair': return 'text-yellow-500';
      case 'poor': return 'text-red-500';
      default: return 'text-gray-400';
    }
  };

  const getConditionIcon = (condition: string) => {
    switch (condition) {
      case 'good': return <FaCheck className="text-green-500" />;
      case 'fair': return <FaExclamationTriangle className="text-yellow-500" />;
      case 'poor': return <FaTimes className="text-red-500" />;
      default: return null;
    }
  };

  const renderGeneralInfoForm = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="propertyAddress" className="block text-sm font-medium text-gray-700 mb-1">
            Property Address <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="propertyAddress"
            name="propertyAddress"
            value={inspectionData.propertyAddress}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label htmlFor="inspectionDate" className="block text-sm font-medium text-gray-700 mb-1">
            Inspection Date <span className="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="inspectionDate"
            name="inspectionDate"
            value={inspectionData.inspectionDate}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="inspectorName" className="block text-sm font-medium text-gray-700 mb-1">
            Inspector Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="inspectorName"
            name="inspectorName"
            value={inspectionData.inspectorName}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-1">
            Client Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="clientName"
            name="clientName"
            value={inspectionData.clientName}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label htmlFor="propertyType" className="block text-sm font-medium text-gray-700 mb-1">
            Property Type
          </label>
          <input
            type="text"
            id="propertyType"
            name="propertyType"
            value={inspectionData.propertyType}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <div>
          <label htmlFor="yearBuilt" className="block text-sm font-medium text-gray-700 mb-1">
            Year Built
          </label>
          <input
            type="text"
            id="yearBuilt"
            name="yearBuilt"
            value={inspectionData.yearBuilt}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <div>
          <label htmlFor="squareFootage" className="block text-sm font-medium text-gray-700 mb-1">
            Square Footage
          </label>
          <input
            type="text"
            id="squareFootage"
            name="squareFootage"
            value={inspectionData.squareFootage}
            onChange={handleGeneralInfoChange}
            className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      <div>
        <label htmlFor="generalNotes" className="block text-sm font-medium text-gray-700 mb-1">
          General Notes
        </label>
        <textarea
          id="generalNotes"
          name="generalNotes"
          value={inspectionData.generalNotes}
          onChange={handleGeneralInfoChange}
          rows={4}
          className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        />
      </div>
    </div>
  );

  const renderCategoryTabs = () => (
    <div className="mb-6 overflow-x-auto">
      <div className="flex space-x-2 pb-2">
        <button
          onClick={() => setActiveTab('general')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${activeTab === 'general' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
        >
          General Info
        </button>
        {defaultCategories.map(category => (
          <button
            key={category}
            onClick={() => setActiveTab(category)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${activeTab === category ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            {category}
          </button>
        ))}
      </div>
    </div>
  );

  const renderCategoryItems = (category: string) => {
    const categoryItems = inspectionData.items.filter(item => item.category === category);

    return (
      <div className="space-y-6">
        <h3 className="text-xl font-semibold text-dark-800">{category} Inspection</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Condition</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {categoryItems.map(item => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.item}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <select
                      value={item.condition}
                      onChange={(e) => handleItemChange(item.id, 'condition', e.target.value as 'good' | 'fair' | 'poor' | 'n/a')}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="n/a">N/A</option>
                      <option value="good">Good</option>
                      <option value="fair">Fair</option>
                      <option value="poor">Poor</option>
                    </select>
                  </td>
                  <td className="px-6 py-4">
                    <input
                      type="text"
                      value={item.notes}
                      onChange={(e) => handleItemChange(item.id, 'notes', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Add notes here..."
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderInspectionForm = () => (
    <div className="bg-white rounded-xl shadow-card p-8">
      {renderCategoryTabs()}

      <div className="mt-6">
        {activeTab === 'general' ? renderGeneralInfoForm() : renderCategoryItems(activeTab)}
      </div>

      <div className="mt-8 flex justify-center">
        <button
          onClick={handleGenerateReport}
          className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-8 py-4 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center justify-center transform hover:scale-105"
          disabled={!inspectionData.propertyAddress || !inspectionData.inspectionDate || !inspectionData.inspectorName || !inspectionData.clientName}
        >
          <FaFilePdf className="mr-2" />
          Generate Report
        </button>
      </div>
    </div>
  );

  const renderReportPreview = () => (
    <div className="bg-white rounded-xl shadow-card p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-dark-800">Report Preview</h2>
        <div className="flex space-x-4">
          <button
            onClick={handleDownloadPDF}
            className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center"
          >
            <FaFilePdf className="mr-2" />
            Save as PDF
          </button>
          <button
            onClick={() => window.print()}
            className="bg-dark-700 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center"
          >
            <FaPrint className="mr-2" />
            Print
          </button>
          <button
            onClick={() => setShowPreview(false)}
            className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-300 transition-all duration-300"
          >
            Edit Report
          </button>
        </div>
      </div>

      <div id="report-content" ref={reportRef} className="p-8 border border-gray-200 rounded-lg">
        {/* Report Header */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-dark-800 mb-2">Property Inspection Report</h1>
          <p className="text-xl text-dark-600">{inspectionData.propertyAddress}</p>
          <p className="text-gray-500">Inspection Date: {new Date(inspectionData.inspectionDate).toLocaleDateString()}</p>
        </div>

        {/* Property Information */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">Property Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p><span className="font-semibold">Address:</span> {inspectionData.propertyAddress}</p>
              <p><span className="font-semibold">Property Type:</span> {inspectionData.propertyType || 'N/A'}</p>
              <p><span className="font-semibold">Year Built:</span> {inspectionData.yearBuilt || 'N/A'}</p>
            </div>
            <div>
              <p><span className="font-semibold">Square Footage:</span> {inspectionData.squareFootage || 'N/A'}</p>
              <p><span className="font-semibold">Client:</span> {inspectionData.clientName}</p>
              <p><span className="font-semibold">Inspector:</span> {inspectionData.inspectorName}</p>
            </div>
          </div>
        </div>

        {/* General Notes */}
        {inspectionData.generalNotes && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">General Notes</h2>
            <p className="whitespace-pre-line">{inspectionData.generalNotes}</p>
          </div>
        )}

        {/* Inspection Summary */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">Inspection Summary</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h3 className="font-semibold text-green-700 flex items-center"><FaCheck className="mr-2" /> Good Condition</h3>
              <p className="text-2xl font-bold text-green-600">
                {inspectionData.items.filter(item => item.condition === 'good').length}
              </p>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h3 className="font-semibold text-yellow-700 flex items-center"><FaExclamationTriangle className="mr-2" /> Fair Condition</h3>
              <p className="text-2xl font-bold text-yellow-600">
                {inspectionData.items.filter(item => item.condition === 'fair').length}
              </p>
            </div>
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h3 className="font-semibold text-red-700 flex items-center"><FaTimes className="mr-2" /> Poor Condition</h3>
              <p className="text-2xl font-bold text-red-600">
                {inspectionData.items.filter(item => item.condition === 'poor').length}
              </p>
            </div>
          </div>
        </div>

        {/* Detailed Inspection Results */}
        <div>
          <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">Detailed Inspection Results</h2>

          {defaultCategories.map(category => {
            const categoryItems = inspectionData.items.filter(item => item.category === category);
            const hasItems = categoryItems.some(item => item.condition !== 'n/a');

            if (!hasItems) return null;

            return (
              <div key={category} className="mb-8">
                <h3 className="text-xl font-semibold text-dark-800 mb-4">{category}</h3>
                <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg overflow-hidden">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Condition</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categoryItems.filter(item => item.condition !== 'n/a').map(item => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.item}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center ${getConditionColor(item.condition)}`}>
                            {getConditionIcon(item.condition)}
                            <span className="ml-1 capitalize">{item.condition}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">{item.notes || 'No notes'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="mt-12 pt-6 border-t border-gray-200 text-center text-gray-500 text-sm">
          <p>This report is generated by Digital Realtor Inspection Tool</p>
          <p>Report generated on {new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="py-12 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/agent')}
            className="flex items-center text-primary-600 hover:text-primary-700 mb-4"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-dark-800 mb-2">Inspection Report Generator</h1>
          <p className="text-dark-500">
            Create detailed property inspection reports with our easy-to-use tool.
          </p>
        </div>

        {showPreview ? renderReportPreview() : renderInspectionForm()}
      </div>
    </div>
  );
};

export default InspectionReportGenerator;
