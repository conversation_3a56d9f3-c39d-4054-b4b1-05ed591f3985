# Render Deployment Guide for Digital Realtor

This guide will help you deploy your Digital Realtor backend to Render.

## Prerequisites

- GitHub repository with your Digital Realtor project
- Render account (sign up at [render.com](https://render.com) if you don't have one)

## Option 1: Deploy from GitHub Repository (Recommended)

### Step 1: Connect GitHub to Render

1. Log in to your Render dashboard at [dashboard.render.com](https://dashboard.render.com)
2. Click "New" and select "Web Service"
3. Select "Build and deploy from a Git repository"
4. Connect your GitHub account if not already connected
5. Select your "digital-realtor" repository

### Step 2: Configure Web Service

1. Enter a name for your service (e.g., "digital-realtor-api")
2. Select the region closest to your users
3. Select "Node" as the runtime
4. Set the build command: `cd backend && npm install`
5. Set the start command: `cd backend && node server.js`
6. Select an instance type (Free tier is fine for testing)
7. Click "Create Web Service"

### Step 3: Configure Environment Variables

1. After the service is created, go to the "Environment" tab
2. Add the following environment variables:
   - `NODE_ENV`: `production`
   - `PORT`: `10000` (Render will override this, but it's good to set)
   - `MONGO_URI`: Your MongoDB connection string
   - `JWT_SECRET`: Your JWT secret
   - `OPENAI_API_KEY`: Your OpenAI API key
   - `ANTHROPIC_API_KEY`: Your Anthropic/Claude API key
   - Any other environment variables your application needs
3. Click "Save Changes"

## Option 2: Manual Deployment

### Step 1: Create a New Web Service

1. Log in to your Render dashboard
2. Click "New" and select "Web Service"
3. Select "Deploy from a .zip file"

### Step 2: Upload Deployment Package

1. Click "Upload a .zip file"
2. Select the `digital-realtor-backend-deploy.zip` file
3. Enter a name for your service (e.g., "digital-realtor-api")
4. Select the region closest to your users
5. Select "Node" as the runtime
6. Set the build command: `npm install`
7. Set the start command: `node server.js`
8. Select an instance type (Free tier is fine for testing)
9. Click "Create Web Service"

### Step 3: Configure Environment Variables

Same as in Option 1, Step 3.

## Step 4: Verify Deployment

1. Wait for the deployment to complete (this may take a few minutes)
2. Click on the service URL to open your deployed API
3. You should see a response from the health check endpoint

## Step 5: Update Frontend Configuration

1. Update your frontend configuration to use the new Render URL
2. In your frontend code, update the API base URL to point to your Render service URL
3. Rebuild and deploy your frontend

## Troubleshooting

If you encounter issues with your deployment:

1. Check the Render logs for error messages
2. Verify that all environment variables are set correctly
3. Make sure your MongoDB instance is accessible from Render
4. Check that your CORS configuration allows requests from your frontend domain

## Continuous Deployment

Render automatically deploys new changes when you push to your GitHub repository. To update your deployment:

1. Make changes to your code
2. Commit and push to GitHub
3. Render will automatically detect the changes and deploy them

For manual deployments, you'll need to re-upload the .zip file and redeploy.
