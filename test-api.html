<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #357ab8;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div>
        <button id="testGet">Test GET /api/users/clear-all-test</button>
        <button id="testDelete">Test DELETE /api/users/clear-all</button>
    </div>
    
    <h2>Response:</h2>
    <pre id="response">Click a button to test the API...</pre>
    
    <script>
        document.getElementById('testGet').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:5000/api/users/clear-all-test');
                const data = await response.json();
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('response').textContent = `Error: ${error.message}`;
            }
        });
        
        document.getElementById('testDelete').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:5000/api/users/clear-all', {
                    method: 'DELETE'
                });
                const data = await response.json();
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('response').textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
