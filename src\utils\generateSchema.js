// OpenAI API integration
import { getOpenAIClient } from './openaiConfig';

// Sample schema data
const sampleSchema = {
  "buyer": {
    "fullName": {
      "pdfField": "Buyer_Name",
      "label": "Buyer Name",
      "type": "text",
      "infer": false
    },
    "email": {
      "pdfField": "Buyer_Email",
      "label": "Buyer Email",
      "type": "text",
      "infer": false
    }
  },
  "seller": {
    "fullName": {
      "pdfField": "Seller_Name",
      "label": "Seller Name",
      "type": "text",
      "infer": false
    }
  },
  "property": {
    "address": {
      "pdfField": "Property_Address",
      "label": "Property Address",
      "type": "textarea",
      "infer": false
    },
    "unit": {
      "pdfField": "Unit_Number",
      "label": "Unit # (Optional)",
      "type": "text",
      "infer": false
    },
    "parkingIncluded": {
      "pdfField": "Parking_Included",
      "label": "Parking Included?",
      "type": "checkbox",
      "infer": false
    }
  }
};

/**
 * Generate a JSON schema from PDF field names using OpenAI
 * @param {string[]} fieldNames - Array of PDF field names
 * @returns {Promise<Object>} - The generated schema
 */
export async function generateSchemaFromFields(fieldNames) {
  try {
    console.log('Generating schema from field names:', fieldNames);

    // Get the OpenAI client
    const openai = getOpenAIClient();

    if (!openai) {
      console.warn('OpenAI client not available, using mock implementation');
      throw new Error('OpenAI client not available');
    }

    const prompt = `
You are a contract generation assistant. Below is a list of PDF form field names extracted from a real estate contract form.
Your task is to:
- Group fields into logical sections (e.g., Buyer, Property, Financials)
- Map each field to a suggested user input key
- Suggest UI input type (text, textarea, checkbox, number, date, dropdown)
- Provide a friendly label to display in the UI
- Indicate whether the field can be inferred using AI (true/false)
- Output all of this in a JSON format

PDF Field Names:
${fieldNames.join(' ')}

Please respond with a JSON object only, no additional text.
`;

    // Call the OpenAI API
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        { role: "system", content: "You are a helpful assistant that generates JSON schemas for contract generation." },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 1500,
    });

    // Parse the response
    const schemaText = response.choices[0].message.content.trim();

    // Extract JSON from the response (in case there's any additional text)
    const jsonMatch = schemaText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Could not extract JSON from the response');
    }

    const schema = JSON.parse(jsonMatch[0]);
    return schema;
  } catch (error) {
    console.error('Error generating schema:', error);

    // Return the sample schema as a fallback
    console.log('Returning sample schema as fallback');
    return sampleSchema;
  }
}

/**
 * Generate a field mapping from the schema
 * @param {Object} schema - The generated schema
 * @returns {Object} - The field mapping
 */
export function generateFieldMapping(schema) {
  const fieldMap = {};

  // Process each section in the schema
  Object.entries(schema).forEach(([section, fields]) => {
    // Handle nested fields
    if (typeof fields === 'object' && !Array.isArray(fields)) {
      Object.entries(fields).forEach(([fieldName, fieldInfo]) => {
        if (fieldInfo.pdfField) {
          fieldMap[fieldInfo.pdfField] = `${section}.${fieldName}`;
        }
      });
    }
    // Handle direct fields
    else if (fields.pdfField) {
      fieldMap[fields.pdfField] = section;
    }
  });

  return fieldMap;
}

/**
 * Save the schema and field mapping to files
 * @param {Object} schema - The generated schema
 * @param {Object} fieldMap - The field mapping
 */
export function saveSchemaAndMapping(schema, fieldMap) {
  // In a browser environment, we can't save to a file
  // Instead, we'll log the schema and field mapping to the console
  console.log('Schema generated:', schema);
  console.log('Field mapping generated:', fieldMap);

  // In a real implementation, this would save the schema and field mapping to a file or database
  return { schema, fieldMap };
}

/**
 * Main function to generate and save the schema and field mapping
 */
export async function generateAndSaveSchema() {
  try {
    // In a browser environment, we can't load from a file
    // Instead, we'll use a sample field names array
    const sampleFieldNames = [
      "Buyer_Name",
      "Seller_Name",
      "Buyer_Email",
      "Property_Address",
      "Unit_Number",
      "Parking_Included"
    ];

    // Generate the schema
    const schema = await generateSchemaFromFields(sampleFieldNames);

    // Generate the field mapping
    const fieldMap = generateFieldMapping(schema);

    // Save the schema and field mapping
    return saveSchemaAndMapping(schema, fieldMap);
  } catch (error) {
    console.error('Error generating and saving schema:', error);
    // Return the sample schema and a generated field mapping as a fallback
    const fieldMap = generateFieldMapping(sampleSchema);
    return { schema: sampleSchema, fieldMap };
  }
}

export default {
  generateSchemaFromFields,
  generateFieldMapping,
  saveSchemaAndMapping,
  generateAndSaveSchema
};
