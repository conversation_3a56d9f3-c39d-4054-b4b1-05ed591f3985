// Simple build script for Netlify
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('=== Digital Realtor Netlify Simple Build ====\n');

try {
  // Step 1: Clean previous build
  console.log('Cleaning previous build...');
  if (fs.existsSync(path.join(__dirname, 'prod-build'))) {
    fs.rmSync(path.join(__dirname, 'prod-build'), { recursive: true, force: true });
  }
  console.log('✓ Previous build cleaned\n');

  // Step 2: Install dependencies
  console.log('Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('✓ Dependencies installed\n');

  // Step 3: Build the application
  console.log('Building application for Netlify...');
  execSync('npx vite build --outDir prod-build', { stdio: 'inherit' });
  console.log('✓ Build completed\n');

  // Step 4: Create Netlify configuration files
  console.log('Creating Netlify configuration files...');

  // Create _redirects file for client-side routing
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', '_redirects'),
    '/* /index.html 200'
  );

  // Create a robots.txt file
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'robots.txt'),
    'User-agent: *\nAllow: /'
  );

  console.log('✓ Netlify configuration files created\n');

  // Step 5: Copy environment variables
  console.log('Copying environment variables...');
  if (fs.existsSync(path.join(__dirname, '.env.production'))) {
    fs.copyFileSync(
      path.join(__dirname, '.env.production'),
      path.join(__dirname, 'prod-build', '.env.production.local')
    );
    console.log('✓ Environment variables copied');
  } else {
    console.log('Warning: .env.production file not found');
  }

  // Create a runtime configuration file
  const runtimeConfig = {
    BUILD_TIME: new Date().toISOString(),
    VERSION: '1.0.0',
    CLARITY_ID: 'r7yjq54tfv'
  };

  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'config.json'),
    JSON.stringify(runtimeConfig, null, 2)
  );

  console.log('✓ Runtime configuration created\n');

  console.log('Netlify build completed successfully!');
  console.log('The build is available in the prod-build directory.');

} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}
