import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaClipboardCheck, FaArrowLeft, FaFilePdf } from 'react-icons/fa';
import { useApp } from '../../context/SimpleAppContext';

const SimpleInspectionReportGenerator = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const [formData, setFormData] = useState({
    propertyAddress: '',
    inspectionDate: new Date().toISOString().split('T')[0],
    inspectorName: '',
    clientName: '',
    propertyType: '',
    yearBuilt: '',
    squareFootage: '',
    generalNotes: '',
  });
  const [showPreview, setShowPreview] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleGenerateReport = () => {
    setShowPreview(true);
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="py-12 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/agent')}
            className="flex items-center text-primary-600 hover:text-primary-700 mb-4"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-dark-800 mb-2">Inspection Report Generator</h1>
          <p className="text-dark-500">
            Create detailed property inspection reports with our easy-to-use tool.
          </p>
        </div>

        {showPreview ? (
          <div className="bg-white rounded-xl shadow-md p-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-dark-800">Report Preview</h2>
              <div className="flex space-x-4">
                <button
                  onClick={handlePrint}
                  className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center"
                >
                  <FaFilePdf className="mr-2" />
                  Save as PDF
                </button>
                <button
                  onClick={() => setShowPreview(false)}
                  className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-300 transition-all duration-300"
                >
                  Edit Report
                </button>
              </div>
            </div>

            <div id="report-content" className="p-8 border border-gray-200 rounded-lg">
              <div className="mb-8 text-center">
                <h1 className="text-3xl font-bold text-dark-800 mb-2">Property Inspection Report</h1>
                <p className="text-xl text-dark-600">{formData.propertyAddress}</p>
                <p className="text-gray-500">Inspection Date: {new Date(formData.inspectionDate).toLocaleDateString()}</p>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">Property Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p><span className="font-semibold">Address:</span> {formData.propertyAddress}</p>
                    <p><span className="font-semibold">Property Type:</span> {formData.propertyType || 'N/A'}</p>
                    <p><span className="font-semibold">Year Built:</span> {formData.yearBuilt || 'N/A'}</p>
                  </div>
                  <div>
                    <p><span className="font-semibold">Square Footage:</span> {formData.squareFootage || 'N/A'}</p>
                    <p><span className="font-semibold">Client:</span> {formData.clientName}</p>
                    <p><span className="font-semibold">Inspector:</span> {formData.inspectorName}</p>
                  </div>
                </div>
              </div>

              {formData.generalNotes && (
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">General Notes</h2>
                  <p className="whitespace-pre-line">{formData.generalNotes}</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-8">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="propertyAddress" className="block text-sm font-medium text-gray-700 mb-1">
                    Property Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="propertyAddress"
                    name="propertyAddress"
                    value={formData.propertyAddress}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="inspectionDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Inspection Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    id="inspectionDate"
                    name="inspectionDate"
                    value={formData.inspectionDate}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="inspectorName" className="block text-sm font-medium text-gray-700 mb-1">
                    Inspector Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="inspectorName"
                    name="inspectorName"
                    value={formData.inspectorName}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-1">
                    Client Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="clientName"
                    name="clientName"
                    value={formData.clientName}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="propertyType" className="block text-sm font-medium text-gray-700 mb-1">
                    Property Type
                  </label>
                  <input
                    type="text"
                    id="propertyType"
                    name="propertyType"
                    value={formData.propertyType}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="yearBuilt" className="block text-sm font-medium text-gray-700 mb-1">
                    Year Built
                  </label>
                  <input
                    type="text"
                    id="yearBuilt"
                    name="yearBuilt"
                    value={formData.yearBuilt}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label htmlFor="squareFootage" className="block text-sm font-medium text-gray-700 mb-1">
                    Square Footage
                  </label>
                  <input
                    type="text"
                    id="squareFootage"
                    name="squareFootage"
                    value={formData.squareFootage}
                    onChange={handleChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="generalNotes" className="block text-sm font-medium text-gray-700 mb-1">
                  General Notes
                </label>
                <textarea
                  id="generalNotes"
                  name="generalNotes"
                  value={formData.generalNotes}
                  onChange={handleChange}
                  rows={4}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div className="mt-8 flex justify-center">
                <button
                  onClick={handleGenerateReport}
                  className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-8 py-4 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center justify-center transform hover:scale-105"
                  disabled={!formData.propertyAddress || !formData.inspectionDate || !formData.inspectorName || !formData.clientName}
                >
                  <FaFilePdf className="mr-2" />
                  Generate Report
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleInspectionReportGenerator;
