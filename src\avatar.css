/* Avatar Styles */
.avatar-container {
  width: 32px;
  height: 32px;
  position: relative;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-700), var(--secondary-700));
  color: white;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 247, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);
  transition: all 0.3s ease;
}

.avatar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-cyan);
  opacity: 0.5;
  box-shadow: 0 0 8px var(--accent-cyan);
}

.avatar.active {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  border: 1px solid var(--accent-cyan);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);
}

.avatar.active::after {
  opacity: 0.8;
  box-shadow: 0 0 12px var(--accent-cyan);
}

.avatar-container-large {
  width: 40px;
  height: 40px;
  position: relative;
  flex-shrink: 0;
}

.avatar-large {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  color: white;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 247, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);
}

.avatar-large::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-cyan);
  opacity: 0.6;
  box-shadow: 0 0 10px var(--accent-cyan);
}

/* User Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: 100%;
  margin-top: 0.5rem;
  right: 0;
  z-index: 1000;
  min-width: 14rem;
  padding: 0.75rem 0;
  background-color: rgba(13, 21, 38, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(0, 247, 255, 0.2);
  box-shadow: 
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(0, 247, 255, 0.15);
  transform-origin: top right;
  animation: dropdown-appear 0.3s ease-out forwards;
  overflow: hidden;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
