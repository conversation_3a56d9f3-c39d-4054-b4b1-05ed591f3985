/* Futuristic <PERSON><PERSON> */

:root {
  --primary-color: #4a6bff;
  --secondary-color: #6e3adc;
  --accent-color: #00f7ff;
  --dark-color: #0a1128;
  --light-color: #f0f4ff;
  --gradient-start: #4a6bff;
  --gradient-end: #c961de;
  --neon-glow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.3);
}

/* Primary Button - Gradient with glow */
.primary-button {
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: var(--neon-glow);
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  text-decoration: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.primary-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7), 0 0 30px rgba(0, 247, 255, 0.4);
  background: linear-gradient(90deg, var(--gradient-end), var(--gradient-start));
}

/* Secondary Button - Outlined with accent color */
.secondary-button {
  background: rgba(0, 247, 255, 0.05);
  border: 1px solid var(--accent-color);
  color: var(--accent-color);
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  text-decoration: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.secondary-button:hover {
  background-color: rgba(0, 247, 255, 0.15);
  transform: translateY(-3px);
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.3);
  border-color: rgba(0, 247, 255, 0.8);
}

/* Tertiary Button - Subtle background */
.tertiary-button {
  background: rgba(74, 107, 255, 0.08);
  border: 1px solid rgba(74, 107, 255, 0.2);
  color: #d0d8ff;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  text-decoration: none;
  backdrop-filter: blur(4px);
}

.tertiary-button:hover {
  background: rgba(74, 107, 255, 0.15);
  transform: translateY(-3px);
  border-color: rgba(74, 107, 255, 0.4);
  box-shadow: 0 0 10px rgba(74, 107, 255, 0.2);
  color: white;
}

/* Dropdown Menu Button Styles */
.dropdown-menu .tertiary-button,
.user-dropdown .tertiary-button {
  background: rgba(74, 107, 255, 0.05);
  border-color: rgba(74, 107, 255, 0.1);
  transform: none;
  width: 100%;
  justify-content: flex-start;
  padding: 0.25rem 0.4rem;
  margin-bottom: 0.1rem;
  font-size: 0.75rem;
  min-height: 1.9rem;
  border-radius: 5px;
  letter-spacing: 0.2px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

.dropdown-menu .tertiary-button::before,
.user-dropdown .tertiary-button::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  background: var(--accent-color);
  transition: width 0.3s ease;
  box-shadow: 0 0 5px var(--accent-color);
  opacity: 0.7;
}

/* Agent Tools section in dropdown */
.dropdown-menu svg.w-3.5 {
  min-width: 14px;
  min-height: 14px;
}

.dropdown-menu .tertiary-button:hover,
.user-dropdown .tertiary-button:hover {
  background: rgba(74, 107, 255, 0.15);
  transform: translateX(3px);
  border-color: rgba(74, 107, 255, 0.3);
  box-shadow: 0 0 8px rgba(0, 247, 255, 0.15);
}

.dropdown-menu .tertiary-button:hover::before,
.user-dropdown .tertiary-button:hover::before {
  width: 100%;
}

.dropdown-menu .button-icon,
.user-dropdown .button-icon {
  min-width: 14px;
  width: 14px;
  height: 14px;
  margin-right: 0.35rem;
  color: var(--accent-color);
  flex-shrink: 0;
  stroke-width: 2.2px;
}

/* Icon Button - For buttons with icons */
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button-icon {
  font-size: 1rem;
}

/* Small Button */
.button-sm {
  padding: 0.4rem 0.9rem;
  font-size: 0.85rem;
  border-radius: 6px;
}

/* Large Button */
.button-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Full Width Button */
.button-full {
  width: 100%;
  display: flex;
}

/* Disabled Button */
.primary-button:disabled,
.secondary-button:disabled,
.tertiary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading State */
.button-loading {
  position: relative;
  color: transparent !important;
}

.button-loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Rounded Button */
.button-rounded {
  border-radius: 30px;
}

/* Button Group */
.button-group {
  display: flex;
  gap: 0.8rem;
}

.button-group.vertical {
  flex-direction: column;
}

/* Shadow Glow Effects */
.shadow-glow-cyan {
  box-shadow: 0 0 8px rgba(0, 247, 255, 0.4);
}

.shadow-glow-cyan-lg {
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.6), 0 0 30px rgba(0, 247, 255, 0.3);
}

.shadow-glow-secondary {
  box-shadow: 0 0 8px rgba(110, 58, 220, 0.4);
}

.shadow-glow-secondary-lg {
  box-shadow: 0 0 15px rgba(110, 58, 220, 0.6), 0 0 30px rgba(110, 58, 220, 0.3);
}

.shadow-glow-tertiary {
  box-shadow: 0 0 8px rgba(74, 107, 255, 0.4);
}

.shadow-glow-tertiary-lg {
  box-shadow: 0 0 15px rgba(74, 107, 255, 0.6), 0 0 30px rgba(74, 107, 255, 0.3);
}

.shadow-glow-accent {
  box-shadow: 0 0 8px rgba(201, 97, 222, 0.4);
}

.shadow-glow-accent-lg {
  box-shadow: 0 0 15px rgba(201, 97, 222, 0.6), 0 0 30px rgba(201, 97, 222, 0.3);
}

/* Accent Button */
.accent-button {
  background: rgba(201, 97, 222, 0.08);
  border: 1px solid rgba(201, 97, 222, 0.2);
  color: #f0d8ff;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  text-decoration: none;
  backdrop-filter: blur(4px);
}

.accent-button:hover {
  background: rgba(201, 97, 222, 0.15);
  transform: translateY(-3px);
  border-color: rgba(201, 97, 222, 0.4);
  box-shadow: 0 0 10px rgba(201, 97, 222, 0.2);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }
}
