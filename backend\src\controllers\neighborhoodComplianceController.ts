import { Request, Response } from 'express';
import { NeighborhoodComplianceService } from '../services/neighborhoodComplianceService';

export class NeighborhoodComplianceController {
  private service: NeighborhoodComplianceService;

  constructor() {
    this.service = new NeighborhoodComplianceService();
  }

  /**
   * Generate a neighborhood and compliance report
   */
  generateReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { propertyAddress, zipCode, llm_provider } = req.body;

      // Validate required fields
      if (!propertyAddress) {
        res.status(400).json({ error: 'Property address is required' });
        return;
      }

      // Validate LLM provider
      if (llm_provider !== 'openai' && llm_provider !== 'claude') {
        res.status(400).json({ error: 'Invalid LLM provider. Must be "openai" or "claude"' });
        return;
      }

      // Generate the report
      const report = await this.service.generateReport(propertyAddress, zipCode, llm_provider);

      // Return the report
      res.status(200).json(report);
    } catch (error) {
      console.error('Error generating neighborhood & compliance report:', error);
      res.status(500).json({ 
        error: 'Failed to generate neighborhood & compliance report',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}
