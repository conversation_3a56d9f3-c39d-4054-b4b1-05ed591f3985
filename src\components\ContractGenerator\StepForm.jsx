import React, { useState } from 'react';
import { FaArrowLeft, FaArrowRight, FaLightbulb, FaSpinner } from 'react-icons/fa';
import './ContractGenerator.css';

const StepForm = ({
  currentStep,
  formData,
  updateFormData,
  updateArrayField,
  onNext,
  onPrevious
}) => {
  // State for AI suggestion loading
  const [isLoadingAiSuggestion, setIsLoadingAiSuggestion] = useState(false);

  // Function to handle input changes
  const handleInputChange = (section, field, value) => {
    updateFormData(section, field, value);
  };

  // Function to handle checkbox changes for fixtures
  const handleCheckboxChange = (fixture) => {
    const currentFixtures = [...formData.fixturesIncluded];
    if (currentFixtures.includes(fixture)) {
      // Remove fixture if already selected
      const updatedFixtures = currentFixtures.filter(item => item !== fixture);
      updateArrayField('fixturesIncluded', updatedFixtures);
    } else {
      // Add fixture if not selected
      update<PERSON><PERSON><PERSON><PERSON><PERSON>('fixturesIncluded', [...currentFixtures, fixture]);
    }
  };

  // Function to get AI suggestions
  const getAiSuggestion = async (field) => {
    setIsLoadingAiSuggestion(true);

    try {
      // Simulate AI suggestion (in a real app, this would call the backend)
      await new Promise(resolve => setTimeout(resolve, 1500));

      switch (field) {
        case 'earnestMoney':
          // Calculate 5% of purchase price
          if (formData.financials.purchasePrice) {
            const suggestedEarnestMoney = Math.round(parseFloat(formData.financials.purchasePrice) * 0.05);
            updateFormData('financials', 'earnestMoney', suggestedEarnestMoney.toString());
          }
          break;

        case 'closingDate':
          // Suggest a date 30-45 days from today
          const today = new Date();
          const suggestedDate = new Date(today.setDate(today.getDate() + 45));
          const formattedDate = suggestedDate.toISOString().split('T')[0];
          updateFormData('financials', 'closingDate', formattedDate);
          break;

        case 'fixtures':
          // Suggest common fixtures
          const suggestedFixtures = ['Refrigerator', 'Oven/Range', 'Dishwasher', 'Microwave', 'Window Treatments'];
          updateArrayField('fixturesIncluded', suggestedFixtures);
          break;

        default:
          break;
      }
    } catch (error) {
      console.error('Error getting AI suggestion:', error);
    } finally {
      setIsLoadingAiSuggestion(false);
    }
  };

  // Function to validate current step before proceeding
  const validateStep = () => {
    switch (currentStep) {
      case 1: // Buyer's Name
        return formData.buyer.fullName.trim() !== '';
      case 3: // Property Address
        return formData.property.address.trim() !== '';
      case 5: // Purchase Price
        return formData.financials.purchasePrice !== '' &&
               parseFloat(formData.financials.purchasePrice) > 0;
      default:
        return true;
    }
  };

  // Function to handle next button click with validation
  const handleNext = () => {
    if (validateStep()) {
      onNext();
    } else {
      alert('Please fill in all required fields before proceeding.');
    }
  };

  // Render different form fields based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="step-content">
            <h2 className="step-title">Let's start with the basics!</h2>
            <div className="form-group">
              <label htmlFor="buyerName">Who is the buyer for this property?</label>
              <input
                type="text"
                id="buyerName"
                value={formData.buyer.fullName}
                onChange={(e) => handleInputChange('buyer', 'fullName', e.target.value)}
                placeholder="Enter buyer's full name"
                required
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="step-content">
            <h2 className="step-title">Seller Information</h2>
            <div className="form-group">
              <label htmlFor="sellerName">Great! Do you know the seller's full name?</label>
              <input
                type="text"
                id="sellerName"
                value={formData.seller.fullName}
                onChange={(e) => handleInputChange('seller', 'fullName', e.target.value)}
                placeholder="Enter seller's full name"
              />
              <p className="helper-text">If unsure, you can leave this blank, and we'll fill it in later.</p>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="step-content">
            <h2 className="step-title">Property Details</h2>
            <div className="form-group">
              <label htmlFor="propertyAddress">Where is the property located?</label>
              <textarea
                id="propertyAddress"
                value={formData.property.address}
                onChange={(e) => handleInputChange('property', 'address', e.target.value)}
                placeholder="Enter full property address"
                required
                rows={3}
              />
            </div>
            <div className="form-group">
              <label htmlFor="propertyUnit">Unit Number (if applicable)</label>
              <input
                type="text"
                id="propertyUnit"
                value={formData.property.unit}
                onChange={(e) => handleInputChange('property', 'unit', e.target.value)}
                placeholder="e.g., Apt 4B"
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="step-content">
            <h2 className="step-title">Parking Information</h2>
            <div className="form-group">
              <label className="toggle-label">Does this property include parking?</label>
              <div className="toggle-container">
                <button
                  type="button"
                  className={`toggle-button ${formData.property.parkingIncluded ? 'active' : ''}`}
                  onClick={() => handleInputChange('property', 'parkingIncluded', true)}
                >
                  Yes
                </button>
                <button
                  type="button"
                  className={`toggle-button ${!formData.property.parkingIncluded ? 'active' : ''}`}
                  onClick={() => handleInputChange('property', 'parkingIncluded', false)}
                >
                  No
                </button>
              </div>
            </div>

            {formData.property.parkingIncluded && (
              <div className="form-group">
                <label htmlFor="parkingDetails">Please describe the parking</label>
                <input
                  type="text"
                  id="parkingDetails"
                  value={formData.property.parkingDetails}
                  onChange={(e) => handleInputChange('property', 'parkingDetails', e.target.value)}
                  placeholder="e.g., Assigned spot #12, Garage space"
                />
              </div>
            )}
          </div>
        );

      case 5:
        return (
          <div className="step-content">
            <h2 className="step-title">Financial Details</h2>
            <div className="form-group">
              <label htmlFor="purchasePrice">What is the agreed purchase price?</label>
              <div className="input-with-prefix">
                <span className="input-prefix">$</span>
                <input
                  type="number"
                  id="purchasePrice"
                  value={formData.financials.purchasePrice}
                  onChange={(e) => handleInputChange('financials', 'purchasePrice', e.target.value)}
                  placeholder="Enter amount"
                  min="0"
                  required
                />
              </div>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="step-content">
            <h2 className="step-title">Earnest Money Deposit</h2>
            <div className="form-group">
              <label htmlFor="earnestMoney">How much earnest money will be deposited?</label>
              <div className="input-with-prefix">
                <span className="input-prefix">$</span>
                <input
                  type="number"
                  id="earnestMoney"
                  value={formData.financials.earnestMoney}
                  onChange={(e) => handleInputChange('financials', 'earnestMoney', e.target.value)}
                  placeholder="Enter amount"
                  min="0"
                />
              </div>
              <p className="helper-text">Typically 5% of the purchase price. Leave blank to let us suggest it!</p>

              <button
                type="button"
                className="btn-suggest"
                onClick={() => getAiSuggestion('earnestMoney')}
                disabled={isLoadingAiSuggestion}
              >
                {isLoadingAiSuggestion ? (
                  <>
                    <FaSpinner className="spinner" /> Calculating...
                  </>
                ) : (
                  <>
                    <FaLightbulb /> Suggest for me
                  </>
                )}
              </button>
            </div>
          </div>
        );

      case 7:
        return (
          <div className="step-content">
            <h2 className="step-title">Closing Date</h2>
            <div className="form-group">
              <label htmlFor="closingDate">When is the closing date scheduled?</label>
              <input
                type="date"
                id="closingDate"
                value={formData.financials.closingDate}
                onChange={(e) => handleInputChange('financials', 'closingDate', e.target.value)}
                min={new Date().toISOString().split('T')[0]} // Today's date as minimum
              />

              <button
                type="button"
                className="btn-suggest"
                onClick={() => getAiSuggestion('closingDate')}
                disabled={isLoadingAiSuggestion}
              >
                {isLoadingAiSuggestion ? (
                  <>
                    <FaSpinner className="spinner" /> Calculating...
                  </>
                ) : (
                  <>
                    <FaLightbulb /> Suggest a date
                  </>
                )}
              </button>
            </div>
          </div>
        );

      case 8:
        return (
          <div className="step-content">
            <h2 className="step-title">HOA / Condo Association</h2>
            <div className="form-group">
              <label className="toggle-label">Is there a Homeowners Association (HOA) or Condo Association?</label>
              <div className="toggle-container">
                <button
                  type="button"
                  className={`toggle-button ${formData.associations.hoa ? 'active' : ''}`}
                  onClick={() => handleInputChange('associations', 'hoa', true)}
                >
                  Yes
                </button>
                <button
                  type="button"
                  className={`toggle-button ${!formData.associations.hoa ? 'active' : ''}`}
                  onClick={() => handleInputChange('associations', 'hoa', false)}
                >
                  No
                </button>
              </div>
            </div>

            {formData.associations.hoa && (
              <div className="form-group">
                <label htmlFor="monthlyFees">What are the monthly association fees?</label>
                <div className="input-with-prefix">
                  <span className="input-prefix">$</span>
                  <input
                    type="number"
                    id="monthlyFees"
                    value={formData.associations.monthlyFees}
                    onChange={(e) => handleInputChange('associations', 'monthlyFees', e.target.value)}
                    placeholder="Enter amount"
                    min="0"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 9:
        return (
          <div className="step-content">
            <h2 className="step-title">Fixtures and Appliances</h2>
            <div className="form-group">
              <label>Which fixtures and appliances are included in the sale?</label>

              <div className="checkbox-grid">
                {['Refrigerator', 'Oven/Range', 'Dishwasher', 'Washer/Dryer', 'Microwave', 'Ceiling Fans', 'Window Treatments', 'Light Fixtures'].map((fixture) => (
                  <div key={fixture} className="checkbox-item">
                    <input
                      type="checkbox"
                      id={`fixture-${fixture}`}
                      checked={formData.fixturesIncluded.includes(fixture)}
                      onChange={() => handleCheckboxChange(fixture)}
                    />
                    <label htmlFor={`fixture-${fixture}`}>{fixture}</label>
                  </div>
                ))}
              </div>

              <button
                type="button"
                className="btn-suggest"
                onClick={() => getAiSuggestion('fixtures')}
                disabled={isLoadingAiSuggestion}
              >
                {isLoadingAiSuggestion ? (
                  <>
                    <FaSpinner className="spinner" /> Suggesting...
                  </>
                ) : (
                  <>
                    <FaLightbulb /> Suggest common fixtures
                  </>
                )}
              </button>
            </div>
          </div>
        );

      case 10:
        return (
          <div className="step-content">
            <h2 className="step-title">Additional Terms</h2>
            <div className="form-group">
              <label htmlFor="additionalTerms">Any additional terms or conditions you'd like to include?</label>
              <textarea
                id="additionalTerms"
                value={formData.additionalTerms}
                onChange={(e) => updateArrayField('additionalTerms', e.target.value)}
                placeholder="e.g., Seller to repaint living room before closing."
                rows={4}
              />
              <p className="helper-text">Optional: Include any special requests or conditions not covered elsewhere.</p>
            </div>
          </div>
        );

      case 11:
        return (
          <div className="step-content">
            <h2 className="step-title">Buyer Contact Information</h2>
            <div className="form-group">
              <label htmlFor="buyerEmail">What's your email address?</label>
              <input
                type="email"
                id="buyerEmail"
                value={formData.buyer.email}
                onChange={(e) => handleInputChange('buyer', 'email', e.target.value)}
                placeholder="Enter your email address"
              />
              <p className="helper-text">We'll use this to send you a copy of the contract.</p>
            </div>
          </div>
        );

      case 12:
        return (
          <div className="step-content">
            <h2 className="step-title">Ready to Generate Your Contract</h2>
            <p className="final-step-text">
              You've completed all the questions! In the next step, you'll see a summary of all your answers for review before generating the contract.
            </p>
            <p className="final-step-text">
              Click "Next" to review your information.
            </p>
          </div>
        );

      default:
        return (
          <div className="step-content">
            <h2 className="step-title">Additional steps will be implemented here</h2>
            <p>This is a placeholder for future steps in the contract generation process.</p>
          </div>
        );
    }
  };

  return (
    <div className="step-form">
      <div className="step-indicator">Step {currentStep} of 12</div>

      {renderStepContent()}

      <div className="step-buttons">
        {currentStep > 1 && (
          <button
            type="button"
            className="btn-previous"
            onClick={onPrevious}
          >
            <FaArrowLeft /> Back
          </button>
        )}

        <button
          type="button"
          className="btn-next"
          onClick={handleNext}
        >
          Next <FaArrowRight />
        </button>
      </div>
    </div>
  );
};

export default StepForm;
