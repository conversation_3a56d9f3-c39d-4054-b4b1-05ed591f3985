import React, { useState, useEffect } from 'react';
import { FaFileDownload, FaEnvelope, FaCheckCircle, FaSpinner } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { getApiUrl, USE_MOCK_IN_PRODUCTION, USE_MOCK_IN_DEVELOPMENT } from '../../utils/apiConfig';
import './ContractGenerator.css';

const SuccessScreen = ({ pdfUrl }) => {
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [fullPdfUrl, setFullPdfUrl] = useState('');

  useEffect(() => {
    // If the pdfUrl is a relative path, convert it to a full URL
    if (pdfUrl) {
      if (pdfUrl.startsWith('http')) {
        // Already a full URL
        setFullPdfUrl(pdfUrl);
        console.log(`Using full PDF URL: ${pdfUrl}`);
      } else {
        // Check if we're using the mock implementation
        const isProduction = window.location.hostname === 'aidigitalrealtor.com' ||
                            window.location.hostname === 'www.aidigitalrealtor.com';
        const isDevelopment = window.location.hostname === 'localhost' ||
                             window.location.hostname === '127.0.0.1';

        // Use mock implementation in production or development if configured
        if ((isProduction && USE_MOCK_IN_PRODUCTION) || (isDevelopment && USE_MOCK_IN_DEVELOPMENT)) {
          // In production or development with mock implementation, use the sample contract
          const sampleUrl = '/sample-contract.pdf';
          setFullPdfUrl(sampleUrl);
          console.log(`Using sample contract in ${isProduction ? 'production' : 'development'}: ${sampleUrl}`);
        } else {
          // Relative path, convert to full URL
          const baseUrl = pdfUrl.startsWith('/')
            ? getApiUrl('') // Use the API base URL
            : getApiUrl('/'); // Use the API base URL with a trailing slash

          const url = pdfUrl.startsWith('/')
            ? `${baseUrl}${pdfUrl.substring(1)}` // Remove leading slash to avoid double slashes
            : `${baseUrl}${pdfUrl}`;

          setFullPdfUrl(url);
          console.log(`Converted PDF URL: ${pdfUrl} -> ${url}`);
        }
      }
    }
  }, [pdfUrl]);

  // Function to handle email sending
  const handleEmailContract = async () => {
    setIsSendingEmail(true);

    try {
      if (!fullPdfUrl) {
        throw new Error('Contract not ready yet. Please wait for the contract to load.');
      }

      // In a real implementation, this would call the backend API to send an email
      // For now, we'll simulate the email sending
      console.log(`Sending email with contract URL: ${fullPdfUrl}`);

      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 1500));
      setEmailSent(true);
    } catch (error) {
      console.error('Error sending email:', error);
      alert(`Failed to send email: ${error.message || 'Please try again.'}`);
    } finally {
      setIsSendingEmail(false);
    }
  };
  return (
    <div className="success-screen">
      <div className="success-icon">
        <FaCheckCircle />
      </div>

      <h2 className="success-title">🎉 Your contract is ready!</h2>
      <p className="success-message">
        Your Chicago Association of REALTORS® Condominium Purchase Contract has been successfully generated.
      </p>

      <div className="success-actions">
        {fullPdfUrl ? (
          <a
            href={fullPdfUrl}
            download="Chicago_Condo_Purchase_Contract.pdf"
            className="btn-download"
            target="_blank"
            rel="noopener noreferrer"
          >
            <FaFileDownload /> Download Contract
          </a>
        ) : (
          <button className="btn-download" disabled>
            <FaSpinner className="spinner" /> Loading Contract...
          </button>
        )}

        <button
          className="btn-email"
          onClick={handleEmailContract}
          disabled={!fullPdfUrl || isSendingEmail || emailSent}
        >
          {isSendingEmail ? (
            <>
              <FaSpinner className="spinner" /> Sending...
            </>
          ) : emailSent ? (
            <>
              <FaCheckCircle /> Email Sent
            </>
          ) : !fullPdfUrl ? (
            <>
              <FaSpinner className="spinner" /> Loading...
            </>
          ) : (
            <>
              <FaEnvelope /> Email Contract
            </>
          )}
        </button>
      </div>

      <div className="success-next-steps">
        <h3>Next Steps</h3>
        <ol>
          <li>Review the contract with all parties</li>
          <li>Sign the contract (digital or physical signatures)</li>
          <li>Submit earnest money as specified</li>
          <li>Schedule property inspections</li>
        </ol>
      </div>

      <div className="success-footer">
        <Link to="/" className="btn-home">
          Return to Home
        </Link>

        <Link to="/contract-generator" className="btn-new-contract">
          Create Another Contract
        </Link>
      </div>
    </div>
  );
};

export default SuccessScreen;
