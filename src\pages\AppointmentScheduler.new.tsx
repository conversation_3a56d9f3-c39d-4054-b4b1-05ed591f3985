import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaCalendarAlt, FaA<PERSON><PERSON><PERSON><PERSON>, FaSpinner } from 'react-icons/fa';
import { useApp } from '../context/SimpleAppContext';
import axios from 'axios';
import 'react-calendar/dist/Calendar.css';
import '../calendar-styles.css';
import appointmentService from '../services/appointmentService';

// Define types
interface AppointmentFormData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  propertyAddress: string;
  propertyType: string;
  preferredDate: string;
  preferredTime: string;
  additionalNotes: string;
}

interface ScheduledAppointment {
  id: string;
  clientName: string;
  clientEmail: string;
  date: string;
  time: string;
  propertyAddress: string;
  status: string;
  notes: string;
  googleCalendarEventId?: string;
}

interface CreateAppointmentData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  propertyAddress: string;
  propertyType: string;
  date: string;
  time: string;
  status: string;
  notes: string;
  userId: string;
}

interface AppointmentResponse {
  suggestedSlots: {
    date: string;
    slots: string[];
  }[];
  preparationTips: string[];
  followUpReminder: string;
}

const AppointmentScheduler: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const [isLoading, setIsLoading] = useState(false);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState('');
  
  // Initialize form data
  const [formData, setFormData] = useState<AppointmentFormData>({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    propertyAddress: '',
    propertyType: 'House',
    preferredDate: '',
    preferredTime: '',
    additionalNotes: ''
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    // Validate form
    if (!formData.clientName || !formData.clientEmail || !formData.propertyAddress || !formData.preferredDate || !formData.preferredTime) {
      setError('Please fill in all required fields');
      return;
    }
    
    setIsBooking(true);
    
    try {
      // Create appointment data
      const appointmentData: CreateAppointmentData = {
        clientName: formData.clientName,
        clientEmail: formData.clientEmail,
        clientPhone: formData.clientPhone,
        propertyAddress: formData.propertyAddress,
        propertyType: formData.propertyType,
        date: formData.preferredDate,
        time: formData.preferredTime,
        status: 'pending',
        notes: formData.additionalNotes,
        userId: state.user?.id || 'guest'
      };
      
      // Save appointment
      const savedAppointment = await appointmentService.createClientAppointment(appointmentData);
      
      alert('Appointment scheduled successfully!');
      
      // Reset form
      setFormData({
        clientName: '',
        clientEmail: '',
        clientPhone: '',
        propertyAddress: '',
        propertyType: 'House',
        preferredDate: '',
        preferredTime: '',
        additionalNotes: ''
      });
      
    } catch (error) {
      console.error('Error booking appointment:', error);
      setError('Failed to book appointment. Please try again.');
    } finally {
      setIsBooking(false);
    }
  };

  // Reset the form
  const handleReset = () => {
    setFormData({
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      propertyAddress: '',
      propertyType: 'House',
      preferredDate: '',
      preferredTime: '',
      additionalNotes: ''
    });
    setError('');
  };

  // Go back to previous page
  const handleBack = () => {
    navigate(-1);
  };

  // Available time slots
  const timeSlots = [
    '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM',
    '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM'
  ];

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="flex items-center mb-6">
        <button 
          onClick={handleBack}
          className="mr-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300"
        >
          <FaArrowLeft className="mr-2" /> Back
        </button>
        <h1 className="text-3xl font-bold">Appointment Scheduler</h1>
      </div>

      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <FaCalendarAlt className="mr-2 text-blue-600" /> Schedule an Appointment
        </h2>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 mb-1">Client Name *</label>
              <input
                type="text"
                name="clientName"
                value={formData.clientName}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-gray-700 mb-1">Client Email *</label>
              <input
                type="email"
                name="clientEmail"
                value={formData.clientEmail}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-gray-700 mb-1">Client Phone</label>
              <input
                type="tel"
                name="clientPhone"
                value={formData.clientPhone}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-gray-700 mb-1">Property Type</label>
              <select
                name="propertyType"
                value={formData.propertyType}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="House">House</option>
                <option value="Condo">Condo</option>
                <option value="Apartment">Apartment</option>
                <option value="Townhouse">Townhouse</option>
                <option value="Land">Land</option>
                <option value="Commercial">Commercial</option>
              </select>
            </div>
            
            <div>
              <label className="block text-gray-700 mb-1">Property Address *</label>
              <input
                type="text"
                name="propertyAddress"
                value={formData.propertyAddress}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-gray-700 mb-1">Preferred Date *</label>
              <input
                type="date"
                name="preferredDate"
                value={formData.preferredDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-gray-700 mb-1">Preferred Time *</label>
              <select
                name="preferredTime"
                value={formData.preferredTime}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select a time</option>
                {timeSlots.map((time) => (
                  <option key={time} value={time}>
                    {time}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Additional Notes</label>
            <textarea
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleInputChange}
              rows={4}
              className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>
          
          <div className="flex justify-center space-x-4 mt-6">
            <button
              type="button"
              onClick={handleReset}
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-6 rounded-lg transition-all duration-300"
            >
              Reset
            </button>
            <button
              type="submit"
              disabled={isBooking}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg flex items-center transition-all duration-300"
            >
              {isBooking ? (
                <>
                  <FaSpinner className="animate-spin mr-2" /> Booking...
                </>
              ) : (
                'Book Appointment'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AppointmentScheduler;
