// Environment configuration
// API keys are removed for security - they will be injected at build time
(function() {
  // Check if we're in development mode
  const isDevelopment =
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1';

  window.__ENV__ = {
    VITE_OPENAI_API_KEY: "", // Will be injected from environment variables
    VITE_CLAUDE_API_KEY: "", // Will be injected from environment variables
    VITE_GOOGLE_API_KEY: "", // Will be injected from environment variables
    VITE_GOOGLE_CLIENT_ID: "", // Will be injected from environment variables
    VITE_WALKSCORE_API_KEY: "", // Will be injected from environment variables
    VITE_RAPIDAPI_SCHOOLDIGGER_KEY: "", // Will be injected from environment variables
    VITE_CENSUS_API_KEY: "", // Will be injected from environment variables
    VITE_GOOGLE_MAPS_API_KEY: "", // Will be injected from environment variables
    VITE_BACKEND_URL: isDevelopment ? "http://localhost:5000" : "https://digital-realtor-api.onrender.com",
    VITE_FRONTEND_URL: isDevelopment ? "http://localhost:5174" : "https://aidigitalrealtor.com",
    VITE_CLARITY_ID: "r7yjq54tfv",
    NODE_ENV: isDevelopment ? "development" : "production"
  };

  // Debug logging for development
  if (isDevelopment) {
    console.log('%c[DEV] Environment Configuration Loaded', 'color: #00b9ff; font-weight: bold');
    console.log('Backend URL:', window.__ENV__.VITE_BACKEND_URL);
    console.log('Frontend URL:', window.__ENV__.VITE_FRONTEND_URL);
  }
})();
