/* Contact.module.css */

.contactContainer {
  padding: 2rem 1rem;
}

@media (min-width: 768px) {
  .contactContainer {
    padding: 3rem 2rem;
  }
}

.contactCard {
  background: rgba(13, 21, 38, 0.7);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 255, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  padding: 24px;
}

.contactCard:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.25);
}

.contactCardHeader {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.contactCardTitle {
  font-size: 1.625rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.875rem;
}

.contactCardDivider {
  flex-grow: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin-left: 0.75rem;
}

@media (min-width: 768px) {
  .contactCard {
    padding: 32px;
  }

  .contactCardHeader {
    margin-bottom: 32px;
  }
}

.formGrid {
  display: grid;
  gap: 24px;
}

.formRow {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .formRow.cols2 {
    grid-template-columns: 1fr 1fr;
  }
}

.formGroup {
  margin-bottom: 20px;
}

.formLabel {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  margin-bottom: 0.625rem;
  line-height: 1.4;
}

.formLabel .required {
  color: var(--accent-cyan);
  margin-left: 0.375rem;
  font-weight: bold;
}

.formLabel .optional {
  color: var(--primary-400);
  font-weight: normal;
  margin-left: 0.375rem;
  font-size: 0.75rem;
  opacity: 0.9;
}

.formInput {
  width: 100%;
  padding: 0.875rem 1rem;
  min-height: 3.25rem;
  background-color: rgba(42, 63, 102, 0.3);
  border: 1px solid var(--primary-700);
  border-radius: 0.5rem;
  color: white;
  font-size: 1rem;
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
  appearance: none;
}

.formInput:focus {
  outline: none;
  border-color: var(--accent-cyan);
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.formInput::placeholder {
  color: var(--primary-400);
  opacity: 0.8;
}

/* Select styling */
select.formInput {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2300ffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

select.formInput option {
  background-color: rgba(13, 21, 38, 1);
  color: white;
}

.formTextarea {
  min-height: 140px;
  resize: vertical;
  padding-top: 1rem;
  padding-bottom: 1rem;
  line-height: 1.6;
}

.formSubmitButton {
  background: linear-gradient(to right, #00b9ff, #9900ff);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 9999px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  min-height: 3.25rem;
  cursor: pointer;
  font-size: 1rem;
}

.formSubmitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.formSubmitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.contactInfoItem {
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 0.5rem;
}

.contactInfoItem:hover {
  transform: translateX(8px);
  background-color: rgba(0, 255, 255, 0.05);
}

.contactInfoIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: rgba(13, 21, 38, 0.5);
  color: var(--accent-cyan);
  border-radius: 50%;
  margin-right: 16px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.contactInfoItem:hover .contactInfoIcon {
  border-color: rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.contactInfoContent {
  flex: 1;
}

.contactInfoTitle {
  font-weight: 600;
  color: var(--primary-200);
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.contactInfoItem:hover .contactInfoTitle {
  color: var(--accent-cyan);
}

.contactInfoValue {
  color: var(--primary-300);
  font-size: 0.875rem;
}

.contactInfoValue a {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contactInfoValue a:hover {
  color: var(--accent-cyan);
  text-decoration: underline;
}

.successMessage {
  background-color: rgba(13, 21, 38, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.5);
  color: var(--accent-cyan);
  padding: 16px;
  border-radius: 0.5rem;
  margin-bottom: 24px;
  animation: fadeIn 0.5s ease;
}

.successIcon {
  color: var(--accent-cyan);
  margin-right: 8px;
  font-size: 1.25rem;
}

.successTitle {
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 8px;
}

.successMessage p {
  color: var(--primary-200);
  margin-top: 8px;
}

.errorMessage {
  background-color: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  color: #ef4444;
  padding: 16px;
  border-radius: 0.5rem;
  margin-bottom: 24px;
  animation: fadeIn 0.5s ease;
}

.errorIcon {
  color: #ef4444;
  margin-right: 8px;
  font-size: 1.25rem;
}

.errorTitle {
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 8px;
}

.errorMessage p {
  color: var(--primary-200);
  margin-top: 8px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .contactCard {
    padding: 20px;
  }

  .formGrid {
    gap: 20px;
  }

  .formRow {
    gap: 20px;
  }

  .formGroup {
    margin-bottom: 16px;
  }

  .formLabel {
    margin-bottom: 8px;
  }

  .formInput {
    padding: 10px 14px;
    min-height: 44px;
  }

  .formTextarea {
    min-height: 120px;
    padding: 14px;
  }
}

@media (min-width: 768px) {
  .contactCard {
    padding: 32px;
  }
}
