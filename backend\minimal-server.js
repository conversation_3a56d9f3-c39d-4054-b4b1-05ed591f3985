const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

console.log('🚀 Starting Minimal Digital Realtor API for testing');
console.log(`📍 Port: ${PORT}`);

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:5174',
      'https://aidigitalrealtor.com',
      'https://www.aidigitalrealtor.com'
    ];

    // In development, allow all localhost origins
    if (process.env.NODE_ENV !== 'production' && origin && origin.includes('localhost')) {
      return callback(null, true);
    }

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Length', 'Content-Type'],
  credentials: true,
  optionsSuccessStatus: 200,
  preflightContinue: false,
  maxAge: 86400 // 24 hours
};

// Middleware
app.use(cors(corsOptions));

// Add a general CORS middleware for all routes
app.use((req, res, next) => {
  // In development, allow all origins
  if (process.env.NODE_ENV !== 'production') {
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('📋 Health check requested');
  res.status(200).json({ status: 'ok', message: 'Minimal server is running' });
});

// Mock endpoint to extract appointment data from natural language
app.post('/api/claude/extract-appointment-data', async (req, res) => {
  try {
    const { naturalLanguageInput } = req.body;

    if (!naturalLanguageInput) {
      return res.status(400).json({ error: 'Natural language input is required' });
    }

    console.log('📅 Processing appointment extraction request (MOCK)');
    console.log('Input:', naturalLanguageInput.substring(0, 100) + '...');

    // Mock extracted data for testing
    const extractedData = {
      clientName: "John Smith",
      clientEmail: "<EMAIL>",
      clientPhone: "************",
      propertyAddress: "123 Main Street, Anytown, CA 90210",
      propertyType: "House",
      date: "2024-01-15",
      time: "2:00 PM",
      notes: "Property viewing appointment"
    };

    console.log('✅ Successfully extracted appointment data (MOCK)');
    return res.status(200).json({ extractedData });
  } catch (error) {
    console.error('Error extracting appointment data:', error);
    return res.status(500).json({ error: 'Failed to extract appointment data. Please try again.' });
  }
});

// Catch-all route for debugging
app.use('*', (req, res) => {
  console.log(`❓ Unknown route requested: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ error: 'Route not found', method: req.method, url: req.originalUrl });
});

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 Minimal Digital Realtor API server is running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/health`);
  console.log(`🤖 Mock appointment extraction: http://localhost:${PORT}/api/claude/extract-appointment-data`);
  console.log('🔧 This is a minimal server for testing Google Calendar integration');
});
