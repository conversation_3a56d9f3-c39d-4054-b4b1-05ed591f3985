# Render Deployment Script for Digital Realtor Backend

# Create a temporary directory for deployment files
$tempDir = "render-deploy-temp"
if (Test-Path $tempDir) {
    Remove-Item -Recurse -Force $tempDir
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

Write-Host "Preparing backend files for deployment..."

# Copy backend files to the temporary directory
Copy-Item -Recurse -Path "backend\*" -Destination $tempDir

# Copy package.json and package-lock.json
Copy-Item -Path "package.json" -Destination $tempDir
Copy-Item -Path "package-lock.json" -Destination $tempDir -ErrorAction SilentlyContinue

# Create a deployment package
$deployPackage = "digital-realtor-backend-render-deploy.zip"
Write-Host "Creating deployment package: $deployPackage"

# Remove existing package if it exists
if (Test-Path $deployPackage) {
    Remove-Item -Force $deployPackage
}

# Create the zip file
Compress-Archive -Path "$tempDir\*" -DestinationPath $deployPackage

# Clean up temporary directory
Remove-Item -Recurse -Force $tempDir

Write-Host "`nDeployment package created successfully: $deployPackage"
Write-Host "`nTo deploy to Render:"
Write-Host "1. Log in to your Render dashboard (https://dashboard.render.com)"
Write-Host "2. Navigate to your backend service (or create a new one)"
Write-Host "3. Go to the 'Manual Deploy' section"
Write-Host "4. Upload the $deployPackage file"
Write-Host "5. Click 'Deploy' to update your backend"
Write-Host "`nAlternatively, you can deploy directly from your GitHub repository."
Write-Host "See RENDER_DEPLOYMENT.md for detailed instructions."
