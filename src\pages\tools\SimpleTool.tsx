import { useNavigate, use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { useApp } from '../../context/SimpleAppContext';
import { useState } from 'react';
import InspectionReportGenerator from './InspectionReportGenerator';
import TestInspectionComponent from './TestInspectionComponent';
import SimpleInspectionReportGenerator from './SimpleInspectionReportGenerator';
import BasicInspectionTool from './BasicInspectionTool';

const SimpleTool = () => {
  const navigate = useNavigate();
  const { toolId } = useParams<{ toolId: string }>();
  const { state } = useApp();
  const [isLoading, setIsLoading] = useState(false);

  // Tool information based on toolId
  const getToolInfo = () => {
    switch (toolId) {
      case 'listing':
        return {
          title: 'Listing Generator',
          description: 'Create compelling property listings with AI-generated descriptions and marketing tips.',
          icon: (
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          ),
          color: 'from-blue-500 to-blue-600',
        };
      case 'appointment':
        return {
          title: 'Appointment Scheduler',
          description: 'Get AI recommendations for scheduling client appointments and viewing preparations.',
          icon: (
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          ),
          color: 'from-purple-500 to-purple-600',
        };
      case 'analysis':
        return {
          title: 'Competitive Analysis',
          description: 'Generate detailed market analysis and competitive property comparisons.',
          icon: (
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
            </svg>
          ),
          color: 'from-blue-500 to-purple-500',
        };
      case 'inspection':
        return {
          title: 'Inspection Report Generator',
          description: 'Analyze inspection findings and generate client-friendly summary reports.',
          icon: (
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
          ),
          color: 'from-purple-500 to-blue-500',
        };
      case 'lease':
        return {
          title: 'Lease Analysis',
          description: 'Review lease agreements and identify potential concerns or negotiation points.',
          icon: (
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          ),
          color: 'from-blue-600 to-purple-600',
        };
      default:
        return {
          title: 'Unknown Tool',
          description: 'Tool not found.',
          icon: (
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          color: 'from-gray-500 to-gray-600',
        };
    }
  };

  const toolInfo = getToolInfo();

  const handleGenerateDemo = async () => {
    setIsLoading(true);
    // Simulate API call with a delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
  };

  // Always render the BasicInspectionTool for testing
  return <BasicInspectionTool />;

  return (
    <div className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        {/* Header with back button */}
        <div className="mb-8 animate-fade-in">
          <button
            onClick={() => navigate('/agent')}
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors mb-4 group border border-transparent hover:border-blue-600 px-3 py-1 rounded-lg"
          >
            <svg className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Dashboard
          </button>
          <h1 className="text-3xl md:text-4xl font-bold text-dark-800 mb-2">{toolInfo.title}</h1>
          <p className="text-dark-500 max-w-3xl">
            {toolInfo.description}
          </p>
        </div>

        {/* Tool Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Tool Area */}
          <div className="lg:col-span-2 animate-slide-in-up">
            <div className="bg-white rounded-xl shadow-md p-8 mb-8">
              <div className="flex items-center mb-6">
                <div className={`w-12 h-12 rounded-full bg-gradient-to-br ${toolInfo.color} flex items-center justify-center text-white mr-4`}>
                  {toolInfo.icon}
                </div>
                <h2 className="text-2xl font-bold text-dark-800">Generate Content</h2>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-dark-700 font-medium mb-2">Property Type</label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                    <option value="">Select property type</option>
                    <option value="single-family">Single Family Home</option>
                    <option value="condo">Condominium</option>
                    <option value="townhouse">Townhouse</option>
                    <option value="multi-family">Multi-Family</option>
                    <option value="land">Land</option>
                  </select>
                </div>

                <div>
                  <label className="block text-dark-700 font-medium mb-2">Property Details</label>
                  <textarea
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    rows={5}
                    placeholder="Enter property details (bedrooms, bathrooms, square footage, special features, etc.)"
                  ></textarea>
                </div>

                <div>
                  <label className="block text-dark-700 font-medium mb-2">Target Audience</label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                    <option value="">Select target audience</option>
                    <option value="first-time">First-time Homebuyers</option>
                    <option value="luxury">Luxury Buyers</option>
                    <option value="investors">Investors</option>
                    <option value="retirees">Retirees</option>
                    <option value="families">Families</option>
                  </select>
                </div>

                <div>
                  <label className="block text-dark-700 font-medium mb-2">Tone</label>
                  <div className="flex flex-wrap gap-3">
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                      <input type="radio" name="tone" className="mr-2" />
                      Professional
                    </label>
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                      <input type="radio" name="tone" className="mr-2" />
                      Conversational
                    </label>
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                      <input type="radio" name="tone" className="mr-2" />
                      Enthusiastic
                    </label>
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                      <input type="radio" name="tone" className="mr-2" />
                      Luxury
                    </label>
                  </div>
                </div>

                <div className="pt-4">
                  <button
                    onClick={handleGenerateDemo}
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-lg font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-1 flex justify-center items-center border border-transparent"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generating...
                      </>
                    ) : (
                      <>Generate {toolInfo.title.split(' ')[0]} Content</>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-8">
            {/* Tips Card */}
            <div className="bg-white rounded-xl shadow-md p-6 animate-fade-in delay-100">
              <h3 className="text-xl font-bold text-dark-800 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Tips for Best Results
              </h3>
              <ul className="space-y-3 text-dark-600">
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Be specific about unique property features</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Include neighborhood amenities and benefits</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Choose a tone that matches your target audience</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Always review and personalize AI-generated content</span>
                </li>
              </ul>
            </div>

            {/* Recent Generations */}
            <div className="bg-white rounded-xl shadow-md p-6 animate-fade-in delay-200">
              <h3 className="text-xl font-bold text-dark-800 mb-4">Recent Generations</h3>
              <p className="text-dark-500 text-sm italic">Your recent generations will appear here</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleTool;
