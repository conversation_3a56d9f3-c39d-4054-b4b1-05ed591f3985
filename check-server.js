// This script checks if the server is running
import fetch from 'node-fetch';

const PORT = process.env.PORT || 5000;
const API_URL = `http://localhost:${PORT}/api`;

async function checkServer() {
  try {
    console.log(`Checking server at: ${API_URL}`);
    const response = await fetch(API_URL);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Server is running!');
      console.log('Response:', data);
      return true;
    } else {
      console.error(`Server returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error(`Error connecting to server: ${error.message}`);
    return false;
  }
}

// Check the contract generator endpoint
async function checkContractGenerator() {
  try {
    const url = `http://localhost:${PORT}/api/contract-generator`;
    console.log(`Checking contract generator endpoint at: ${url}`);
    
    // We'll just do a GET request to see if the endpoint exists
    // In a real scenario, we would need to send a POST request with form data
    const response = await fetch(url);
    
    console.log(`Status: ${response.status}`);
    
    // Try to parse the response
    try {
      const text = await response.text();
      console.log('Response:', text.substring(0, 200));
    } catch (parseError) {
      console.error(`Error parsing response: ${parseError.message}`);
    }
    
    return response.status !== 404; // Consider it a success if it's not a 404
  } catch (error) {
    console.error(`Error connecting to contract generator: ${error.message}`);
    return false;
  }
}

// Run the checks
async function runChecks() {
  const serverRunning = await checkServer();
  
  if (serverRunning) {
    const contractGeneratorRunning = await checkContractGenerator();
    
    if (contractGeneratorRunning) {
      console.log('Contract Generator endpoint is available!');
    } else {
      console.error('Contract Generator endpoint is not available.');
    }
  } else {
    console.error('Server is not running.');
  }
}

runChecks();
