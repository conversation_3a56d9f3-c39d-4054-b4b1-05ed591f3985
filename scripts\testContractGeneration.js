import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PDFDocument } from 'pdf-lib';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the contract template PDF
const CONTRACT_TEMPLATE_PATH = path.join(__dirname, '..', 'contract-template.pdf.pdf');

// Path to save the test PDF
const TEST_PDF_PATH = path.join(__dirname, 'test_contract.pdf');

// Mock data for testing
const mockData = {
  buyer: {
    fullName: "<PERSON>",
    email: "<EMAIL>"
  },
  seller: {
    fullName: "<PERSON>"
  },
  property: {
    address: "123 Main St, Chicago, IL 60614",
    unit: "5A",
    parkingIncluded: true,
    parkingDetails: "Garage spot #12"
  },
  financials: {
    purchasePrice: 400000,
    earnestMoney: 20000,
    closingDate: "2025-07-15"
  },
  associations: {
    hoa: true,
    monthlyFees: 300
  },
  fixturesIncluded: [
    "Refrigerator", "Oven", "Washer/Dryer", "Dishwasher", "Microwave"
  ],
  additionalTerms: "Seller to fix balcony before closing."
};

/**
 * Test PDF form filling with mock data
 */
async function testPdfGeneration() {
  try {
    console.log('Starting PDF generation test...');
    console.log(`Using template: ${CONTRACT_TEMPLATE_PATH}`);
    
    // Check if the PDF exists
    if (!fs.existsSync(CONTRACT_TEMPLATE_PATH)) {
      console.error(`PDF template not found at: ${CONTRACT_TEMPLATE_PATH}`);
      return;
    }
    
    // Read the PDF template
    const pdfBytes = fs.readFileSync(CONTRACT_TEMPLATE_PATH);
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(pdfBytes);
    
    // Get the form from the PDF
    const form = pdfDoc.getForm();
    
    // Track which fields were successfully filled
    const filledFields = new Set();
    
    console.log('Filling PDF form fields...');
    
    // Buyer Information
    const buyerField = form.getTextField('Buyer_Name');
    if (buyerField) {
      buyerField.setText(mockData.buyer.fullName || '');
      filledFields.add('Buyer_Name');
      console.log('✓ Filled Buyer_Name');
    } else {
      console.log('✗ Field Buyer_Name not found');
    }
    
    // Buyer Email
    const buyerEmailField = form.getTextField('Buyer_Email');
    if (buyerEmailField) {
      buyerEmailField.setText(mockData.buyer.email || '');
      filledFields.add('Buyer_Email');
      console.log('✓ Filled Buyer_Email');
    } else {
      console.log('✗ Field Buyer_Email not found');
    }
    
    // Seller Information
    const sellerField = form.getTextField('Seller_Name');
    if (sellerField) {
      sellerField.setText(mockData.seller.fullName || 'To Be Determined');
      filledFields.add('Seller_Name');
      console.log('✓ Filled Seller_Name');
    } else {
      console.log('✗ Field Seller_Name not found');
    }
    
    // Property Information
    const addressField = form.getTextField('Property_Address');
    if (addressField) {
      addressField.setText(mockData.property.address || '');
      filledFields.add('Property_Address');
      console.log('✓ Filled Property_Address');
    } else {
      console.log('✗ Field Property_Address not found');
    }
    
    const unitField = form.getTextField('Property_Unit');
    if (unitField) {
      unitField.setText(mockData.property.unit || '');
      filledFields.add('Property_Unit');
      console.log('✓ Filled Property_Unit');
    } else {
      console.log('✗ Field Property_Unit not found');
    }
    
    // Parking Information
    const parkingField = form.getCheckBox('Parking_Included');
    if (parkingField) {
      if (mockData.property.parkingIncluded) {
        parkingField.check();
      } else {
        parkingField.uncheck();
      }
      filledFields.add('Parking_Included');
      console.log('✓ Set Parking_Included checkbox');
    } else {
      console.log('✗ Field Parking_Included not found');
    }
    
    const parkingDetailsField = form.getTextField('Parking_Details');
    if (parkingDetailsField) {
      parkingDetailsField.setText(mockData.property.parkingIncluded ? (mockData.property.parkingDetails || '') : '');
      filledFields.add('Parking_Details');
      console.log('✓ Filled Parking_Details');
    } else {
      console.log('✗ Field Parking_Details not found');
    }
    
    // Financial Information
    const priceField = form.getTextField('Purchase_Price');
    if (priceField) {
      const formattedPrice = mockData.financials.purchasePrice 
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(mockData.financials.purchasePrice)
        : '';
      priceField.setText(formattedPrice);
      filledFields.add('Purchase_Price');
      console.log('✓ Filled Purchase_Price');
    } else {
      console.log('✗ Field Purchase_Price not found');
    }
    
    const earnestMoneyField = form.getTextField('Earnest_Money');
    if (earnestMoneyField) {
      const formattedEarnestMoney = mockData.financials.earnestMoney 
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(mockData.financials.earnestMoney)
        : '';
      earnestMoneyField.setText(formattedEarnestMoney);
      filledFields.add('Earnest_Money');
      console.log('✓ Filled Earnest_Money');
    } else {
      console.log('✗ Field Earnest_Money not found');
    }
    
    const closingDateField = form.getTextField('Closing_Date');
    if (closingDateField) {
      if (mockData.financials.closingDate) {
        const date = new Date(mockData.financials.closingDate);
        const formattedDate = date.toLocaleDateString('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric'
        });
        closingDateField.setText(formattedDate);
      } else {
        closingDateField.setText('');
      }
      filledFields.add('Closing_Date');
      console.log('✓ Filled Closing_Date');
    } else {
      console.log('✗ Field Closing_Date not found');
    }
    
    // Contract Date (Today's Date)
    const contractDateField = form.getTextField('Contract_Date');
    if (contractDateField) {
      const today = new Date();
      const formattedToday = today.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
      contractDateField.setText(formattedToday);
      filledFields.add('Contract_Date');
      console.log('✓ Filled Contract_Date');
    } else {
      console.log('✗ Field Contract_Date not found');
    }
    
    // HOA Information
    const hoaField = form.getCheckBox('HOA_Exists');
    if (hoaField) {
      if (mockData.associations.hoa) {
        hoaField.check();
      } else {
        hoaField.uncheck();
      }
      filledFields.add('HOA_Exists');
      console.log('✓ Set HOA_Exists checkbox');
    } else {
      console.log('✗ Field HOA_Exists not found');
    }
    
    const feesField = form.getTextField('HOA_Fees');
    if (feesField) {
      const formattedFees = mockData.associations.hoa && mockData.associations.monthlyFees 
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(mockData.associations.monthlyFees)
        : '';
      feesField.setText(formattedFees);
      filledFields.add('HOA_Fees');
      console.log('✓ Filled HOA_Fees');
    } else {
      console.log('✗ Field HOA_Fees not found');
    }
    
    // Fixtures
    const fixturesField = form.getTextField('Fixtures_Included');
    if (fixturesField) {
      const fixturesText = mockData.fixturesIncluded && mockData.fixturesIncluded.length > 0 
        ? mockData.fixturesIncluded.join(', ')
        : 'Standard appliances';
      fixturesField.setText(fixturesText);
      filledFields.add('Fixtures_Included');
      console.log('✓ Filled Fixtures_Included');
    } else {
      console.log('✗ Field Fixtures_Included not found');
    }
    
    // Additional Terms
    const termsField = form.getTextField('Additional_Terms');
    if (termsField) {
      termsField.setText(mockData.additionalTerms || '');
      filledFields.add('Additional_Terms');
      console.log('✓ Filled Additional_Terms');
    } else {
      console.log('✗ Field Additional_Terms not found');
    }
    
    // Log any fields that weren't filled
    console.log('\nChecking for empty fields in the PDF form...');
    const allFields = form.getFields();
    allFields.forEach(field => {
      const fieldName = field.getName();
      
      // Skip fields that were already filled
      if (filledFields.has(fieldName)) {
        return;
      }
      
      // Check if the field is a text field and is empty
      if (field.constructor.name === 'PDFTextField') {
        const value = field.getText();
        if (!value) {
          console.log(`Warning: Field ${fieldName} is empty`);
        }
      }
      // Check if the field is a checkbox
      else if (field.constructor.name === 'PDFCheckBox') {
        console.log(`Warning: Checkbox ${fieldName} was not explicitly set`);
      }
    });
    
    // Flatten the form (makes it non-editable)
    form.flatten();
    
    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save();
    
    // Write the PDF to disk
    fs.writeFileSync(TEST_PDF_PATH, filledPdfBytes);
    
    console.log(`\nTest PDF generated successfully at: ${TEST_PDF_PATH}`);
  } catch (error) {
    console.error('Error generating test PDF:', error);
  }
}

// Run the test
testPdfGeneration();
