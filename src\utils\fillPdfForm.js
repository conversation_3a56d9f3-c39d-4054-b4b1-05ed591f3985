// PDF-lib integration (mock for now)
// When the real PDF-lib package is installed, this can be updated

// Mock PDF document for fallback
const mockPDFDocument = {
  load: async () => {
    return {
      getForm: () => ({
        getFields: () => [],
        getTextField: () => ({
          setText: () => {}
        }),
        getCheckBox: () => ({
          check: () => {},
          uncheck: () => {}
        }),
        flatten: () => {}
      }),
      save: async () => new Uint8Array([])
    };
  }
};

/**
 * Fill a PDF form with data
 * @param {Object} formData - The form data
 * @param {Object} fieldMap - The field mapping
 * @returns {Promise<Uint8Array>} - The filled PDF as a byte array
 */
export async function fillPdfForm(formData, fieldMap) {
  try {
    console.log('Filling PDF form with data:', formData);
    console.log('Using field mapping:', fieldMap);

    // For now, we'll use the mock implementation
    // When the real PDF-lib package is installed, this can be updated
    console.log('Using mock implementation');

    // Load the PDF document (mock)
    const pdfDoc = await mockPDFDocument.load();

    // Get the form from the PDF
    const form = pdfDoc.getForm();

    // Track which fields were successfully filled
    const filledFields = new Set();

    // Fill the form fields
    Object.entries(fieldMap).forEach(([pdfField, jsonPath]) => {
      try {
        const value = getNestedValue(formData, jsonPath);

        if (value !== undefined && value !== null) {
          console.log(`Filling field ${pdfField} with value:`, value);

          // Get the field
          const field = form.getTextField(pdfField);

          if (field) {
            // Format the value based on the field name
            let formattedValue = value;

            if (pdfField === 'Purchase_Price' || pdfField === 'Earnest_Money' || pdfField === 'HOA_Fees') {
              // Format currency
              formattedValue = formatCurrency(value);
            } else if (pdfField === 'Closing_Date' || pdfField === 'Contract_Date') {
              // Format date
              formattedValue = formatDate(value);
            } else if (Array.isArray(value)) {
              // Format array
              formattedValue = value.join(', ');
            } else if (typeof value === 'boolean') {
              // Format boolean
              formattedValue = value ? 'Yes' : 'No';
            }

            // Set the field value
            field.setText(String(formattedValue));
            filledFields.add(pdfField);
          } else {
            // Try to get a checkbox
            const checkbox = form.getCheckBox(pdfField);

            if (checkbox) {
              if (value === true) {
                checkbox.check();
              } else {
                checkbox.uncheck();
              }

              filledFields.add(pdfField);
            } else {
              console.warn(`Field ${pdfField} not found in the PDF`);
            }
          }
        }
      } catch (error) {
        console.error(`Error filling field ${pdfField}:`, error);
      }
    });

    // Flatten the form (makes it non-editable)
    form.flatten();

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save();

    console.log('Mock PDF form filled successfully');

    return filledPdfBytes;
  } catch (error) {
    console.error('Error filling PDF form:', error);
    // Return an empty byte array as a fallback
    return new Uint8Array([]);
  }
}

/**
 * Get a nested value from an object using a dot-notation path
 * @param {Object} obj - The object to get the value from
 * @param {string} path - The path to the value (e.g., 'financials.earnestMoney')
 * @returns {*} - The value at the path, or undefined if not found
 */
function getNestedValue(obj, path) {
  const keys = path.split('.');
  let value = obj;

  for (const key of keys) {
    if (value === undefined || value === null) {
      return undefined;
    }

    value = value[key];
  }

  return value;
}

/**
 * Format a value as currency
 * @param {number|string} value - The value to format
 * @returns {string} - The formatted currency string
 */
function formatCurrency(value) {
  if (typeof value === 'string') {
    value = parseFloat(value.replace(/[^0-9.-]+/g, ''));
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
}

/**
 * Format a value as a date
 * @param {string|Date} value - The value to format
 * @returns {string} - The formatted date string
 */
function formatDate(value) {
  const date = typeof value === 'string' ? new Date(value) : value;

  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  });
}

/**
 * Save a filled PDF to a file
 * @param {Uint8Array} pdfBytes - The filled PDF as a byte array
 * @param {string} outputPath - The path to save the PDF to
 * @returns {string} - The URL to the saved PDF
 */
export function savePdf(pdfBytes, outputPath) {
  // In a browser environment, we can't save to a file
  // Instead, we'll log the PDF bytes to the console
  console.log('PDF bytes:', pdfBytes);
  console.log(`In a real implementation, the PDF would be saved to: ${outputPath}`);

  // In a real implementation, this would save the PDF to a file
  return outputPath;
}

/**
 * Main function to test filling a PDF form
 */
export async function testFillPdfForm() {
  try {
    // Sample field mapping
    const sampleFieldMap = {
      "Buyer_Name": "buyer.fullName",
      "Seller_Name": "seller.fullName",
      "Property_Address": "property.address",
      "Purchase_Price": "financials.purchasePrice",
      "Earnest_Money": "financials.earnestMoney",
      "Closing_Date": "financials.closingDate"
    };

    // Sample form data
    const sampleFormData = {
      buyer: { fullName: "John Doe" },
      seller: { fullName: "Jane Smith" },
      property: { address: "1260 W Washington Blvd Apt 408, Chicago, IL 60607" },
      financials: {
        purchasePrice: 800000,
        earnestMoney: 40000,
        closingDate: "2025-06-30"
      }
    };

    // Fill the PDF form
    const pdfBytes = await fillPdfForm(sampleFormData, sampleFieldMap);

    // Generate a unique filename
    const timestamp = Date.now();
    const filename = `Contract_Test_${timestamp}.pdf`;

    // Save the filled PDF
    const outputPath = `/generated-contracts/${filename}`;
    const savedPath = savePdf(pdfBytes, outputPath);

    console.log(`Test PDF saved to: ${savedPath}`);

    return savedPath;
  } catch (error) {
    console.error('Error testing fill PDF form:', error);

    // Return a fallback path
    return '/generated-contracts/sample-contract.pdf';
  }
}

export default {
  fillPdfForm,
  savePdf,
  testFillPdfForm
};
