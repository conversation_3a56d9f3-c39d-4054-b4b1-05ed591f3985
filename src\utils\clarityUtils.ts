import { clarity } from 'react-microsoft-clarity';

// Microsoft Clarity ID
const CLARITY_ID = 'r7yjq54tfv';

/**
 * Initialize Microsoft Clarity
 * Only initializes in production environment to avoid tracking development usage
 */
export const initClarity = (): void => {
  if (process.env.NODE_ENV === 'production') {
    try {
      clarity.init(CLARITY_ID);
      console.log('Microsoft Clarity initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Microsoft Clarity:', error);
    }
  } else {
    console.log('Microsoft Clarity not initialized in development environment');
  }
};

/**
 * Identify a user in Clarity
 * @param userId - The user's ID
 * @param userProperties - Additional user properties
 */
export const identifyUser = (userId: string, userProperties?: Record<string, any>): void => {
  if (clarity.hasStarted()) {
    clarity.identify(userId, userProperties || {});
  }
};

/**
 * Set a custom tag in Clarity
 * @param key - The tag key
 * @param value - The tag value
 */
export const setTag = (key: string, value: string): void => {
  if (clarity.hasStarted()) {
    clarity.setTag(key, value);
  }
};

/**
 * Track a custom event in Clarity
 * @param eventName - The name of the event
 */
export const trackEvent = (eventName: string): void => {
  if (clarity.hasStarted()) {
    clarity.setEvent(eventName);
  }
};

/**
 * Stop tracking user behavior analytics
 */
export const stopTracking = (): void => {
  if (clarity.hasStarted()) {
    clarity.stop();
  }
};

/**
 * Restart tracking user behavior analytics
 */
export const startTracking = (): void => {
  if (clarity.hasStarted()) {
    clarity.start();
  }
};

/**
 * Set cookie consent for Clarity
 */
export const setCookieConsent = (): void => {
  if (clarity.hasStarted()) {
    clarity.consent();
  }
};
