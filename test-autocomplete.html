<!DOCTYPE html>
<html>
<head>
    <title>Test Autocomplete</title>
</head>
<body>
    <h1>Test Autocomplete API</h1>
    <button onclick="testAutocomplete()">Test Autocomplete</button>
    <div id="result"></div>

    <script>
        async function testAutocomplete() {
            try {
                console.log('Testing autocomplete...');
                const response = await fetch('http://localhost:3001/api/places/autocomplete?input=1260');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
