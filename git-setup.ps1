# Git Setup Script for Digital Realtor Project

# Check if Git is installed
try {
    $gitVersion = git --version
    Write-Host "Git is installed: $gitVersion"
} catch {
    Write-Host "Git is not installed or not in PATH. Please install Git from https://git-scm.com/download/win"
    exit 1
}

# Initialize Git repository
Write-Host "Initializing Git repository..."
git init

# Add all files to Git
Write-Host "Adding files to Git..."
git add .

# Create initial commit
Write-Host "Creating initial commit..."
git commit -m "Initial commit of Digital Realtor project"

# Instructions for connecting to GitHub
Write-Host "`nTo connect to GitHub, follow these steps:"
Write-Host "1. Create a new repository on GitHub (without README, .gitignore, or license)"
Write-Host "2. Run the following commands to connect to your GitHub repository:"
Write-Host "   git remote add origin https://github.com/yourusername/digital-realtor.git"
Write-Host "   git branch -M main"
Write-Host "   git push -u origin main"
Write-Host "`nReplace 'yourusername' with your actual GitHub username."
