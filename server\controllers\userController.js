import asyncHandler from 'express-async-handler';
import crypto from 'crypto';
import generateToken from '../utils/generateToken.js';
import User from '../models/userModel.js';
import sendEmail from '../utils/sendEmail.js';

// Store verification codes temporarily (in a real app, use Redis or a database)
const verificationCodes = new Map();

// @desc    Auth user & get token
// @route   POST /api/users/login
// @access  Public
const authUser = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  const user = await User.findOne({ email });

  if (user && (await user.matchPassword(password))) {
    // Check if user is verified
    if (!user.isVerified) {
      res.status(401);
      throw new Error('Please verify your email before logging in');
    }

    res.json({
      _id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.isAdmin,
      isRealtor: user.isRealtor,
      isVerified: user.isVerified,
      token: generateToken(user._id),
    });
  } else {
    res.status(401);
    throw new Error('Invalid email or password');
  }
});

// @desc    Register a new user
// @route   POST /api/users
// @access  Public
const registerUser = asyncHandler(async (req, res) => {
  const { name, email, password, verificationCode } = req.body;

  // Check if verification code is provided
  if (!verificationCode) {
    res.status(400);
    throw new Error('Verification code is required');
  }

  console.log('\n\n==== REGISTRATION VERIFICATION CHECK ====');
  console.log(`EMAIL: ${email}`);
  console.log(`VERIFICATION CODE PROVIDED: ${verificationCode}`);

  // Verify the code
  const verificationData = verificationCodes.get(email);
  console.log(`VERIFICATION DATA FROM STORAGE:`, verificationData);

  if (!verificationData) {
    res.status(400);
    throw new Error('No verification code found for this email. Please request a new code.');
  }

  // Check if code is expired
  if (Date.now() > verificationData.expires) {
    verificationCodes.delete(email); // Clean up expired code
    res.status(400);
    throw new Error('Verification code has expired. Please request a new code.');
  }

  // Check if code matches
  console.log(`COMPARING CODES: '${verificationData.code}' vs '${verificationCode}'`);
  if (verificationData.code !== verificationCode) {
    res.status(400);
    throw new Error('Invalid verification code. Please try again.');
  }

  console.log('VERIFICATION SUCCESSFUL!');
  console.log('======================================\n\n');

  // Check if user already exists
  const userExists = await User.findOne({ email });
  if (userExists) {
    res.status(400);
    throw new Error('User already exists');
  }

  // Create the user with verified status
  const user = await User.create({
    name,
    email,
    password,
    isVerified: true, // User is already verified
  });

  if (user) {
    // Clean up the verification code
    verificationCodes.delete(email);

    // Return user data without token (no auto-login)
    res.status(201).json({
      _id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.isAdmin,
      isRealtor: user.isRealtor,
      isVerified: user.isVerified,
      message: 'Account created successfully. You can now log in.',
    });
  } else {
    res.status(400);
    throw new Error('Invalid user data');
  }
});

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id);

  if (user) {
    res.json({
      _id: user._id,
      name: user.name,
      email: user.email,
      isAdmin: user.isAdmin,
      isRealtor: user.isRealtor,
    });
  } else {
    res.status(404);
    throw new Error('User not found');
  }
});

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id);

  if (user) {
    user.name = req.body.name || user.name;
    user.email = req.body.email || user.email;
    if (req.body.password) {
      user.password = req.body.password;
    }

    const updatedUser = await user.save();

    res.json({
      _id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      isAdmin: updatedUser.isAdmin,
      isRealtor: updatedUser.isRealtor,
      token: generateToken(updatedUser._id),
    });
  } else {
    res.status(404);
    throw new Error('User not found');
  }
});

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
const getUsers = asyncHandler(async (req, res) => {
  const users = await User.find({});
  res.json(users);
});

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
const deleteUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id);

  if (user) {
    await user.remove();
    res.json({ message: 'User removed' });
  } else {
    res.status(404);
    throw new Error('User not found');
  }
});

// @desc    Get user by ID
// @route   GET /api/users/:id
// @access  Private/Admin
const getUserById = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id).select('-password');

  if (user) {
    res.json(user);
  } else {
    res.status(404);
    throw new Error('User not found');
  }
});

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
const updateUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id);

  if (user) {
    user.name = req.body.name || user.name;
    user.email = req.body.email || user.email;
    user.isAdmin = req.body.isAdmin === undefined ? user.isAdmin : req.body.isAdmin;
    user.isRealtor = req.body.isRealtor === undefined ? user.isRealtor : req.body.isRealtor;

    const updatedUser = await user.save();

    res.json({
      _id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      isAdmin: updatedUser.isAdmin,
      isRealtor: updatedUser.isRealtor,
    });
  } else {
    res.status(404);
    throw new Error('User not found');
  }
});

// @desc    Verify user email
// @route   GET /api/users/verify/:token
// @access  Public
const verifyEmail = asyncHandler(async (req, res) => {
  const { token } = req.params;

  // Find user with the token and check if it's expired
  const user = await User.findOne({
    verificationToken: token,
    verificationTokenExpires: { $gt: Date.now() },
  });

  if (!user) {
    res.status(400);
    throw new Error('Invalid or expired verification token');
  }

  // Update user to verified and remove token
  user.isVerified = true;
  user.verificationToken = undefined;
  user.verificationTokenExpires = undefined;
  await user.save();

  res.status(200).json({ message: 'Email verified successfully. You can now log in.' });
});

// @desc    Resend verification email
// @route   POST /api/users/verify/send
// @access  Public
const sendVerificationEmail = asyncHandler(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    res.status(400);
    throw new Error('Please provide an email address');
  }

  const user = await User.findOne({ email });

  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  if (user.isVerified) {
    res.status(400);
    throw new Error('Email is already verified');
  }

  // Generate new verification token
  const verificationToken = crypto.randomBytes(32).toString('hex');
  const verificationTokenExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

  // Update user with new token
  user.verificationToken = verificationToken;
  user.verificationTokenExpires = verificationTokenExpires;
  await user.save();

  // Send verification email
  const verificationUrl = `${req.protocol}://${req.get('host')}/verify/${verificationToken}`;

  try {
    await sendEmail({
      email: user.email,
      subject: 'Digital Realtor - Verify Your Email',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #4A6BFF;">Digital Realtor</h1>
          </div>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
            <h2>Verify Your Email Address</h2>
            <p>Hi ${user.name},</p>
            <p>Please verify your email address by clicking the button below:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" style="background-color: #4A6BFF; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Verify Email</a>
            </div>
            <p>If the button doesn't work, you can also click on the link below or copy and paste it into your browser:</p>
            <p><a href="${verificationUrl}">${verificationUrl}</a></p>
            <p>This link will expire in 24 hours.</p>
            <p>If you did not create an account, please ignore this email.</p>
          </div>
          <div style="margin-top: 20px; text-align: center; font-size: 12px; color: #666;">
            <p>© ${new Date().getFullYear()} Digital Realtor. All rights reserved.</p>
          </div>
        </div>
      `,
    });

    res.status(200).json({ message: 'Verification email sent successfully' });
  } catch (error) {
    console.error('Error sending verification email:', error);
    res.status(500);
    throw new Error('Error sending verification email');
  }
});

// @desc    Request verification code before registration
// @route   POST /api/users/verify/request-code
// @access  Public
const requestVerificationCode = asyncHandler(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    res.status(400);
    throw new Error('Please provide an email address');
  }

  // Check if email already exists
  const userExists = await User.findOne({ email });
  if (userExists) {
    res.status(400);
    throw new Error('User with this email already exists');
  }

  // Generate a random 6-digit verification code
  const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

  // Log the verification code for testing
  console.log('\n\n==== VERIFICATION CODE FOR TESTING ====');
  console.log(`EMAIL: ${email}`);
  console.log(`CODE: ${verificationCode} (RANDOM CODE)`);
  console.log('======================================\n\n');

  // Store the code with expiration (30 minutes)
  verificationCodes.set(email, {
    code: verificationCode,
    expires: Date.now() + 30 * 60 * 1000, // 30 minutes
  });

  // Send verification email with code
  try {
    // Log the verification code for testing
    console.log('\n\n==== VERIFICATION CODE FOR TESTING ====');
    console.log(`EMAIL: ${email}`);
    console.log(`CODE: ${verificationCode}`);
    console.log('======================================\n\n');

    await sendEmail({
      email: email,
      subject: 'Digital Realtor - Your Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #4A6BFF;">Digital Realtor</h1>
          </div>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
            <h2>Your Verification Code</h2>
            <p>Please use the following code to verify your email address:</p>
            <div style="text-align: center; margin: 30px 0;">
              <div style="font-size: 32px; letter-spacing: 5px; font-weight: bold; color: #4A6BFF; padding: 15px; background-color: #e9ecef; border-radius: 5px; display: inline-block;">${verificationCode}</div>
            </div>
            <p>This code will expire in 30 minutes.</p>
            <p>If you did not request this code, please ignore this email.</p>
          </div>
          <div style="margin-top: 20px; text-align: center; font-size: 12px; color: #666;">
            <p>© ${new Date().getFullYear()} Digital Realtor. All rights reserved.</p>
          </div>
        </div>
      `,
    });

    // In development mode, include the verification code in the response for testing
    if (process.env.NODE_ENV !== 'production') {
      res.status(200).json({
        message: 'Verification code sent successfully',
        verificationCode: verificationCode
      });
    } else {
      res.status(200).json({ message: 'Verification code sent successfully' });
    }
  } catch (error) {
    console.error('Error sending verification code:', error);
    res.status(500);
    throw new Error('Error sending verification code');
  }
});

// @desc    Check verification code
// @route   POST /api/users/verify/check-code
// @access  Public
const checkVerificationCode = asyncHandler(async (req, res) => {
  const { email, code } = req.body;

  console.log('Checking verification code:', { email, code });

  if (!email || !code) {
    res.status(400);
    throw new Error('Please provide email and verification code');
  }

  // Get stored verification data
  const verificationData = verificationCodes.get(email);
  console.log('Verification data from storage:', verificationData);

  if (!verificationData) {
    res.status(400);
    throw new Error('No verification code found for this email. Please request a new code.');
  }

  // Check if code is expired
  if (Date.now() > verificationData.expires) {
    verificationCodes.delete(email); // Clean up expired code
    res.status(400);
    throw new Error('Verification code has expired. Please request a new code.');
  }

  // Check if code matches
  console.log(`Comparing codes: '${verificationData.code}' vs '${code}'`);
  if (verificationData.code !== code) {
    res.status(400);
    throw new Error('Invalid verification code. Please try again.');
  }

  // Code is valid
  console.log('Verification successful!');
  res.status(200).json({ message: 'Verification code is valid', valid: true });
});

// @desc    Clear all user accounts (development only)
// @route   DELETE /api/users/clear-all
// @access  Public (but only available in development)
const clearAllUsers = asyncHandler(async (req, res) => {
  // For demo purposes, we'll allow this in any environment
  // In a real app, you would check process.env.NODE_ENV === 'development'
  console.log('Current NODE_ENV:', process.env.NODE_ENV || 'not set');

  // Always allow for demo purposes
  // if (process.env.NODE_ENV !== 'development') {
  //   res.status(403);
  //   throw new Error('This endpoint is only available in development mode');
  // }

  // Get all existing user emails before deletion (for logging purposes)
  const existingUsers = await User.find({}, 'email');
  const emailsCleared = existingUsers.map(user => user.email);

  // Clear all verification codes
  verificationCodes.clear();

  // Delete all users
  await User.deleteMany({});

  // Log the cleared emails
  console.log('Cleared the following email addresses:', emailsCleared);

  res.status(200).json({
    message: 'All user accounts have been cleared',
    emailsCleared: emailsCleared.length > 0 ? emailsCleared : ['No emails were cleared']
  });
});

export {
  authUser,
  registerUser,
  getUserProfile,
  updateUserProfile,
  getUsers,
  deleteUser,
  getUserById,
  updateUser,
  verifyEmail,
  sendVerificationEmail,
  requestVerificationCode,
  checkVerificationCode,
  clearAllUsers,
};
