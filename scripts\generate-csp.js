const fs = require('fs');
const path = require('path');

// Get the directory name (CommonJS has __dirname available globally)

// Production CSP configuration
const productionCSP = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://www.clarity.ms", "https://www.googletagmanager.com", "https://maps.googleapis.com", "https://apis.google.com"],
  'style-src': ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
  'img-src': ["'self'", "data:", "blob:", "https://www.clarity.ms", "https://c.clarity.ms", "https://*.clarity.ms", "https://c.bing.com", "https://maps.googleapis.com", "https://*.blob.core.windows.net", "https://photos.zillowstatic.com", "https://*.zillowstatic.com"],
  'font-src': ["'self'", "https://fonts.gstatic.com"],
  'connect-src': [
    "'self'",
    "https://api.openai.com",
    "https://api.anthropic.com",
    "https://maps.googleapis.com",
    "https://apis.google.com",
    "https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com",
    "https://digital-realtor-api.onrender.com",
    "https://l.clarity.ms",
    "https://*.clarity.ms",
    "https://schooldigger-k-12-school-data-api.p.rapidapi.com",
    "https://api.walkscore.com",
    "https://photos.zillowstatic.com",
    "https://*.zillowstatic.com"
  ],
  'frame-src': ["'self'"]
};

// Convert the CSP object to a string
const cspString = Object.entries(productionCSP)
  .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
  .join('; ');

// Path to the production index.html
const indexPath = path.join(__dirname, '../dist/index.html');

// Read the index.html file
fs.readFile(indexPath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading index.html:', err);
    return;
  }

  // Add the CSP meta tag to the head
  const updatedHtml = data.replace(
    '<head>',
    `<head>\n    <meta http-equiv="Content-Security-Policy" content="${cspString}" />`
  );

  // Write the updated index.html file
  fs.writeFile(indexPath, updatedHtml, 'utf8', (err) => {
    if (err) {
      console.error('Error writing index.html:', err);
      return;
    }

    console.log('Added CSP to production index.html');
  });
});
