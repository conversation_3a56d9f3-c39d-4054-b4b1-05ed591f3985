/**
 * Zillow API Service
 * This service handles fetching property data from the Zillow API via our backend proxy
 */

// Define the API base URL
const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000';

/**
 * Helper function to extract address components from a Google Place result
 * @param place - The Google Place result object
 * @returns Object with address and citystatezip fields formatted for Zillow API
 */
export function mapGooglePlaceToZillowFields(place: any) {
  if (!place || !place.address_components) {
    return { address: '', citystatezip: '' };
  }

  const getComponent = (type: string) =>
    place.address_components?.find((c: any) => c.types.includes(type))?.long_name || '';

  const streetNumber = getComponent('street_number');
  const route = getComponent('route');
  const city = getComponent('locality');
  const state = getComponent('administrative_area_level_1');
  const zip = getComponent('postal_code');

  return {
    address: `${streetNumber} ${route}`.trim(),
    citystatezip: `${city}, ${state} ${zip}`.trim()
  };
}

/**
 * Fetches property data from the Zillow API using coordinates
 * @param lat - Latitude
 * @param lng - Longitude
 * @param searchRadius - Search radius in miles (optional, default: 0.1)
 * @param includeSold - Whether to include sold properties (optional, default: true)
 * @returns Promise with property data or error
 */
export async function fetchPropertyDataByCoordinates(
  lat: number,
  lng: number,
  searchRadius: number = 0.1,
  includeSold: boolean = true
) {
  if (typeof lat !== 'number' || typeof lng !== 'number') {
    throw new Error('Invalid coordinates: lat and lng must be numbers');
  }

  try {
    const params = new URLSearchParams({
      lat: lat.toString(),
      lng: lng.toString(),
      d: searchRadius.toString(),
      includeSold: includeSold.toString()
    });

    const response = await fetch(
      `${backendUrl}/api/zillow/property-by-coordinates?${params.toString()}`
    );

    if (!response.ok) {
      const errorData = await response.json();

      // Check if the error is related to API key configuration
      if (errorData.message && errorData.message.includes('RAPIDAPI_ZILLOW_KEY')) {
        console.error('Zillow API key not configured on the server. Using fallback data.');
        return getFallbackPropertyData(lat, lng);
      }

      throw new Error(errorData.message || `Zillow API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching property data from Zillow by coordinates:', error);

    // If there's an error, use fallback data
    if (error instanceof Error && error.message.includes('RAPIDAPI_ZILLOW_KEY')) {
      console.warn('Using fallback property data due to API key configuration issue');
      return getFallbackPropertyData(lat, lng);
    }

    throw error;
  }
}

/**
 * Provides fallback property data when the Zillow API is unavailable
 * @param address - Property address or lat coordinate
 * @param citystatezip - City, state, zip or lng coordinate
 * @returns Mock property data
 */
function getFallbackPropertyData(address: string | number, citystatezip: string | number) {
  // Generate a deterministic price based on the address string
  let basePrice = 500000;
  let priceFactor = 0;

  // If coordinates were passed
  if (typeof address === 'number' && typeof citystatezip === 'number') {
    const lat = address;
    const lng = citystatezip;
    priceFactor = Math.abs(Math.sin(lat * 10)) * 200000 + Math.abs(Math.cos(lng * 10)) * 300000;
  } else {
    // Use string values to generate a deterministic price
    const addressStr = String(address);
    const cityStateZipStr = String(citystatezip);

    // Simple hash function for strings
    const hashString = (str: string) => {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i);
        hash |= 0; // Convert to 32bit integer
      }
      return Math.abs(hash);
    };

    priceFactor = (hashString(addressStr) % 500000) + (hashString(cityStateZipStr) % 300000);
  }

  const price = Math.round((basePrice + priceFactor) / 1000) * 1000;

  // Generate square footage based on price
  const sqft = Math.round((price / 500) + 800);

  // Generate lot size
  const lotSize = (Math.round((sqft / 1000) * 10) / 10).toFixed(1) + " acres";

  // Format the address for display
  const displayAddress = typeof address === 'number' ?
    "Sample Property" :
    `${address}, ${citystatezip}`;

  return {
    address: displayAddress,
    price: price,
    zestimate: price,
    bedrooms: 3,
    bathrooms: 2,
    livingArea: sqft,
    livingAreaSqFt: sqft,
    lotSize: lotSize,
    lotSizeSqFt: sqft * 3,
    yearBuilt: 2010,
    homeType: "Single Family",
    propertyType: "Single Family Home",
    parkingFeatures: "2-car garage",
    homeFeatures: "Hardwood floors, Granite countertops, Stainless steel appliances",
    pricePerSqFt: Math.round(price / sqft),
    _fallback: true // Flag to indicate this is fallback data
  };
}

/**
 * Fetches property data from the Zillow API using address and city/state/zip
 * @param address - The property address (street number and name)
 * @param citystatezip - The city, state, and zip code
 * @returns Promise with property data or error
 */
export async function fetchPropertyDataByAddress(address: string, citystatezip: string) {
  if (!address || !citystatezip) {
    throw new Error('Address and city/state/zip are required');
  }

  try {
    const params = new URLSearchParams({
      address: address,
      citystatezip: citystatezip
    });

    const response = await fetch(
      `${backendUrl}/api/zillow/property-by-address?${params.toString()}`
    );

    if (!response.ok) {
      const errorData = await response.json();

      // Check if the error is related to API key configuration
      if (errorData.message && errorData.message.includes('RAPIDAPI_ZILLOW_KEY')) {
        console.error('Zillow API key not configured on the server. Using fallback data.');
        return getFallbackPropertyData(address, citystatezip);
      }

      throw new Error(errorData.message || `Zillow API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching property data from Zillow by address:', error);

    // If there's an error, use fallback data
    if (error instanceof Error && error.message.includes('RAPIDAPI_ZILLOW_KEY')) {
      console.warn('Using fallback property data due to API key configuration issue');
      return getFallbackPropertyData(address, citystatezip);
    }

    throw error;
  }
}

/**
 * Fetches property data from the Zillow API (legacy method)
 * @param address - The property address (street number and name)
 * @param citystatezip - The city, state, and zip code
 * @returns Promise with property data or error
 * @deprecated Use fetchPropertyDataByAddress instead
 */
export async function fetchPropertyData(address: string, citystatezip?: string) {
  if (!address) return null;

  try {
    const params = new URLSearchParams({
      address: encodeURIComponent(address)
    });

    if (citystatezip) {
      params.append('citystatezip', encodeURIComponent(citystatezip));
    }

    const response = await fetch(
      `${backendUrl}/api/zillow/property?${params.toString()}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Zillow API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching property data from Zillow:', error);
    throw error;
  }
}

/**
 * Gets AI-suggested address corrections when Zillow API fails
 * @param address - The address that failed to return results
 * @param provider - The AI provider to use (openai or claude)
 * @returns Promise with array of suggested address corrections
 */
export async function getAddressCorrectionSuggestions(address: string, provider: 'openai' | 'claude' = 'openai') {
  if (!address) return [];

  try {
    const response = await fetch(
      `${backendUrl}/api/zillow/suggest-corrections`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ address, provider })
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to get address suggestions: ${response.status}`);
    }

    const data = await response.json();
    return data.suggestions || [];
  } catch (error) {
    console.error('Error getting address correction suggestions:', error);
    return [];
  }
}
