import { Appointment, CreateAppointmentData } from '../models/Appointment';
import axios from 'axios';

// Base URL for the API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

export const DatabaseAppointmentService = {
  // Create a new appointment
  createAppointment: async (data: CreateAppointmentData): Promise<Appointment> => {
    try {
      console.log('Creating appointment in database for user:', data.userId);
      const response = await axios.post(`${API_BASE_URL}/appointments`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('Appointment created in database:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating appointment in database:', error);
      
      // Fallback to local storage if database fails
      console.log('Falling back to local storage for appointment creation');
      const localAppointment = await import('./LocalAppointmentService').then(
        module => module.LocalAppointmentService.createAppointment(data)
      );
      return localAppointment;
    }
  },

  // Get all appointments
  getAllAppointments: async (): Promise<Appointment[]> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/appointments`);
      console.log('Retrieved all appointments from database:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error getting all appointments from database:', error);
      
      // Fallback to local storage if database fails
      console.log('Falling back to local storage for getting all appointments');
      const localAppointments = await import('./LocalAppointmentService').then(
        module => module.LocalAppointmentService.getAllAppointments()
      );
      return localAppointments;
    }
  },

  // Get appointments for a specific user
  getUserAppointments: async (userId: string): Promise<Appointment[]> => {
    try {
      console.log(`Retrieving appointments for user ${userId} from database`);
      const response = await axios.get(`${API_BASE_URL}/appointments/user/${userId}`);
      console.log(`Retrieved ${response.data.length} appointments for user ${userId} from database`);
      return response.data;
    } catch (error) {
      console.error(`Error getting appointments for user ${userId} from database:`, error);
      
      // Fallback to local storage if database fails
      console.log('Falling back to local storage for getting user appointments');
      const localAppointments = await import('./LocalAppointmentService').then(
        module => module.LocalAppointmentService.getUserAppointments(userId)
      );
      return localAppointments;
    }
  },

  // Get appointment by ID
  getAppointmentById: async (id: string): Promise<Appointment | undefined> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/appointments/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error getting appointment ${id} from database:`, error);
      
      // Fallback to local storage if database fails
      console.log('Falling back to local storage for getting appointment by ID');
      const localAppointment = await import('./LocalAppointmentService').then(
        module => module.LocalAppointmentService.getAppointmentById(id)
      );
      return localAppointment;
    }
  },

  // Update an appointment
  updateAppointment: async (id: string, data: Partial<Appointment>): Promise<Appointment | null> => {
    try {
      const response = await axios.put(`${API_BASE_URL}/appointments/${id}`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      console.log(`Updated appointment ${id} in database`);
      return response.data;
    } catch (error) {
      console.error(`Error updating appointment ${id} in database:`, error);
      
      // Fallback to local storage if database fails
      console.log('Falling back to local storage for updating appointment');
      const localAppointment = await import('./LocalAppointmentService').then(
        module => module.LocalAppointmentService.updateAppointment(id, data)
      );
      return localAppointment;
    }
  },

  // Delete an appointment
  deleteAppointment: async (id: string): Promise<boolean> => {
    try {
      await axios.delete(`${API_BASE_URL}/appointments/${id}`);
      console.log(`Deleted appointment ${id} from database`);
      return true;
    } catch (error) {
      console.error(`Error deleting appointment ${id} from database:`, error);
      
      // Fallback to local storage if database fails
      console.log('Falling back to local storage for deleting appointment');
      const success = await import('./LocalAppointmentService').then(
        module => module.LocalAppointmentService.deleteAppointment(id)
      );
      return success;
    }
  },
};
