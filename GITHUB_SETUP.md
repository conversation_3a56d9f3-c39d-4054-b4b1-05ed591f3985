# GitHub Setup Guide for Digital Realtor

This guide will help you set up your Digital Realtor project with GitHub.

## Prerequisites

- Git installed on your computer
- GitHub account
- Digital Realtor project files on your computer

## Step 1: Install Git (if not already installed)

1. Download Git from [git-scm.com](https://git-scm.com/download/win)
2. Run the installer and follow the instructions
3. Verify installation by opening a command prompt and typing:
   ```
   git --version
   ```

## Step 2: Configure Git

Set your username and email:

```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

## Step 3: Initialize Git Repository

Navigate to your project directory and initialize Git:

```bash
cd path\to\digital-realtor
git init
```

## Step 4: Add Files to Git

Add all project files to Git:

```bash
git add .
```

## Step 5: Create Initial Commit

Create your first commit:

```bash
git commit -m "Initial commit of Digital Realtor project"
```

## Step 6: Create a GitHub Repository

1. Go to [GitHub](https://github.com/) and sign in
2. Click the "+" icon in the top right and select "New repository"
3. Name your repository "digital-realtor"
4. Do not initialize with README, .gitignore, or license
5. Click "Create repository"

## Step 7: Connect Local Repository to GitHub

Run these commands to connect your local repository to GitHub:

```bash
git remote add origin https://github.com/yourusername/digital-realtor.git
git branch -M main
git push -u origin main
```

Replace `yourusername` with your actual GitHub username.

## Step 8: Verify Repository Setup

1. Refresh your GitHub repository page
2. You should see all your project files in the repository

## Using the PowerShell Script

Alternatively, you can run the included PowerShell script:

```bash
.\git-setup.ps1
```

This script will initialize Git, add all files, and create the initial commit. You'll still need to manually create the GitHub repository and connect it.

## Next Steps

After setting up your GitHub repository, you can:

1. Create branches for new features
2. Make changes and commit them
3. Push changes to GitHub
4. Create pull requests for code review
5. Set up GitHub Actions for CI/CD
