/* Futuristic Header Styles */

:root {
  --primary-color: #4a6bff;
  --secondary-color: #6e3adc;
  --accent-color: #00f7ff;
  --dark-color: #0a1128;
  --light-color: #f0f4ff;
  --gradient-start: #4a6bff;
  --gradient-end: #c961de;
  --neon-glow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.3);
}

/* Futuristic Header */
.futuristic-header {
  background: rgba(10, 12, 26, 0.75);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(74, 107, 255, 0.2);
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 1rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2), 0 0 15px rgba(0, 247, 255, 0.1);
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(74, 107, 255, 0.05), rgba(110, 58, 220, 0.05));
  z-index: -1;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Logo Styles */
.logo-link {
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logo-icon {
  margin-right: 0.5rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  letter-spacing: 1px;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Navigation Styles */
.header-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-left: 1.25rem; /* Reduced margin to create more space */
  position: relative;
  display: flex;
  align-items: center;
  white-space: nowrap; /* Prevent text wrapping */
}

/* Dropdown Menu Styles */
.dropdown-menu {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 247, 255, 0.15);
  border: 1px solid rgba(0, 247, 255, 0.2);
  position: absolute;
  left: -1.5rem;
  top: 100%;
  z-index: 1000;
  margin-top: 0.5rem;
  background-color: rgba(10, 12, 26, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 0.5rem;
  max-width: 100vw;
  overflow: hidden;
  animation: dropdown-appear 0.3s ease-out forwards;
  transform-origin: top center;
}

/* Features Dropdown Specific Styles */
.features-dropdown {
  width: 280px;
  max-height: 400px;
  overflow-y: auto;
}

/* User Dropdown Menu Styles */
.user-dropdown {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 247, 255, 0.15);
  border: 1px solid rgba(0, 247, 255, 0.2);
  position: absolute;
  right: 0;
  left: auto;
  top: 100%;
  z-index: 1000;
  margin-top: 0.5rem;
  background-color: rgba(10, 12, 26, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 0.5rem;
  max-width: 100vw;
  overflow: hidden;
  animation: dropdown-appear 0.3s ease-out forwards;
  transform-origin: top right;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Ensure dropdown menu has consistent styling */
.dropdown-menu {
  transform-origin: top center !important;
}

/* Menu Item Styles */
.menu-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.4rem;
  border-radius: 4px;
  color: var(--primary-200);
  font-size: 0.7rem;
  transition: all 0.2s ease;
  background: rgba(74, 107, 255, 0.05);
  border: 1px solid rgba(74, 107, 255, 0.1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.menu-item span {
  text-decoration: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-item:hover {
  background: rgba(74, 107, 255, 0.15);
  transform: translateX(2px);
  color: white;
  border-color: rgba(74, 107, 255, 0.3);
  box-shadow: 0 0 8px rgba(0, 247, 255, 0.15);
  text-decoration: none;
}

/* Removed underline effect */

.menu-icon {
  width: 8px;
  height: 8px;
  margin-right: 0.25rem;
  color: var(--accent-color);
  flex-shrink: 0;
  stroke-width: 1px;
  min-width: 8px;
  min-height: 8px;
  max-width: 8px;
  max-height: 8px;
}

.nav-link {
  color: #a0aec0;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0.5rem 0.6rem; /* Reduced horizontal padding */
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  margin: 0;
  outline: none;
  border-radius: 6px;
  letter-spacing: 0.3px;
  white-space: nowrap; /* Prevent text wrapping */
}

.nav-link:hover {
  color: white;
  background: rgba(74, 107, 255, 0.1);
  box-shadow: 0 0 8px rgba(0, 247, 255, 0.2);
}

.nav-link.active {
  color: white;
  background: rgba(74, 107, 255, 0.15);
  box-shadow: 0 0 12px rgba(0, 247, 255, 0.25);
}

.nav-icon {
  margin-right: 0.5rem;
}

/* User Menu */
.user-menu {
  margin-left: 2rem;
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 12, 26, 0.85);
  backdrop-filter: blur(10px);
  z-index: 1001;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  border-bottom: 1px solid rgba(0, 247, 255, 0.2);
  box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.3);
}

.mobile-menu.open {
  transform: translateY(0);
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.mobile-menu-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-item {
  margin-bottom: 1rem;
}

.mobile-nav-link {
  color: #a0aec0;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.2rem;
  display: block;
  padding: 0.5rem 0;
  transition: all 0.3s;
  border-bottom: 1px solid rgba(74, 107, 255, 0.1);
}

.mobile-nav-link:hover {
  color: white;
  border-bottom-color: var(--accent-color);
}

.mobile-nav-link.active {
  color: white;
  border-bottom-color: var(--accent-color);
}

/* Mobile Features Dropdown */
.mobile-features-dropdown {
  position: relative;
}

.mobile-features-submenu {
  background-color: rgba(20, 24, 52, 0.5);
  border-top: 1px solid rgba(74, 107, 255, 0.1);
  border-bottom: 1px solid rgba(74, 107, 255, 0.1);
  padding: 0.5rem 0;
  animation: submenu-appear 0.3s ease-out forwards;
}

@keyframes submenu-appear {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-submenu-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  color: #a0aec0;
  text-decoration: none;
  transition: all 0.2s;
  font-size: 0.85rem;
  border-bottom: none;
}

.mobile-submenu-link:hover {
  color: white;
  background-color: rgba(74, 107, 255, 0.1);
}

.mobile-submenu-header {
  display: flex;
  align-items: center;
  padding: 0.3rem 1rem 0.3rem 2.5rem;
  color: var(--secondary-400);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

.mobile-submenu-header svg {
  min-width: 8px;
  min-height: 8px;
  max-width: 8px;
  max-height: 8px;
  flex-shrink: 0;
}

/* Features Button Styles */
.features-button {
  position: relative;
  overflow: hidden;
}

.features-button::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.features-button:hover::after,
.features-button.active::after {
  transform: scaleX(1);
}

.features-button.active {
  color: var(--accent-color);
}

/* User Avatar Styles */
.user-avatar {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--secondary-700) 100%);
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);
  border: 1px solid rgba(0, 247, 255, 0.2);
  color: #ffffff;
}

.user-avatar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-cyan);
  box-shadow: 0 0 8px var(--accent-cyan);
  opacity: 0.5;
}

.user-avatar-active {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);
  border: 1px solid var(--accent-cyan);
  color: #ffffff;
}

.user-avatar-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-cyan);
  box-shadow: 0 0 12px var(--accent-cyan);
  opacity: 0.8;
}

.user-avatar-large {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);
  border: 1px solid rgba(0, 247, 255, 0.3);
  color: #ffffff;
}

.user-avatar-large::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-cyan);
  box-shadow: 0 0 10px var(--accent-cyan);
  opacity: 0.6;
}

.shadow-glow {
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2), 0 0 5px rgba(0, 247, 255, 0.1);
}

.shadow-glow-hover {
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3), 0 0 8px rgba(0, 247, 255, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .nav-list {
    display: none;
  }

  .mobile-menu-button {
    display: block;
  }

  .user-menu {
    margin-left: 1rem;
  }
}
