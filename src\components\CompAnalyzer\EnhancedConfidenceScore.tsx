import React from 'react';
import { FaInfoCircle, FaCheckCircle, FaExclamationTriangle, FaTimesCircle } from 'react-icons/fa';

interface EnhancedConfidenceScoreProps {
  score: number;
  explanation?: string;
}

const EnhancedConfidenceScore: React.FC<EnhancedConfidenceScoreProps> = ({ score, explanation }) => {
  // Determine confidence level and styling
  const getConfidenceLevel = () => {
    if (score >= 90) return { 
      level: 'Very High', 
      color: 'text-green-400',
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-700/30',
      icon: <FaCheckCircle className="text-green-400 mr-2" size={20} />
    };
    if (score >= 80) return { 
      level: 'High', 
      color: 'text-blue-400',
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-700/30',
      icon: <FaCheckCircle className="text-blue-400 mr-2" size={20} />
    };
    if (score >= 70) return { 
      level: 'Moderate', 
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-700/30',
      icon: <FaInfoCircle className="text-yellow-400 mr-2" size={20} />
    };
    if (score >= 60) return { 
      level: 'Fair', 
      color: 'text-orange-400',
      bgColor: 'bg-orange-900/20',
      borderColor: 'border-orange-700/30',
      icon: <FaExclamationTriangle className="text-orange-400 mr-2" size={20} />
    };
    return { 
      level: 'Low', 
      color: 'text-red-400',
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-700/30',
      icon: <FaTimesCircle className="text-red-400 mr-2" size={20} />
    };
  };

  const confidenceInfo = getConfidenceLevel();
  
  // Create a gradient background based on the score
  const gradientStyle = {
    background: `linear-gradient(to right, #10B981 0%, #3B82F6 50%, #F59E0B 75%, #EF4444 100%)`,
    backgroundSize: '100% 100%',
    height: '8px',
    borderRadius: '4px',
  };

  // Create an overlay to show the current score position
  const overlayStyle = {
    width: `${score}%`,
    background: 'rgba(0, 0, 0, 0.3)',
    height: '8px',
    borderRadius: '4px',
  };

  return (
    <div className={`${confidenceInfo.bgColor} backdrop-blur-sm px-4 py-3 rounded-lg border ${confidenceInfo.borderColor} shadow-lg`}>
      <div className="flex items-center justify-between mb-2">
        <div className="text-sm text-primary-300">Confidence Score</div>
        <div className={`text-sm font-medium ${confidenceInfo.color}`}>{confidenceInfo.level}</div>
      </div>
      
      <div className="flex items-center mb-3">
        <div className="text-3xl font-bold text-accent-cyan">{score}%</div>
        {confidenceInfo.icon}
      </div>
      
      {/* Score visualization */}
      <div className="mb-3">
        <div style={gradientStyle}>
          <div style={overlayStyle}></div>
        </div>
        <div className="flex justify-between mt-1 text-xs text-primary-400">
          <span>Low</span>
          <span>Moderate</span>
          <span>High</span>
        </div>
      </div>
      
      {explanation && (
        <div className="text-xs text-primary-200 mt-1 border-t border-primary-700/20 pt-2">
          <span className="font-medium text-primary-100">Analysis Factors:</span> {explanation}
        </div>
      )}
    </div>
  );
};

export default EnhancedConfidenceScore;
