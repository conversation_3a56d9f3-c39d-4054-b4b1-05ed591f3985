/* Validation Message Styles */

/* Common styles for validation message containers */
.validation-message {
  display: flex;
  align-items: flex-start;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

/* Error message styling */
.validation-error {
  color: var(--error-color, #f56565);
}

/* Success message styling */
.validation-success {
  color: var(--success-color, #48bb78);
}

/* Info message styling */
.validation-info {
  color: var(--info-color, #4299e1);
}

/* Warning message styling */
.validation-warning {
  color: var(--warning-color, #ed8936);
}

/* Icon styling for validation messages */
.validation-icon {
  width: 20px;
  height: 20px;
  min-width: 20px; /* Prevent icon from shrinking */
  margin-right: 0.5rem;
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Ensure SVG icons inside the validation icon container are properly sized */
.validation-icon svg {
  width: 100%;
  height: 100%;
}

/* Text styling for validation messages */
.validation-text {
  flex: 1;
  padding-top: 1px; /* Slight adjustment for better vertical alignment with the icon */
}
