/**
 * Custom API handler for the Contract Generator
 * This is used in development to handle contract generation requests
 * without requiring the backend server to be running
 */
import { mockGenerateContract } from './mockApi';
import { generateContract as aiGenerateContract } from './contractGenerator';

/**
 * Generate a contract based on form data
 * @param {Object} formData - The form data for the contract
 * @param {string} aiProvider - The AI provider to use ('openai' or 'claude')
 * @returns {Promise<Object>} - A promise that resolves to the contract data
 */
export const generateContract = async (formData, aiProvider) => {
  console.log('Using custom contract generator API');
  console.log('Form data:', formData);
  console.log('AI provider:', aiProvider);

  try {
    // First, try to use the AI-driven approach
    try {
      console.log('Attempting to use AI-driven contract generator');
      const response = await aiGenerateContract(formData);
      console.log('AI-driven contract generation successful');
      return response;
    } catch (aiError) {
      console.error('Error using AI-driven contract generator:', aiError);
      console.log('Falling back to mock implementation');

      // Fall back to the mock implementation
      const response = await mockGenerateContract({
        ...formData,
        aiProvider
      });

      return response;
    }
  } catch (error) {
    console.error('Error generating contract:', error);
    throw new Error('Failed to generate contract: ' + (error.message || 'Unknown error'));
  }
};

export default {
  generateContract
};
