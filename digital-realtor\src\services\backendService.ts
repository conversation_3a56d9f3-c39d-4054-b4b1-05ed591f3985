import { InspectionReportData } from './inspectionReportService';

const API_BASE_URL = 'http://localhost:3001/api';

// Function to analyze images using <PERSON> via backend
export const analyzeImagesWithClaudeBackend = async (
  images: string[]
): Promise<string> => {
  if (!images || images.length === 0) {
    return '';
  }

  try {
    const response = await fetch(`${API_BASE_URL}/claude/analyze-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ images }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    return data.analysis;
  } catch (error) {
    console.error('Error analyzing images with Claude backend:', error);
    throw error;
  }
};

// Function to generate an inspection report using <PERSON> via backend
export const generateInspectionReportWithClaudeBackend = async (
  inspectionData: InspectionReportData
): Promise<string> => {
  try {
    const response = await fetch(`${API_BASE_URL}/claude/generate-report`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(inspectionData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    return data.report;
  } catch (error) {
    console.error('Error generating inspection report with Claude backend:', error);
    throw error;
  }
};
