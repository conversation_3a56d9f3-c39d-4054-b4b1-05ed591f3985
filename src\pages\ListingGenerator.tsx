import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '../context/SimpleAppContext';
import { generatePropertyListing } from '../services/openaiService';
import { generatePropertyListingWithClaude, extractPropertyDataWithClaude } from '../services/claudeBackendService';
import { analyzePropertyImages } from '../services/imageAnalysisService';
import { FaCommentAlt, FaList, FaImage, FaTrash, FaPlus, FaRobot, FaHome, FaMapMarkerAlt,
  FaBed, FaBath, FaRulerCombined, FaDollarSign, FaCalendarAlt, FaStar, FaFileAlt,
  FaLightbulb, FaBrain, FaCheckCircle, FaArrowLeft, FaArrowRight, FaCopy, FaRedo,
  FaSearch, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';
import axios from 'axios';
import ValidationMessage from '../components/ValidationMessage';
import AddressAutocomplete from '../components/AddressAutocomplete';
import ExtractedPropertyFields from '../components/ExtractedPropertyFields';

// Import toast utilities
import { showSuccess, showError, showInfo, showWarning } from '../utils/toastUtils';
import { validateZillowResponse, normalizeZillowData } from '../utils/zillowValidator';
import {
  parseZillowPropertyDetails,
  validateZillowPropertyDetails,
  zillowDetailsToFormFields,
  zillowDetailsToAiContext,
  ZillowMappedPropertyDetails
} from '../utils/zillowFieldMapping';
import { fetchPropertyDataByCoordinates, fetchPropertyDataByAddress, getAddressCorrectionSuggestions } from '../services/zillowApi';
import { fetchLatLngForAddress } from '../services/placesApi';

import styles from './ListingGenerator.module.css';

// Fallback implementation in case the import fails during build
const extractCoordinatesFromPlace = (place: any): { lat: number, lng: number } | null => {
  if (!place || !place.geometry || !place.geometry.location) {
    return null;
  }

  // Handle both function-based and object-based location formats
  const lat = typeof place.geometry.location.lat === 'function'
    ? place.geometry.location.lat()
    : place.geometry.location.lat;

  const lng = typeof place.geometry.location.lng === 'function'
    ? place.geometry.location.lng()
    : place.geometry.location.lng;

  if (typeof lat !== 'number' || typeof lng !== 'number') {
    console.error('Invalid coordinates format from Google Place result');
    return null;
  }

  return { lat, lng };
};

const ListingGenerator = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedListing, setGeneratedListing] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [usingOpenAI, setUsingOpenAI] = useState(false);
  const [usingCache, setUsingCache] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [inputMode, setInputMode] = useState<'natural' | 'form'>('natural'); // Default to natural language input
  const [naturalLanguageInput, setNaturalLanguageInput] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionSuccess, setExtractionSuccess] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);
  // Address autocomplete and Zillow API integration
  const [selectedAddress, setSelectedAddress] = useState('');
  const [isLoadingZillowData, setIsLoadingZillowData] = useState(false);
  const [zillowData, setZillowData] = useState<any>(null);
  const [zillowError, setZillowError] = useState<string | null>(null);
  const [addressCorrectionSuggestions, setAddressCorrectionSuggestions] = useState<string[]>([]);
  const [showZillowDetails, setShowZillowDetails] = useState(false);
  const [showExtractedFields, setShowExtractedFields] = useState(false);
  const [selectedExtractedFields, setSelectedExtractedFields] = useState<string[]>([]);

  // Load saved selected fields from localStorage
  useEffect(() => {
    try {
      const savedFields = localStorage.getItem('selectedExtractedFields');
      if (savedFields) {
        setSelectedExtractedFields(JSON.parse(savedFields));
      }
    } catch (error) {
      console.warn('Failed to load selected fields from localStorage:', error);
    }
  }, []);

  // Define the form data interface with index signature for string keys
  interface FormData {
    propertyType: string;
    bedrooms: string;
    bathrooms: string;
    squareFeet: string;
    location: string;
    price: string;
    yearBuilt: string;
    features: string;
    description: string;
    lotSize: string;
    parking: string;
    appliances: string;
    pricePerSqFt: string;
    mlsId: string;
    hoaFees: string;
    llmProvider: string;
    listingType: 'sale' | 'rent';
    [key: string]: string;
  }

  const [formData, setFormData] = useState<FormData>({
    propertyType: '',
    bedrooms: '',
    bathrooms: '',
    squareFeet: '',
    location: '',
    price: '',
    yearBuilt: '',
    features: '',
    description: '',
    lotSize: '',
    parking: '',
    appliances: '',
    pricePerSqFt: '',
    mlsId: '',
    hoaFees: '',
    llmProvider: 'openai', // Default to OpenAI
    listingType: 'sale' // Default to 'sale' (vs 'rent')
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [propertyImages, setPropertyImages] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [imageError, setImageError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageDescriptions, setImageDescriptions] = useState<Array<{description: string; features: string[]}>>([]);
  const [isAnalyzingImages, setIsAnalyzingImages] = useState(false);
  const [isInsertingFeatures, setIsInsertingFeatures] = useState<number | null>(null);
  const fileInputRefForm = useRef<HTMLInputElement>(null);
  const fileInputRefNatural = useRef<HTMLInputElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  const handleNaturalLanguageInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNaturalLanguageInput(e.target.value);
    // Clear any previous errors or extraction results
    setError(null);
    setExtractionSuccess(false);
    setShowConfirmation(false);
    setExtractedData(null);
  };

  // Extract property data from natural language using selected LLM
  const extractPropertyData = async (): Promise<boolean> => {
    if (!naturalLanguageInput.trim()) {
      setError('Please enter a description of the property');
      return false;
    }

    setIsExtracting(true);
    setError(null);
    setExtractionSuccess(false);

    try {
      // Log which LLM is being used in development mode
      if (import.meta.env.DEV) {
        console.log(`%c[DEV] Using ${formData.llmProvider === 'openai' ? 'OpenAI' : 'Claude'} for property data extraction`,
          `color: ${formData.llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
      }

      let extractedPropertyData;

      if (formData.llmProvider === 'openai') {
        // Use OpenAI for extraction
        // Get OpenAI API key
        const apiKey = '********************************************************************************************************************************************************************';

        if (!apiKey) {
          throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
        }

        // Define the function schema for extracting property details
        const functionSchema = {
          name: 'extractPropertyDetails',
          description: 'Extract property details from natural language description',
          parameters: {
            type: 'object',
            properties: {
              propertyType: {
                type: 'string',
                description: 'Type of property (House, Condo, Townhouse, etc.)'
              },
              bedrooms: {
                type: 'string',
                description: 'Number of bedrooms'
              },
              bathrooms: {
                type: 'string',
                description: 'Number of bathrooms'
              },
              squareFeet: {
                type: 'string',
                description: 'Square footage of the property'
              },
              location: {
                type: 'string',
                description: 'Location of the property'
              },
              price: {
                type: 'string',
                description: 'Price of the property (numeric value only)'
              },
              yearBuilt: {
                type: 'string',
                description: 'Year the property was built (optional)'
              },
              features: {
                type: 'string',
                description: 'Key features of the property, comma separated (optional)'
              },
              description: {
                type: 'string',
                description: 'Additional details about the property (optional)'
              },
              listingType: {
                type: 'string',
                description: 'Whether the property is for sale or for rent (sale or rent)',
                enum: ['sale', 'rent']
              }
            },
            required: ['propertyType', 'bedrooms', 'bathrooms', 'squareFeet', 'location', 'price']
          }
        };

        // Create the system message with instructions for the AI
        const systemMessage = `You are an AI assistant for real estate agents. Your task is to extract property details from the user's natural language input. Use the extractPropertyDetails function to structure the property data.

        Pay special attention to whether the property is for sale or for rent. Look for clues like:
        - "for sale" or "asking price" or "selling for" indicates a sale listing
        - "for rent" or "monthly rent" or "per month" indicates a rental listing
        - If the price seems very low for the property type and location, it might be a monthly rental price
        - If no clear indication is given, default to "sale"

        Set the listingType field to either "sale" or "rent" accordingly.`;

        // Make the API request to OpenAI using axios
        const response = await axios.post(
          'https://api.openai.com/v1/chat/completions',
          {
            model: 'gpt-4o',
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'user', content: naturalLanguageInput }
            ],
            functions: [functionSchema],
            function_call: { name: 'extractPropertyDetails' },
            temperature: 0.7,
            max_tokens: 800
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`,
              'OpenAI-Beta': 'assistants=v1'
            }
          }
        );

        // Extract the function call arguments
        const functionCall = response.data.choices[0].message.function_call;

        if (functionCall && functionCall.name === 'extractPropertyDetails') {
          try {
            extractedPropertyData = JSON.parse(functionCall.arguments);
            console.log('Extracted property data with OpenAI:', extractedPropertyData);
          } catch (parseError) {
            console.error('Error parsing function call arguments:', parseError);
            throw new Error('Failed to parse property data');
          }
        } else {
          throw new Error('Function call not returned by the API');
        }
      } else {
        // Use Claude for extraction via backend
        extractedPropertyData = await extractPropertyDataWithClaude(naturalLanguageInput);
        console.log('Extracted property data with Claude:', extractedPropertyData);
      }

      if (extractedPropertyData) {
        // Store the extracted data
        setExtractedData(extractedPropertyData);

        // Update the form data with the extracted information
        setFormData({
          ...formData,
          propertyType: extractedPropertyData.propertyType || '',
          bedrooms: extractedPropertyData.bedrooms || '',
          bathrooms: extractedPropertyData.bathrooms || '',
          squareFeet: extractedPropertyData.squareFeet || '',
          location: extractedPropertyData.location || '',
          price: extractedPropertyData.price || '',
          yearBuilt: extractedPropertyData.yearBuilt || '',
          features: extractedPropertyData.features || '',
          description: extractedPropertyData.description || '',
          listingType: extractedPropertyData.listingType || formData.listingType || 'sale'
        });

        setExtractionSuccess(true);
        setShowConfirmation(true);
        return true;
      } else {
        throw new Error('Failed to extract property data');
      }
    } catch (error: any) {
      console.error('Error extracting property data:', error);

      // Provide more detailed error messages
      if (error.response) {
        if (error.response.status === 429) {
          setError(`${formData.llmProvider.toUpperCase()} API rate limit exceeded. Please try again in a few minutes.`);
        } else if (error.response.status === 401 || error.response.status === 403) {
          setError(`Authentication error with ${formData.llmProvider.toUpperCase()} API. Please check your API key.`);
        } else if (error.response.status >= 500) {
          setError(`${formData.llmProvider.toUpperCase()} server error. Please try again later.`);
        } else {
          setError(`Error: ${error.response.data.error?.message || 'Failed to extract property data'}`);
        }
      } else {
        setError(error.message || 'Failed to extract property data');
      }

      return false;
    } finally {
      setIsExtracting(false);
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    const requiredFields = ['propertyType', 'bedrooms', 'bathrooms', 'squareFeet', 'location', 'price'];

    requiredFields.forEach(field => {
      if (!formData[field as keyof typeof formData]) {
        errors[field] = 'This field is required';
      }
    });

    // Validate numeric fields
    if (formData.bedrooms && isNaN(Number(formData.bedrooms))) {
      errors.bedrooms = 'Must be a number';
    }
    if (formData.bathrooms && isNaN(Number(formData.bathrooms))) {
      errors.bathrooms = 'Must be a number';
    }
    if (formData.squareFeet && isNaN(Number(formData.squareFeet))) {
      errors.squareFeet = 'Must be a number';
    }
    if (formData.price && isNaN(Number(formData.price.replace(/,/g, '')))) {
      errors.price = 'Must be a number';
    }
    if (formData.yearBuilt && isNaN(Number(formData.yearBuilt))) {
      errors.yearBuilt = 'Must be a number';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // If using natural language input, extract data first
    if (inputMode === 'natural' && !showConfirmation) {
      if (!naturalLanguageInput.trim()) {
        setError('Please enter a description of the property');
        return;
      }

      setIsExtracting(true);

      try {
        // Extract property data from natural language
        const success = await extractPropertyData();

        if (!success) {
          setError('Failed to extract property details. Please try again or use the form input.');
          return;
        }

        // Stop here - don't generate yet, wait for confirmation
        return;
      } catch (error: any) {
        console.error('Error during natural language processing:', error);
        setError(error.message || 'An error occurred during processing');
        return;
      } finally {
        setIsExtracting(false);
      }
    } else if (showConfirmation) {
      // If we're in confirmation mode, proceed with the extracted data
      // The form data has already been updated with the extracted data
      setShowConfirmation(false);
    } else {
      // Using form input - validate form fields
      if (!validateForm()) {
        return;
      }
    }

    // At this point, we have valid form data (either from the form or extracted from natural language)
    setIsGenerating(true);
    setGeneratedListing(null);
    setUsingOpenAI(false);
    setUsingCache(false);
    setStatusMessage(null);

    try {
      // Log which LLM is being used in development mode
      if (import.meta.env.DEV) {
        console.log(`%c[DEV] Using ${formData.llmProvider === 'openai' ? 'OpenAI' : 'Claude'} for property listing generation`,
          `color: ${formData.llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
      }

      let listing;

      if (formData.llmProvider === 'openai') {
        // Use the hardcoded API key for now
        const apiKey = '********************************************************************************************************************************************************************';

        try {
          // Set up console log interceptor to capture status messages
          const originalConsoleLog = console.log;
          console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');

            if (message.includes('Using cached listing')) {
              setUsingCache(true);
              setStatusMessage('Using cached listing to optimize API usage');
            } else if (message.includes('Rate limiting')) {
              setStatusMessage(`Rate limiting in effect: Waiting before sending request`);
            } else if (message.includes('Rate limit hit')) {
              setStatusMessage(`Rate limit reached. Will retry once...`);
            } else if (message.includes('Rate limit error (429)')) {
              setStatusMessage(`OpenAI API rate limit reached. Will try one more time...`);
            } else if (message.includes('Server requested retry after')) {
              const seconds = message.match(/\d+(\.\d+)?/)?.[0] || '?';
              setStatusMessage(`OpenAI requested we wait ${seconds} seconds before retrying. Please be patient...`);
            } else if (message.includes('Using a 10-second delay')) {
              setStatusMessage(`Waiting 10 seconds before retrying request...`);
            } else if (message.includes('Retrying in')) {
              const seconds = message.match(/\d+(\.\d+)?/)?.[0] || '?';
              setStatusMessage(`Waiting ${seconds} seconds before retrying request...`);
            }
          };

          // Use the OpenAI API to generate the listing
          console.log('Using OpenAI API with key:', apiKey.substring(0, 10) + '...');
          listing = await generatePropertyListing(apiKey, formData);

          // Restore original console.log
          console.log = originalConsoleLog;

          setUsingOpenAI(true);

          // If no status message was set during the API call and we're not using cache,
          // it means a fresh listing was generated successfully
          if (!usingCache && !statusMessage) {
            setStatusMessage('Fresh listing generated successfully with OpenAI');
          }
        } catch (openAIError) {
          console.error('Error with OpenAI API:', openAIError);

          // Check if it's a rate limit error
          if (openAIError instanceof Error && openAIError.message.includes('rate limit')) {
            setError('OpenAI API rate limit reached. The system attempted one retry but was unsuccessful. Please try again later or use a different property configuration.');
            throw openAIError;
          } else {
            console.warn('Falling back to local generation');
            setStatusMessage('OpenAI API error - using local generation instead');
            listing = generateAIListing(formData);
          }
        }
      } else {
        // Use Claude via backend API
        try {
          setStatusMessage('Generating listing with Claude...');

          // Validate form data before sending to Claude
          const requiredFields = ['propertyType', 'bedrooms', 'bathrooms', 'squareFeet', 'location', 'price'];
          const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);

          if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
          }

          listing = await generatePropertyListingWithClaude(formData);
          setStatusMessage('Listing generated successfully with Claude');
        } catch (claudeError) {
          console.error('Error with Claude API:', claudeError);

          // Provide more specific error message
          if (claudeError instanceof Error && claudeError.message.includes('Missing required fields')) {
            setError(`${claudeError.message}. Please fill in all required fields.`);
          } else {
            setError(`Claude API error: ${claudeError instanceof Error ? claudeError.message : 'Unknown error'}. Falling back to local generation.`);
          }

          listing = generateAIListing(formData);
        }
      }

      // If we have image descriptions, append them to the listing
      if (imageDescriptions.length > 0) {
        const imageSection = `
## Property Highlights
${imageDescriptions.map((desc, index) => `### Image ${index + 1}
${desc.description}

${desc.features && desc.features.length > 0 ?
  `**Key Features**: ${desc.features.join(', ')}` :
  ''}`).join('\n\n')}
`;

        // Insert the image descriptions after the property overview section
        const sections = listing.split('##');
        if (sections.length > 2) {
          // Insert after the first section (usually the property overview)
          sections.splice(2, 0, imageSection);
          listing = sections.join('##');
        } else {
          // If we can't find sections, just append to the end
          listing += imageSection;
        }
      }

      setGeneratedListing(listing);
    } catch (error) {
      console.error('Error generating listing:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while generating the listing. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateAIListing = (data: typeof formData) => {
    // This is a placeholder for the actual AI generation
    // In a real application, this would call an API endpoint

    const features = data.features.split(',').map(f => f.trim()).filter(f => f);
    const featuresList = features.length > 0
      ? features.join(', ')
      : 'modern design, open floor plan, and quality finishes';

    const bedroomText = data.bedrooms === '1' ? 'bedroom' : 'bedrooms';
    const bathroomText = data.bathrooms === '1' ? 'bathroom' : 'bathrooms';

    const listingType = data.listingType || 'sale';
    const isSale = listingType === 'sale';

    // Adjust title based on listing type
    const title = isSale
      ? `Stunning ${data.bedrooms}-Bedroom ${data.propertyType} in ${data.location}`
      : `Beautiful ${data.bedrooms}-Bedroom ${data.propertyType} for Rent in ${data.location}`;

    // Adjust price label based on listing type
    const priceLabel = isSale ? 'Price' : 'Monthly Rent';

    const description = `
# ${title}

**${priceLabel}: $${Number(data.price).toLocaleString()}${!isSale ? '/month' : ''}**

## Property Overview
This exceptional ${data.propertyType.toLowerCase()} offers ${data.bedrooms} spacious ${bedroomText} and ${data.bathrooms} elegant ${bathroomText} within approximately ${Number(data.squareFeet).toLocaleString()} square feet of thoughtfully designed living space. ${data.yearBuilt ? `Built in ${data.yearBuilt}, this` : 'This'} property showcases ${featuresList}.

## Location
Situated in the highly desirable ${data.location} area, this home provides convenient access to top-rated schools, shopping centers, dining options, and recreational facilities. The neighborhood is known for its ${Math.random() > 0.5 ? 'friendly community atmosphere and well-maintained streets' : 'charm, safety, and proximity to major transportation routes'}.

## Features
* ${data.bedrooms} spacious ${bedroomText} with ample closet space
* ${data.bathrooms} modern ${bathroomText} with premium fixtures
* Approximately ${Number(data.squareFeet).toLocaleString()} square feet of living space
* ${features.length > 0 ? features.map(f => `Premium ${f.toLowerCase()}`).join('\n* ') : 'Open concept floor plan\n* High-end finishes throughout\n* Energy-efficient appliances\n* Smart home technology'}
${data.description ? `\n## Additional Information\n${data.description}` : ''}

## Why This Property Stands Out
${isSale
  ? `This exceptional home represents a rare opportunity to own a premium property in one of the most sought-after neighborhoods in ${data.location}. The thoughtful design, quality construction, and attention to detail make this a must-see for discerning buyers.

Don't miss your chance to make this stunning property your new home. Schedule a viewing today!`
  : `This exceptional rental property offers an ideal living experience in one of the most sought-after neighborhoods in ${data.location}. The thoughtful design, quality finishes, and convenient location make this a must-see for discerning renters.

Don't miss your chance to make this stunning property your new home. Schedule a viewing today and inquire about lease terms!`}
`;

    return description;
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageError(null);
    const files = e.target.files;

    if (!files || files.length === 0) return;

    // Check if adding these files would exceed the limit of 3 images
    if (propertyImages.length + files.length > 3) {
      setImageError('You can only upload up to 3 images');
      return;
    }

    // Validate file types
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const invalidFiles = Array.from(files).filter(file => !validTypes.includes(file.type));

    if (invalidFiles.length > 0) {
      setImageError('Only JPEG, PNG, GIF, and WebP images are allowed');
      return;
    }

    // Validate file sizes (max 5MB per file)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = Array.from(files).filter(file => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      setImageError('Images must be less than 5MB each');
      return;
    }

    // Add the new files to the state
    const newImages = [...propertyImages, ...Array.from(files)];
    setPropertyImages(newImages);

    // Create URLs for the new images
    const newUrls = Array.from(files).map(file => URL.createObjectURL(file));
    setImageUrls([...imageUrls, ...newUrls]);

    // Reset the file inputs
    if (fileInputRefForm.current) {
      fileInputRefForm.current.value = '';
    }
    if (fileInputRefNatural.current) {
      fileInputRefNatural.current.value = '';
    }
  };

  // Handle image removal
  const handleRemoveImage = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(imageUrls[index]);

    // Remove the image from the state
    const newImages = [...propertyImages];
    newImages.splice(index, 1);
    setPropertyImages(newImages);

    // Remove the URL from the state
    const newUrls = [...imageUrls];
    newUrls.splice(index, 1);
    setImageUrls(newUrls);

    // Remove the description from the state if it exists
    if (imageDescriptions.length > index) {
      const newDescriptions = [...imageDescriptions];
      newDescriptions.splice(index, 1);
      setImageDescriptions(newDescriptions);
    }

    // Update current image index if needed
    if (index <= currentImageIndex && currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    } else if (newUrls.length === 0) {
      setCurrentImageIndex(0);
    }
  };

  // Handle adding Zillow image to property images
  const handleAddZillowImage = async (imgSrc: string) => {
    setImageError(null);

    // Check if we already have 3 images
    if (propertyImages.length >= 3) {
      setImageError('You can only have up to 3 images. Remove an existing image first.');
      return;
    }

    // Check if this image is already added
    if (imageUrls.includes(imgSrc)) {
      setImageError('This image has already been added to your listing.');
      return;
    }

    try {
      // Fetch the image and convert it to a File object
      const response = await fetch(imgSrc);
      if (!response.ok) {
        throw new Error('Failed to fetch image');
      }

      const blob = await response.blob();
      const file = new File([blob], 'zillow-property-image.jpg', { type: blob.type || 'image/jpeg' });

      // Add the file to property images
      const newImages = [...propertyImages, file];
      setPropertyImages(newImages);

      // Add the URL to image URLs (use the original URL for display)
      const newUrls = [...imageUrls, imgSrc];
      setImageUrls(newUrls);

      showSuccess('Property image added to your listing!');
    } catch (error) {
      console.error('Error adding Zillow image:', error);
      setImageError('Failed to add the property image. Please try again.');
    }
  };

  // Analyze property images using the selected LLM provider
  const analyzeImages = async () => {
    if (propertyImages.length === 0) {
      setImageError('Please upload at least one image to analyze');
      return;
    }

    setIsAnalyzingImages(true);
    setError(null);
    setStatusMessage('Analyzing property images...');

    try {
      // Convert images to data URLs
      const imageDataUrls = await Promise.all(
        propertyImages.map(async (file) => {
          return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });
        })
      );

      // Get additional context from the form data
      const propertyType = formData.propertyType || undefined;
      const priceRange = formData.price ? `$${Number(formData.price).toLocaleString()}` : undefined;
      const locationHints = formData.location || undefined;

      // Determine target audience based on property type and price
      let targetAudience: string | undefined = undefined;
      if (formData.propertyType && formData.price) {
        const price = Number(formData.price.replace(/,/g, ''));
        if (price >= 1000000) {
          targetAudience = 'luxury buyers';
        } else if (formData.bedrooms && Number(formData.bedrooms) >= 3) {
          targetAudience = 'families';
        } else if (formData.propertyType.toLowerCase().includes('condo') || formData.propertyType.toLowerCase().includes('apartment')) {
          targetAudience = 'young professionals';
        }
      }

      // Call the backend to analyze the images
      const analysisResults = await Promise.all(
        imageDataUrls.map(async (imageUrl, index) => {
          try {
            console.log(`Analyzing image ${index + 1}...`);
            const result = await analyzePropertyImages(
              [imageUrl],
              formData.llmProvider as 'openai' | 'claude',
              propertyType,
              targetAudience,
              priceRange,
              locationHints
            );

            // Validate the result
            if (!result.features || !Array.isArray(result.features)) {
              console.warn(`Image ${index + 1} analysis returned invalid features:`, result.features);
              // Ensure features is always an array
              result.features = Array.isArray(result.features) ? result.features : [];
            }

            console.log(`Image ${index + 1} analysis complete:`, result);
            return result;
          } catch (error) {
            console.error(`Error analyzing image ${index + 1}:`, error);
            // Return a default result with empty features array
            return {
              description: `Failed to analyze image ${index + 1}. Please try again.`,
              features: []
            };
          }
        })
      );

      console.log('All image analysis results:', analysisResults);
      setImageDescriptions(analysisResults);

      // Auto-fill the description textarea with the generated description
      if (analysisResults.length > 0 && analysisResults[0].description) {
        // If in natural language mode, set the naturalLanguageInput
        if (inputMode === 'natural') {
          setNaturalLanguageInput(analysisResults[0].description);
        }
        // If in form mode, set the description field
        else {
          setFormData(prev => ({
            ...prev,
            description: analysisResults[0].description
          }));
        }
      }

      const successMessage = `Successfully analyzed ${analysisResults.length} image${analysisResults.length !== 1 ? 's' : ''}`;
      setStatusMessage(successMessage);
      showSuccess(successMessage);
    } catch (error) {
      console.error('Error analyzing property images:', error);
      const errorMessage = `Failed to analyze property images: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setError(errorMessage);
      // Show error message using toast instead of alert
      showError("Could not analyze image. Please try again or describe manually.");
    } finally {
      setIsAnalyzingImages(false);
    }
  };

  // Navigate to the next image
  const handleNextImage = () => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === imageUrls.length - 1 ? 0 : prevIndex + 1
      );
    }
  };

  // Navigate to the previous image
  const handlePrevImage = () => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === 0 ? imageUrls.length - 1 : prevIndex - 1
      );
    }
  };

  // Handle keyboard navigation for the carousel
  const handleCarouselKeyDown = (e: React.KeyboardEvent) => {
    if (imageUrls.length > 1) {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        handlePrevImage();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        handleNextImage();
      }
    }
  };

  const handleReset = () => {
    setFormData({
      propertyType: '',
      bedrooms: '',
      bathrooms: '',
      squareFeet: '',
      location: '',
      price: '',
      yearBuilt: '',
      features: '',
      description: '',
      lotSize: '',
      parking: '',
      appliances: '',
      pricePerSqFt: '',
      mlsId: '',
      hoaFees: '',
      llmProvider: formData.llmProvider, // Preserve the LLM provider
      listingType: formData.listingType, // Preserve the listing type
    });
    setGeneratedListing(null);
    setError(null);
    setUsingOpenAI(false);
    setUsingCache(false);
    setStatusMessage(null);
    setFormErrors({});
    setNaturalLanguageInput('');
    setIsExtracting(false);
    setExtractionSuccess(false);
    setShowConfirmation(false);
    setExtractedData(null);

    // Clear images
    imageUrls.forEach(url => URL.revokeObjectURL(url));
    setPropertyImages([]);
    setImageUrls([]);
    setImageDescriptions([]);
    setImageError(null);
    setCurrentImageIndex(0);
    setIsAnalyzingImages(false);

    // Reset Zillow-related state
    setSelectedAddress('');
    setZillowData(null);
    setZillowError(null);
    setAddressCorrectionSuggestions([]);
    setIsLoadingZillowData(false);
    setShowZillowDetails(false);
    setShowExtractedFields(false);
    setSelectedExtractedFields([]);
  };

  // Handle confirmation of extracted data
  const handleConfirmExtraction = () => {
    // Proceed with generating the listing using the extracted data
    setShowConfirmation(false);
    handleSubmit(new Event('submit') as unknown as React.FormEvent);
  };

  // Handle editing the extracted data
  const handleEditExtraction = () => {
    // Switch to form input mode with the extracted data pre-filled
    setInputMode('form');
    setShowConfirmation(false);
  };

  // Handle canceling the extraction
  const handleCancelExtraction = () => {
    setShowConfirmation(false);
    setExtractedData(null);
    setExtractionSuccess(false);
  };

  // Handle address selection from the autocomplete component
  const handleAddressSelect = (address: string, placeDetails?: any) => {
    setSelectedAddress(address);
    setZillowError(null);
    setAddressCorrectionSuggestions([]);

    // Update the location field with the selected address
    setFormData({
      ...formData,
      location: address
    });

    // If place details are provided, store them for later use
    if (placeDetails) {
      // We can automatically fetch Zillow data if we have place details
      handleFetchZillowData(placeDetails);
    }
  };

  // Fetch property data from Zillow API using address
  const handleFetchZillowData = async (placeDetails?: any) => {
    if (!selectedAddress) {
      setZillowError('Please select an address first');
      return;
    }

    setIsLoadingZillowData(true);
    setZillowError(null);
    setAddressCorrectionSuggestions([]);

    try {
      let streetAddress = '';
      let cityStateZip = '';

      // Extract address components from place details if available
      if (placeDetails && placeDetails.address_components) {
        console.log('Place details received:', {
          hasAddressComponents: !!placeDetails.address_components,
          addressComponentsLength: placeDetails.address_components?.length,
          addressComponents: placeDetails.address_components,
          formattedAddress: placeDetails.formatted_address,
          geometry: placeDetails.geometry
        });

        // Parse Google Places address components
        const components = placeDetails.address_components;
        const componentMap: Record<string, string> = {};

        // Map address components to their types
        components.forEach((component: any) => {
          component.types.forEach((type: string) => {
            componentMap[type] = component.long_name;
            if (type === 'administrative_area_level_1' && component.short_name) {
              componentMap['administrative_area_level_1_short'] = component.short_name;
            }
          });
        });

        console.log('Component map:', componentMap);

        // Build street address
        const streetNumber = componentMap['street_number'] || '';
        const route = componentMap['route'] || '';
        const subpremise = componentMap['subpremise'] ? `Apt ${componentMap['subpremise']}` : '';

        streetAddress = [streetNumber, route, subpremise].filter(Boolean).join(' ').trim();

        // Build city, state, zip
        const city = componentMap['locality'] || componentMap['sublocality'] || '';
        const state = componentMap['administrative_area_level_1_short'] || componentMap['administrative_area_level_1'] || '';
        const zip = componentMap['postal_code'] || '';

        cityStateZip = [city, state, zip].filter(Boolean).join(', ').trim();

        console.log('Extracted address components:', {
          streetAddress,
          cityStateZip,
          components: {
            streetNumber,
            route,
            subpremise,
            city,
            state,
            zip
          }
        });
      } else {
        console.log('No place details or address components available:', {
          hasPlaceDetails: !!placeDetails,
          placeDetailsKeys: placeDetails ? Object.keys(placeDetails) : 'none',
          hasAddressComponents: placeDetails?.address_components ? true : false
        });
      }

      // If we couldn't extract components, fall back to coordinates
      if (!streetAddress || !cityStateZip) {
        console.log('Could not extract address components, falling back to coordinates');

        let coordinates;

        // If place details are provided, extract coordinates directly
        if (placeDetails && placeDetails.geometry && placeDetails.geometry.location) {
          coordinates = extractCoordinatesFromPlace(placeDetails);
        } else {
          // Otherwise, geocode the address
          const geocodeResult = await fetchLatLngForAddress(selectedAddress);

          if (!geocodeResult) {
            throw new Error('Could not geocode the address');
          }

          coordinates = {
            lat: geocodeResult.lat,
            lng: geocodeResult.lng
          };
        }

        if (!coordinates) {
          throw new Error('Could not determine coordinates for this address');
        }

        // Fetch property data from Zillow API using coordinates (will fallback to address-based lookup)
        console.log('🔄 FALLBACK: Address-based lookup failed, trying coordinate-based fallback with original address');
        const rawPropertyData = await fetchPropertyDataByCoordinates(
          coordinates.lat,
          coordinates.lng,
          0.1, // 0.1 mile search radius
          true, // include sold properties
          selectedAddress // pass original address - this will trigger address-based lookup
        );

        console.log('Using coordinate-based lookup as fallback (will use address endpoint)');
        return processZillowData(rawPropertyData);
      }

      // Fetch property data from Zillow API using address
      console.log('🏠 FRONTEND ADDRESS PARSING:', {
        originalAddress: selectedAddress,
        parsedStreetAddress: streetAddress,
        parsedCityStateZip: cityStateZip,
        timestamp: new Date().toISOString()
      });
      console.log('Using address-based lookup with:', { streetAddress, cityStateZip });
      const rawPropertyData = await fetchPropertyDataByAddress(
        streetAddress,
        cityStateZip
      );

      return processZillowData(rawPropertyData);

    } catch (error) {
      console.error('Error fetching Zillow data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch property data';
      setZillowError(errorMessage);
      showError(errorMessage);

      // Get address correction suggestions
      try {
        const suggestions = await getAddressCorrectionSuggestions(selectedAddress, formData.llmProvider as 'openai' | 'claude');
        if (suggestions && suggestions.length > 0) {
          setAddressCorrectionSuggestions(suggestions);
          showInfo('We found some address suggestions that might help');
        }
      } catch (suggestionError) {
        console.error('Error getting address suggestions:', suggestionError);
      }
    } finally {
      setIsLoadingZillowData(false);
    }
  };

  // Process Zillow API response data
  const processZillowData = (rawPropertyData: any) => {
    if (!rawPropertyData) {
      throw new Error('No property data found');
    }

    // Log the raw data structure
    console.log('Raw Zillow data structure:', {
      type: typeof rawPropertyData,
      isArray: Array.isArray(rawPropertyData),
      keys: rawPropertyData ? Object.keys(rawPropertyData) : 'null',
      hasResoFacts: rawPropertyData.resoFacts ? true : false,
      hasAtAGlanceFacts: rawPropertyData.resoFacts?.atAGlanceFacts ? true : false,
      hasAddress: !!rawPropertyData.address,
      hasCoordinates: !!(rawPropertyData.latitude && rawPropertyData.longitude)
    });

    // Basic validation to ensure we have a valid object
    if (typeof rawPropertyData !== 'object' || rawPropertyData === null) {
      showError('Invalid Zillow data format. Please try again or enter details manually.');
      setZillowError('Invalid Zillow data format. Please try again or enter details manually.');
      return;
    }

    // Parse the Zillow property details using our new mapping function
    const mappedDetails: ZillowMappedPropertyDetails = parseZillowPropertyDetails(rawPropertyData, selectedAddress);

    // Log the mapped details
    console.log('Mapped Zillow details:', mappedDetails);

    // Check if we have address components
    const hasAddressComponents = !!(mappedDetails.address && (mappedDetails.city || mappedDetails.state || mappedDetails.zip));
    const isCoordinateBased = mappedDetails.address && mappedDetails.address.includes('Property at');

    // Check for address mismatch
    if (mappedDetails._addressMismatch) {
      console.warn('Address mismatch detected between requested and returned property');
      showWarning(`⚠️ Address Mismatch: Found property at "${mappedDetails.address}" instead of "${selectedAddress}". This may be a nearby property or the closest match available.`);
    }

    if (isCoordinateBased) {
      console.warn('Using coordinate-based address fallback');
      showInfo('Could not extract address components, falling back to coordinates');
    } else if (!hasAddressComponents) {
      console.warn('Missing address components');
      showInfo('Address information may be incomplete');
    }

    // Validate the mapped details
    const validation = validateZillowPropertyDetails(mappedDetails);

    // If there are validation issues, show warnings but continue
    if (!validation.valid) {
      console.warn('Zillow data validation issues:', validation.issues);
      showWarning(`Some property data may be invalid: ${validation.issues.join(', ')}`);
    }

    // Convert the mapped details to form fields
    const formFields = zillowDetailsToFormFields(mappedDetails);

    // Create AI context for prompts
    const aiContext = zillowDetailsToAiContext(mappedDetails);
    console.log('AI Context for prompts:', aiContext);

    // Store the original data for reference
    const propertyData = {
      ...rawPropertyData,
      _mappedDetails: mappedDetails,
      _formFields: formFields,
      _aiContext: aiContext,
      _fallback: rawPropertyData._fallback === true,
      _incomplete: !validation.valid,
      _isCoordinateBased: isCoordinateBased
    };

    // Set the data
    setZillowData(propertyData);

    // Check if this is fallback data
    const isFallbackData = rawPropertyData._fallback === true;

    // Initialize all available fields as selected
    const availableFields = [];
    if (mappedDetails.propertyType) availableFields.push('propertyType');
    if (mappedDetails.bedrooms !== null) availableFields.push('bedrooms');
    if (mappedDetails.bathrooms !== null) availableFields.push('bathrooms');
    if (mappedDetails.squareFeet !== null) availableFields.push('squareFeet');
    if (mappedDetails.yearBuilt !== null) availableFields.push('yearBuilt');
    if (mappedDetails.lotSize) availableFields.push('lotSize');
    if (mappedDetails.parking) availableFields.push('parking');
    if (mappedDetails.appliances && Array.isArray(mappedDetails.appliances) && mappedDetails.appliances.length > 0) availableFields.push('appliances');
    if (mappedDetails.pricePerSqFt !== null) availableFields.push('pricePerSqFt');
    if (mappedDetails.mlsId) availableFields.push('mlsId');
    if (mappedDetails.price !== null) availableFields.push('price');
    if (mappedDetails.hoaFee) availableFields.push('hoaFees');

    // Set all available fields as selected by default
    setSelectedExtractedFields(availableFields);

    // Show the extracted fields section
    setShowExtractedFields(true);

    // Show Zillow details section
    setShowZillowDetails(true);

    // If using fallback data, show a warning
    if (isFallbackData) {
      setZillowError('Using estimated property data. The Zillow API is currently unavailable. You can edit these values manually.');
      showWarning('Using estimated property data. The Zillow API is currently unavailable.');
    } else if (!validation.valid) {
      setZillowError(`Some property data may be invalid: ${validation.issues.join(', ')}`);
    } else {
      showSuccess('Property data retrieved successfully!');
    }
  };

  // Retry Zillow API fetch with a suggested address
  const retryFetchWithSuggestedAddress = (suggestedAddress: string) => {
    setSelectedAddress(suggestedAddress);
    setFormData({
      ...formData,
      location: suggestedAddress
    });

    // Clear previous suggestions
    setAddressCorrectionSuggestions([]);

    // Try to parse the address into components
    const addressParts = suggestedAddress.split(',').map(part => part.trim());

    if (addressParts.length >= 2) {
      // Assume first part is street address, rest is city/state/zip
      const streetAddress = addressParts[0];
      const cityStateZip = addressParts.slice(1).join(', ');

      console.log('Parsed suggested address:', { streetAddress, cityStateZip });

      // Fetch Zillow data with the suggested address components
      setIsLoadingZillowData(true);

      fetchPropertyDataByAddress(streetAddress, cityStateZip)
        .then(data => {
          processZillowData(data);
        })
        .catch(error => {
          console.error('Error fetching with suggested address:', error);
          showError('Failed to fetch property data with the suggested address');

          // Fall back to geocoding
          setTimeout(() => {
            handleFetchZillowData();
          }, 100);
        })
        .finally(() => {
          setIsLoadingZillowData(false);
        });
    } else {
      // If we can't parse the address, fall back to geocoding
      setTimeout(() => {
        handleFetchZillowData(); // Will geocode the address and then fetch Zillow data
      }, 100);
    }
  };

  // Toggle Zillow details section
  const toggleZillowDetails = () => {
    setShowZillowDetails(!showZillowDetails);
  };

  // Toggle extracted fields section
  const toggleExtractedFields = () => {
    const newValue = !showExtractedFields;
    console.log('Toggling extracted fields:', { current: showExtractedFields, new: newValue });
    setShowExtractedFields(newValue);

    // Show a toast notification for better user feedback
    if (newValue) {
      showInfo('Showing extracted property fields');
    }
  };

  // Toggle selection of an extracted field
  const handleToggleExtractedField = (fieldId: string) => {
    setSelectedExtractedFields(prev =>
      prev.includes(fieldId)
        ? prev.filter(id => id !== fieldId)
        : [...prev, fieldId]
    );

    // Save to localStorage
    try {
      const updatedFields = selectedExtractedFields.includes(fieldId)
        ? selectedExtractedFields.filter(id => id !== fieldId)
        : [...selectedExtractedFields, fieldId];
      localStorage.setItem('selectedExtractedFields', JSON.stringify(updatedFields));
    } catch (error) {
      console.warn('Failed to save selected fields to localStorage:', error);
    }
  };

  // Select all extracted fields
  const handleSelectAllFields = () => {
    if (!zillowData) return;

    // Get all available field IDs
    const allFields = [];
    if (zillowData.homeType || zillowData.propertyType) allFields.push('propertyType');
    if (zillowData.bedrooms) allFields.push('bedrooms');
    if (zillowData.bathrooms) allFields.push('bathrooms');
    if (zillowData.livingArea || zillowData.livingAreaSqFt) allFields.push('squareFeet');
    if (zillowData.yearBuilt) allFields.push('yearBuilt');
    if (zillowData.lotSize || zillowData.lotSizeSqFt) allFields.push('lotSize');
    if (Array.isArray(zillowData.parking) ? zillowData.parking.length > 0 : zillowData.parkingFeatures) allFields.push('parking');
    if (Array.isArray(zillowData.appliances) ? zillowData.appliances.length > 0 : zillowData.homeFeatures) allFields.push('appliances');
    if (zillowData.pricePerSqFt) allFields.push('pricePerSqFt');
    if (zillowData.mlsId || zillowData.mlsNumber) allFields.push('mlsId');
    if (zillowData.price || zillowData.zestimate) allFields.push('price');

    setSelectedExtractedFields(allFields);

    // Save to localStorage
    try {
      localStorage.setItem('selectedExtractedFields', JSON.stringify(allFields));
    } catch (error) {
      console.warn('Failed to save selected fields to localStorage:', error);
    }
  };

  // Deselect all extracted fields
  const handleDeselectAllFields = () => {
    setSelectedExtractedFields([]);

    // Save to localStorage
    try {
      localStorage.setItem('selectedExtractedFields', JSON.stringify([]));
    } catch (error) {
      console.warn('Failed to save selected fields to localStorage:', error);
    }
  };

  // Apply selected extracted fields to the form
  const handleApplyExtractedFields = () => {
    if (!zillowData || selectedExtractedFields.length === 0) return;

    // Get the mapped details and form fields from the Zillow data
    const mappedDetails = zillowData._mappedDetails as ZillowMappedPropertyDetails;
    const formFields = zillowData._formFields as Record<string, string>;

    if (!mappedDetails || !formFields) {
      console.error('Missing mapped details or form fields in Zillow data');
      showError('Could not apply fields. Please try fetching the property data again.');
      return;
    }

    console.log('Applying fields from:', { mappedDetails, formFields });

    // Create a new form data object with only the selected fields updated
    const updatedFormData = { ...formData };

    // Update only the selected fields from the form fields
    selectedExtractedFields.forEach(fieldId => {
      const value = formFields[fieldId];
      if (value !== undefined && value !== null) {
        updatedFormData[fieldId] = value;
      }
    });

    // Add HOA fees if available
    if (formFields.hoaFees && !updatedFormData.hoaFees) {
      updatedFormData.hoaFees = formFields.hoaFees;
    }

    // Get the AI context
    const aiContext = zillowData._aiContext;
    console.log('AI Context for prompts:', aiContext);

    // Update the form data
    setFormData(updatedFormData);

    // Show a success message or notification
    showSuccess(`Applied ${selectedExtractedFields.length} field(s) to the form`);
  };

  return (
    <div className="min-h-screen bg-dark-900 text-primary-100">
      <div className={styles.pageContainer}>
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className={styles.pageTitle}>AI Listing Generator</h1>
          <p className={styles.pageSubtitle}>
            Our advanced <span className="text-accent-cyan">agentic AI</span> technology creates compelling, professional property listings based on your input. Save time and impress clients with engaging, optimized content tailored to your specific property details.
          </p>

          {/* LLM Indicator - shows in all modes */}
          <div className="mt-4 py-1.5 px-3 bg-dark-800/50 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-dark-800/70 transition-colors">
            <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                 style={{ backgroundColor: formData.llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
            <span className="text-xs">
              Powered by <span className="font-semibold"
                style={{ color: formData.llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                {formData.llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
              </span>
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form Section */}
          <div className="lg:col-span-2">
            <div className={styles.card}>
              <div className={styles.cardHeader}>
                <h2 className={styles.cardTitle}>
                  <FaHome className={styles.cardIcon} />
                  Property Details
                </h2>
              </div>

              <div className={styles.cardBody}>
                {/* Input Mode Toggles */}
                <div className={styles.toggleContainer}>
                  <button
                    type="button"
                    onClick={() => setInputMode('natural')}
                    className={`${styles.toggleButton} ${inputMode === 'natural' ? styles.active : ''}`}
                  >
                    <FaCommentAlt />
                    Natural Language
                  </button>
                  <button
                    type="button"
                    onClick={() => setInputMode('form')}
                    className={`${styles.toggleButton} ${inputMode === 'form' ? styles.active : ''}`}
                  >
                    <FaList />
                    Form Input
                  </button>
                </div>

                {/* LLM Provider Toggle */}
                <div className={styles.toggleContainer}>
                  <button
                    type="button"
                    onClick={() => setFormData({...formData, llmProvider: 'openai'})}
                    className={`${styles.toggleButton} ${formData.llmProvider === 'openai' ? styles.active : ''}`}
                  >
                    <FaRobot style={{ color: formData.llmProvider === 'openai' ? 'white' : '#10a37f' }} />
                    OpenAI
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({...formData, llmProvider: 'claude'})}
                    className={`${styles.toggleButton} ${formData.llmProvider === 'claude' ? styles.active : ''}`}
                  >
                    <FaRobot style={{ color: formData.llmProvider === 'claude' ? 'white' : '#a27aff' }} />
                    Claude
                  </button>
                </div>

                {/* Listing Type Toggle */}
                <div className={styles.toggleContainer}>
                  <button
                    type="button"
                    onClick={() => setFormData({...formData, listingType: 'sale'})}
                    className={`${styles.toggleButton} ${formData.listingType === 'sale' ? styles.active : ''}`}
                  >
                    <FaDollarSign style={{ color: formData.listingType === 'sale' ? 'white' : '#10a37f' }} />
                    For Sale
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({...formData, listingType: 'rent'})}
                    className={`${styles.toggleButton} ${formData.listingType === 'rent' ? styles.active : ''}`}
                  >
                    <FaHome style={{ color: formData.listingType === 'rent' ? 'white' : '#a27aff' }} />
                    For Rent
                  </button>
                </div>
              </div>

              {error && (
                <div className="bg-red-500/10 border border-red-500/30 text-red-100 px-4 py-3 rounded-lg mb-6 shadow-sm">
                  <ValidationMessage type="error" message={error} />
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Natural Language Input */}
                {inputMode === 'natural' && !showConfirmation && (
                  <div className={styles.formGrid}>
                    {/* Image Upload Section */}
                    <div className={styles.inputGroup}>
                      <label htmlFor="propertyImagesNatural" className={styles.inputLabel}>
                        <FaImage className={styles.inputIcon} />
                        Property Images <span className={styles.optional}>(Up to 3 images)</span>
                      </label>

                      {/* Image Preview */}
                      {imageUrls.length > 0 && (
                        <div className={styles.imageGrid}>
                          {imageUrls.map((url, index) => (
                            <div key={index} className={styles.imageContainer}>
                              <img
                                src={url}
                                alt={`Property image ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                              <button
                                type="button"
                                onClick={() => handleRemoveImage(index)}
                                className={styles.removeButton}
                                aria-label="Remove image"
                              >
                                <FaTrash size={12} />
                              </button>

                              {/* AI-Detected Features Indicator */}
                              {imageDescriptions[index] && imageDescriptions[index].features && imageDescriptions[index].features.length > 0 && (
                                <div className="absolute top-1 right-8 z-20">
                                  <div className="bg-accent-cyan/20 text-xs rounded-full px-2 py-0.5 shadow border border-accent-cyan/30 text-accent-cyan">
                                    ✓ AI analyzed
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Upload Button */}
                      <div className="flex flex-wrap items-center gap-3 mb-2">
                        {propertyImages.length < 3 && (
                          <div className="flex items-center">
                            <input
                              type="file"
                              id="propertyImagesNatural"
                              ref={fileInputRefNatural}
                              accept="image/jpeg,image/png,image/gif,image/webp"
                              onChange={handleImageUpload}
                              className="hidden"
                              multiple={propertyImages.length < 2}
                            />
                            <label
                              htmlFor="propertyImagesNatural"
                              className={styles.uploadButton}
                            >
                              <FaPlus className={styles.uploadIcon} />
                              {propertyImages.length === 0 ? 'Add Images' : 'Add More Images'}
                            </label>
                            <span className="ml-3 text-xs text-primary-400">
                              {3 - propertyImages.length} {3 - propertyImages.length === 1 ? 'slot' : 'slots'} remaining
                            </span>
                          </div>
                        )}

                        {propertyImages.length > 0 && (
                          <button
                            type="button"
                            onClick={analyzeImages}
                            disabled={isAnalyzingImages}
                            className={styles.uploadButton}
                          >
                            {isAnalyzingImages ? (
                              <>
                                <div className="animate-spin mr-2 h-4 w-4 text-accent-cyan">
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                </div>
                                Analyzing...
                              </>
                            ) : (
                              <>
                                <FaRobot className={styles.uploadIcon} />
                                Analyze Images
                              </>
                            )}
                          </button>
                        )}
                      </div>

                      {/* Error Message */}
                      {imageError && (
                        <div className="mt-2">
                          <ValidationMessage type="error" message={imageError} />
                        </div>
                      )}

                      {/* Analysis Success Message */}
                      {imageDescriptions.length > 0 && !isAnalyzingImages && (
                        <div className="mt-2">
                          <ValidationMessage
                            type="success"
                            message={`${imageDescriptions.length} image${imageDescriptions.length !== 1 ? 's' : ''} analyzed successfully! You can now proceed with ${inputMode === 'natural' ? 'Extract & Generate' : 'Generate Listing'}`}
                          />

                          {/* Feature Selection Panel */}
                          {imageDescriptions.map((imageDesc, imgIndex) => (
                            imageDesc.features && imageDesc.features.length > 0 && (
                              <div key={imgIndex} className="bg-dark-800 rounded-lg p-4 mt-3 text-sm border border-dark-600 shadow-md">
                                <div className="font-medium mb-2 text-accent-cyan flex items-center">
                                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  AI-Detected Features (Image {imgIndex + 1}):
                                </div>
                                <div className="bg-dark-700/50 rounded-md p-3 mb-3">
                                  <ul className="space-y-1.5">
                                    {imageDesc.features.map((feature, featureIdx) => {
                                      // Create a unique ID for each feature checkbox
                                      const featureId = `feature-${imgIndex}-${featureIdx}`;
                                      return (
                                        <li key={featureId}>
                                          <label className="flex items-center space-x-2 cursor-pointer hover:bg-dark-600/30 p-1 rounded transition-colors">
                                            <input
                                              type="checkbox"
                                              id={featureId}
                                              defaultChecked={true}
                                              className="form-checkbox text-blue-500 rounded"
                                            />
                                            <span className="text-primary-100 text-xs">{feature}</span>
                                          </label>
                                        </li>
                                      );
                                    })}
                                  </ul>
                                </div>
                                <div className="flex justify-end">
                                  <button
                                    onClick={() => {
                                      // Set loading state for this specific button
                                      setIsInsertingFeatures(imgIndex);

                                      setTimeout(() => {
                                        try {
                                          // Get all checked features
                                          const checkboxes = document.querySelectorAll(`input[id^="feature-${imgIndex}-"]:checked`);
                                          const selectedFeatures = Array.from(checkboxes).map(cb => {
                                            const id = (cb as HTMLInputElement).id;
                                            const featureIdx = parseInt(id.split('-')[2]);
                                            return imageDesc.features[featureIdx];
                                          });

                                          if (selectedFeatures.length === 0) {
                                            setIsInsertingFeatures(null);
                                            return;
                                          }

                                          // Format the features into a readable phrase
                                          const featureText = selectedFeatures.join(', ');

                                          // Insert into the description textarea
                                          const currentText = naturalLanguageInput;
                                          const newText = currentText
                                            ? `${currentText}\n\nIncludes ${featureText}.`
                                            : `Includes ${featureText}.`;

                                          setNaturalLanguageInput(newText);
                                        } finally {
                                          // Clear loading state
                                          setIsInsertingFeatures(null);
                                        }
                                      }, 300); // Small delay for visual feedback
                                    }}
                                    disabled={isInsertingFeatures === imgIndex}
                                    className={`mt-1 text-sm text-white ${isInsertingFeatures === imgIndex ? 'bg-blue-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg transform hover:-translate-y-0.5'} px-4 py-2 rounded-lg shadow-md flex items-center justify-center transition-all duration-200 min-w-[200px]`}
                                  >
                                    {isInsertingFeatures === imgIndex ? (
                                      <>
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Inserting...
                                      </>
                                    ) : (
                                      <>
                                        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                        </svg>
                                        Insert Selected into Description
                                      </>
                                    )}
                                  </button>
                                </div>
                              </div>
                            )
                          ))}
                        </div>
                      )}
                    </div>

                    <div className={styles.inputGroup}>
                      <label htmlFor="naturalLanguageInput" className={styles.inputLabel}>
                        <FaFileAlt className={styles.inputIcon} />
                        Describe the Property <span className={styles.required}>*</span>
                      </label>
                      <textarea
                        id="naturalLanguageInput"
                        value={naturalLanguageInput}
                        onChange={handleNaturalLanguageInputChange}
                        rows={6}
                        placeholder="Describe the property in your own words. For example: 'I have a beautiful 3-bedroom, 2-bathroom house in San Francisco with 2000 square feet. It was built in 2010 and has hardwood floors, granite countertops, and a spacious backyard. The asking price is $1.2 million.'"
                        className={styles.input + ' ' + styles.textarea}
                        disabled={isExtracting}
                      ></textarea>
                      <p className="text-xs text-primary-400 mt-1">Our AI will extract the property details and generate a professional listing.</p>
                    </div>
                  </div>
                )}

                {/* Extraction Confirmation Panel */}
                {showConfirmation && (
                  <div className="bg-dark-800/70 p-6 rounded-xl border border-accent-cyan/50 text-primary-100 mb-6">
                    <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Extracted Property Details</span>
                    </h3>
                    <p className="text-primary-200 mb-4">We've extracted the following details from your description. Please confirm or edit before generating the listing.</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">Property Type:</span>
                        <p className="text-primary-100">{formData.propertyType || 'Not specified'}</p>
                      </div>
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">Location:</span>
                        <p className="text-primary-100">{formData.location || 'Not specified'}</p>
                      </div>
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">Bedrooms:</span>
                        <p className="text-primary-100">{formData.bedrooms || 'Not specified'}</p>
                      </div>
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">Bathrooms:</span>
                        <p className="text-primary-100">{formData.bathrooms || 'Not specified'}</p>
                      </div>
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">Square Feet:</span>
                        <p className="text-primary-100">{formData.squareFeet || 'Not specified'}</p>
                      </div>
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">{formData.listingType === 'rent' ? 'Monthly Rent' : 'Price'}:</span>
                        <p className="text-primary-100">${formData.price ? Number(formData.price).toLocaleString() : 'Not specified'}</p>
                      </div>
                      <div className="bg-dark-700/50 p-3 rounded-md">
                        <span className="text-xs text-primary-400">Listing Type:</span>
                        <p className="text-primary-100">{formData.listingType === 'rent' ? 'For Rent' : 'For Sale'}</p>
                      </div>
                      {formData.yearBuilt && (
                        <div className="bg-dark-700/50 p-3 rounded-md">
                          <span className="text-xs text-primary-400">Year Built:</span>
                          <p className="text-primary-100">{formData.yearBuilt}</p>
                        </div>
                      )}
                      {formData.features && (
                        <div className="bg-dark-700/50 p-3 rounded-md">
                          <span className="text-xs text-primary-400">Features:</span>
                          <p className="text-primary-100">{formData.features}</p>
                        </div>
                      )}
                    </div>

                    {formData.description && (
                      <div className="bg-dark-700/50 p-3 rounded-md mb-4">
                        <span className="text-xs text-primary-400">Additional Details:</span>
                        <p className="text-primary-100">{formData.description}</p>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-3 justify-center">
                      <button
                        type="button"
                        onClick={handleConfirmExtraction}
                        style={{
                          background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                          color: 'white',
                          padding: '0.75rem 1.5rem',
                          borderRadius: '9999px',
                          fontWeight: '500',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          transition: 'all 0.3s ease',
                          border: 'none',
                          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        }}
                        className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                      >
                        <span className="inline-flex items-center justify-center whitespace-nowrap">
                          <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
                          </svg>
                          Confirm & Generate Listing
                        </span>
                      </button>
                      <button
                        type="button"
                        onClick={handleEditExtraction}
                        style={{
                          background: 'white',
                          color: '#00b9ff',
                          padding: '0.75rem 1.5rem',
                          borderRadius: '9999px',
                          fontWeight: '500',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          transition: 'all 0.3s ease',
                          border: 'none',
                          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        }}
                        className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                      >
                        <span className="inline-flex items-center justify-center whitespace-nowrap">
                          <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          Edit Details
                        </span>
                      </button>
                      <button
                        type="button"
                        onClick={handleCancelExtraction}
                        style={{
                          background: 'rgba(255, 255, 255, 0.1)',
                          color: 'white',
                          padding: '0.75rem 1.5rem',
                          borderRadius: '9999px',
                          fontWeight: '500',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          transition: 'all 0.3s ease',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        }}
                        className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                      >
                        <span className="inline-flex items-center justify-center whitespace-nowrap">
                          <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                          Cancel
                        </span>
                      </button>
                    </div>
                  </div>
                )}

                {/* Form Input Fields - Only show when in form mode or when not showing confirmation */}
                {inputMode === 'form' && !showConfirmation && (
                  <>
                  {/* Image Upload Section */}
                  <div className="mb-6">
                    <label htmlFor="propertyImages" className="block text-sm font-medium text-primary-200 mb-2 flex items-center">
                      <FaImage className="mr-2 text-accent-cyan" />
                      Property Images <span className="text-xs text-primary-400 ml-2">(Up to 3 images)</span>
                    </label>

                    {/* Image Preview */}
                    {imageUrls.length > 0 && (
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                        {imageUrls.map((url, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-w-16 aspect-h-9 overflow-hidden rounded-lg border border-primary-700 bg-dark-700">
                              <img
                                src={url}
                                alt={`Property image ${index + 1}`}
                                className="object-cover w-full h-full brightness-90 hover:brightness-100 transition-all duration-300"
                              />
                            </div>
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(index)}
                              className="absolute top-2 right-2 bg-red-500/80 hover:bg-red-600 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-md"
                              style={{
                                background: 'rgba(220, 38, 38, 0.8)',
                                backdropFilter: 'blur(4px)'
                              }}
                              aria-label="Remove image"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                              </svg>
                            </button>

                            {/* AI-Detected Features Indicator */}
                            {imageDescriptions[index] && imageDescriptions[index].features && imageDescriptions[index].features.length > 0 && (
                              <div className="absolute top-2 left-2 z-20">
                                <div className="bg-accent-cyan/20 text-xs rounded-full px-2 py-0.5 shadow border border-accent-cyan/30 text-accent-cyan">
                                  ✓ AI analyzed
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Upload Button */}
                    <div className="flex flex-wrap items-center gap-3">
                      {propertyImages.length < 3 && (
                        <div className="flex items-center">
                          <input
                            type="file"
                            id="propertyImages"
                            ref={fileInputRefForm}
                            accept="image/jpeg,image/png,image/gif,image/webp"
                            onChange={handleImageUpload}
                            className="hidden"
                            multiple={propertyImages.length < 2}
                          />
                          <label
                            htmlFor="propertyImages"
                            className="cursor-pointer flex items-center justify-center px-4 py-2 bg-dark-700 hover:bg-dark-600 border border-primary-700 hover:border-accent-cyan rounded-md text-primary-200 transition-colors duration-300"
                          >
                            <FaPlus className="mr-2 text-accent-cyan" />
                            {propertyImages.length === 0 ? 'Add Images' : 'Add More Images'}
                          </label>
                          <span className="ml-3 text-xs text-primary-400">
                            {3 - propertyImages.length} {3 - propertyImages.length === 1 ? 'slot' : 'slots'} remaining
                          </span>
                        </div>
                      )}

                      {propertyImages.length > 0 && (
                        <button
                          type="button"
                          onClick={analyzeImages}
                          disabled={isAnalyzingImages}
                          className={`flex items-center justify-center px-4 py-2 ${isAnalyzingImages ? 'bg-dark-600 cursor-not-allowed' : 'bg-dark-700 hover:bg-dark-600'} border border-primary-700 hover:border-accent-cyan rounded-md text-primary-200 transition-colors duration-300`}
                        >
                          {isAnalyzingImages ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-accent-cyan" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Analyzing...
                            </>
                          ) : (
                            <>
                              <FaRobot className="mr-2 text-accent-cyan" />
                              Analyze Images
                            </>
                          )}
                        </button>
                      )}
                    </div>

                    {/* Error Message */}
                    {imageError && (
                      <div className="mt-2">
                        <ValidationMessage type="error" message={imageError} />
                      </div>
                    )}

                    {/* Analysis Success Message */}
                    {imageDescriptions.length > 0 && !isAnalyzingImages && (
                      <div className="mt-2 mb-2">
                        <ValidationMessage
                          type="success"
                          message={`${imageDescriptions.length} image${imageDescriptions.length !== 1 ? 's' : ''} analyzed successfully! You can now proceed with Generate Listing`}
                        />

                        {/* Feature Selection Panel */}
                        {imageDescriptions.map((imageDesc, imgIndex) => (
                          imageDesc.features && imageDesc.features.length > 0 && (
                            <div key={imgIndex} className="bg-dark-800 rounded-lg p-4 mt-3 text-sm border border-dark-600 shadow-md">
                              <div className="font-medium mb-2 text-accent-cyan flex items-center">
                                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                AI-Detected Features (Image {imgIndex + 1}):
                              </div>
                              <div className="bg-dark-700/50 rounded-md p-3 mb-3">
                                <ul className="space-y-1.5">
                                  {imageDesc.features.map((feature, featureIdx) => {
                                    // Create a unique ID for each feature checkbox
                                    const featureId = `form-feature-${imgIndex}-${featureIdx}`;
                                    return (
                                      <li key={featureId}>
                                        <label className="flex items-center space-x-2 cursor-pointer hover:bg-dark-600/30 p-1 rounded transition-colors">
                                          <input
                                            type="checkbox"
                                            id={featureId}
                                            defaultChecked={true}
                                            className="form-checkbox text-blue-500 rounded"
                                          />
                                          <span className="text-primary-100 text-xs">{feature}</span>
                                        </label>
                                      </li>
                                    );
                                  })}
                                </ul>
                              </div>
                              <div className="flex justify-end">
                                <button
                                  onClick={() => {
                                    // Set loading state for this specific button
                                    setIsInsertingFeatures(imgIndex);

                                    setTimeout(() => {
                                      try {
                                        // Get all checked features
                                        const checkboxes = document.querySelectorAll(`input[id^="form-feature-${imgIndex}-"]:checked`);
                                        const selectedFeatures = Array.from(checkboxes).map(cb => {
                                          const id = (cb as HTMLInputElement).id;
                                          const featureIdx = parseInt(id.split('-')[2]);
                                          return imageDesc.features[featureIdx];
                                        });

                                        if (selectedFeatures.length === 0) {
                                          setIsInsertingFeatures(null);
                                          return;
                                        }

                                        // Format the features into a readable phrase
                                        const featureText = selectedFeatures.join(', ');

                                        // Insert into the description textarea
                                        const currentText = formData.description;
                                        const newText = currentText
                                          ? `${currentText}\n\nIncludes ${featureText}.`
                                          : `Includes ${featureText}.`;

                                        setFormData({...formData, description: newText});
                                      } finally {
                                        // Clear loading state
                                        setIsInsertingFeatures(null);
                                      }
                                    }, 300); // Small delay for visual feedback
                                  }}
                                  disabled={isInsertingFeatures === imgIndex}
                                  className={`mt-1 text-sm text-white ${isInsertingFeatures === imgIndex ? 'bg-blue-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg transform hover:-translate-y-0.5'} px-4 py-2 rounded-lg shadow-md flex items-center justify-center transition-all duration-200 min-w-[200px]`}
                                >
                                  {isInsertingFeatures === imgIndex ? (
                                    <>
                                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                      Inserting...
                                    </>
                                  ) : (
                                    <>
                                      <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                      </svg>
                                      Insert Selected into Description
                                    </>
                                  )}
                                </button>
                              </div>
                            </div>
                          )
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Listing Type Toggle */}
                <div className="mb-6">
                  <label className="flex items-center text-sm font-medium text-primary-100 mb-2 whitespace-nowrap">
                    <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Listing Type <span className="text-accent-cyan">*</span>
                  </label>
                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, listingType: 'sale'})}
                      className={`flex-1 py-2.5 px-4 rounded-lg flex items-center justify-center transition-all duration-200 ${
                        formData.listingType === 'sale'
                          ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
                          : 'bg-dark-700 text-primary-300 border border-primary-700 hover:bg-dark-600'
                      }`}
                    >
                      <FaDollarSign className={`mr-2 ${formData.listingType === 'sale' ? 'text-white' : 'text-primary-400'}`} />
                      For Sale
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, listingType: 'rent'})}
                      className={`flex-1 py-2.5 px-4 rounded-lg flex items-center justify-center transition-all duration-200 ${
                        formData.listingType === 'rent'
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                          : 'bg-dark-700 text-primary-300 border border-primary-700 hover:bg-dark-600'
                      }`}
                    >
                      <FaHome className={`mr-2 ${formData.listingType === 'rent' ? 'text-white' : 'text-primary-400'}`} />
                      For Rent
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="propertyType" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                      Property Type <span className="text-accent-cyan">*</span>
                    </label>
                    <select
                      id="propertyType"
                      name="propertyType"
                      value={formData.propertyType}
                      onChange={handleChange}
                      className={`block w-full pl-4 pr-12 py-3 bg-dark-700 border ${formErrors.propertyType ? 'border-red-500' : 'border-primary-700 focus:border-accent-cyan'} rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base`}
                    >
                      <option value="" className="bg-dark-900 text-primary-200">Select property type</option>
                      <option value="House" className="bg-dark-900 text-primary-200">House</option>
                      <option value="Condo" className="bg-dark-900 text-primary-200">Condo</option>
                      <option value="Townhouse" className="bg-dark-900 text-primary-200">Townhouse</option>
                      <option value="Apartment" className="bg-dark-900 text-primary-200">Apartment</option>
                      <option value="Duplex" className="bg-dark-900 text-primary-200">Duplex</option>
                      <option value="Penthouse" className="bg-dark-900 text-primary-200">Penthouse</option>
                      <option value="Villa" className="bg-dark-900 text-primary-200">Villa</option>
                    </select>
                    {formErrors.propertyType && <div className="mt-1"><ValidationMessage type="error" message={formErrors.propertyType} /></div>}
                  </div>

                  <div>
                    <label htmlFor="location" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      Property Address <span className="text-accent-cyan">*</span>
                    </label>

                    {/* Google Places Autocomplete */}
                    <div className="mb-2">
                      <AddressAutocomplete
                        initialValue={formData.location}
                        onSelect={handleAddressSelect}
                        placeholder="Start typing an address..."
                        inputClassName="bg-dark-700 border border-primary-700 focus:border-accent-cyan text-primary-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-cyan"
                        dropdownClassName="bg-dark-800/95 backdrop-blur-sm border border-primary-700/50 shadow-glow-sm"
                      />
                    </div>

                    {/* Fetch Property Info Button */}
                    <div className="flex flex-wrap gap-3 mb-4">
                      <button
                        type="button"
                        onClick={handleFetchZillowData}
                        disabled={isLoadingZillowData || !selectedAddress}
                        className={`flex items-center justify-center gap-2 px-5 py-2 rounded-md ${
                          isLoadingZillowData || !selectedAddress
                            ? 'bg-blue-600/30 text-blue-300/70 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700 text-white font-medium shadow transition'
                        }`}
                      >
                        {isLoadingZillowData ? (
                          <>
                            <svg className="animate-spin w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Fetching...</span>
                          </>
                        ) : (
                          <>
                            <FaSearch className="w-5 h-5" />
                            <span>Fetch Property Info</span>
                          </>
                        )}
                      </button>

                      {/* Toggle Zillow Details Button - Only show if we have Zillow data */}
                      {zillowData && (
                        <div className="flex flex-wrap gap-3">
                          <button
                            type="button"
                            onClick={toggleZillowDetails}
                            className="secondary-button button-sm flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105 hover:shadow-glow-cyan"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={showZillowDetails
                                ? "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                                : "M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"} />
                            </svg>
                            <span className="text-sm font-medium">{showZillowDetails ? 'Hide Details' : 'Show Details'}</span>
                          </button>

                          <button
                            type="button"
                            onClick={toggleExtractedFields}
                            className="secondary-button button-sm flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105 hover:shadow-glow-cyan"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={showExtractedFields
                                ? "M6 18L18 6M6 6l12 12"
                                : "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"} />
                            </svg>
                            <span className="text-sm font-medium">{showExtractedFields ? 'Hide Extracted Fields' : 'Show Extracted Fields'}</span>
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Zillow Error Message */}
                    {zillowError && (
                      <div className="mt-2 text-sm text-red-400 bg-red-900/20 p-3 rounded-lg border border-red-900/30">
                        <div className="flex items-start">
                          <FaExclamationTriangle className="text-red-400 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <p className="font-medium">We couldn't retrieve property data for this address from Zillow.</p>
                            <p className="mt-1">You can still manually enter the details to continue generating your listing.</p>
                          </div>
                        </div>

                        {/* Address Correction Suggestions */}
                        {addressCorrectionSuggestions.length > 0 && (
                          <div className="mt-3 text-sm text-yellow-300">
                            <p className="flex items-center">
                              <FaInfoCircle className="mr-2" />
                              Zillow didn't recognize this address. Try one of these instead:
                            </p>
                            <ul className="mt-2 space-y-1.5 ml-6">
                              {addressCorrectionSuggestions.map((addr, idx) => (
                                <li key={idx}>
                                  <button
                                    onClick={() => retryFetchWithSuggestedAddress(addr)}
                                    className="underline text-blue-300 hover:text-blue-200 text-left"
                                  >
                                    {addr}
                                  </button>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Extracted Property Fields Section */}
                    {zillowData && showExtractedFields ? (
                      <ExtractedPropertyFields
                        propertyData={zillowData}
                        selectedFields={selectedExtractedFields}
                        onToggleField={handleToggleExtractedField}
                        onApplySelected={handleApplyExtractedFields}
                        onSelectAll={handleSelectAllFields}
                        onDeselectAll={handleDeselectAllFields}
                        isFallbackData={zillowData._fallback === true}
                        isIncompleteData={zillowData._incomplete === true}
                        validationIssue={zillowData._validationIssue}
                      />
                    ) : null}

                    {/* Zillow Property Details Section */}
                    {zillowData && showZillowDetails && (
                      <div className="mt-3 mb-4 bg-dark-800/70 p-4 rounded-lg border border-accent-cyan/30">
                        <h4 className="text-sm font-medium text-accent-cyan mb-2 flex items-center">
                          <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          {zillowData._fallback ? 'Estimated Property Details' : 'Property Details from Zillow'}
                        </h4>

                        {/* Show warning if using fallback data */}
                        {zillowData._fallback && (
                          <div className="mb-3 text-xs text-yellow-300 bg-yellow-900/20 p-2 rounded border border-yellow-900/30 flex items-start">
                            <FaExclamationTriangle className="text-yellow-300 mr-2 mt-0.5 flex-shrink-0" />
                            <span>
                              Using estimated property data. The Zillow API is currently unavailable.
                              You can edit these values manually.
                            </span>
                          </div>
                        )}

                        {/* Property Image Preview */}
                        {zillowData.imgSrc && (
                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <h5 className="text-xs font-medium text-accent-cyan flex items-center">
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                Property Image from Zillow
                              </h5>
                              <button
                                type="button"
                                onClick={() => handleAddZillowImage(zillowData.imgSrc)}
                                className="text-xs px-2 py-1 bg-accent-cyan/20 hover:bg-accent-cyan/30 text-accent-cyan rounded border border-accent-cyan/30 transition-colors duration-200"
                              >
                                Add to Listing
                              </button>
                            </div>
                            <div className="relative rounded-lg overflow-hidden border border-primary-700/50 bg-dark-700/30">
                              <img
                                src={zillowData.imgSrc}
                                alt="Property from Zillow"
                                className="w-full h-32 object-cover hover:scale-105 transition-transform duration-300"
                                onLoad={(e) => {
                                  // Image loaded successfully, ensure error message is hidden
                                  const errorDiv = e.currentTarget.nextElementSibling as HTMLElement;
                                  if (errorDiv) {
                                    errorDiv.style.display = 'none';
                                  }
                                }}
                                onError={(e) => {
                                  // Image failed to load, hide image and show error message
                                  e.currentTarget.style.display = 'none';
                                  const errorDiv = e.currentTarget.nextElementSibling as HTMLElement;
                                  if (errorDiv) {
                                    errorDiv.style.display = 'flex';
                                  }
                                }}
                              />
                              <div className="hidden w-full h-32 bg-dark-700/50 items-center justify-center text-primary-400 text-xs">
                                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                                Image failed to load
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {zillowData.bedrooms && (
                            <div className="bg-dark-700/50 p-2 rounded">
                              <span className="text-primary-400">Bedrooms:</span>
                              <p className="text-primary-100">{zillowData.bedrooms}</p>
                            </div>
                          )}

                          {zillowData.bathrooms && (
                            <div className="bg-dark-700/50 p-2 rounded">
                              <span className="text-primary-400">Bathrooms:</span>
                              <p className="text-primary-100">{zillowData.bathrooms}</p>
                            </div>
                          )}

                          {zillowData.livingArea && (
                            <div className="bg-dark-700/50 p-2 rounded">
                              <span className="text-primary-400">Square Feet:</span>
                              <p className="text-primary-100">{zillowData.livingArea}</p>
                            </div>
                          )}

                          {zillowData.lotSize && (
                            <div className="bg-dark-700/50 p-2 rounded">
                              <span className="text-primary-400">Lot Size:</span>
                              <p className="text-primary-100">{zillowData.lotSize}</p>
                            </div>
                          )}

                          {zillowData.yearBuilt && (
                            <div className="bg-dark-700/50 p-2 rounded">
                              <span className="text-primary-400">Year Built:</span>
                              <p className="text-primary-100">{zillowData.yearBuilt}</p>
                            </div>
                          )}

                          {zillowData.pricePerSqFt && (
                            <div className="bg-dark-700/50 p-2 rounded">
                              <span className="text-primary-400">Price Per SqFt:</span>
                              <p className="text-primary-100">${zillowData.pricePerSqFt}</p>
                            </div>
                          )}

                          {zillowData.parking && zillowData.parking.length > 0 && (
                            <div className="bg-dark-700/50 p-2 rounded col-span-2">
                              <span className="text-primary-400">Parking:</span>
                              <p className="text-primary-100">{zillowData.parking.join(', ')}</p>
                            </div>
                          )}

                          {zillowData.appliances && zillowData.appliances.length > 0 && (
                            <div className="bg-dark-700/50 p-2 rounded col-span-2">
                              <span className="text-primary-400">Appliances:</span>
                              <p className="text-primary-100">{zillowData.appliances.join(', ')}</p>
                            </div>
                          )}

                          {zillowData.mlsId && (
                            <div className="bg-dark-700/50 p-2 rounded col-span-2">
                              <span className="text-primary-400">MLS ID:</span>
                              <p className="text-primary-100">{zillowData.mlsId}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {formErrors.location && <div className="mt-1"><ValidationMessage type="error" message={formErrors.location} /></div>}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="bedrooms" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                      Bedrooms <span className="text-accent-cyan">*</span>
                    </label>
                    <input
                      type="text"
                      id="bedrooms"
                      name="bedrooms"
                      value={formData.bedrooms}
                      onChange={handleChange}
                      placeholder="3"
                      className={`block w-full pl-4 pr-12 py-3 bg-dark-700 border ${formErrors.bedrooms ? 'border-red-500' : 'border-primary-700 focus:border-accent-cyan'} rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base`}
                    />
                    {formErrors.bedrooms && <div className="mt-1"><ValidationMessage type="error" message={formErrors.bedrooms} /></div>}
                  </div>

                  <div>
                    <label htmlFor="bathrooms" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                      Bathrooms <span className="text-accent-cyan">*</span>
                    </label>
                    <input
                      type="text"
                      id="bathrooms"
                      name="bathrooms"
                      value={formData.bathrooms}
                      onChange={handleChange}
                      placeholder="2"
                      className={`block w-full pl-4 pr-12 py-3 bg-dark-700 border ${formErrors.bathrooms ? 'border-red-500' : 'border-primary-700 focus:border-accent-cyan'} rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base`}
                    />
                    {formErrors.bathrooms && <div className="mt-1"><ValidationMessage type="error" message={formErrors.bathrooms} /></div>}
                  </div>

                  <div className="min-w-[150px]">
                    <label htmlFor="squareFeet" className="inline-flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap w-full">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                      </svg>
                      <span className="flex-shrink-0">Square Feet <span className="text-accent-cyan">*</span></span>
                    </label>
                    <input
                      type="text"
                      id="squareFeet"
                      name="squareFeet"
                      value={formData.squareFeet}
                      onChange={handleChange}
                      placeholder="2000"
                      className={`block w-full pl-4 pr-12 py-3 bg-dark-700 border ${formErrors.squareFeet ? 'border-red-500' : 'border-primary-700 focus:border-accent-cyan'} rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base`}
                    />
                    {formErrors.squareFeet && <div className="mt-1"><ValidationMessage type="error" message={formErrors.squareFeet} /></div>}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="price" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Price <span className="text-accent-cyan">*</span>
                    </label>
                    <input
                      type="text"
                      id="price"
                      name="price"
                      value={formData.price}
                      onChange={handleChange}
                      placeholder="450000"
                      className={`block w-full pl-4 pr-12 py-3 bg-dark-700 border ${formErrors.price ? 'border-red-500' : 'border-primary-700 focus:border-accent-cyan'} rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base`}
                    />
                    {formErrors.price && <div className="mt-1"><ValidationMessage type="error" message={formErrors.price} /></div>}
                  </div>

                  <div>
                    <label htmlFor="yearBuilt" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Year Built <span className="text-primary-400">(optional)</span>
                    </label>
                    <input
                      type="text"
                      id="yearBuilt"
                      name="yearBuilt"
                      value={formData.yearBuilt}
                      onChange={handleChange}
                      placeholder="2010"
                      className={`block w-full pl-4 pr-12 py-3 bg-dark-700 border ${formErrors.yearBuilt ? 'border-red-500' : 'border-primary-700 focus:border-accent-cyan'} rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base`}
                    />
                    {formErrors.yearBuilt && <div className="mt-1"><ValidationMessage type="error" message={formErrors.yearBuilt} /></div>}
                  </div>
                </div>

                {/* Additional Zillow-sourced fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="lotSize" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                      </svg>
                      Lot Size <span className="text-primary-400">(optional)</span>
                    </label>
                    <input
                      type="text"
                      id="lotSize"
                      name="lotSize"
                      value={formData.lotSize}
                      onChange={handleChange}
                      placeholder="0.25 acres, 10,000 sqft, etc."
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    />
                  </div>

                  <div>
                    <label htmlFor="parking" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                      </svg>
                      Parking <span className="text-primary-400">(optional)</span>
                    </label>
                    <input
                      type="text"
                      id="parking"
                      name="parking"
                      value={formData.parking}
                      onChange={handleChange}
                      placeholder="2-car garage, carport, street parking, etc."
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="appliances" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      Appliances <span className="text-primary-400">(optional)</span>
                    </label>
                    <input
                      type="text"
                      id="appliances"
                      name="appliances"
                      value={formData.appliances}
                      onChange={handleChange}
                      placeholder="refrigerator, dishwasher, washer/dryer, etc."
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    />
                  </div>

                  <div>
                    <label htmlFor="pricePerSqFt" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Price Per SqFt <span className="text-primary-400">(optional)</span>
                    </label>
                    <input
                      type="text"
                      id="pricePerSqFt"
                      name="pricePerSqFt"
                      value={formData.pricePerSqFt}
                      onChange={handleChange}
                      placeholder="250"
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    />
                  </div>

                  <div>
                    <label htmlFor="hoaFees" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                      <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                      </svg>
                      HOA Fees <span className="text-primary-400">(optional)</span>
                    </label>
                    <input
                      type="text"
                      id="hoaFees"
                      name="hoaFees"
                      value={formData.hoaFees}
                      onChange={handleChange}
                      placeholder="300 per month"
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="features" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                    <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                    Key Features <span className="text-primary-400">(comma separated, optional)</span>
                  </label>
                  <input
                    type="text"
                    id="features"
                    name="features"
                    value={formData.features}
                    onChange={handleChange}
                    placeholder="hardwood floors, granite countertops, pool, etc."
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                  />
                </div>

                <div>
                  <label htmlFor="description" className="flex items-center text-sm font-medium text-primary-100 mb-1 whitespace-nowrap">
                    <svg className="h-4 w-4 text-primary-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Additional Details <span className="text-primary-400">(optional)</span>
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                    placeholder="Any additional information about the property..."
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                  ></textarea>
                </div>
                  </>
                )}

                {/* Loading Indicator for Extraction */}
                {isExtracting && (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="w-16 h-16 mb-4 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <svg className="animate-spin h-10 w-10 text-accent-cyan" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                      <div className="absolute inset-0 flex items-center justify-center animate-pulse opacity-50">
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-accent-cyan/20 to-primary-500/20"></div>
                      </div>
                    </div>
                    <p className="text-primary-200 text-center">Analyzing your property description...</p>
                    <p className="text-primary-400 text-sm text-center mt-2">Our AI is extracting property details to generate a professional listing.</p>
                  </div>
                )}

                {/* Form Buttons - Only show when not extracting or showing confirmation */}
                {!isExtracting && !showConfirmation && (
                  <div className="flex justify-center gap-4 pt-4">
                    <button
                      type="submit"
                      disabled={isGenerating}
                      style={{
                        background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                        color: 'white',
                        padding: '1rem 2rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      {isGenerating ? (
                        <span className="inline-flex items-center justify-center whitespace-nowrap">
                          <svg className="animate-spin mr-2 h-4 w-4 text-white" width="16" height="16" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Generating...
                        </span>
                      ) : (
                        <span className="inline-flex items-center justify-center whitespace-nowrap">
                          <svg className="w-4 h-4 mr-1.5" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          {inputMode === 'natural' ? 'Extract & Generate' : 'Generate Listing'}
                        </span>
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={handleReset}
                      style={{
                        background: 'white',
                        color: '#00b9ff',
                        padding: '1rem 2rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      <span className="inline-flex items-center justify-center whitespace-nowrap">
                        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Reset Form
                      </span>
                    </button>
                  </div>
                )}
              </form>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-8">
            {/* Tips Card */}
            <div className="glass-card p-6 animate-fade-in delay-100 border-glow">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <svg className="w-4 h-4 mr-1.5 text-accent-cyan flex-shrink-0" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Tips for Best Results</span>
              </h3>
              <ul className="space-y-3 text-primary-200">
                <li className="flex items-start">
                  <svg className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 mt-0.5" width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Be specific about property features to generate more detailed listings</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 mt-0.5" width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Include neighborhood details in the location field for better context</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 mt-0.5" width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Add unique selling points in the additional details section</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 mt-0.5" width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Review and personalize the AI-generated content before using it</span>
                </li>
              </ul>
            </div>

            {/* AI Badge */}
            <div className="glass-card p-6 animate-fade-in delay-200 border-glow">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-accent-cyan to-primary-500 flex items-center justify-center text-white mr-3 animate-pulse">
                  <svg className="w-5 h-5" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Agentic AI Powered</span>
                  <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-accent-cyan/20 text-accent-cyan rounded-full border border-accent-cyan/30">
                    {formData.llmProvider === 'claude' ? 'Claude 4' : 'OpenAI GPT-4o'}
                  </span>
                </h3>
              </div>
              <p className="text-primary-200 text-sm">
                Our advanced AI, powered by {formData.llmProvider === 'claude' ? "Anthropic's Claude 4 model" : "OpenAI's GPT-4o model"}, analyzes property details to create compelling, market-optimized listings that highlight key selling points and appeal to your target buyers. The agentic AI adapts to your specific property details to generate unique, engaging content.
              </p>
              <div className="mt-3 space-y-2">
                <div className="flex items-center text-xs text-primary-300">
                  <div className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 flex items-center justify-center">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span>Smart caching to optimize API usage</span>
                </div>
                <div className="flex items-center text-xs text-primary-300">
                  <div className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 flex items-center justify-center">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span>Rate limiting with single retry</span>
                </div>
                <div className="flex items-center text-xs text-primary-300">
                  <div className="w-3.5 h-3.5 mr-1.5 text-accent-cyan flex-shrink-0 flex items-center justify-center">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span>Optimized token usage for cost efficiency</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Generated Listing Section */}
        {generatedListing && (
          <div className="mt-12 animate-fade-in">
            <div className="glass-card p-8 border-glow">
              <h2 className="text-2xl font-bold text-white mb-3 flex items-center">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Generated Listing</span>
                <div className="flex items-center">
                  {(usingOpenAI || formData.llmProvider) && (
                    <span className="ml-3 px-2 py-1 text-xs font-medium bg-accent-cyan/20 text-accent-cyan rounded-full border border-accent-cyan/30 flex items-center">
                      <div className="w-3 h-3 mr-1 flex items-center justify-center">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M13 10V3L4 14h7v7l9-11h-7z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      {formData.llmProvider === 'claude' ? 'Claude 4 Powered' : 'OpenAI Powered'}
                    </span>
                  )}
                  {usingCache && (
                    <span className="ml-2 px-2 py-1 text-xs font-medium bg-green-500/20 text-green-300 rounded-full border border-green-500/30 flex items-center">
                      <div className="w-3 h-3 mr-1 flex items-center justify-center">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      Cached
                    </span>
                  )}
                </div>
                <div className="flex-grow border-t border-primary-800/30 ml-4"></div>
              </h2>

              {statusMessage && (
                <div className="mb-4 px-4 py-2 bg-primary-800/50 border border-primary-700/50 rounded-md text-sm text-primary-200">
                  <ValidationMessage
                    type={statusMessage.includes('successfully') ? 'success' : 'info'}
                    message={statusMessage}
                  />
                </div>
              )}

              <div className="bg-dark-800/70 p-6 rounded-xl border border-primary-700/50 text-primary-100 prose prose-invert max-w-none">
                {/* Property Images Carousel */}
                {imageUrls.length > 0 && (
                  <div
                    className="mb-6 relative"
                    onKeyDown={handleCarouselKeyDown}
                    tabIndex={0}
                  >
                    {/* Current Image */}
                    <div
                      className="overflow-hidden rounded-xl border border-primary-700/50 shadow-lg aspect-w-16 aspect-h-9 relative"
                      role="region"
                      aria-roledescription="carousel"
                      aria-label="Property images"
                    >
                      <img
                        src={imageUrls[currentImageIndex]}
                        alt={`Property image ${currentImageIndex + 1} of ${imageUrls.length}`}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-500 ease-in-out"
                      />

                      {/* Navigation Arrows - Only show if there are multiple images */}
                      {imageUrls.length > 1 && (
                        <>
                          {/* Left Arrow */}
                          <button
                            onClick={handlePrevImage}
                            onKeyDown={(e) => e.key === 'Enter' && handlePrevImage()}
                            style={{
                              background: 'white',
                              color: '#00b9ff',
                              padding: '0.5rem',
                              borderRadius: '9999px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              transition: 'all 0.3s ease',
                              border: 'none',
                              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                            }}
                            className="absolute left-4 top-1/2 transform -translate-y-1/2 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-accent-cyan hover:shadow-lg hover:-translate-y-0.5 hover:scale-105"
                            aria-label="Previous image"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 19l-7-7 7-7" />
                            </svg>
                          </button>

                          {/* Right Arrow */}
                          <button
                            onClick={handleNextImage}
                            onKeyDown={(e) => e.key === 'Enter' && handleNextImage()}
                            style={{
                              background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                              color: 'white',
                              padding: '0.5rem',
                              borderRadius: '9999px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              transition: 'all 0.3s ease',
                              border: 'none',
                              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                            }}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-accent-cyan hover:shadow-lg hover:-translate-y-0.5 hover:scale-105"
                            aria-label="Next image"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
                            </svg>
                          </button>

                          {/* Screen reader text for current slide */}
                          <div className="sr-only" aria-live="polite">
                            Image {currentImageIndex + 1} of {imageUrls.length}
                          </div>
                        </>
                      )}
                    </div>

                    {/* Image Indicators */}
                    {imageUrls.length > 1 && (
                      <div className="flex justify-center mt-4 space-x-3" role="tablist" aria-label="Image navigation">
                        {imageUrls.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            onKeyDown={(e) => e.key === 'Enter' && setCurrentImageIndex(index)}
                            style={{
                              background: index === currentImageIndex
                                ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                                : 'rgba(255, 255, 255, 0.1)',
                              color: 'white',
                              padding: '0.25rem 0.5rem',
                              minWidth: '1.5rem',
                              borderRadius: '9999px',
                              fontWeight: '500',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              transition: 'all 0.3s ease',
                              border: index === currentImageIndex ? 'none' : '1px solid rgba(255, 255, 255, 0.2)',
                              boxShadow: index === currentImageIndex ? '0 4px 6px rgba(0, 0, 0, 0.1)' : 'none',
                              fontSize: '0.75rem',
                            }}
                            className={`transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-accent-cyan hover:shadow-lg hover:-translate-y-0.5 hover:scale-105`}
                            aria-label={`Go to image ${index + 1}`}
                            aria-selected={index === currentImageIndex}
                            role="tab"
                            tabIndex={index === currentImageIndex ? 0 : -1}
                          >
                            {index + 1}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Property Header with Price, Address, and Details */}
                <div className="mb-6 pb-6 border-b border-primary-700/50">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-end">
                    <div>
                      {/* Price */}
                      <div className="text-4xl font-bold text-accent-cyan mb-2">
                        ${Number(formData.price).toLocaleString()}
                      </div>

                      {/* Address */}
                      <div className="text-xl text-primary-100 mb-3">
                        {formData.location}
                      </div>

                      {/* Property Details */}
                      <div className="flex items-center space-x-4 text-primary-300">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                          </svg>
                          <span>{formData.propertyType}</span>
                        </div>
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                          </svg>
                          <span>{formData.bedrooms} Beds</span>
                        </div>
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>{formData.bathrooms} Baths</span>
                        </div>
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                          </svg>
                          <span>{Number(formData.squareFeet).toLocaleString()} Sq Ft</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* AI Generated Content */}
                {generatedListing.split('\n').map((line, index) => {
                  if (line.startsWith('# ')) {
                    return <h1 key={index} className="text-2xl font-bold text-accent-cyan mt-0">{line.substring(2)}</h1>;
                  } else if (line.startsWith('## ')) {
                    return <h2 key={index} className="text-xl font-bold text-primary-300 mt-4">{line.substring(3)}</h2>;
                  } else if (line.startsWith('* ')) {
                    return <div key={index} className="flex items-start my-1">
                      <span className="text-accent-cyan mr-2">•</span>
                      <span>{line.substring(2)}</span>
                    </div>;
                  } else if (line.startsWith('**')) {
                    return <p key={index} className="font-bold text-accent-cyan">{line.replace(/\*\*/g, '')}</p>;
                  } else if (line.trim() === '') {
                    return <div key={index} className="h-4"></div>;
                  } else {
                    return <p key={index}>{line}</p>;
                  }
                })}
              </div>

              <div className="mt-6 flex flex-col sm:flex-row gap-4">
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(generatedListing);
                    alert('Listing copied to clipboard!');
                  }}
                  style={{
                    background: 'white',
                    color: '#00b9ff',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                >
                  <svg className="w-4 h-4 mr-1.5" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                  Copy to Clipboard
                </button>
                <button
                  onClick={handleReset}
                  style={{
                    background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                    color: 'white',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                >
                  <span className="inline-flex items-center justify-center whitespace-nowrap">
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Generate New Listing
                  </span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ListingGenerator;
