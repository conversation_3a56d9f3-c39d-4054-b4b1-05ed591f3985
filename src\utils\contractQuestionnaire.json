{"steps": [{"id": "step1", "title": "Buyer Information", "questions": [{"id": "buyerName", "text": "What is the buyer's full name?", "field": "buyer.fullName", "type": "text", "required": true, "defaultValue": "", "conditionalOn": null}, {"id": "buyerEmail", "text": "What is the buyer's email address?", "field": "buyer.email", "type": "text", "required": true, "defaultValue": "", "conditionalOn": null}]}, {"id": "step2", "title": "Seller Information", "questions": [{"id": "sellerName", "text": "What is the seller's full name?", "field": "seller.fullName", "type": "text", "required": true, "defaultValue": "", "conditionalOn": null}]}, {"id": "step3", "title": "Property Details", "questions": [{"id": "propertyAddress", "text": "What is the property address?", "field": "property.address", "type": "textarea", "required": true, "defaultValue": "", "conditionalOn": null}, {"id": "propertyUnit", "text": "What is the unit number (if applicable)?", "field": "property.unit", "type": "text", "required": false, "defaultValue": "", "conditionalOn": null}]}, {"id": "step4", "title": "Parking", "questions": [{"id": "parkingIncluded", "text": "Does the property include parking?", "field": "property.parkingIncluded", "type": "checkbox", "required": false, "defaultValue": false, "conditionalOn": null}, {"id": "parkingDetails", "text": "Please provide details about the parking (space number, location, etc.)", "field": "property.parkingDetails", "type": "text", "required": false, "defaultValue": "", "conditionalOn": "property.parkingIncluded"}]}, {"id": "step5", "title": "Financial Details", "questions": [{"id": "purchasePrice", "text": "What is the purchase price of the property?", "field": "financials.purchasePrice", "type": "number", "required": true, "defaultValue": "", "conditionalOn": null}, {"id": "<PERSON><PERSON><PERSON>", "text": "How much earnest money will be provided? (Typically 5% of purchase price)", "field": "financials.earnestMoney", "type": "number", "required": false, "defaultValue": "", "conditionalOn": null}, {"id": "closingDate", "text": "What is the expected closing date?", "field": "financials.closingDate", "type": "date", "required": false, "defaultValue": "", "conditionalOn": null}]}, {"id": "step6", "title": "HOA Information", "questions": [{"id": "hoaExists", "text": "Is there a Homeowners Association (HOA)?", "field": "associations.hoa", "type": "checkbox", "required": false, "defaultValue": false, "conditionalOn": null}, {"id": "hoaFees", "text": "What are the monthly HOA fees?", "field": "associations.monthlyFees", "type": "number", "required": false, "defaultValue": "", "conditionalOn": "associations.hoa"}]}, {"id": "step7", "title": "Fixtures & Appliances", "questions": [{"id": "fixturesIncluded", "text": "Which fixtures and appliances are included in the sale?", "field": "fixturesIncluded", "type": "multiselect", "options": ["Refrigerator", "Oven/Range", "Dishwasher", "Microwave", "<PERSON>her", "Dryer", "Window Treatments", "Light Fixtures", "Ceiling Fans", "Water Heater", "HVAC System"], "required": false, "defaultValue": [], "conditionalOn": null}]}, {"id": "step8", "title": "Additional Terms", "questions": [{"id": "additionalTerms", "text": "Are there any additional terms or conditions for this contract?", "field": "additionalTerms", "type": "textarea", "required": false, "defaultValue": "", "conditionalOn": null}]}]}