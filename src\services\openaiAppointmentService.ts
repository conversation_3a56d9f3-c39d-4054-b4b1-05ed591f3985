// OpenAI Appointment Service
// This service handles appointment data extraction using OpenAI via the backend

const API_BASE_URL = 'http://localhost:3001/api';

// Function to extract appointment data from natural language using OpenAI via backend
export const extractAppointmentDataWithOpenAI = async (naturalLanguageInput: string): Promise<any> => {
  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to OpenAI backend for appointment data extraction', 'color: #00b9ff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/openai/extract-appointment-data`);
      console.log('Input length:', naturalLanguageInput.length);
    }
    
    const response = await fetch(`${API_BASE_URL}/openai/extract-appointment-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ naturalLanguageInput }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    
    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from OpenAI backend (appointment data extraction)', 'color: #00b9ff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Extracted data:', data.extractedData);
    }
    
    return data.extractedData;
  } catch (error) {
    console.error('Error extracting appointment data with OpenAI backend:', error);
    throw error;
  }
};
