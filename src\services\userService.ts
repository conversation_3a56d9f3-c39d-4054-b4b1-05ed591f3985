import api from './api';

// Types
export interface User {
  _id: string;
  name: string;
  email: string;
  isAdmin: boolean;
  isRealtor: boolean;
  isVerified?: boolean;
  token?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  verificationCode?: string;
}

export interface UpdateProfileData {
  name?: string;
  email?: string;
  password?: string;
}

// User API calls
const userService = {
  // Request verification code before registration
  requestVerificationCode: async (email: string): Promise<{ message: string, verificationCode?: string }> => {
    const { data } = await api.post('/users/verify/request-code', { email });
    return data;
  },

  // Check verification code
  checkVerificationCode: async (email: string, code: string): Promise<{ message: string, valid: boolean }> => {
    const { data } = await api.post('/users/verify/check-code', { email, code });
    return data;
  },

  // Send verification email (for existing accounts)
  sendVerificationEmail: async (email: string): Promise<{ message: string }> => {
    const { data } = await api.post('/users/verify/send', { email });
    return data;
  },

  // Verify email with token
  verifyEmail: async (token: string): Promise<{ message: string }> => {
    const { data } = await api.get(`/users/verify/${token}`);
    return data;
  },
  // Login user
  login: async (credentials: LoginCredentials): Promise<User> => {
    const { data } = await api.post('/users/login', credentials);
    localStorage.setItem('userToken', data.token);
    localStorage.setItem('userInfo', JSON.stringify(data));
    return data;
  },

  // Register user
  register: async (userData: RegisterData): Promise<{ message: string }> => {
    const { data } = await api.post('/users', userData);
    // No longer automatically logging in the user
    return data;
  },

  // Get user profile
  getProfile: async (): Promise<User> => {
    const { data } = await api.get('/users/profile');
    return data;
  },

  // Update user profile
  updateProfile: async (profileData: UpdateProfileData): Promise<User> => {
    const { data } = await api.put('/users/profile', profileData);
    localStorage.setItem('userToken', data.token);
    localStorage.setItem('userInfo', JSON.stringify(data));
    return data;
  },

  // Logout user
  logout: (): void => {
    localStorage.removeItem('userToken');
    localStorage.removeItem('userInfo');
  },

  // Get all users (admin only)
  getUsers: async (): Promise<User[]> => {
    const { data } = await api.get('/users');
    return data;
  },

  // Delete user (admin only)
  deleteUser: async (id: string): Promise<{ message: string }> => {
    const { data } = await api.delete(`/users/${id}`);
    return data;
  },

  // Get user by ID (admin only)
  getUserById: async (id: string): Promise<User> => {
    const { data } = await api.get(`/users/${id}`);
    return data;
  },

  // Update user (admin only)
  updateUser: async (id: string, userData: Partial<User>): Promise<User> => {
    const { data } = await api.put(`/users/${id}`, userData);
    return data;
  },

  // Clear all users (development only)
  clearAllUsers: async (): Promise<{ message: string, emailsCleared: string[] }> => {
    const { data } = await api.delete('/users/clear-all');
    return data;
  },
};

export default userService;
