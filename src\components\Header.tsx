import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FaHome, FaBars, FaTimes, FaUserCircle, FaTools, FaSignOutAlt } from 'react-icons/fa';
import { useApp } from '../context/AppContext';

const Header = () => {
  const { state, logout, toggleSidebar } = useApp();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsUserMenuOpen(false);
  }, [location]);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const handleLogin = () => {
    // In a real app, this would navigate to a login page
    // For demo purposes, we'll simulate a login
    navigate('/');
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled
        ? 'bg-white/90 backdrop-blur-md shadow-md py-3'
        : 'bg-transparent py-5'}`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link
          to="/"
          className={`flex items-center space-x-2 ${isScrolled ? 'text-primary-600' : 'text-white'}`}
        >
          <FaHome className="w-5 h-5" />
          <span className="text-2xl font-bold tracking-tight">Digital Realtor</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:block">
          <ul className="flex space-x-8">
            <li>
              <Link
                to="/"
                className={`font-medium transition-colors ${isScrolled
                  ? 'text-dark-600 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'} ${location.pathname === '/' ? 'border-b-2 border-primary-500' : ''}`}
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                to="/properties"
                className={`font-medium transition-colors ${isScrolled
                  ? 'text-dark-600 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'} ${location.pathname === '/properties' ? 'border-b-2 border-primary-500' : ''}`}
              >
                Properties
              </Link>
            </li>
            <li>
              <Link
                to="/about"
                className={`font-medium transition-colors ${isScrolled
                  ? 'text-dark-600 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'} ${location.pathname === '/about' ? 'border-b-2 border-primary-500' : ''}`}
              >
                About
              </Link>
            </li>
            <li>
              <Link
                to="/contact"
                className={`font-medium transition-colors ${isScrolled
                  ? 'text-dark-600 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'} ${location.pathname === '/contact' ? 'border-b-2 border-primary-500' : ''}`}
              >
                Contact
              </Link>
            </li>
            {state.user.isAuthenticated && state.user.role === 'agent' && (
              <li>
                <Link
                  to="/agent"
                  className={`font-medium transition-colors ${isScrolled
                    ? 'text-dark-600 hover:text-primary-600'
                    : 'text-white/90 hover:text-white'} ${location.pathname.startsWith('/agent') ? 'border-b-2 border-primary-500' : ''}`}
                >
                  Agent Tools
                </Link>
              </li>
            )}
          </ul>
        </nav>

        {/* User Menu (Desktop) */}
        <div className="hidden md:block relative">
          {state.user.isAuthenticated ? (
            <div>
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className={`flex items-center space-x-2 font-medium transition-colors ${isScrolled
                  ? 'text-dark-600 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'}`}
              >
                <FaUserCircle className="text-xl" />
                <span>{state.user.name}</span>
              </button>

              {isUserMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-10">
                  {state.user.role === 'agent' && (
                    <Link
                      to="/agent"
                      className="block px-4 py-2 text-dark-600 hover:bg-gray-100 flex items-center"
                    >
                      <FaTools className="mr-2" />
                      Agent Tools
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-dark-600 hover:bg-gray-100 flex items-center"
                  >
                    <FaSignOutAlt className="mr-2" />
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLogin}
                className={`flex items-center space-x-2 font-medium transition-colors ${isScrolled
                  ? 'text-dark-600 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'}`}
              >
                <FaUserCircle />
                <span>Sign In</span>
              </button>
              <button className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-5 py-2 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5">
                Get Started
              </button>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-2xl"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? (
            <FaTimes className={isScrolled ? 'text-dark-800' : 'text-white'} />
          ) : (
            <FaBars className={isScrolled ? 'text-dark-800' : 'text-white'} />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white shadow-lg animate-fade-in">
          <nav className="container mx-auto px-4 py-5">
            <ul className="space-y-4">
              <li>
                <Link
                  to="/"
                  className={`block font-medium text-dark-600 hover:text-primary-600 ${location.pathname === '/' ? 'text-primary-600' : ''}`}
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/properties"
                  className={`block font-medium text-dark-600 hover:text-primary-600 ${location.pathname === '/properties' ? 'text-primary-600' : ''}`}
                >
                  Properties
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className={`block font-medium text-dark-600 hover:text-primary-600 ${location.pathname === '/about' ? 'text-primary-600' : ''}`}
                >
                  About
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className={`block font-medium text-dark-600 hover:text-primary-600 ${location.pathname === '/contact' ? 'text-primary-600' : ''}`}
                >
                  Contact
                </Link>
              </li>
              {state.user.isAuthenticated && state.user.role === 'agent' && (
                <li>
                  <Link
                    to="/agent"
                    className={`block font-medium text-dark-600 hover:text-primary-600 ${location.pathname.startsWith('/agent') ? 'text-primary-600' : ''}`}
                  >
                    Agent Tools
                  </Link>
                </li>
              )}

              {/* Mobile User Menu */}
              <li className="pt-4 border-t border-gray-100">
                {state.user.isAuthenticated ? (
                  <div className="space-y-2">
                    <div className="font-medium text-dark-800">{state.user.name}</div>
                    <button
                      onClick={handleLogout}
                      className="flex items-center space-x-2 font-medium text-dark-600 hover:text-primary-600"
                    >
                      <FaSignOutAlt />
                      <span>Sign Out</span>
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <button
                      onClick={handleLogin}
                      className="flex items-center space-x-2 font-medium text-dark-600 hover:text-primary-600"
                    >
                      <FaUserCircle />
                      <span>Sign In</span>
                    </button>
                    <button className="w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-5 py-2 rounded-full font-medium hover:shadow-lg transition-all duration-300">
                      Get Started
                    </button>
                  </div>
                )}
              </li>
            </ul>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
