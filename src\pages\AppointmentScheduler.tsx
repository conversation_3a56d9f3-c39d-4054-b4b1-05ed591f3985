import React, { useState, useRef } from 'react';
import { FaCalendar<PERSON>lt, Fa<PERSON><PERSON><PERSON>, Fa<PERSON>ser, FaHome, FaPhone, FaEnvelope, FaClock, FaRobot, FaBuilding, FaNotesMedical, FaCheck, FaCommentAlt, FaEdit, FaPrint, FaGoogle, FaDownload, FaCalendarPlus } from 'react-icons/fa';
import { useApp } from '../context/SimpleAppContext';
import appointmentService from '../services/appointmentService';
import { extractAppointmentDataWithOpenAI } from '../services/openaiAppointmentService';
import { extractAppointmentDataWithClaude } from '../services/claudeBackendService';
import { generateGoogleCalendarUrl, downloadIcsFile } from '../utils/calendarUtils';
import { Appointment } from '../models/Appointment';

// Define types
interface AppointmentFormData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  propertyAddress: string;
  propertyType: string;
  preferredDate: string;
  preferredTime: string;
  additionalNotes: string;
}

interface CreateAppointmentData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  propertyAddress: string;
  propertyType: string;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  notes: string;
  userId: string;
}

const AppointmentScheduler: React.FC = () => {
  const { state } = useApp();
  const printRef = useRef<HTMLDivElement>(null);
  const [isBooking, setIsBooking] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [llmProvider, setLlmProvider] = useState<'openai' | 'claude'>('openai');
  const [inputMode, setInputMode] = useState<'form' | 'natural'>('form');
  const [naturalLanguageInput, setNaturalLanguageInput] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);
  const [statusMessage, setStatusMessage] = useState('');
  const [scheduledAppointment, setScheduledAppointment] = useState<Appointment | null>(null);
  const [showAppointmentDetails, setShowAppointmentDetails] = useState(false);
  const [calendarLink, setCalendarLink] = useState('');
  const [calendarLinkGenerated, setCalendarLinkGenerated] = useState(false);

  // Initialize form data
  const [formData, setFormData] = useState<AppointmentFormData>({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    propertyAddress: '',
    propertyType: 'House',
    preferredDate: '',
    preferredTime: '',
    additionalNotes: ''
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate form
    if (!formData.clientName || !formData.clientEmail || !formData.propertyAddress || !formData.preferredDate || !formData.preferredTime) {
      setError('Please fill in all required fields');
      return;
    }

    setIsBooking(true);

    try {
      // Create appointment data
      const appointmentData: CreateAppointmentData = {
        clientName: formData.clientName,
        clientEmail: formData.clientEmail,
        clientPhone: formData.clientPhone,
        propertyAddress: formData.propertyAddress,
        propertyType: formData.propertyType,
        date: formData.preferredDate,
        time: formData.preferredTime,
        status: 'pending',
        notes: formData.additionalNotes,
        userId: state.user?.id || 'guest'
      };

      // Save appointment
      const savedAppointment = await appointmentService.createClientAppointment(appointmentData);

      // Store the scheduled appointment
      setScheduledAppointment(savedAppointment);
      setShowAppointmentDetails(true);
      setStatusMessage('Appointment scheduled successfully!');

      // Reset form fields but keep the appointment details visible
      setFormData({
        clientName: '',
        clientEmail: '',
        clientPhone: '',
        propertyAddress: '',
        propertyType: 'House',
        preferredDate: '',
        preferredTime: '',
        additionalNotes: ''
      });

      // Reset other states
      setShowConfirmation(false);
      setNaturalLanguageInput('');
      setExtractedData(null);

    } catch (error) {
      console.error('Error booking appointment:', error);
      setError('Failed to book appointment. Please try again.');
    } finally {
      setIsBooking(false);
    }
  };

  // Reset the form
  const handleReset = () => {
    setFormData({
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      propertyAddress: '',
      propertyType: 'House',
      preferredDate: '',
      preferredTime: '',
      additionalNotes: ''
    });
    setNaturalLanguageInput('');
    setExtractedData(null);
    setShowConfirmation(false);
    setShowAppointmentDetails(false);
    setScheduledAppointment(null);
    setCalendarLink('');
    setCalendarLinkGenerated(false);
    setError('');
    setStatusMessage('');
  };

  // Print appointment details
  const handlePrintAppointment = () => {
    if (!printRef.current) return;

    const originalContents = document.body.innerHTML;
    const printContents = printRef.current.innerHTML;

    document.body.innerHTML = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <h1 style="text-align: center; color: #00b9ff;">Appointment Details</h1>
        ${printContents}
      </div>
    `;

    window.print();
    document.body.innerHTML = originalContents;
    window.location.reload(); // Reload to restore React app state
  };

  // Add to Google Calendar
  const handleAddToGoogleCalendar = () => {
    if (!scheduledAppointment) return;

    try {
      // Generate Google Calendar URL
      const googleCalendarUrl = generateGoogleCalendarUrl(scheduledAppointment);

      // Set the calendar link
      setCalendarLink(googleCalendarUrl);
      setCalendarLinkGenerated(true);

      // Open in a new tab
      window.open(googleCalendarUrl, '_blank');
    } catch (error) {
      console.error('Error generating Google Calendar link:', error);
      setError('Failed to generate Google Calendar link. Please try again.');
    }
  };

  // Download ICS file
  const handleDownloadIcs = () => {
    if (!scheduledAppointment) return;

    try {
      downloadIcsFile(scheduledAppointment);
    } catch (error) {
      console.error('Error downloading ICS file:', error);
      setError('Failed to download ICS file. Please try again.');
    }
  };



  // Process natural language input
  const processNaturalLanguageInput = async () => {
    if (!naturalLanguageInput.trim()) {
      setError('Please enter your appointment details');
      return;
    }

    setError('');
    setIsProcessing(true);
    setStatusMessage(`Processing your request with ${llmProvider === 'openai' ? 'OpenAI' : 'Claude'}...`);

    try {
      // Extract appointment data using the selected LLM provider
      const data = llmProvider === 'openai'
        ? await extractAppointmentDataWithOpenAI(naturalLanguageInput)
        : await extractAppointmentDataWithClaude(naturalLanguageInput);

      // Update form data with extracted information
      setExtractedData(data);

      // Update form fields with extracted data
      setFormData({
        clientName: data.clientName || '',
        clientEmail: data.clientEmail || '',
        clientPhone: data.clientPhone || '',
        propertyAddress: data.propertyAddress || '',
        propertyType: data.propertyType || 'House',
        preferredDate: data.date ? new Date(data.date).toISOString().split('T')[0] : '',
        preferredTime: data.time || '',
        additionalNotes: data.notes || ''
      });

      // Show confirmation
      setShowConfirmation(true);
      setStatusMessage('Appointment details extracted successfully!');
    } catch (error) {
      console.error('Error processing natural language input:', error);
      setError('Failed to process your request. Please try again or use the form instead.');
      setStatusMessage('');
    } finally {
      setIsProcessing(false);
    }
  };


  // Available time slots
  const timeSlots = [
    '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM',
    '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM'
  ];

  return (
    <div className="py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12 animate-fade-in">
          <h1 className="text-4xl font-bold text-white mb-4 neon-text">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Appointment Scheduler</span>
          </h1>
          <p className="text-primary-200 max-w-3xl mx-auto">
            Our advanced <span className="text-accent-cyan">agentic AI</span> technology helps you schedule and manage property viewings and client appointments efficiently.
          </p>

          {/* LLM Indicator */}
          <div className="mt-4 py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
            <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                 style={{ backgroundColor: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
            <span className="text-xs">
              Powered by <span className="font-semibold"
                style={{ color: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                {llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
              </span>
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form Section */}
          <div className="lg:col-span-2 animate-slide-in-up">
            <div className="glass-card p-8 border-glow">
              <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Schedule an Appointment</span>
                <div className="flex-grow border-t border-primary-800/30 ml-4"></div>
              </h2>

              {/* LLM Provider Toggle */}
              <div className="flex justify-center mb-6">
                <div className="bg-dark-800 p-1 rounded-lg flex">
                  <button
                    type="button"
                    onClick={() => setLlmProvider('openai')}
                    style={{
                      background: llmProvider === 'openai'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'openai'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaRobot className="mr-2" style={{ color: '#10a37f' }} />
                      OpenAI GPT-4o
                    </span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setLlmProvider('claude')}
                    style={{
                      background: llmProvider === 'claude'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'claude'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaRobot className="mr-2" style={{ color: '#a27aff' }} />
                      Claude 4
                    </span>
                  </button>
                </div>
              </div>

              {error && (
                <div className="bg-red-500/20 border border-red-500/50 text-red-100 px-4 py-3 rounded-md mb-4">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{error}</span>
                  </div>
                </div>
              )}

              {/* Input Mode Toggle */}
              <div className="flex justify-center mb-6">
                <div className="bg-dark-800 p-1 rounded-lg flex">
                  <button
                    type="button"
                    onClick={() => setInputMode('form')}
                    style={{
                      background: inputMode === 'form'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${inputMode === 'form'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaEdit className="mr-2" />
                      Form Input
                    </span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setInputMode('natural')}
                    style={{
                      background: inputMode === 'natural'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${inputMode === 'natural'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaCommentAlt className="mr-2" />
                      Natural Language
                    </span>
                  </button>
                </div>
              </div>

              {/* Status Message */}
              {statusMessage && (
                <div className="bg-primary-900/50 border border-accent-cyan/30 text-primary-100 px-4 py-3 rounded-md mb-4">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0 text-accent-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{statusMessage}</span>
                  </div>
                </div>
              )}

              {/* Natural Language Input */}
              {inputMode === 'natural' && (
                <div className="mb-6">
                  <div className="group">
                    <label htmlFor="naturalLanguageInput" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                      <FaCommentAlt className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                      Describe your appointment
                    </label>
                    <textarea
                      id="naturalLanguageInput"
                      value={naturalLanguageInput}
                      onChange={(e) => setNaturalLanguageInput(e.target.value)}
                      rows={6}
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                      placeholder="Describe your appointment in natural language. For example: 'I need to schedule a viewing for John Smith (<EMAIL>) at 123 Main Street on June 15th at 2:30 PM. He's interested in the 3-bedroom house and his phone number is (*************.'"
                    ></textarea>
                  </div>
                  <div className="mt-4 flex justify-center">
                    <button
                      type="button"
                      onClick={processNaturalLanguageInput}
                      disabled={isProcessing || !naturalLanguageInput.trim()}
                      style={{
                        background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                        color: 'white',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        opacity: isProcessing || !naturalLanguageInput.trim() ? 0.7 : 1,
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 flex items-center justify-center group"
                    >
                      {isProcessing ? (
                        <>
                          <FaSpinner className="animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <FaRobot className="mr-2 group-hover:animate-pulse" />
                          Process with {llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Confirmation Section */}
              {showConfirmation && extractedData && (
                <div className="mb-6 bg-primary-900/30 border border-accent-cyan/20 rounded-lg p-4">
                  <h3 className="text-xl font-semibold text-accent-cyan mb-3 flex items-center">
                    <FaCheck className="mr-2 text-sm" /> Appointment Details Extracted
                  </h3>
                  <p className="text-primary-200 mb-4">
                    We've extracted the following appointment details. Please review and make any necessary adjustments before scheduling.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-start">
                      <span className="text-primary-300 mr-2">Client:</span>
                      <span className="text-white">{formData.clientName}</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-primary-300 mr-2">Email:</span>
                      <span className="text-white">{formData.clientEmail}</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-primary-300 mr-2">Phone:</span>
                      <span className="text-white">{formData.clientPhone || 'Not provided'}</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-primary-300 mr-2">Property Type:</span>
                      <span className="text-white">{formData.propertyType}</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-primary-300 mr-2">Address:</span>
                      <span className="text-white">{formData.propertyAddress}</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-primary-300 mr-2">Date/Time:</span>
                      <span className="text-white">{formData.preferredDate} at {formData.preferredTime}</span>
                    </div>
                  </div>
                  {formData.additionalNotes && (
                    <div className="mt-2">
                      <span className="text-primary-300">Notes:</span>
                      <p className="text-white mt-1">{formData.additionalNotes}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Form Input */}
              {(inputMode === 'form' || showConfirmation) && (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="group">
                      <label htmlFor="clientName" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                        <FaUser className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                        Client Name <span className="text-red-500 ml-1">*</span>
                      </label>
                      <input
                        type="text"
                        id="clientName"
                        name="clientName"
                        value={formData.clientName}
                        onChange={handleInputChange}
                        className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                        required
                        placeholder="John Doe"
                      />
                    </div>

                    <div className="group">
                      <label htmlFor="clientEmail" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                        <FaEnvelope className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                        Client Email <span className="text-red-500 ml-1">*</span>
                      </label>
                      <input
                        type="email"
                        id="clientEmail"
                        name="clientEmail"
                        value={formData.clientEmail}
                        onChange={handleInputChange}
                        className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                        required
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="group">
                      <label htmlFor="clientPhone" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                        <FaPhone className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                        Client Phone
                      </label>
                      <input
                        type="tel"
                        id="clientPhone"
                        name="clientPhone"
                        value={formData.clientPhone}
                        onChange={handleInputChange}
                        className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                        placeholder="(*************"
                      />
                    </div>

                    <div className="group">
                      <label htmlFor="propertyType" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                        <FaBuilding className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                        Property Type
                      </label>
                      <select
                        id="propertyType"
                        name="propertyType"
                        value={formData.propertyType}
                        onChange={handleInputChange}
                        className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                      >
                        <option value="House" className="bg-dark-900 text-primary-200">House</option>
                        <option value="Condo" className="bg-dark-900 text-primary-200">Condo</option>
                        <option value="Apartment" className="bg-dark-900 text-primary-200">Apartment</option>
                        <option value="Townhouse" className="bg-dark-900 text-primary-200">Townhouse</option>
                        <option value="Land" className="bg-dark-900 text-primary-200">Land</option>
                        <option value="Commercial" className="bg-dark-900 text-primary-200">Commercial</option>
                      </select>
                    </div>
                  </div>

                  <div className="group">
                    <label htmlFor="propertyAddress" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                      <FaHome className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                      Property Address <span className="text-red-500 ml-1">*</span>
                    </label>
                    <input
                      type="text"
                      id="propertyAddress"
                      name="propertyAddress"
                      value={formData.propertyAddress}
                      onChange={handleInputChange}
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                      required
                      placeholder="123 Main St, City, State, ZIP"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="group">
                      <label htmlFor="preferredDate" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                        <FaCalendarAlt className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                        Preferred Date <span className="text-red-500 ml-1">*</span>
                      </label>
                      <input
                        type="date"
                        id="preferredDate"
                        name="preferredDate"
                        value={formData.preferredDate}
                        onChange={handleInputChange}
                        className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                        required
                      />
                    </div>

                    <div className="group">
                      <label htmlFor="preferredTime" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                        <FaClock className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                        Preferred Time <span className="text-red-500 ml-1">*</span>
                      </label>
                      <select
                        id="preferredTime"
                        name="preferredTime"
                        value={formData.preferredTime}
                        onChange={handleInputChange}
                        className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                        required
                      >
                        <option value="" className="bg-dark-900 text-primary-200">Select a time</option>
                        {timeSlots.map((time) => (
                          <option key={time} value={time} className="bg-dark-900 text-primary-200">
                            {time}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="group">
                    <label htmlFor="additionalNotes" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                      <FaNotesMedical className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                      Additional Notes
                    </label>
                    <textarea
                      id="additionalNotes"
                      name="additionalNotes"
                      value={formData.additionalNotes}
                      onChange={handleInputChange}
                      rows={4}
                      className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                      placeholder="Any special requirements or notes about the appointment..."
                    ></textarea>
                  </div>

                  <div className="mt-8 flex justify-center space-x-4">
                    <button
                      type="button"
                      onClick={handleReset}
                      style={{
                        background: 'white',
                        color: '#00b9ff',
                        padding: '1rem 2rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      <span className="inline-flex items-center justify-center whitespace-nowrap">
                        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Reset Form
                      </span>
                    </button>
                    <button
                      type="submit"
                      style={{
                        background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                        color: 'white',
                        padding: '1rem 2rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 flex items-center justify-center group"
                      disabled={isBooking}
                    >
                      {isBooking ? (
                        <>
                          <FaSpinner className="animate-spin mr-2" />
                          Scheduling...
                        </>
                      ) : (
                        <>
                          <FaCalendarAlt className="mr-2 group-hover:animate-pulse" />
                          Schedule Appointment
                        </>
                      )}
                    </button>
                  </div>
                </form>
              )}

              {/* Appointment Details Section */}
              {showAppointmentDetails && scheduledAppointment && (
                <div className="mt-8 bg-primary-900/30 border border-accent-cyan/20 rounded-lg p-6 animate-fade-in" ref={printRef}>
                  <h3 className="text-2xl font-semibold text-accent-cyan mb-4 flex items-center">
                    <FaCheck className="mr-2 text-lg" /> Appointment Scheduled Successfully
                  </h3>

                  <div className="bg-dark-800/50 p-5 rounded-lg border border-primary-700/30 mb-6">
                    <h4 className="text-xl font-medium text-white mb-4 pb-2 border-b border-primary-700/30">
                      Appointment Details
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                      <div>
                        <p className="text-primary-300 text-sm">Client Name:</p>
                        <p className="text-white font-medium">{scheduledAppointment.clientName}</p>
                      </div>

                      <div>
                        <p className="text-primary-300 text-sm">Client Email:</p>
                        <p className="text-white font-medium">{scheduledAppointment.clientEmail}</p>
                      </div>

                      <div>
                        <p className="text-primary-300 text-sm">Client Phone:</p>
                        <p className="text-white font-medium">{scheduledAppointment.clientPhone || 'Not provided'}</p>
                      </div>

                      <div>
                        <p className="text-primary-300 text-sm">Property Type:</p>
                        <p className="text-white font-medium">{scheduledAppointment.propertyType}</p>
                      </div>

                      <div className="md:col-span-2">
                        <p className="text-primary-300 text-sm">Property Address:</p>
                        <p className="text-white font-medium">{scheduledAppointment.propertyAddress}</p>
                      </div>

                      <div>
                        <p className="text-primary-300 text-sm">Date:</p>
                        <p className="text-white font-medium">{scheduledAppointment.date}</p>
                      </div>

                      <div>
                        <p className="text-primary-300 text-sm">Time:</p>
                        <p className="text-white font-medium">{scheduledAppointment.time}</p>
                      </div>

                      <div className="md:col-span-2">
                        <p className="text-primary-300 text-sm">Status:</p>
                        <p className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300">
                          {scheduledAppointment.status.charAt(0).toUpperCase() + scheduledAppointment.status.slice(1)}
                        </p>
                      </div>

                      {scheduledAppointment.notes && (
                        <div className="md:col-span-2">
                          <p className="text-primary-300 text-sm">Notes:</p>
                          <p className="text-white">{scheduledAppointment.notes}</p>
                        </div>
                      )}

                      <div className="md:col-span-2">
                        <p className="text-primary-300 text-sm">Appointment ID:</p>
                        <p className="text-white font-mono text-sm">{scheduledAppointment.id}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4 justify-center">
                    <button
                      type="button"
                      onClick={handleAddToGoogleCalendar}
                      style={{
                        background: '#4285F4',
                        color: 'white',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      <FaGoogle className="mr-2" />
                      Add to Google Calendar
                    </button>

                    <button
                      type="button"
                      onClick={handleDownloadIcs}
                      style={{
                        background: '#00b9ff',
                        color: 'white',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      <FaDownload className="mr-2" />
                      Download ICS File
                    </button>

                    <button
                      type="button"
                      onClick={handlePrintAppointment}
                      style={{
                        background: 'white',
                        color: '#00b9ff',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      <FaPrint className="mr-2" />
                      Print Details
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Info Section */}
          <div className="lg:col-span-1 animate-slide-in-up">
            <div className="glass-card p-8 border-glow h-full">
              <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">How It Works</span>
                <div className="flex-grow border-t border-primary-800/30 ml-4"></div>
              </h2>

              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mr-4 shadow-lg">
                    <span className="text-lg font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-primary-100">Fill in the Details</h3>
                    <p className="text-primary-300 text-sm mt-1">Enter client and property information along with your preferred date and time.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mr-4 shadow-lg">
                    <span className="text-lg font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-primary-100">AI Processing</h3>
                    <p className="text-primary-300 text-sm mt-1">Our AI analyzes the information and schedules the appointment in your calendar.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mr-4 shadow-lg">
                    <span className="text-lg font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-primary-100">Confirmation</h3>
                    <p className="text-primary-300 text-sm mt-1">Receive confirmation and the appointment is added to your schedule.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mr-4 shadow-lg">
                    <span className="text-lg font-bold">4</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-primary-100">Reminders</h3>
                    <p className="text-primary-300 text-sm mt-1">Automatic reminders are sent to both you and the client before the appointment.</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-4 bg-dark-800/50 rounded-lg border border-primary-700/30">
                <h4 className="text-accent-cyan font-medium mb-2 flex items-center">
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Pro Tip:
                </h4>
                <p className="text-primary-200 text-sm">
                  Schedule appointments at least 24 hours in advance to ensure proper notification and preparation time for all parties involved.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentScheduler;
