import { FaR<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aH<PERSON>, FaChartLine } from 'react-icons/fa';

const About = () => {
  return (
    <div className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-dark-800 mb-6">About Digital Realtor</h1>
          <p className="text-xl text-dark-600 max-w-3xl mx-auto">
            We're revolutionizing the real estate industry with AI technology that makes finding your dream home easier than ever before.
          </p>
        </div>

        {/* Our Mission */}
        <div className="bg-white rounded-xl shadow-card p-8 mb-16">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <img
                src="https://images.unsplash.com/photo-1560520031-3a4dc4e9de0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1473&q=80"
                alt="Our Mission"
                className="rounded-lg shadow-md"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold text-dark-800 mb-4">Our Mission</h2>
              <p className="text-dark-700 mb-4">
                At Digital Realtor, our mission is to transform the way people find and purchase homes by leveraging the power of artificial intelligence. We believe that finding your perfect home should be an exciting and stress-free experience.
              </p>
              <p className="text-dark-700 mb-4">
                Our AI assistant is designed to understand your unique preferences, lifestyle needs, and budget constraints to provide personalized property recommendations that truly match what you're looking for.
              </p>
              <p className="text-dark-700">
                We're committed to transparency, innovation, and exceptional customer service at every step of your home buying journey.
              </p>
            </div>
          </div>
        </div>

        {/* Our Technology */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-dark-800 text-center mb-12">Our Technology</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-xl shadow-card p-6 text-center">
              <div className="bg-primary-100 text-primary-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaRobot className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-2">AI Assistant</h3>
              <p className="text-dark-600">
                Our conversational AI understands your needs and preferences to provide personalized recommendations.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-card p-6 text-center">
              <div className="bg-primary-100 text-primary-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaSearch className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-2">Smart Search</h3>
              <p className="text-dark-600">
                Advanced algorithms that go beyond basic filters to find properties that match your lifestyle.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-card p-6 text-center">
              <div className="bg-primary-100 text-primary-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaHome className="w-5 h-5" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-2">Virtual Tours</h3>
              <p className="text-dark-600">
                Explore properties from the comfort of your home with our immersive virtual tour technology.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-card p-6 text-center">
              <div className="bg-primary-100 text-primary-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaChartLine className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-2">Market Insights</h3>
              <p className="text-dark-600">
                Data-driven insights on property values, neighborhood trends, and investment potential.
              </p>
            </div>
          </div>
        </div>

        {/* Contact CTA */}
        <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl shadow-md p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">Ready to Find Your Dream Home?</h2>
          <p className="text-xl mb-6 max-w-2xl mx-auto">
            Let our AI assistant help you discover the perfect property tailored to your needs.
          </p>
          <div className="flex justify-center">
            <a
              href="/contact"
              className="bg-white text-primary-600 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors"
            >
              Contact Us Today
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
