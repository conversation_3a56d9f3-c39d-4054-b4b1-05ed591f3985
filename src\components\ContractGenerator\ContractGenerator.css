/* Contract Generator Styles */

/* Main Container */
.contract-generator-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  color: #f0f4ff;
}

/* AI Provider Toggle */
.ai-provider-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 0.5rem;
  background: rgba(10, 12, 26, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(74, 107, 255, 0.2);
}

.ai-provider-label {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  color: #a0aec0;
  font-size: 0.9rem;
}

.ai-icon {
  margin-right: 0.5rem;
  color: #38bdf8;
}

.ai-provider-buttons {
  display: flex;
  gap: 0.5rem;
}

.ai-provider-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(74, 107, 255, 0.3);
  color: #a0aec0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ai-provider-button:hover {
  background: rgba(30, 41, 59, 0.9);
  color: #f0f4ff;
}

.ai-provider-button.active {
  background: rgba(74, 107, 255, 0.2);
  border-color: #4a6bff;
  color: #f0f4ff;
}

.contract-generator-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
  background: linear-gradient(90deg, #4a6bff, #00f7ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.3);
}

.contract-generator-subtitle {
  font-size: 1.1rem;
  text-align: center;
  color: #a0aec0;
  margin-bottom: 2rem;
}

.contract-generator-form-container {
  background: rgba(10, 12, 26, 0.7);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 247, 255, 0.1);
  border: 1px solid rgba(74, 107, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* Progress Bar */
.progress-container {
  margin-bottom: 2rem;
}

.progress-bar {
  height: 8px;
  background: rgba(74, 107, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a6bff, #00f7ff);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #a0aec0;
  text-align: right;
}

/* Step Form */
.step-form {
  display: flex;
  flex-direction: column;
}

.step-indicator {
  font-size: 0.9rem;
  color: #a0aec0;
  margin-bottom: 1.5rem;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #f0f4ff;
}

.step-content {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #f0f4ff;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  background: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(74, 107, 255, 0.3);
  color: #f0f4ff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #4a6bff;
  box-shadow: 0 0 0 2px rgba(74, 107, 255, 0.2);
}

.helper-text {
  font-size: 0.85rem;
  color: #a0aec0;
  margin-top: 0.5rem;
}

/* Toggle Buttons */
.toggle-label {
  margin-bottom: 0.75rem;
}

.toggle-container {
  display: flex;
  gap: 1rem;
}

.toggle-button {
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  background: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(74, 107, 255, 0.3);
  color: #a0aec0;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-button.active {
  background: rgba(74, 107, 255, 0.2);
  border-color: #4a6bff;
  color: #f0f4ff;
}

/* Input with Prefix */
.input-with-prefix {
  display: flex;
  align-items: center;
}

.input-prefix {
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(74, 107, 255, 0.3);
  border-right: none;
  border-radius: 6px 0 0 6px;
  color: #a0aec0;
}

.input-with-prefix input {
  border-radius: 0 6px 6px 0;
}

/* Navigation Buttons */
.step-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.btn-previous,
.btn-next,
.btn-generate {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-previous {
  background: rgba(30, 41, 59, 0.7);
  color: #a0aec0;
  border: 1px solid rgba(74, 107, 255, 0.3);
}

.btn-previous:hover {
  background: rgba(30, 41, 59, 0.9);
  color: #f0f4ff;
}

.btn-next,
.btn-generate {
  background: linear-gradient(90deg, #4a6bff, #00f7ff);
  color: #f0f4ff;
}

.btn-next:hover,
.btn-generate:hover {
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);
  transform: translateY(-2px);
}

.btn-generate {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
}

/* Suggestion Button */
.btn-suggest {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  margin-top: 0.75rem;
  background: rgba(74, 107, 255, 0.1);
  color: #38bdf8;
  border: 1px solid rgba(74, 107, 255, 0.3);
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-suggest:hover {
  background: rgba(74, 107, 255, 0.2);
  color: #f0f4ff;
}

.btn-suggest:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Checkbox Grid */
.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 0.75rem;
  margin: 1rem 0;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-item label {
  margin: 0;
  font-weight: normal;
  cursor: pointer;
}

/* Final Step Text */
.final-step-text {
  color: #a0aec0;
  margin-bottom: 1rem;
  line-height: 1.5;
}

/* Summary Review */
.summary-review {
  padding: 1rem;
}

.summary-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #f0f4ff;
}

.summary-subtitle {
  font-size: 1rem;
  color: #a0aec0;
  margin-bottom: 2rem;
}

.summary-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(74, 107, 255, 0.2);
}

.summary-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #00f7ff;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 0.9rem;
  color: #a0aec0;
  margin-bottom: 0.25rem;
}

.summary-value {
  font-size: 1.1rem;
  color: #f0f4ff;
}

.summary-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.summary-list-item {
  background: rgba(74, 107, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  color: #f0f4ff;
  font-size: 0.9rem;
}

.summary-text {
  background: rgba(30, 41, 59, 0.5);
  padding: 1rem;
  border-radius: 6px;
  color: #f0f4ff;
  white-space: pre-wrap;
}

.summary-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

/* Success Screen */
.success-screen {
  text-align: center;
  padding: 2rem 1rem;
}

.success-icon {
  font-size: 4rem;
  color: #10b981;
  margin-bottom: 1.5rem;
}

.success-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #f0f4ff;
}

.success-message {
  font-size: 1.1rem;
  color: #a0aec0;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.btn-download,
.btn-email {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-download {
  background: linear-gradient(90deg, #4a6bff, #00f7ff);
  color: #f0f4ff;
  border: none;
}

.btn-download:hover {
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);
  transform: translateY(-2px);
}

.btn-email {
  background: rgba(30, 41, 59, 0.7);
  color: #a0aec0;
  border: 1px solid rgba(74, 107, 255, 0.3);
}

.btn-email:hover {
  background: rgba(30, 41, 59, 0.9);
  color: #f0f4ff;
}

.success-next-steps {
  background: rgba(30, 41, 59, 0.5);
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: left;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.success-next-steps h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #00f7ff;
}

.success-next-steps ol {
  padding-left: 1.5rem;
  color: #f0f4ff;
}

.success-next-steps li {
  margin-bottom: 0.5rem;
}

.success-footer {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn-home,
.btn-new-contract {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-home {
  background: rgba(30, 41, 59, 0.7);
  color: #a0aec0;
  border: 1px solid rgba(74, 107, 255, 0.3);
}

.btn-home:hover {
  background: rgba(30, 41, 59, 0.9);
  color: #f0f4ff;
}

.btn-new-contract {
  background: rgba(74, 107, 255, 0.2);
  color: #f0f4ff;
  border: 1px solid rgba(74, 107, 255, 0.5);
}

.btn-new-contract:hover {
  background: rgba(74, 107, 255, 0.3);
}

/* Spinner Animation */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .contract-generator-container {
    padding: 1rem;
  }

  .contract-generator-form-container {
    padding: 1.5rem;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .success-actions,
  .success-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-download,
  .btn-email,
  .btn-home,
  .btn-new-contract {
    width: 100%;
    justify-content: center;
  }
}
