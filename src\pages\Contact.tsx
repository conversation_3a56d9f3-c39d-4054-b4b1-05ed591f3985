import { useState, useEffect, useRef } from 'react';
import { FaEnvelope, FaPaperPlane, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import ValidationMessage from '../components/ValidationMessage';
import styles from './Contact.module.css';

const Contact = () => {
  // Form refs for custom validation
  const nameRef = useRef<HTMLInputElement>(null);
  const emailRef = useRef<HTMLInputElement>(null);
  const subjectRef = useRef<HTMLSelectElement>(null);
  const messageRef = useRef<HTMLTextAreaElement>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when field is changed
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const newErrors = { ...formErrors };

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Please enter your name';
      isValid = false;
      nameRef.current?.focus();
    }

    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'Please enter your email address';
      isValid = false;
      if (isValid) emailRef.current?.focus();
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
      isValid = false;
      if (isValid) emailRef.current?.focus();
    }

    // Validate subject
    if (!formData.subject) {
      newErrors.subject = 'Please select a subject';
      isValid = false;
      if (isValid) subjectRef.current?.focus();
    }

    // Validate message
    if (!formData.message.trim()) {
      newErrors.message = 'Please enter your message';
      isValid = false;
      if (isValid) messageRef.current?.focus();
    }

    setFormErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Construct the message content
      const messageContent = `
Name: ${formData.name}
Email: ${formData.email}
${formData.phone ? `Phone: ${formData.phone}` : ''}
Subject: ${formData.subject}

Message:
${formData.message}
      `;

      // Determine if we're in development or production
      const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

      let response;

      if (isDevelopment) {
        // In development, use the backend proxy
        response = await fetch('/api/send-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            subject: formData.subject,
            message: formData.message
          })
        });
      } else {
        // In production, call the AWS API Gateway directly with CORS handling
        try {
          // Prepare the payload for the API
          const payload = {
            toEmail: '<EMAIL>',
            subject: 'New Contact Us Message',
            message: messageContent
          };

          // Call the AWS API Gateway endpoint
          response = await fetch('https://9nkq8lk4ck.execute-api.us-east-1.amazonaws.com/prod/send-email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
          });
        } catch (corsError) {
          console.error('CORS error when calling API directly:', corsError);
          throw new Error('Unable to send email due to CORS restrictions. Please try again later or contact us <NAME_EMAIL>');
        }
      }

      if (!response.ok) {
        let errorMessage;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || `Failed to send email: ${response.status} ${response.statusText}`;
        } catch (e) {
          errorMessage = `Failed to send email: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      let data;
      try {
        data = await response.json();
      } catch (e) {
        // If we can't parse the response as JSON, assume success
        data = { success: true };
      }
      console.log('Email sent successfully:', data);

      // Set form as submitted
      setFormSubmitted(true);

      // Reset form after submission
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
      });
    } catch (err) {
      console.error('Error sending email:', err);
      setError(err instanceof Error ? err.message : 'Failed to send email. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const [activeSection, setActiveSection] = useState(0);

  // Animation effect for sections
  useEffect(() => {
    const timer = setTimeout(() => {
      if (activeSection < 3) {
        setActiveSection(prev => prev + 1);
      }
    }, 300);
    return () => clearTimeout(timer);
  }, [activeSection]);

  return (
    <div className="py-16 relative z-10">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16 animate-fade-in relative">
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl -z-10"></div>

          <div className="inline-block mb-4 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
            <span className="text-accent-cyan text-sm font-medium">Get In Touch</span>
          </div>

          <h1 className="text-5xl font-bold mb-6 text-glow text-white">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Contact</span> Us
          </h1>

          <p className="text-xl text-primary-200 max-w-3xl mx-auto leading-relaxed">
            Have questions about our services or need assistance? We're here to help you find your dream home.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {/* Contact Information */}
          <div className={`lg:col-span-1 transition-all duration-500 ${activeSection >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className={`${styles.contactCard} relative overflow-hidden h-full`}>
              <div className="absolute top-0 right-0 w-64 h-64 bg-secondary-400/5 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-accent-cyan/5 rounded-full blur-3xl"></div>

              <div className={styles.contactCardHeader}>
                <h2 className={styles.contactCardTitle}>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Get in Touch</span>
                  <div className={styles.contactCardDivider}></div>
                </h2>
              </div>

              <div className="space-y-6">
                <div className={styles.contactInfoItem}>
                  <div className={styles.contactInfoIcon}>
                    <FaEnvelope className="animate-pulse" />
                  </div>
                  <div className={styles.contactInfoContent}>
                    <h3 className={styles.contactInfoTitle}>Email</h3>
                    <p className={styles.contactInfoValue}>
                      <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className={`lg:col-span-2 transition-all duration-500 ${activeSection >= 2 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className={`${styles.contactCard} relative overflow-hidden`}>
              <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/5 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl"></div>

              <div className={styles.contactCardHeader}>
                <h2 className={styles.contactCardTitle}>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Send Us a Message</span>
                  <div className={styles.contactCardDivider}></div>
                </h2>
              </div>

              {formSubmitted ? (
                <div className="bg-dark-800/50 border border-accent-cyan/50 text-accent-cyan p-4 rounded-lg mb-6 animate-fade-in">
                  <div className="flex items-center">
                    <FaCheckCircle className="text-accent-cyan mr-2 text-xl" />
                    <h3 className="font-bold text-lg mb-0">Thank You!</h3>
                  </div>
                  <p className="mt-2 text-primary-200">Your message has been sent successfully. We'll get back to you as soon as possible.</p>
                </div>
              ) : null}

              {error ? (
                <div className="bg-red-500/10 border border-red-500/30 text-red-100 px-4 py-3 rounded-lg mb-6 shadow-sm">
                  <ValidationMessage type="error" message={error} />
                </div>
              ) : null}

              <form onSubmit={handleSubmit} className={styles.formGrid}>
                <div className={`${styles.formRow} ${styles.cols2}`}>
                  <div className={styles.formGroup}>
                    <label htmlFor="name" className={styles.formLabel}>
                      Your Name <span className={styles.required}>*</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      ref={nameRef}
                      className={`${styles.formInput} ${formErrors.name ? 'border-red-500' : ''}`}
                      placeholder="John Doe"
                    />
                    {formErrors.name && (
                      <div className="mt-2">
                        <ValidationMessage type="error" message={formErrors.name} />
                      </div>
                    )}
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="email" className={styles.formLabel}>
                      Email Address <span className={styles.required}>*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      ref={emailRef}
                      className={`${styles.formInput} ${formErrors.email ? 'border-red-500' : ''}`}
                      placeholder="<EMAIL>"
                    />
                    {formErrors.email && (
                      <div className="mt-2">
                        <ValidationMessage type="error" message={formErrors.email} />
                      </div>
                    )}
                  </div>
                </div>

                <div className={`${styles.formRow} ${styles.cols2}`}>
                  <div className={styles.formGroup}>
                    <label htmlFor="phone" className={styles.formLabel}>
                      <span className="whitespace-nowrap">Phone Number <span className={styles.optional}>(optional)</span></span>
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className={styles.formInput}
                      placeholder="(*************"
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="subject" className={styles.formLabel}>
                      Subject <span className={styles.required}>*</span>
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      ref={subjectRef}
                      className={`${styles.formInput} ${formErrors.subject ? 'border-red-500' : ''}`}
                    >
                      <option value="" disabled>Select a subject</option>
                      <option value="General Inquiry">General Inquiry</option>
                      <option value="Property Information">Property Information</option>
                      <option value="Schedule Viewing">Schedule Viewing</option>
                      <option value="Technical Support">Technical Support</option>
                      <option value="Other">Other</option>
                    </select>
                    {formErrors.subject && (
                      <div className="mt-2">
                        <ValidationMessage type="error" message={formErrors.subject} />
                      </div>
                    )}
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="message" className={styles.formLabel}>
                    Message <span className={styles.required}>*</span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    ref={messageRef}
                    rows={6}
                    className={`${styles.formInput} ${styles.formTextarea} ${formErrors.message ? 'border-red-500' : ''}`}
                    placeholder="How can we help you?"
                  ></textarea>
                  {formErrors.message && (
                    <div className="mt-2">
                      <ValidationMessage type="error" message={formErrors.message} />
                    </div>
                  )}
                </div>

                <div className="text-center pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={styles.formSubmitButton}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      <>
                        <FaPaperPlane className="mr-2 group-hover:-translate-y-1 group-hover:translate-x-1 transition-transform duration-300" />
                        Send Message
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default Contact;
