import { Link } from 'react-router-dom';
import { Fa<PERSON><PERSON><PERSON><PERSON>, FaHome, FaRobot, FaChartLine, FaTools, FaMapMarkerAlt } from 'react-icons/fa';
import { useApp } from '../context/AppContext';

const Home = () => {
  const { state } = useApp();

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[700px] flex items-center overflow-hidden">
        {/* Background image */}
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1973&q=80"
            alt="Luxury home"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-dark-900/80 to-dark-800/60"></div>
        </div>

        {/* Content */}
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              <span className="block">Find Your Dream Home</span>
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-400 to-secondary-400">With AI Assistance</span>
            </h1>
            <p className="text-xl text-white/90 mb-8 max-w-2xl">
              Our AI-powered assistant helps you discover the perfect property tailored to your unique needs and preferences.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                to="/properties"
                className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-8 py-4 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 text-center"
              >
                Browse Properties
              </Link>
              {state.user.isAuthenticated && state.user.role === 'agent' ? (
                <Link
                  to="/agent"
                  className="bg-white text-primary-600 px-8 py-4 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center justify-center"
                >
                  <FaTools className="mr-2" />
                  Agent Tools
                </Link>
              ) : (
                <Link
                  to="/login"
                  className="bg-white text-primary-600 px-8 py-4 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 flex items-center justify-center"
                >
                  <FaRobot className="mr-2" />
                  Try AI Assistant
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-dark-800 mb-4 inline-block relative">
              <span className="inline-block w-12 h-1.5 bg-gradient-to-r from-primary-500 to-secondary-500 absolute -top-4 left-1/2 transform -translate-x-1/2"></span>
              AI-Powered Real Estate
            </h2>
            <p className="text-dark-500 max-w-2xl mx-auto">
              Our platform combines cutting-edge AI technology with real estate expertise to provide an unparalleled experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-card hover:shadow-hover transition-shadow group">
              <div className="bg-gradient-to-br from-primary-500 to-primary-600 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform">
                <FaHome className="w-5 h-5" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-3">Smart Property Matching</h3>
              <p className="text-dark-500 mb-4">
                Our AI analyzes your preferences and needs to match you with properties that truly fit your lifestyle.
              </p>
              <Link to="/properties" className="text-primary-600 font-medium flex items-center group-hover:translate-x-1 transition-transform">
                <span>Explore properties</span>
                <FaArrowRight className="ml-2" />
              </Link>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-card hover:shadow-hover transition-shadow group">
              <div className="bg-gradient-to-br from-secondary-500 to-secondary-600 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform">
                <FaRobot className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-3">AI Assistant</h3>
              <p className="text-dark-500 mb-4">
                Get instant answers to your questions and personalized guidance throughout your real estate journey.
              </p>
              <Link to="/login" className="text-primary-600 font-medium flex items-center group-hover:translate-x-1 transition-transform">
                <span>Try the assistant</span>
                <FaArrowRight className="ml-2" />
              </Link>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-card hover:shadow-hover transition-shadow group">
              <div className="bg-gradient-to-br from-primary-500 to-secondary-500 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform">
                <FaChartLine className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-3">Market Insights</h3>
              <p className="text-dark-500 mb-4">
                Access data-driven insights on property values, neighborhood trends, and investment potential.
              </p>
              <Link to="/login" className="text-primary-600 font-medium flex items-center group-hover:translate-x-1 transition-transform">
                <span>View insights</span>
                <FaArrowRight className="ml-2" />
              </Link>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-card hover:shadow-hover transition-shadow group">
              <div className="bg-gradient-to-br from-secondary-600 to-primary-600 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform">
                <FaMapMarkerAlt className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-dark-800 mb-3">Neighborhood & Compliance</h3>
              <p className="text-dark-500 mb-4">
                Get comprehensive insights about neighborhoods and property compliance requirements.
              </p>
              <Link to="/neighborhood-compliance" className="text-primary-600 font-medium flex items-center group-hover:translate-x-1 transition-transform">
                <span>Explore neighborhoods</span>
                <FaArrowRight className="ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Agent Tools Promo (only visible to non-agents) */}
      {(!state.user.isAuthenticated || state.user.role !== 'agent') && (
        <section className="py-16 bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold mb-4">Are You a Real Estate Agent?</h2>
                <p className="text-white/90 mb-6">
                  Access our suite of AI-powered tools designed specifically for real estate professionals. Generate listings, schedule appointments, analyze properties, and more with the power of AI.
                </p>
                <Link
                  to="/login"
                  className="inline-block bg-white text-primary-600 px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5"
                >
                  Access Agent Tools
                </Link>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Listing Generator</h3>
                  <p className="text-white/80 text-sm">
                    Create compelling property listings with AI-generated descriptions.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Appointment Scheduler</h3>
                  <p className="text-white/80 text-sm">
                    Get AI recommendations for scheduling client appointments.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Competitive Analysis</h3>
                  <p className="text-white/80 text-sm">
                    Generate detailed market analysis and property comparisons.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Inspection Reports</h3>
                  <p className="text-white/80 text-sm">
                    Analyze inspection findings and generate client-friendly reports.
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md p-4 rounded-lg col-span-2">
                  <h3 className="font-bold mb-2">Neighborhood & Compliance</h3>
                  <p className="text-white/80 text-sm">
                    Generate comprehensive reports on neighborhoods and property compliance requirements.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-dark-800 mb-4">Ready to Find Your Dream Home?</h2>
          <p className="text-dark-500 max-w-2xl mx-auto mb-8">
            Start your journey today with our AI-powered platform and discover properties that truly match your lifestyle.
          </p>
          <Link
            to="/properties"
            className="inline-block bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-8 py-4 rounded-full font-medium hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5"
          >
            Get Started Now
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
