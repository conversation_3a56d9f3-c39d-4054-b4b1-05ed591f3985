import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

console.log(`${colors.bright}${colors.cyan}=== Digital Realtor Netlify Production Build ====${colors.reset}\n`);

try {
  // Step 1: Clean previous build
  console.log(`${colors.yellow}Cleaning previous build...${colors.reset}`);
  if (fs.existsSync(path.join(__dirname, 'prod-build'))) {
    fs.rmSync(path.join(__dirname, 'prod-build'), { recursive: true, force: true });
  }
  console.log(`${colors.green}✓ Previous build cleaned${colors.reset}\n`);

  // Step 2: Install dependencies
  console.log(`${colors.yellow}Installing dependencies...${colors.reset}`);
  execSync('npm install', { stdio: 'inherit' });
  console.log(`${colors.green}✓ Dependencies installed${colors.reset}\n`);

  // Step 3: Run linting (optional)
  console.log(`${colors.yellow}Running linting...${colors.reset}`);
  try {
    execSync('npm run lint', { stdio: 'inherit' });
    console.log(`${colors.green}✓ Linting passed${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.yellow}Linting found issues, but continuing with build...${colors.reset}\n`);
  }

  // Step 4: Build the application
  console.log(`${colors.yellow}Building application for Netlify...${colors.reset}`);

  // Ensure TypeScript and ESLint are available
  try {
    execSync('npx tsc --version', { stdio: 'inherit' });
  } catch (error) {
    console.log(`${colors.yellow}TypeScript not found in path, installing globally...${colors.reset}`);
    execSync('npm install -g typescript', { stdio: 'inherit' });
  }

  try {
    execSync('npx eslint --version', { stdio: 'inherit' });
  } catch (error) {
    console.log(`${colors.yellow}ESLint not found in path, installing globally...${colors.reset}`);
    execSync('npm install -g eslint', { stdio: 'inherit' });
  }

  // Use cross-platform way to set NODE_ENV and NETLIFY_BUILD
  const buildCmd = process.platform === 'win32'
    ? 'set NODE_ENV=production && set NETLIFY_BUILD=true && npx tsc -p tsconfig.prod.json && vite build --outDir prod-build'
    : 'NODE_ENV=production NETLIFY_BUILD=true npx tsc -p tsconfig.prod.json && vite build --outDir prod-build';

  try {
    execSync(buildCmd, { stdio: 'inherit' });
    console.log(`${colors.green}✓ Build completed${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.yellow}Build encountered issues, attempting to continue...${colors.reset}`);
    // Try to build without TypeScript check
    const fallbackBuildCmd = process.platform === 'win32'
      ? 'set NODE_ENV=production && set NETLIFY_BUILD=true && vite build --outDir prod-build'
      : 'NODE_ENV=production NETLIFY_BUILD=true vite build --outDir prod-build';
    execSync(fallbackBuildCmd, { stdio: 'inherit' });
    console.log(`${colors.green}✓ Build completed with fallback method${colors.reset}\n`);
  }

  // Step 5: Create Netlify configuration files
  console.log(`${colors.yellow}Creating Netlify configuration files...${colors.reset}`);

  // Create _redirects file for client-side routing
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', '_redirects'),
    '/* /index.html 200'
  );

  // Create a robots.txt file
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'robots.txt'),
    'User-agent: *\nAllow: /'
  );

  console.log(`${colors.green}✓ Netlify configuration files created${colors.reset}\n`);

  // Step 6: Optimize assets
  console.log(`${colors.yellow}Optimizing assets...${colors.reset}`);

  // Create a .env.production.local file in the prod-build directory
  // This file will be used by Netlify during deployment
  if (fs.existsSync(path.join(__dirname, '.env.production'))) {
    fs.copyFileSync(
      path.join(__dirname, '.env.production'),
      path.join(__dirname, 'prod-build', '.env.production.local')
    );
    console.log(`${colors.green}✓ Environment variables copied${colors.reset}`);
  } else {
    console.log(`${colors.yellow}Warning: .env.production file not found${colors.reset}`);
  }

  // Copy the prod-build-package.json to the prod-build directory as package.json
  if (fs.existsSync(path.join(__dirname, 'prod-build-package.json'))) {
    fs.copyFileSync(
      path.join(__dirname, 'prod-build-package.json'),
      path.join(__dirname, 'prod-build', 'package.json')
    );
    console.log(`${colors.green}✓ Package.json copied to build directory${colors.reset}`);
  } else {
    // Create a minimal package.json in the prod-build directory
    const buildPackageJson = {
      name: "digital-realtor-build",
      version: "1.0.0",
      description: "Digital Realtor Production Build",
      engines: {
        node: "20.11.1",
        npm: "10.8.2"
      },
      private: true,
      type: "module"
    };

    fs.writeFileSync(
      path.join(__dirname, 'prod-build', 'package.json'),
      JSON.stringify(buildPackageJson, null, 2)
    );
    console.log(`${colors.green}✓ Package.json created in build directory${colors.reset}`);
  }

  // Create a runtime configuration file
  const runtimeConfig = {
    BUILD_TIME: new Date().toISOString(),
    VERSION: JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8')).version,
    CLARITY_ID: 'r7yjq54tfv'
  };

  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'config.json'),
    JSON.stringify(runtimeConfig, null, 2)
  );

  console.log(`${colors.green}✓ Runtime configuration created${colors.reset}\n`);

  // Step 7: Verify build
  console.log(`${colors.yellow}Verifying build...${colors.reset}`);

  // Check if index.html exists
  if (fs.existsSync(path.join(__dirname, 'prod-build', 'index.html'))) {
    console.log(`${colors.green}✓ index.html exists${colors.reset}`);
  } else {
    throw new Error('index.html not found in build directory');
  }

  // Check if assets directory exists
  if (fs.existsSync(path.join(__dirname, 'prod-build', 'assets'))) {
    console.log(`${colors.green}✓ Assets directory exists${colors.reset}`);
  } else {
    throw new Error('Assets directory not found in build directory');
  }

  console.log(`${colors.green}✓ Build verification passed${colors.reset}\n`);

  // Print build size information
  console.log(`${colors.magenta}Build size information:${colors.reset}`);
  const getTotalSize = (directory) => {
    let totalSize = 0;
    const files = fs.readdirSync(directory, { withFileTypes: true });

    for (const file of files) {
      const filePath = path.join(directory, file.name);
      if (file.isDirectory()) {
        totalSize += getTotalSize(filePath);
      } else {
        totalSize += fs.statSync(filePath).size;
      }
    }

    return totalSize;
  };

  const totalSize = getTotalSize(path.join(__dirname, 'prod-build'));
  console.log(`${colors.magenta}Total build size: ${(totalSize / (1024 * 1024)).toFixed(2)} MB${colors.reset}\n`);

  console.log(`${colors.bright}${colors.green}Netlify production build completed successfully!${colors.reset}`);
  console.log(`${colors.cyan}The build is available in the 'prod-build' directory.${colors.reset}`);
  console.log(`${colors.cyan}To deploy to Netlify:${colors.reset}`);
  console.log(`${colors.yellow}  1. Upload the 'prod-build' directory to Netlify${colors.reset}`);
  console.log(`${colors.yellow}  2. Or connect your GitHub repository to Netlify and use the netlify.toml configuration${colors.reset}`);
  console.log(`${colors.yellow}  3. Preview the build locally with: npm run preview:netlify${colors.reset}`);

} catch (error) {
  console.error(`${colors.red}Build failed:${colors.reset}`, error);
  process.exit(1);
}
