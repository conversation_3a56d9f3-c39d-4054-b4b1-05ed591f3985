import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaHome, FaImage, FaSpinner, FaDownload, FaPalette } from 'react-icons/fa';
import { useApp } from '../../context/SimpleAppContext';
import ImageUploader from '../../components/ImageUploader';
import StyleSelector from '../../components/virtual-staging/StyleSelector';
import StagingResults from '../../components/virtual-staging/StagingResults';
import { generateStagingSuggestions, generateStagingImage } from '../../services/virtualStagingService';
import ValidationMessage from '../../components/ValidationMessage';

// Define the design style options
export type DesignStyle = 'Modern' | 'Rustic' | 'Luxury' | 'Minimalist' | 'Scandinavian';

// Define the staging suggestions response type
export interface StagingSuggestions {
  textSuggestions: string;
  imageUrl?: string;
}

const VirtualStagingTool = () => {
  const navigate = useNavigate();
  const { state } = useApp();

  // State for the uploaded image
  const [roomImage, setRoomImage] = useState<string[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<DesignStyle>('Modern');
  const [isGenerating, setIsGenerating] = useState(false);
  const [stagingResults, setStagingResults] = useState<StagingSuggestions | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [imageError, setImageError] = useState<string | null>(null);

  // Handle image upload
  const handleImagesUploaded = (images: string[]) => {
    setRoomImage(images);
    setImageError(null);
  };

  // Handle style selection
  const handleStyleChange = (style: DesignStyle) => {
    setSelectedStyle(style);
  };

  // Handle generate suggestions
  const handleGenerateSuggestions = async () => {
    // Validate inputs
    if (roomImage.length === 0) {
      setImageError('Please upload an image of the room');
      return;
    }

    setError(null);
    setIsGenerating(true);

    try {
      // Generate staging suggestions
      const suggestions = await generateStagingSuggestions(roomImage[0], selectedStyle);

      // Generate staging image (optional)
      let imageUrl: string | undefined = undefined;

      try {
        imageUrl = await generateStagingImage(roomImage[0], selectedStyle, suggestions.textSuggestions);
      } catch (imageError) {
        console.error('Error generating staging image:', imageError);
        // We'll continue even if image generation fails
      }

      setStagingResults({
        textSuggestions: suggestions.textSuggestions,
        imageUrl
      });
    } catch (err: any) {
      console.error('Error generating staging suggestions:', err);

      // Use the error message from the service if available
      setError(err.message || 'We couldn\'t generate staging suggestions at this time. Please try again later.');

      // If it's a 404 error, show a more specific message
      if (err.message && err.message.includes('not available')) {
        setError('The Virtual Staging service is not yet available on the production server. Please run the application locally for full functionality.');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle download report
  const handleDownloadReport = () => {
    if (!stagingResults) return;

    // Create a simple HTML report
    const reportContent = `
      <html>
        <head>
          <title>Virtual Staging Suggestions</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #0066cc; }
            h2 { color: #333; margin-top: 20px; }
            img { max-width: 100%; border-radius: 8px; margin: 20px 0; }
            .suggestions { white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 8px; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
            .note { background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 12px; margin: 15px 0; border-radius: 4px; }
          </style>
        </head>
        <body>
          <h1>Virtual Staging Suggestions</h1>
          <p><strong>Design Style:</strong> ${selectedStyle}</p>

          <h2>Original Room</h2>
          <img src="${roomImage[0]}" alt="Original Room" />

          ${stagingResults.imageUrl ? `
            <h2>Staged Room Concept</h2>
            <div class="note">
              <p><strong>Note:</strong> If the staged image doesn't appear below, you can view it in the application or try accessing this report in a different browser.</p>
            </div>
            <img src="${stagingResults.imageUrl}" alt="Staged Room Concept" onerror="this.style.display='none'; document.getElementById('image-error').style.display='block';" />
            <div id="image-error" style="display:none; background-color: #f8d7da; border-left: 4px solid #dc3545; padding: 12px; margin: 15px 0; border-radius: 4px;">
              <p><strong>Image could not be loaded</strong></p>
              <p>The staged room image could not be displayed due to security restrictions or because the image is no longer available.</p>
            </div>
          ` : ''}

          <h2>Staging Recommendations</h2>
          <div class="suggestions">${stagingResults.textSuggestions.replace(/\n/g, '<br>')}</div>

          <div class="footer">
            Generated by Digital Realtor Virtual Staging Tool
          </div>
        </body>
      </html>
    `;

    // Create a blob and download it
    const blob = new Blob([reportContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `virtual-staging-${selectedStyle.toLowerCase()}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="py-12 bg-dark-900 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-700 bg-white hover:bg-gray-100 px-4 py-2 rounded-lg transition-all duration-300 mb-4 hover:shadow-sm border border-gray-300 font-medium"
          >
            <FaArrowLeft className="mr-2 text-gray-500" />
            Back
          </button>
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
            <FaHome className="mr-3 text-accent-cyan" />
            Virtual Staging Suggestions
          </h1>
          <p className="text-primary-200">
            Upload an empty room photo and let AI suggest staging ideas.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Input Form */}
          <div className="lg:col-span-1">
            <div className="bg-dark-800 rounded-xl shadow-card p-6 border border-primary-700/30">
              <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                <FaImage className="mr-2 text-accent-cyan" />
                Room Details
              </h2>

              {/* Step 1: Upload Photo */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-primary-200 mb-3">Step 1: Upload Room Photo</h3>
                <ImageUploader
                  onImagesUploaded={handleImagesUploaded}
                  maxImages={1}
                  isAnalyzing={isGenerating}
                />
                {imageError && (
                  <ValidationMessage type="error" message={imageError} />
                )}
              </div>

              {/* Step 2: Select Design Style */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-primary-200 mb-3">Step 2: Select Design Style</h3>
                <StyleSelector
                  selectedStyle={selectedStyle}
                  onStyleChange={handleStyleChange}
                  disabled={isGenerating}
                />
              </div>

              {/* Step 3: Generate Button */}
              <div>
                <h3 className="text-lg font-medium text-primary-200 mb-3">Step 3: Generate Suggestions</h3>
                <button
                  onClick={handleGenerateSuggestions}
                  disabled={isGenerating || roomImage.length === 0}
                  aria-disabled={isGenerating || roomImage.length === 0}
                  className="w-full py-3 px-6 rounded-lg flex items-center justify-center font-semibold transition-all duration-300 bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg disabled:bg-gray-500 disabled:text-gray-200 disabled:cursor-not-allowed disabled:opacity-80 disabled:hover:shadow-none disabled:hover:bg-gray-500"
                >
                  {isGenerating ? (
                    <>
                      <FaSpinner className="animate-spin mr-2 text-lg" />
                      Generating Suggestions...
                    </>
                  ) : (
                    <>
                      <FaPalette className="mr-2 text-lg" />
                      Generate Staging Suggestions
                    </>
                  )}
                </button>
                {error && (
                  <ValidationMessage type="error" message={error} />
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Results */}
          <div className="lg:col-span-2">
            {stagingResults ? (
              <div className="bg-dark-800 rounded-xl shadow-card p-6 border border-primary-700/30">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <FaPalette className="mr-2 text-accent-cyan" />
                    Staging Suggestions
                  </h2>
                  <button
                    onClick={handleDownloadReport}
                    className="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-5 py-2.5 rounded-lg transition-all duration-300 hover:shadow-lg focus:ring-2 focus:ring-blue-300 focus:outline-none disabled:bg-gray-500 disabled:text-gray-200 disabled:cursor-not-allowed disabled:hover:shadow-none"
                  >
                    <FaDownload className="mr-2.5 text-lg" />
                    Download Report
                  </button>
                </div>
                <StagingResults
                  originalImage={roomImage[0]}
                  suggestions={stagingResults}
                  designStyle={selectedStyle}
                />
              </div>
            ) : (
              <div className="bg-dark-800 rounded-xl shadow-card p-8 border border-primary-700/30 flex flex-col items-center justify-center min-h-[400px] text-center">
                <FaImage className="text-5xl text-primary-700 mb-4" />
                <h3 className="text-xl font-medium text-primary-200 mb-2">No Suggestions Yet</h3>
                <p className="text-primary-400 max-w-md">
                  Upload a room photo and select a design style, then click "Generate Staging Suggestions" to get AI-powered recommendations.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VirtualStagingTool;
