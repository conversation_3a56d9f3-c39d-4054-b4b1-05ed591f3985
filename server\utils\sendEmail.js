import nodemailer from 'nodemailer';

const sendEmail = async (options) => {
  // Log that we're attempting to send an email
  console.log(`Attempting to send email to: ${options.email}`);

  // Create a transporter using SMTP
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '<EMAIL>',
      pass: process.env.EMAIL_PASS || 'your-email-password'
    }
  });

  // For development/testing, also log the email content
  if (process.env.NODE_ENV !== 'production') {
    console.log('==== EMAIL CONTENT FOR TESTING ====');
    console.log(`To: ${options.email}`);
    console.log(`Subject: ${options.subject}`);
    console.log('Content:', options.html);
    console.log('============================');

    // Extract verification code from HTML content for testing
    let verificationCode = '';
    if (options.html.includes('Your Verification Code')) {
      const match = options.html.match(/letter-spacing: 5px;[^>]*>([0-9]{6})<\/div>/);
      if (match && match[1]) {
        verificationCode = match[1];
        console.log('\n\n==== VERIFICATION CODE FOR TESTING ====');
        console.log(`CODE: ${verificationCode}`);
        console.log('======================================\n\n');
      }
    }
  }

  // Define email options
  const mailOptions = {
    from: process.env.EMAIL_FROM || 'Digital Realtor <<EMAIL>>',
    to: options.email,
    subject: options.subject,
    html: options.html
  };

  try {
    // Send the email
    const info = await transporter.sendMail(mailOptions);
    console.log(`Email sent successfully: ${info.messageId}`);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);

    // For development, return a mock success to allow testing without email setup
    if (process.env.NODE_ENV !== 'production') {
      console.log('Returning mock success for development environment');
      return {
        messageId: 'mock-message-id',
        accepted: [options.email],
        rejected: [],
        pending: [],
        response: 'Mock email sent successfully'
      };
    }

    // In production, throw the error
    throw error;
  }
};

export default sendEmail;
