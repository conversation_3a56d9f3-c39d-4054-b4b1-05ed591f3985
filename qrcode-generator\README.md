# Digital Realtor QR Code Generator

This tool generates QR codes for the Digital Realtor application (https://aidigitalrealtor.com/).

## Generated QR Codes

The following QR codes have been generated:

1. **Standard QR Code (PNG)**: `output/digital-realtor-qrcode.png`
2. **Standard QR Code (SVG)**: `output/digital-realtor-qrcode.svg`
3. **Branded QR Code (Blue)**: `output/digital-realtor-branded-qrcode.png`
4. **Final QR Code**: `output/digital-realtor-qrcode-final.png`

## Usage

### Generate Standard QR Codes

```bash
node generate-qrcode.js
```

This generates both PNG and SVG versions of the QR code.

### Generate Branded QR Code

```bash
node generate-branded-qrcode.js
```

This generates a blue QR code with a larger margin.

### Generate Final QR Code

```bash
node generate-qrcode-with-logo.js
```

This generates a QR code that can be manually combined with a logo.

## Adding a Logo

To add a logo to the QR code:

1. Open the generated QR code in an image editor like Photoshop, GIMP, or even PowerPoint
2. Create or import your Digital Realtor logo
3. Resize the logo to about 20-30% of the QR code size
4. Center the logo on the QR code
5. Save the result

**Tip**: Make sure the logo has a white background or is transparent to maintain good contrast with the QR code.

## QR Code Best Practices

1. **Test the QR code**: Always scan the QR code with multiple devices to ensure it works correctly.
2. **Use error correction**: The QR codes are generated with high error correction (level H), which allows for up to 30% of the QR code to be damaged or obscured.
3. **Maintain contrast**: Ensure there's good contrast between the QR code and its background.
4. **Size matters**: When printing, make sure the QR code is at least 1 inch (2.5 cm) in size.
5. **Include a call to action**: When displaying the QR code, include text that tells users what to expect when they scan it.

## Customization

You can customize the QR codes by modifying the options in the JavaScript files:

- Change colors
- Adjust margins
- Modify size
- Change error correction level

## Dependencies

- [qrcode](https://www.npmjs.com/package/qrcode): For generating QR codes
