# MongoDB Setup Guide for Digital Realtor

This guide will help you set up MongoDB for the Digital Realtor application.

## Prerequisites

1. MongoDB installed locally or a MongoDB Atlas account
2. Node.js and npm installed

## Option 1: Local MongoDB Setup

### Install MongoDB Community Edition

#### Windows
1. Download the MongoDB Community Server from [MongoDB Download Center](https://www.mongodb.com/try/download/community)
2. Follow the installation wizard
3. MongoDB will be installed as a service and should start automatically

#### macOS
```bash
# Using Homebrew
brew tap mongodb/brew
brew install mongodb-community
brew services start mongodb-community
```

#### Linux (Ubuntu)
```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Create a list file for MongoDB
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Reload local package database
sudo apt-get update

# Install MongoDB packages
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod

# Verify that MongoDB has started successfully
sudo systemctl status mongod
```

### Configure MongoDB

1. MongoDB should be running on the default port 27017
2. The application is configured to connect to a database named `digitalrealtordb`
3. No additional configuration is needed as the database and collections will be created automatically

## Option 2: MongoDB Atlas (Cloud) Setup

1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/cloud/atlas/register)
2. Create a new cluster (the free tier is sufficient)
3. In the Security tab, create a database user with read and write privileges
4. In the Network Access tab, add your IP address to the IP Access List
5. In the Clusters tab, click "Connect" and select "Connect your application"
6. Copy the connection string and replace `<password>` with your database user's password

## Update Environment Variables

1. Open the `.env` file in the root directory of the project
2. Update the `MONGO_URI` variable with your MongoDB connection string:

For local MongoDB:
```
MONGO_URI=mongodb://localhost:27017/digitalrealtordb
```

For MongoDB Atlas:
```
MONGO_URI=mongodb+srv://<username>:<password>@<cluster-url>/digitalrealtordb?retryWrites=true&w=majority
```

## Test the Connection

Run the following command to test the MongoDB connection:

```bash
npm run test:db
```

If successful, you should see:
```
MongoDB Connected: <hostname>
Connection successful!
Database name: digitalrealtordb
Connection state: 1
Connection closed
```

## Seed the Database

To populate the database with sample data:

```bash
npm run data:import
```

To clear all data from the database:

```bash
npm run data:destroy
```

## Running the Application with MongoDB

To run both the frontend and backend with MongoDB:

```bash
npm run dev:full
```

This will start:
- The React frontend on http://localhost:5173 (or another port if 5173 is in use)
- The Express backend on http://localhost:5000

## Troubleshooting

### Connection Issues
- Ensure MongoDB is running
- Check that the connection string in `.env` is correct
- For Atlas, ensure your IP is in the allowed list

### Authentication Issues
- Verify username and password in the connection string
- Ensure the user has the correct permissions

### Database Operations Issues
- Check the console for specific error messages
- Ensure the models are correctly defined
- Verify that the routes are properly configured

## Additional Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [Mongoose Documentation](https://mongoosejs.com/docs/)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
