// This file adds a test appointment to the database or localStorage for debugging purposes
import { Appointment, CreateAppointmentData } from '../models/Appointment';
import appointmentService from '../services/appointmentService';

// Function to add a test appointment
export async function addTestAppointment(userId = 'guest'): Promise<Appointment | null> {
  // Create a test appointment
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Format date as "Month Day, Year" (e.g., "June 15, 2023")
  const formattedDate = tomorrow.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Format time as "HH:MM AM/PM" (e.g., "10:00 AM")
  const formattedTime = tomorrow.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const testAppointmentData: CreateAppointmentData = {
    clientName: 'Test Client',
    clientEmail: '<EMAIL>',
    clientPhone: '************',
    propertyAddress: '123 Test Street, Test City',
    propertyType: 'House',
    date: formattedDate,
    time: formattedTime,
    status: 'confirmed',
    notes: 'This is a test appointment added for debugging purposes.',
    userId: userId
  };

  try {
    // Save to database
    console.log('Adding test appointment to database for user:', userId);
    const savedAppointment = await appointmentService.createClientAppointment(testAppointmentData);
    console.log('Test appointment added to database:', savedAppointment);
    return savedAppointment;
  } catch (error) {
    console.error('Error saving test appointment to database:', error);

    // Fallback to localStorage
    try {
      // Get existing appointments from localStorage
      let appointments: Appointment[] = [];
      try {
        const storedAppointments = localStorage.getItem('localAppointments');
        if (storedAppointments) {
          appointments = JSON.parse(storedAppointments);
        }
      } catch (localError) {
        console.error('Error loading appointments from localStorage:', localError);
      }

      // Create a local appointment object
      const localAppointment: Appointment = {
        id: `test-appt-${Date.now()}`,
        ...testAppointmentData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add to local array and save
      appointments.push(localAppointment);
      localStorage.setItem('localAppointments', JSON.stringify(appointments));
      console.log('Test appointment added to localStorage as fallback:', localAppointment);
      return localAppointment;
    } catch (localError) {
      console.error('Error saving test appointment to localStorage:', localError);
      return null;
    }
  }
}

// Function to clear all appointments
export async function clearAllAppointments(): Promise<boolean> {
  try {
    // Clear from database (this would be a real API call in production)
    console.log('Attempting to clear appointments from database');
    // This would be an API call to delete all appointments for the user
    // For now, we'll just clear localStorage as a fallback

    // Clear from localStorage as fallback
    localStorage.removeItem('localAppointments');
    console.log('All appointments cleared from localStorage');
    return true;
  } catch (error) {
    console.error('Error clearing appointments:', error);

    // Try to clear localStorage as a fallback
    try {
      localStorage.removeItem('localAppointments');
      console.log('All appointments cleared from localStorage (fallback)');
      return true;
    } catch (localError) {
      console.error('Error clearing appointments from localStorage:', localError);
      return false;
    }
  }
}
