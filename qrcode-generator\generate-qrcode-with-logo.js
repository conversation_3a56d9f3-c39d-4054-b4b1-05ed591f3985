const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

// Configuration
const appUrl = 'https://aidigitalrealtor.com/'; // Correct production URL
const outputDir = path.join(__dirname, 'output');
const qrFilename = 'digital-realtor-qrcode-final.png';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Options for QR code generation
const qrOptions = {
  errorCorrectionLevel: 'H', // High error correction capability
  type: 'image/png',
  quality: 0.92,
  margin: 2,
  width: 500,
  color: {
    dark: '#0066cc', // Blue dots
    light: '#ffffff' // White background
  }
};

// Generate QR code
QRCode.toFile(
  path.join(outputDir, qrFilename),
  appUrl,
  qrOptions,
  function (err) {
    if (err) {
      console.error('Error generating QR code:', err);
    } else {
      console.log(`QR code generated successfully: ${path.join(outputDir, qrFilename)}`);
      console.log('\nInstructions for adding a logo:');
      console.log('1. Open the generated QR code in an image editor like Photoshop, GIMP, or even PowerPoint');
      console.log('2. Create or import your Digital Realtor logo');
      console.log('3. Resize the logo to about 20-30% of the QR code size');
      console.log('4. Center the logo on the QR code');
      console.log('5. Save the result');
      console.log('\nTip: Make sure the logo has a white background or is transparent');
      console.log('     to maintain good contrast with the QR code.');
    }
  }
);

// Also generate a terminal QR code for quick testing
QRCode.toString(appUrl, { type: 'terminal' }, function (err, terminalCode) {
  if (err) {
    console.error('Error generating terminal QR code:', err);
  } else {
    console.log('\nQR Code for quick testing (scan with your phone camera):');
    console.log(terminalCode);
  }
});
