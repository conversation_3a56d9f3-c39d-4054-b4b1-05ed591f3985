import React, { useState, useEffect } from 'react';
import { <PERSON>aR<PERSON><PERSON>, <PERSON>aMagic, FaSpinner } from 'react-icons/fa';
import StepForm from './StepForm';
import ProgressBar from './ProgressBar';
import SummaryReview from './SummaryReview';
import SuccessScreen from './SuccessScreen';
import { getApiUrl, API_ENDPOINTS, USE_MOCK_IN_PRODUCTION, USE_MOCK_IN_DEVELOPMENT } from '../../utils/apiConfig';
import { mockGenerateContract } from '../../utils/mockApi';
import { generateContract as apiGenerateContract } from '../../utils/contractGeneratorApi';
import { inferMissingValues } from '../../utils/inferMissingValues';
import { generateContract as aiGenerateContract } from '../../utils/contractGenerator';
import './ContractGenerator.css';

const ContractGenerator = () => {
  // State to track current step
  const [currentStep, setCurrentStep] = useState(1);

  // Total number of steps in the form
  const totalSteps = 12;

  // State to store all form data
  const [formData, setFormData] = useState({
    buyer: {
      fullName: '',
      email: ''
    },
    seller: {
      fullName: ''
    },
    property: {
      address: '',
      unit: '',
      parkingIncluded: false,
      parkingDetails: ''
    },
    financials: {
      purchasePrice: '',
      earnestMoney: '',
      closingDate: ''
    },
    associations: {
      hoa: false,
      monthlyFees: ''
    },
    fixturesIncluded: [],
    additionalTerms: ''
  });

  // State to track if PDF is being generated
  const [isGenerating, setIsGenerating] = useState(false);

  // State to store the generated PDF URL
  const [pdfUrl, setPdfUrl] = useState(null);

  // State to track the selected AI provider
  const [aiProvider, setAiProvider] = useState('openai'); // 'openai' or 'claude'

  // Function to handle form data updates
  const updateFormData = (section, field, value) => {
    setFormData(prevData => ({
      ...prevData,
      [section]: {
        ...prevData[section],
        [field]: value
      }
    }));
  };

  // Function to update array fields (like fixturesIncluded)
  const updateArrayField = (field, value) => {
    setFormData(prevData => ({
      ...prevData,
      [field]: value
    }));
  };

  // Function to move to the next step
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Function to go back to the previous step
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Function to handle form submission and PDF generation
  const generateContract = async () => {
    setIsGenerating(true);

    try {
      // First, try to infer any missing values using AI
      await inferMissingValues();

      // Check if we should use the mock implementation
      const isProduction = window.location.hostname === 'aidigitalrealtor.com' ||
                          window.location.hostname === 'www.aidigitalrealtor.com';
      const isDevelopment = window.location.hostname === 'localhost' ||
                           window.location.hostname === '127.0.0.1';

      // Use mock implementation in production if configured
      if (isProduction && USE_MOCK_IN_PRODUCTION) {
        console.log('Using mock contract generator in production');

        // Use the mock implementation
        const mockData = await mockGenerateContract({
          ...formData,
          aiProvider // Include the selected AI provider
        });

        console.log('Mock contract generated:', mockData);

        // Set the PDF URL from the mock data
        setPdfUrl(mockData.downloadUrl);
        setCurrentStep(totalSteps + 1); // Move to success screen
        return;
      }

      // In development, use our AI-driven approach
      if (isDevelopment && !USE_MOCK_IN_DEVELOPMENT) {
        console.log('Using AI-driven contract generator in development');

        try {
          // Use our AI-driven approach
          const data = await aiGenerateContract(formData);

          console.log('Contract generated:', data);

          // Set the PDF URL from the response
          setPdfUrl(data.downloadUrl);
          setCurrentStep(totalSteps + 1); // Move to success screen
          return;
        } catch (error) {
          console.error('Error generating contract with AI:', error);

          // Fall back to the custom API handler
          console.log('Falling back to custom API handler');

          try {
            // Use our custom API handler as a fallback
            const data = await apiGenerateContract(formData, aiProvider);

            console.log('Contract generated (fallback):', data);

            // Set the PDF URL from the response
            setPdfUrl(data.downloadUrl);
            setCurrentStep(totalSteps + 1); // Move to success screen
            return;
          } catch (fallbackError) {
            console.error('Error with fallback contract generation:', fallbackError);
            throw fallbackError;
          }
        }
      }

      // Use the mock implementation in development if configured
      if (isDevelopment && USE_MOCK_IN_DEVELOPMENT) {
        console.log('Using mock contract generator in development');

        try {
          // Use the mock implementation
          const mockData = await mockGenerateContract({
            ...formData,
            aiProvider // Include the selected AI provider
          });

          console.log('Mock contract generated:', mockData);

          // Set the PDF URL from the mock data
          setPdfUrl(mockData.downloadUrl);
          setCurrentStep(totalSteps + 1); // Move to success screen
          return;
        } catch (error) {
          console.error('Error generating contract with mock:', error);
          throw error;
        }
      }

      // Otherwise, send the data to the backend to generate the PDF
      const apiUrl = getApiUrl(API_ENDPOINTS.CONTRACT_GENERATOR);
      console.log(`Sending contract generation request to: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          aiProvider // Include the selected AI provider
        })
      });

      if (!response.ok) {
        let errorMessage = 'Failed to generate contract';

        try {
          // Try to parse the response as JSON
          const contentType = response.headers.get('content-type');

          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();

            // Check for rate limiting error
            if (errorData.status === 'error' && errorData.message.includes('Too many')) {
              alert("You've reached the maximum number of contract generations allowed per hour. Please try again later.");
              return;
            }

            errorMessage = errorData.message || errorMessage;
          } else {
            // If not JSON, it might be HTML or some other format
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            errorMessage = `Server returned ${response.status} ${response.statusText}`;
          }
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
        }

        throw new Error(errorMessage);
      }

      let data;
      try {
        data = await response.json();

        if (data.status === 'success') {
          setPdfUrl(data.downloadUrl);
          setCurrentStep(totalSteps + 1); // Move to success screen
        } else {
          throw new Error(data.message || 'Failed to generate contract');
        }
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        throw new Error('Invalid response from server. Please try again.');
      }
    } catch (error) {
      console.error('Error generating contract:', error);
      alert(`There was an error generating your contract: ${error.message || 'Please try again.'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // Function to infer missing values using AI
  const inferMissingValues = async () => {
    // Check if we need to infer any values
    const needsInference =
      !formData.financials.earnestMoney ||
      !formData.financials.closingDate ||
      formData.fixturesIncluded.length === 0;

    if (!needsInference) {
      return; // No inference needed
    }

    try {
      // In a production environment, this would call the backend API with the selected AI provider
      console.log(`Using ${aiProvider} for AI inference`);

      // Check if we should use the mock implementation
      const isProduction = window.location.hostname === 'aidigitalrealtor.com' ||
                          window.location.hostname === 'www.aidigitalrealtor.com';
      const isDevelopment = window.location.hostname === 'localhost' ||
                           window.location.hostname === '127.0.0.1';

      // Use mock implementation in production if configured
      if (isProduction && USE_MOCK_IN_PRODUCTION) {
        // Simulate different behavior based on the selected AI provider
        if (aiProvider === 'claude') {
          console.log('Using Claude for inference (mock)');

          // Infer earnest money if missing (5% of purchase price with Claude's calculation)
          if (!formData.financials.earnestMoney && formData.financials.purchasePrice) {
            // Claude might suggest a slightly different percentage based on its training
            const suggestedEarnestMoney = Math.round(parseFloat(formData.financials.purchasePrice) * 0.05);
            setFormData(prevData => ({
              ...prevData,
              financials: {
                ...prevData.financials,
                earnestMoney: suggestedEarnestMoney.toString()
              }
            }));
          }

          // Infer closing date if missing (45 days from today with Claude's calculation)
          if (!formData.financials.closingDate) {
            const today = new Date();
            // Claude might suggest a slightly different timeframe
            const suggestedDate = new Date(today.setDate(today.getDate() + 45));
            const formattedDate = suggestedDate.toISOString().split('T')[0];
            setFormData(prevData => ({
              ...prevData,
              financials: {
                ...prevData.financials,
                closingDate: formattedDate
              }
            }));
          }

          // Infer fixtures if none selected (Claude's suggestions)
          if (formData.fixturesIncluded.length === 0) {
            // Claude might suggest a slightly different set of fixtures
            const suggestedFixtures = ['Refrigerator', 'Oven/Range', 'Dishwasher', 'Microwave', 'Window Treatments', 'Light Fixtures'];
            setFormData(prevData => ({
              ...prevData,
              fixturesIncluded: suggestedFixtures
            }));
          }
        } else {
          // Default to OpenAI
          console.log('Using OpenAI for inference (mock)');

          // Infer earnest money if missing (5% of purchase price with OpenAI's calculation)
          if (!formData.financials.earnestMoney && formData.financials.purchasePrice) {
            const suggestedEarnestMoney = Math.round(parseFloat(formData.financials.purchasePrice) * 0.05);
            setFormData(prevData => ({
              ...prevData,
              financials: {
                ...prevData.financials,
                earnestMoney: suggestedEarnestMoney.toString()
              }
            }));
          }

          // Infer closing date if missing (45 days from today with OpenAI's calculation)
          if (!formData.financials.closingDate) {
            const today = new Date();
            const suggestedDate = new Date(today.setDate(today.getDate() + 45));
            const formattedDate = suggestedDate.toISOString().split('T')[0];
            setFormData(prevData => ({
              ...prevData,
              financials: {
                ...prevData.financials,
                closingDate: formattedDate
              }
            }));
          }

          // Infer fixtures if none selected (OpenAI's suggestions)
          if (formData.fixturesIncluded.length === 0) {
            const suggestedFixtures = ['Refrigerator', 'Oven/Range', 'Dishwasher', 'Microwave', 'Window Treatments'];
            setFormData(prevData => ({
              ...prevData,
              fixturesIncluded: suggestedFixtures
            }));
          }
        }
      } else {
        // In development, use our AI-driven approach
        try {
          // Create a mock schema for testing
          const mockSchema = {
            buyer: {
              fullName: { infer: false },
              email: { infer: false }
            },
            seller: {
              fullName: { infer: false }
            },
            property: {
              address: { infer: false },
              unit: { infer: false },
              parkingIncluded: { infer: false },
              parkingDetails: { infer: true }
            },
            financials: {
              purchasePrice: { infer: false },
              earnestMoney: { infer: true },
              closingDate: { infer: true }
            },
            associations: {
              hoa: { infer: false },
              monthlyFees: { infer: true }
            },
            fixturesIncluded: { infer: true }
          };

          // Use our AI-driven approach to infer missing values
          const inferredData = await inferMissingValues(formData, mockSchema);

          // Update the form data with the inferred values
          setFormData(inferredData);

          console.log('AI inference complete:', inferredData);
        } catch (error) {
          console.error('Error using AI inference:', error);

          // Fall back to the mock implementation
          console.log('Falling back to mock inference');

          // Default to OpenAI
          console.log('Using OpenAI for inference (fallback)');

          // Infer earnest money if missing (5% of purchase price with OpenAI's calculation)
          if (!formData.financials.earnestMoney && formData.financials.purchasePrice) {
            const suggestedEarnestMoney = Math.round(parseFloat(formData.financials.purchasePrice) * 0.05);
            setFormData(prevData => ({
              ...prevData,
              financials: {
                ...prevData.financials,
                earnestMoney: suggestedEarnestMoney.toString()
              }
            }));
          }

          // Infer closing date if missing (45 days from today with OpenAI's calculation)
          if (!formData.financials.closingDate) {
            const today = new Date();
            const suggestedDate = new Date(today.setDate(today.getDate() + 45));
            const formattedDate = suggestedDate.toISOString().split('T')[0];
            setFormData(prevData => ({
              ...prevData,
              financials: {
                ...prevData.financials,
                closingDate: formattedDate
              }
            }));
          }

          // Infer fixtures if none selected (OpenAI's suggestions)
          if (formData.fixturesIncluded.length === 0) {
            const suggestedFixtures = ['Refrigerator', 'Oven/Range', 'Dishwasher', 'Microwave', 'Window Treatments'];
            setFormData(prevData => ({
              ...prevData,
              fixturesIncluded: suggestedFixtures
            }));
          }
        }
      }
    } catch (error) {
      console.error('Error inferring values:', error);
      // Continue with contract generation even if inference fails
    }
  };

  // Render different components based on current step
  const renderStep = () => {
    // If we're at the success screen
    if (currentStep > totalSteps) {
      return <SuccessScreen pdfUrl={pdfUrl} />;
    }

    // If we're at the summary review step
    if (currentStep === totalSteps) {
      return (
        <SummaryReview
          formData={formData}
          onPrevious={prevStep}
          onSubmit={generateContract}
          isGenerating={isGenerating}
        />
      );
    }

    // Otherwise, render the step form
    return (
      <StepForm
        currentStep={currentStep}
        formData={formData}
        updateFormData={updateFormData}
        updateArrayField={updateArrayField}
        onNext={nextStep}
        onPrevious={prevStep}
      />
    );
  };

  return (
    <div className="contract-generator-container">
      <h1 className="contract-generator-title">AI Contract Generator</h1>
      <p className="contract-generator-subtitle">
        Generate a Chicago Association of REALTORS® Condominium Purchase Contract in minutes
      </p>

      {/* AI Provider Toggle */}
      <div className="ai-provider-toggle">
        <div className="ai-provider-label">
          <FaRobot className="ai-icon" />
          <span>Powered by:</span>
        </div>
        <div className="ai-provider-buttons">
          <button
            className={`ai-provider-button ${aiProvider === 'openai' ? 'active' : ''}`}
            onClick={() => setAiProvider('openai')}
          >
            OpenAI
          </button>
          <button
            className={`ai-provider-button ${aiProvider === 'claude' ? 'active' : ''}`}
            onClick={() => setAiProvider('claude')}
          >
            Claude
          </button>
        </div>
      </div>

      {currentStep <= totalSteps && (
        <ProgressBar currentStep={currentStep} totalSteps={totalSteps} />
      )}

      <div className="contract-generator-form-container">
        {renderStep()}
      </div>
    </div>
  );
};

export default ContractGenerator;
