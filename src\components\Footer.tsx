import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-transparent text-blue-300 pt-12 pb-8 relative z-10 backdrop-blur-md bg-dark-900/70 border-t border-primary-900/30 mt-20">
      <div className="app-background absolute inset-0 -z-10 opacity-30"></div>
      <div className="max-w-5xl mx-auto px-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm justify-center">
        {/* Column 1: Quick Links */}
        <div>
          <h3 className="text-white font-semibold mb-4">Quick Links</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Home
              </Link>
            </li>
            <li>
              <Link to="/about" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                About
              </Link>
            </li>
            <li>
              <Link to="/properties" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Properties
              </Link>
            </li>
            <li>
              <Link to="/contact" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Contact
              </Link>
            </li>
          </ul>
        </div>

        {/* Column 2: Marketing Tools */}
        <div>
          <h3 className="text-white font-semibold mb-4">Marketing Tools</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/listing-generator" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Listing Generator
              </Link>
            </li>
            <li>
              <Link to="/virtual-staging" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Virtual Staging
              </Link>
            </li>
          </ul>
        </div>

        {/* Column 3: Analysis & Compliance Tools */}
        <div>
          <h3 className="text-white font-semibold mb-4">Analysis & Compliance Tools</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/comp-analyzer" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Comp Analyzer
              </Link>
            </li>
            <li>
              <Link to="/neighborhood-compliance" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Neighborhood & Compliance
              </Link>
            </li>
            <li>
              <Link to="/contract-generator" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Contract Generator
              </Link>
            </li>
            <li>
              <Link to="/appointment-scheduler" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Appointment Scheduler
              </Link>
            </li>
            <li>
              <Link to="/inspection-report" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                Inspection Report Generator
              </Link>
            </li>
          </ul>
        </div>

        {/* Column 4: Contact Us */}
        <div>
          <h3 className="text-white font-semibold mb-4">Contact Us</h3>
          <a
            href="mailto:<EMAIL>"
            className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow inline-block"
          >
            <EMAIL>
          </a>
        </div>
      </div>

      {/* Copyright Section */}
      <div className="text-center text-xs text-gray-400 mt-10">
        © 2025 <span className="text-blue-400">Digital Realtor</span>. All rights reserved.
      </div>
    </footer>
  );
};

export default Footer;
