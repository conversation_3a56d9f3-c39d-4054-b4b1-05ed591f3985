/**
 * Utility functions for mapping Zillow API response fields to UI form fields
 */

/**
 * TypeScript interface for mapped Zillow property details
 */
export interface ZillowMappedPropertyDetails {
  bedrooms: number | null;
  bathrooms: number | null;
  squareFeet: number | null;
  yearBuilt: number | null;
  parking: string | null;
  hoaFee: string | number | null;
  propertyType: string | null;
  lotSize: string | number | null;
  price: number | null;
  pricePerSqFt: number | null;
  address: string | null;
  city: string | null;
  state: string | null;
  zip: string | null;
  mlsId: string | null;
  appliances: string[] | null;
  features: string[] | null;
  taxHistory: Array<{year: number, amount: number}> | null; // Tax history for last 2 years
  taxSummary: string | null; // Formatted tax summary
  _addressMismatch?: boolean; // Flag to indicate if returned address doesn't match requested
}

/**
 * Helper function to parse a string to a number, returning null if invalid
 * @param value - The value to parse
 * @returns The parsed number or null
 */
function parseNumberOrNull(value: any): number | null {
  if (value === null || value === undefined) return null;

  // If it's already a number, return it
  if (typeof value === 'number') return value;

  // If it's a string, try to parse it
  if (typeof value === 'string') {
    // Remove any non-numeric characters except decimal point
    const cleanedValue = value.replace(/[^0-9.]/g, '');
    const parsed = parseFloat(cleanedValue);
    return isNaN(parsed) ? null : parsed;
  }

  return null;
}

/**
 * Helper function to clean HOA fee values
 * @param value - The HOA fee value to clean
 * @returns The cleaned HOA fee value
 */
function cleanHOA(value: any): string | number | null {
  if (value === null || value === undefined) return null;

  console.log('Cleaning HOA fee:', { value, type: typeof value });

  // If it's a number, return it
  if (typeof value === 'number') return value;

  // If it's a string, clean it
  if (typeof value === 'string') {
    // Extract just the numeric part for consistent processing
    const numericPart = value
      .replace(/\$/g, '')
      .replace(/monthly/i, '')
      .replace(/per month/i, '')
      .replace(/month/i, '')
      .replace(/,/g, '')
      .trim();

    // Try to parse as a number
    const parsed = parseFloat(numericPart);
    if (!isNaN(parsed)) {
      return parsed;
    }

    // If we couldn't parse it, return the cleaned string
    return numericPart;
  }

  return null;
}

/**
 * Helper function to extract a fact from the atAGlanceFacts array
 * @param facts - The atAGlanceFacts array
 * @param label - The fact label to search for
 * @returns The fact value or null
 */
function getFact(facts: any[], label: string): string | null {
  const fact = facts.find(f =>
    f?.factLabel?.toLowerCase().includes(label.toLowerCase())
  );
  return fact?.factValue ?? null;
}

/**
 * Helper function to extract and format tax history information
 * @param response - The Zillow API response
 * @returns Tax history and summary
 */
function extractTaxInformation(response: any): {
  taxHistory: Array<{year: number, amount: number}> | null;
  taxSummary: string | null;
} {
  const currentYear = new Date().getFullYear();
  const taxHistory: Array<{year: number, amount: number}> = [];

  // Check for tax history in various possible locations
  const possibleTaxFields = [
    response.taxHistory,
    response.resoFacts?.taxHistory,
    response.propertyTaxes,
    response.annualTaxes,
    response.taxRecords
  ];

  // Look for tax history array
  for (const taxField of possibleTaxFields) {
    if (Array.isArray(taxField)) {
      taxField.forEach((tax: any) => {
        // Handle various date/time formats
        let year: number;
        const timeField = tax.year || tax.taxYear || tax.time;

        if (typeof timeField === 'string') {
          // Handle date strings like "2023-01-01", "2023", "Jan 2023", etc.
          const dateMatch = timeField.match(/(\d{4})/);
          year = dateMatch ? parseInt(dateMatch[1]) : NaN;
        } else if (typeof timeField === 'number') {
          // Handle timestamp or year number
          if (timeField > 10000000000) {
            // Looks like a timestamp (milliseconds)
            year = new Date(timeField).getFullYear();
          } else if (timeField > 1000000000) {
            // Looks like a timestamp (seconds)
            year = new Date(timeField * 1000).getFullYear();
          } else {
            // Assume it's already a year
            year = timeField;
          }
        } else {
          year = NaN;
        }

        const amount = parseFloat(tax.amount || tax.taxPaid || tax.value || tax.tax);

        if (!isNaN(year) && !isNaN(amount) && year >= currentYear - 3 && year <= currentYear) {
          taxHistory.push({ year, amount });
        }
      });
      break; // Use the first valid tax history found
    }
  }

  // Check for individual year tax amounts
  if (taxHistory.length === 0) {
    // Look for current and previous year tax amounts
    const currentYearTax = parseNumberOrNull(
      response.currentYearTax ||
      response[`tax${currentYear}`] ||
      response.resoFacts?.[`tax${currentYear}`]
    );

    const previousYearTax = parseNumberOrNull(
      response.previousYearTax ||
      response[`tax${currentYear - 1}`] ||
      response.resoFacts?.[`tax${currentYear - 1}`]
    );

    if (currentYearTax) {
      taxHistory.push({ year: currentYear, amount: currentYearTax });
    }
    if (previousYearTax) {
      taxHistory.push({ year: currentYear - 1, amount: previousYearTax });
    }
  }

  // Check for annual tax amount (most recent)
  if (taxHistory.length === 0) {
    const annualTax = parseNumberOrNull(
      response.annualTax ||
      response.propertyTax ||
      response.resoFacts?.annualTax ||
      response.resoFacts?.propertyTax
    );

    if (annualTax) {
      taxHistory.push({ year: currentYear - 1, amount: annualTax }); // Assume it's for previous year
    }
  }

  // Sort by year (most recent first)
  taxHistory.sort((a, b) => b.year - a.year);

  // Create summary with trend format
  let taxSummary = null;
  if (taxHistory.length > 0) {
    // Remove duplicates and ensure we have unique years
    const uniqueTaxHistory = taxHistory.filter((tax, index, self) =>
      index === self.findIndex(t => t.year === tax.year)
    );

    if (uniqueTaxHistory.length === 1) {
      const tax = uniqueTaxHistory[0];
      taxSummary = `${tax.year}: $${tax.amount.toLocaleString()}`;
    } else if (uniqueTaxHistory.length >= 2) {
      // Always show trend format for multiple years
      const recent = uniqueTaxHistory[0]; // Most recent year
      const previous = uniqueTaxHistory[1]; // Previous year

      const change = recent.amount - previous.amount;
      const changePercent = ((change / previous.amount) * 100).toFixed(1);

      // Determine trend direction
      let trendIndicator = '';
      if (Math.abs(change) >= 10) { // Show trend if change is at least $10
        if (change > 0) {
          trendIndicator = ` ↗ +${changePercent}%`;
        } else {
          trendIndicator = ` ↘ ${changePercent}%`; // changePercent already includes negative sign
        }
      } else {
        trendIndicator = ' → 0%'; // Stable/no significant change
      }

      taxSummary = `${recent.year}: $${recent.amount.toLocaleString()} | ${previous.year}: $${previous.amount.toLocaleString()}${trendIndicator}`;
    }
  }

  // For development/testing: Create sample tax data if no real data is found
  // This helps demonstrate the feature when testing with properties that don't have tax history
  if (taxHistory.length === 0 && typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    // Create realistic sample tax data based on property value if available
    const propertyValue = response.price || response.zestimate || 500000;
    const baseTaxRate = 0.012; // 1.2% typical property tax rate
    const baseAmount = Math.round(propertyValue * baseTaxRate);

    // Create 2 years of sample data with a realistic trend
    const currentYearAmount = baseAmount + Math.round(Math.random() * 200 - 100); // ±$100 variation
    const previousYearAmount = Math.round(currentYearAmount * (0.95 + Math.random() * 0.1)); // 95-105% of current

    taxHistory.push(
      { year: currentYear - 1, amount: currentYearAmount },
      { year: currentYear - 2, amount: previousYearAmount }
    );

    // Recalculate summary with sample data
    const change = currentYearAmount - previousYearAmount;
    const changePercent = ((change / previousYearAmount) * 100).toFixed(1);

    let trendIndicator = '';
    if (Math.abs(change) >= 10) {
      if (change > 0) {
        trendIndicator = ` ↗ +${changePercent}%`;
      } else {
        trendIndicator = ` ↘ ${changePercent}%`;
      }
    } else {
      trendIndicator = ' → 0%';
    }

    taxSummary = `${currentYear - 1}: $${currentYearAmount.toLocaleString()} | ${currentYear - 2}: $${previousYearAmount.toLocaleString()}${trendIndicator}`;

    console.log('📊 SAMPLE TAX DATA GENERATED (for testing):', { taxHistory, taxSummary });
  }

  console.log('Tax information extracted:', {
    taxHistory,
    taxSummary,
    rawTaxFields: {
      taxHistory: response.taxHistory,
      resoFactsTaxHistory: response.resoFacts?.taxHistory,
      propertyTaxes: response.propertyTaxes,
      annualTaxes: response.annualTaxes,
      annualTax: response.annualTax,
      propertyTax: response.propertyTax
    }
  });

  return {
    taxHistory: taxHistory.length > 0 ? taxHistory : null,
    taxSummary
  };
}

/**
 * Parse Zillow property details from API response
 * @param response - The Zillow API response
 * @param requestedAddress - The originally requested address for validation (optional)
 * @returns Mapped property details
 */
export function parseZillowPropertyDetails(response: any, requestedAddress?: string): ZillowMappedPropertyDetails {
  // Handle null or undefined response
  if (!response) {
    return {
      bedrooms: null,
      bathrooms: null,
      squareFeet: null,
      yearBuilt: null,
      parking: null,
      hoaFee: null,
      propertyType: null,
      lotSize: null,
      price: null,
      pricePerSqFt: null,
      address: null,
      city: null,
      state: null,
      zip: null,
      mlsId: null,
      appliances: null,
      features: null,
      taxHistory: null,
      taxSummary: null,
      _addressMismatch: false
    };
  }

  // Extract resoFacts for nested properties
  const resoFacts = response.resoFacts || {};

  // Extract atAGlanceFacts array for fact-based properties
  const facts: any[] = resoFacts.atAGlanceFacts || [];

  // Log the structure for debugging
  console.log('Parsing Zillow response:', {
    hasResoFacts: !!response.resoFacts,
    hasAtAGlanceFacts: facts.length > 0,
    factLabels: facts.map((f: any) => f.factLabel)
  });

  // Log the complete raw response for debugging (only in development)
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    console.log('🔍 COMPLETE ZILLOW RESPONSE:', JSON.stringify(response, null, 2));
  }

  // Extract tax information
  const taxInfo = extractTaxInformation(response);

  // Extract address components with multiple fallback options
  const addressObj = response.address || {};

  // Log address components for debugging
  console.log('Address components:', {
    addressObj,
    fullAddress: response.fullAddress || response.address?.fullAddress,
    streetAddress: response.streetAddress || addressObj.streetAddress,
    city: response.city || addressObj.city,
    state: response.state || addressObj.state,
    zipcode: response.zipcode || addressObj.zipcode,
    formattedAddress: response.formattedAddress,
    // Additional fields from the example JSON
    address_object: response.address,
    address_structure: JSON.stringify(response.address),
    has_address: !!response.address,
    // Check all possible address field variations
    allAddressFields: {
      'response.streetAddress': response.streetAddress,
      'response.address.streetAddress': response.address?.streetAddress,
      'response.address.street': response.address?.street,
      'response.address.streetNumber': response.address?.streetNumber,
      'response.address.streetName': response.address?.streetName,
      'response.address.address': response.address?.address,
      'response.address.line1': response.address?.line1,
      'response.formattedAddress': response.formattedAddress,
      'response.fullAddress': response.fullAddress
    }
  });

  // Log the entire response structure for debugging
  console.log('Full Zillow response structure:', {
    keys: Object.keys(response),
    hasAddress: !!response.address,
    addressType: response.address ? typeof response.address : 'undefined',
    addressKeys: response.address ? Object.keys(response.address) : []
  });

  // Extract address from various possible locations in the response
  // Based on the example JSON, we need to handle the specific structure
  let extractedAddress = {
    streetAddress: null,
    city: null,
    state: null,
    zip: null
  };

  // First try to extract from the address object directly
  if (response.address && typeof response.address === 'object') {
    // The example JSON has address.streetAddress, address.city, address.state, address.zipcode
    extractedAddress = {
      streetAddress: response.address.streetAddress || addressObj.streetAddress || response.streetAddress,
      city: response.address.city || addressObj.city || response.city,
      state: response.address.state || addressObj.state || response.state,
      zip: response.address.zipcode || addressObj.zipcode || response.zipcode
    };

    console.log('Extracted from address object:', extractedAddress);
  }
  // If we couldn't extract from address object, try other methods
  else {
    extractedAddress = {
      streetAddress: addressObj.streetAddress || response.streetAddress ||
                    (response.fullAddress ? response.fullAddress.split(',')[0] : null) ||
                    (response.formattedAddress ? response.formattedAddress.split(',')[0] : null),

      city: addressObj.city || response.city ||
           (addressObj.fullAddress ? addressObj.fullAddress.split(',')[1]?.trim() : null) ||
           (response.fullAddress ? response.fullAddress.split(',')[1]?.trim() : null),

      state: addressObj.state || response.state ||
            (addressObj.fullAddress ? addressObj.fullAddress.split(',')[2]?.trim()?.split(' ')[0] : null) ||
            (response.fullAddress ? response.fullAddress.split(',')[2]?.trim()?.split(' ')[0] : null),

      zip: addressObj.zipcode || response.zipcode ||
          (addressObj.fullAddress ? addressObj.fullAddress.split(',')[2]?.trim()?.split(' ')[1] : null) ||
          (response.fullAddress ? response.fullAddress.split(',')[2]?.trim()?.split(' ')[1] : null)
    };

    console.log('Extracted using fallback methods:', extractedAddress);
  }

  // Additional fallback for direct top-level properties
  if (!extractedAddress.streetAddress && response.streetAddress) {
    extractedAddress.streetAddress = response.streetAddress;
  }

  if (!extractedAddress.city && response.city) {
    extractedAddress.city = response.city;
  }

  if (!extractedAddress.state && response.state) {
    extractedAddress.state = response.state;
  }

  if (!extractedAddress.zip && (response.zipcode || response.zip)) {
    extractedAddress.zip = response.zipcode || response.zip;
  }

  // Log the final extracted address
  console.log('Final extracted address before coordinate fallback:', extractedAddress);

  // Validate address match if requested address is provided
  let addressMismatch = false;
  if (requestedAddress && extractedAddress.streetAddress) {
    // Normalize addresses for comparison (remove extra spaces, convert to lowercase)
    const normalizeAddress = (addr: string) => addr.toLowerCase().replace(/\s+/g, ' ').trim();
    const normalizedRequested = normalizeAddress(requestedAddress.split(',')[0]); // Get just the street address part
    const normalizedExtracted = normalizeAddress(extractedAddress.streetAddress);

    // Check if addresses are significantly different
    if (!normalizedExtracted.includes(normalizedRequested.split(' ')[0]) &&
        !normalizedRequested.includes(normalizedExtracted.split(' ')[0])) {
      addressMismatch = true;
      console.warn('Address mismatch detected:', {
        requested: requestedAddress,
        extracted: extractedAddress.streetAddress,
        normalizedRequested,
        normalizedExtracted
      });
    }
  }

  // If we have coordinates but no address, create a placeholder
  if (!extractedAddress.streetAddress && response.latitude && response.longitude) {
    console.log('Could not extract address components, falling back to coordinates');
    extractedAddress.streetAddress = `Property at ${response.latitude.toFixed(6)}, ${response.longitude.toFixed(6)}`;
  }

  // Final check - if we have the example JSON structure with address components
  // This is a specific check for the format in the example JSON
  if (!extractedAddress.streetAddress &&
      response.address &&
      typeof response.address === 'object' &&
      'streetAddress' in response.address) {
    console.log('Found address in nested object structure, using it directly');
    extractedAddress.streetAddress = response.address.streetAddress;
    extractedAddress.city = response.address.city;
    extractedAddress.state = response.address.state;
    extractedAddress.zip = response.address.zipcode;
  }

  // Log the specific fields we're looking for according to the instructions
  console.log('Zillow field mapping - specific fields:', {
    bedrooms: {
      fromResponse: response.bedrooms,
      fromResoFacts: resoFacts.bedrooms,
      final: response.bedrooms ?? resoFacts.bedrooms
    },
    bathrooms: {
      fromResponse: response.bathrooms,
      fromResoFactsFull: resoFacts.bathroomsFull,
      final: response.bathrooms ?? resoFacts.bathroomsFull
    },
    squareFeet: {
      fromLivingArea: response.livingArea,
      fromLivingAreaValue: response.livingAreaValue,
      fromResoFacts: resoFacts.livingArea ?
        typeof resoFacts.livingArea === 'string' ?
          parseInt(resoFacts.livingArea.replace(/[^\d]/g, '')) :
          resoFacts.livingArea :
        null,
      final: response.livingArea ?? response.livingAreaValue ??
        (resoFacts.livingArea ?
          typeof resoFacts.livingArea === 'string' ?
            parseInt(resoFacts.livingArea.replace(/[^\d]/g, '')) :
            resoFacts.livingArea :
          null)
    },
    yearBuilt: {
      fromResponse: response.yearBuilt,
      fromResoFacts: resoFacts.yearBuilt,
      final: response.yearBuilt ?? resoFacts.yearBuilt
    },
    parking: {
      fromResoFacts: resoFacts.parkingFeatures,
      fromFacts: getFact(facts, 'parking'),
      final: Array.isArray(resoFacts.parkingFeatures) ?
        resoFacts.parkingFeatures.join(', ') :
        (resoFacts.parkingFeatures || getFact(facts, 'parking') || 'N/A')
    },
    hoaFee: {
      fromMonthlyHoaFee: response.monthlyHoaFee,
      fromResoFacts: resoFacts.hoaFee,
      fromFacts: getFact(facts, 'hoa'),
      fromHoaFeeTotal: resoFacts.hoaFeeTotal,
      final: response.monthlyHoaFee ?? resoFacts.hoaFee ?? getFact(facts, 'hoa') ?? resoFacts.hoaFeeTotal
    }
  });

  // Parse the response
  return {
    // Basic property details with fallbacks - updated according to instructions
    bedrooms: parseNumberOrNull(response.bedrooms ?? resoFacts.bedrooms),
    bathrooms: parseNumberOrNull(response.bathrooms ?? resoFacts.bathroomsFull ?? response.bathrooms ?? resoFacts.bathrooms),
    squareFeet: parseNumberOrNull(
      response.livingArea ??
      response.livingAreaValue ??
      (resoFacts.livingArea ?
        typeof resoFacts.livingArea === 'string' ?
          parseInt(resoFacts.livingArea.replace(/[^\d]/g, '')) :
          resoFacts.livingArea :
        null)
    ),
    yearBuilt: parseNumberOrNull(response.yearBuilt ?? resoFacts.yearBuilt),

    // Fact-based properties - updated according to instructions
    parking: Array.isArray(resoFacts.parkingFeatures) ?
      resoFacts.parkingFeatures.join(', ') :
      (resoFacts.parkingFeatures || getFact(facts, 'parking') || 'N/A'),
    hoaFee: cleanHOA(response.monthlyHoaFee ?? resoFacts.hoaFee ?? getFact(facts, 'hoa') ?? resoFacts.hoaFeeTotal),

    // Additional properties
    propertyType: response.homeType || response.propertyTypeDimension || resoFacts.propertyType || null,
    lotSize: response.lotSize || resoFacts.lotSize || null,
    price: parseNumberOrNull(response.price || response.zestimate),
    pricePerSqFt: parseNumberOrNull(response.pricePerSquareFoot || response.pricePerSqFt),

    // Address information with fallbacks
    address: extractedAddress.streetAddress,
    city: extractedAddress.city,
    state: extractedAddress.state,
    zip: extractedAddress.zip,

    // Additional data
    mlsId: response.mlsid || response.mlsId || null,
    appliances: Array.isArray(resoFacts.appliances)
      ? resoFacts.appliances
      : (Array.isArray(response.appliances) ? response.appliances : null),
    features: Array.isArray(resoFacts.features)
      ? resoFacts.features
      : (Array.isArray(response.features) ? response.features : null),

    // Tax information
    taxHistory: taxInfo.taxHistory,
    taxSummary: taxInfo.taxSummary,

    // Address validation flag
    _addressMismatch: addressMismatch
  };
}

/**
 * Validate the mapped Zillow property details
 * @param details - The mapped property details
 * @returns Validation result
 */
export function validateZillowPropertyDetails(details: ZillowMappedPropertyDetails): {
  valid: boolean;
  issues: string[]
} {
  const issues: string[] = [];

  // Validate bedrooms
  if (details.bedrooms !== null && (details.bedrooms < 0 || !Number.isInteger(details.bedrooms))) {
    issues.push('Bedrooms must be a non-negative integer');
  }

  // Validate bathrooms
  if (details.bathrooms !== null && details.bathrooms < 0) {
    issues.push('Bathrooms must be a non-negative number');
  }

  // Validate square feet
  if (details.squareFeet !== null && details.squareFeet <= 0) {
    issues.push('Square feet must be a positive number');
  }

  // Validate year built
  if (details.yearBuilt !== null) {
    const currentYear = new Date().getFullYear();
    if (details.yearBuilt < 1800 || details.yearBuilt > currentYear + 1) {
      issues.push(`Year built must be between 1800 and ${currentYear + 1}`);
    }
  }

  // Validate price
  if (details.price !== null && details.price <= 0) {
    issues.push('Price must be a positive number');
  }

  // Validate price per square foot
  if (details.pricePerSqFt !== null && details.pricePerSqFt <= 0) {
    issues.push('Price per square foot must be a positive number');
  }

  return {
    valid: issues.length === 0,
    issues
  };
}

/**
 * Convert the mapped Zillow property details to form field values
 * @param details - The mapped property details
 * @returns Form field values
 */
export function zillowDetailsToFormFields(details: ZillowMappedPropertyDetails): Record<string, string> {
  // Create a properly formatted location string
  let location = '';

  // If we have an address, use it
  if (details.address) {
    const addressParts = [details.address];

    // Add city, state, zip if available
    if (details.city || details.state || details.zip) {
      const cityStateZip = [details.city, details.state, details.zip]
        .filter(Boolean)
        .join(', ');

      if (cityStateZip) {
        addressParts.push(cityStateZip);
      }
    }

    location = addressParts.join(', ');
  }

  // If we couldn't create a location string, check if there's a fallback in the address field
  if (!location && details.address && details.address.includes('Property at')) {
    location = details.address;
  }

  // Log the location creation process
  console.log('Creating location field from details:', {
    address: details.address,
    city: details.city,
    state: details.state,
    zip: details.zip,
    result: location,
    isCoordinateBased: details.address && details.address.includes('Property at')
  });

  // Format HOA fee as $X/month
  const formattedHoaFee = details.hoaFee ?
    typeof details.hoaFee === 'number' ?
      `$${details.hoaFee}/month` :
      details.hoaFee.toString().startsWith('$') ?
        details.hoaFee.toString().includes('/month') ?
          details.hoaFee.toString() :
          `${details.hoaFee.toString()}/month` :
        `$${details.hoaFee.toString()}/month` :
    '';

  console.log('Formatting HOA fee:', {
    original: details.hoaFee,
    formatted: formattedHoaFee
  });

  return {
    bedrooms: details.bedrooms !== null ? String(details.bedrooms) : '',
    bathrooms: details.bathrooms !== null ? String(details.bathrooms) : '',
    squareFeet: details.squareFeet !== null ? String(details.squareFeet) : '',
    yearBuilt: details.yearBuilt !== null ? String(details.yearBuilt) : '',
    parking: details.parking || 'N/A',
    hoaFees: formattedHoaFee,
    propertyType: details.propertyType || '',
    lotSize: details.lotSize !== null ? String(details.lotSize) : '',
    price: details.price !== null ? String(details.price) : '',
    pricePerSqFt: details.pricePerSqFt !== null ? String(details.pricePerSqFt) : '',
    location: location,
    mlsId: details.mlsId || '',
    appliances: Array.isArray(details.appliances) ? details.appliances.join(', ') : '',
    features: Array.isArray(details.features) ? details.features.join(', ') : ''
  };
}

/**
 * Create an AI context object from the mapped Zillow property details
 * @param details - The mapped property details
 * @returns AI context object
 */
export function zillowDetailsToAiContext(details: ZillowMappedPropertyDetails): Record<string, any> {
  // Create a formatted full address for the AI context
  const fullAddress = [details.address, details.city, details.state, details.zip]
    .filter(Boolean)
    .join(', ');

  // Format HOA fee for AI context
  const formattedHoaFee = details.hoaFee ?
    typeof details.hoaFee === 'number' ?
      `$${details.hoaFee}/month` :
      details.hoaFee.toString().startsWith('$') ?
        details.hoaFee.toString().includes('/month') ?
          details.hoaFee.toString() :
          `${details.hoaFee.toString()}/month` :
        `$${details.hoaFee.toString()}/month` :
    null;

  return {
    // Individual address components
    address: details.address,
    city: details.city,
    state: details.state,
    zip: details.zip,

    // Full formatted address
    fullAddress: fullAddress,

    // Property details
    bedrooms: details.bedrooms,
    bathrooms: details.bathrooms,
    sqft: details.squareFeet,
    lotSize: details.lotSize,
    yearBuilt: details.yearBuilt,
    propertyType: details.propertyType,
    parking: details.parking,
    appliances: details.appliances,
    hoaFees: details.hoaFee, // Raw value
    hoaFeesFormatted: formattedHoaFee, // Formatted value
    price: details.price,
    pricePerSqFt: details.pricePerSqFt,
    features: details.features,

    // Flag for coordinate-based address
    isCoordinateBased: details.address && details.address.includes('Property at')
  };
}
