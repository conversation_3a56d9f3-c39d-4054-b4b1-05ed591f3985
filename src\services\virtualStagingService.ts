import { DesignStyle } from '../pages/tools/VirtualStagingTool';

// Define the API base URL
// In development, use the local API endpoint
// In production, use the deployed API endpoint
const API_BASE_URL = import.meta.env.DEV
  ? 'http://localhost:5000/api'
  : 'https://digital-realtor-api.onrender.com/api';

// Cache for storing generated suggestions to avoid redundant API calls
interface CacheEntry {
  timestamp: number;
  data: any;
}

const suggestionCache = new Map<string, CacheEntry>();
const imageCache = new Map<string, CacheEntry>();
const CACHE_TTL = 30 * 60 * 1000; // 30 minutes

// Helper function to create a cache key
const createCacheKey = (image: string, style: DesignStyle): string => {
  // Use a hash of the image (first 100 chars) + style as the cache key
  return `${image.substring(0, 100)}_${style}`;
};

/**
 * Generate staging suggestions for a room image
 * @param image Base64 encoded image
 * @param style Selected design style
 * @returns Object containing text suggestions and optionally an image URL
 */
export const generateStagingSuggestions = async (
  image: string,
  style: DesignStyle
): Promise<{ textSuggestions: string }> => {
  try {
    // Check cache first
    const cacheKey = createCacheKey(image, style);
    const cachedEntry = suggestionCache.get(cacheKey);

    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
      console.log('Using cached staging suggestions');
      return cachedEntry.data;
    }

    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to generate staging suggestions', 'color: #00b9ff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/virtual-staging/suggestions`);
      console.log('Style:', style);
      console.log('Image length:', image.length);
    }

    // Make the API request
    const response = await fetch(`${API_BASE_URL}/virtual-staging/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image,
        style
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Cache the result
    suggestionCache.set(cacheKey, {
      timestamp: Date.now(),
      data: { textSuggestions: data.suggestions }
    });

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received staging suggestions', 'color: #00b9ff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Suggestions length:', data.suggestions.length);
    }

    return { textSuggestions: data.suggestions };
  } catch (error: any) {
    console.error('Error generating staging suggestions:', error);

    // Check if it's a network error (like CORS or server not found)
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      console.error('Network error - server might be down or CORS issue');
      throw new Error('Network error: Unable to connect to the server. Please check your connection or try again later.');
    }

    // Check if it's a 404 error (endpoint not found)
    if (error.message && error.message.includes('404')) {
      console.error('API endpoint not found (404)');
      throw new Error('The staging suggestions service is not available. Please try again later or contact support.');
    }

    // Default error message
    throw new Error(error.message || 'Failed to generate staging suggestions. Please try again.');
  }
};

/**
 * Generate a staged image based on the original image and suggestions
 * @param image Base64 encoded image
 * @param style Selected design style
 * @param suggestions Text suggestions to use for generating the image
 * @returns URL of the generated image
 */
export const generateStagingImage = async (
  image: string,
  style: DesignStyle,
  suggestions: string
): Promise<string> => {
  try {
    // Check cache first
    const cacheKey = createCacheKey(image, style);
    const cachedEntry = imageCache.get(cacheKey);

    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
      console.log('Using cached staging image');
      return cachedEntry.data;
    }

    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to generate staging image', 'color: #00b9ff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/virtual-staging/image`);
      console.log('Style:', style);
      console.log('Image length:', image.length);
      console.log('Suggestions length:', suggestions.length);
    }

    // Create a prompt for DALL-E based on the suggestions and style
    const prompt = `Generate a ${style.toLowerCase()} style interior design for this room. ${
      suggestions.split('\n').slice(0, 5).join(' ')
    }`;

    // Make the API request
    const response = await fetch(`${API_BASE_URL}/virtual-staging/image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        image
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Cache the result
    imageCache.set(cacheKey, {
      timestamp: Date.now(),
      data: data.imageUrl
    });

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received staging image', 'color: #00b9ff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Image URL:', data.imageUrl);
    }

    return data.imageUrl;
  } catch (error: any) {
    console.error('Error generating staging image:', error);

    // Check if it's a network error (like CORS or server not found)
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      console.error('Network error - server might be down or CORS issue');
      throw new Error('Network error: Unable to connect to the server. Please check your connection or try again later.');
    }

    // Check if it's a 404 error (endpoint not found)
    if (error.message && error.message.includes('404')) {
      console.error('API endpoint not found (404)');
      throw new Error('The image generation service is not available. Please try again later or contact support.');
    }

    // Default error message
    throw new Error(error.message || 'Failed to generate staging image. Please try again.');
  }
};
