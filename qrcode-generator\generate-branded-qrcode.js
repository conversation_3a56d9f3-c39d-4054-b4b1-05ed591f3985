const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

// Configuration
const appUrl = 'https://aidigitalrealtor.com/'; // Correct production URL
const outputDir = path.join(__dirname, 'output');
const pngFilename = 'digital-realtor-branded-qrcode.png';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Options for QR code generation
const qrOptions = {
  errorCorrectionLevel: 'H', // High error correction capability
  type: 'image/png',
  quality: 0.92,
  margin: 2,
  width: 500,
  color: {
    dark: '#0066cc', // Blue dots
    light: '#ffffff' // White background
  }
};

// Generate QR code
QRCode.toFile(
  path.join(outputDir, pngFilename),
  appUrl,
  qrOptions,
  function (err) {
    if (err) {
      console.error('Error generating branded QR code:', err);
    } else {
      console.log(`Branded QR code generated successfully: ${path.join(outputDir, pngFilename)}`);
      console.log('\nNote: To add a logo to the center of the QR code, you would need to:');
      console.log('1. Use an image manipulation library like Jimp or Sharp');
      console.log('2. Load the generated QR code');
      console.log('3. Load your logo image');
      console.log('4. Resize the logo to about 20-30% of the QR code size');
      console.log('5. Composite the logo onto the center of the QR code');
      console.log('6. Save the result');
      console.log('\nThis requires additional packages and more complex code.');
      console.log('For now, you can manually add your logo using an image editor.');
    }
  }
);

// Also generate a terminal QR code for quick testing
QRCode.toString(appUrl, { type: 'terminal' }, function (err, terminalCode) {
  if (err) {
    console.error('Error generating terminal QR code:', err);
  } else {
    console.log('\nQR Code for quick testing (scan with your phone camera):');
    console.log(terminalCode);
  }
});
