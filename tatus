[33mcommit a5552e4f0f8b328e7f51e924b62d57cf3582d58e[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m
Author: <PERSON> <<EMAIL>>
Date:   Sun May 4 19:15:22 2025 -0500

    Fix address component extraction with fallback to coordinates

[33mcommit 5838953353e1f60aebdfbabe3da67271e7ed76d5[m
Author: <PERSON> <<EMAIL>>
Date:   Sun May 4 18:57:41 2025 -0500

    Implement Zillow API to UI field mapping with HOA fees support

[33mcommit bf37a44843c7bbf3176b15429ce2323244a74796[m
Author: <PERSON> <<EMAIL>>
Date:   Sun May 4 15:12:45 2025 -0500

    Fix oversized buttons and remove duplicate Show Extracted Fields button
