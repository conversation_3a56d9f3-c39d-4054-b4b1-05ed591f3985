// Production build script for Netlify
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('=== Digital Realtor Netlify Production Build ====\n');

try {
  // Step 1: Clean previous build
  console.log('Cleaning previous build...');
  if (fs.existsSync(path.join(__dirname, 'prod-build'))) {
    fs.rmSync(path.join(__dirname, 'prod-build'), { recursive: true, force: true });
  }
  console.log('✓ Previous build cleaned\n');

  // Step 2: Install ALL dependencies including dev dependencies
  console.log('Installing ALL dependencies including dev dependencies...');
  execSync('npm install --include=dev', { stdio: 'inherit' });
  console.log('✓ All dependencies installed\n');

  // Step 3: Install Vite explicitly
  console.log('Installing Vite explicitly...');
  execSync('npm install --no-save vite@6.2.0 @vitejs/plugin-react typescript', { stdio: 'inherit' });
  console.log('✓ Vite installed\n');

  // Step 4: Build the application
  console.log('Building application for Netlify...');
  execSync('npx vite build --outDir prod-build', { stdio: 'inherit' });
  console.log('✓ Build completed\n');

  // Step 5: Create Netlify configuration files
  console.log('Creating Netlify configuration files...');

  // Create _redirects file for client-side routing
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', '_redirects'),
    '/* /index.html 200'
  );

  // Create a robots.txt file
  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'robots.txt'),
    'User-agent: *\nAllow: /'
  );

  console.log('✓ Netlify configuration files created\n');

  // Step 6: Copy environment variables
  console.log('Copying environment variables...');
  if (fs.existsSync(path.join(__dirname, '.env.production'))) {
    fs.copyFileSync(
      path.join(__dirname, '.env.production'),
      path.join(__dirname, 'prod-build', '.env.production.local')
    );
    console.log('✓ Environment variables copied');
  } else {
    console.log('Warning: .env.production file not found');
  }

  // Create a runtime configuration file
  const runtimeConfig = {
    BUILD_TIME: new Date().toISOString(),
    VERSION: '1.0.0',
    CLARITY_ID: 'r7yjq54tfv'
  };

  fs.writeFileSync(
    path.join(__dirname, 'prod-build', 'config.json'),
    JSON.stringify(runtimeConfig, null, 2)
  );

  console.log('✓ Runtime configuration created\n');

  // Step 7: Copy public files
  console.log('Copying public files...');
  if (fs.existsSync(path.join(__dirname, 'public'))) {
    // Create the destination directory if it doesn't exist
    if (!fs.existsSync(path.join(__dirname, 'prod-build', 'public'))) {
      fs.mkdirSync(path.join(__dirname, 'prod-build', 'public'), { recursive: true });
    }

    // Copy all files from public to prod-build
    const copyDir = (src, dest) => {
      const entries = fs.readdirSync(src, { withFileTypes: true });
      for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        if (entry.isDirectory()) {
          fs.mkdirSync(destPath, { recursive: true });
          copyDir(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      }
    };

    copyDir(path.join(__dirname, 'public'), path.join(__dirname, 'prod-build'));
    console.log('✓ Public files copied');
  } else {
    console.log('Warning: public directory not found');
  }

  console.log('\nNetlify build completed successfully!');
  console.log('The build is available in the prod-build directory.');

} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}
