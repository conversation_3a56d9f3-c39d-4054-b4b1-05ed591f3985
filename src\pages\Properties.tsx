import { useState } from 'react';
import { FaFilter, FaSearch } from 'react-icons/fa';

const Properties = () => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-dark-800 mb-8">
          Properties
        </h1>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters - Mobile Toggle */}
          <div className="lg:hidden mb-4">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="w-full bg-white p-3 rounded-lg shadow-md flex items-center justify-center gap-2"
            >
              <FaFilter />
              <span>{isFilterOpen ? 'Hide Filters' : 'Show Filters'}</span>
            </button>
          </div>

          {/* Filters Sidebar */}
          <div
            className={`${
              isFilterOpen ? 'block' : 'hidden'
            } lg:block lg:w-1/4 bg-white p-6 rounded-lg shadow-md h-fit`}
          >
            <h2 className="text-xl font-bold text-dark-800 mb-4">Filters</h2>

            {/* Price Range */}
            <div className="mb-6">
              <label className="block text-dark-700 font-medium mb-2">Price Range</label>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Min"
                />
                <span>to</span>
                <input
                  type="number"
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Max"
                />
              </div>
            </div>

            {/* Bedrooms */}
            <div className="mb-6">
              <label className="block text-dark-700 font-medium mb-2">Bedrooms</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Any</option>
                <option value="1">1+</option>
                <option value="2">2+</option>
                <option value="3">3+</option>
                <option value="4">4+</option>
                <option value="5">5+</option>
              </select>
            </div>

            {/* Bathrooms */}
            <div className="mb-6">
              <label className="block text-dark-700 font-medium mb-2">Bathrooms</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Any</option>
                <option value="1">1+</option>
                <option value="2">2+</option>
                <option value="3">3+</option>
                <option value="4">4+</option>
              </select>
            </div>

            {/* Property Type */}
            <div className="mb-6">
              <label className="block text-dark-700 font-medium mb-2">Property Type</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Any</option>
                <option value="Single Family">Single Family</option>
                <option value="Condo">Condo</option>
                <option value="Townhouse">Townhouse</option>
                <option value="Multi-Family">Multi-Family</option>
                <option value="Land">Land</option>
              </select>
            </div>

            {/* Reset Filters */}
            <button
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
            >
              Reset Filters
            </button>
          </div>

          {/* Properties List */}
          <div className="lg:w-3/4">
            {/* Search Bar */}
            <div className="bg-white p-4 rounded-lg shadow-md mb-6">
              <div className="flex">
                <input
                  type="text"
                  placeholder="Search by location, property type, or keywords..."
                  className="flex-grow p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <button
                  className="bg-primary-600 text-white p-2 rounded-r-md hover:bg-primary-700"
                >
                  <FaSearch />
                </button>
              </div>
            </div>

            {/* Properties Grid - Placeholder */}
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <h2 className="text-2xl font-bold text-dark-800 mb-4">Coming Soon</h2>
              <p className="text-dark-500 max-w-lg mx-auto mb-6">
                Our property listings are currently being updated. Check back soon to browse our extensive collection of properties.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Properties;
