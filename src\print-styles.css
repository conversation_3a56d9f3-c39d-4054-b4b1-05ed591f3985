@media print {
  /* Hide everything except the report */
  body * {
    visibility: hidden;
  }
  
  /* Show only the report content */
  #report-content, #report-content * {
    visibility: visible;
  }
  
  /* Position the report at the top of the page */
  #report-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  
  /* Hide buttons and other UI elements */
  button, .no-print {
    display: none !important;
  }
  
  /* Ensure proper page breaks */
  .page-break {
    page-break-after: always;
  }
  
  /* Improve table appearance */
  table {
    border-collapse: collapse;
    width: 100%;
  }
  
  th, td {
    border: 1px solid #ddd;
    padding: 8px;
  }
  
  /* Ensure background colors print */
  .bg-green-50 {
    background-color: #f0fdf4 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .bg-yellow-50 {
    background-color: #fefce8 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .bg-red-50 {
    background-color: #fef2f2 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  /* Text colors */
  .text-green-500, .text-green-600, .text-green-700 {
    color: #22c55e !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .text-yellow-500, .text-yellow-600, .text-yellow-700 {
    color: #eab308 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .text-red-500, .text-red-600, .text-red-700 {
    color: #ef4444 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
