/**
 * Utility functions for validating Zillow API responses
 */

/**
 * Validates a Zillow API response to ensure it contains the required fields
 *
 * @param data - The data returned from the Zillow API
 * @returns An object indicating whether the data is valid, with the reason if invalid
 */
export function validateZillowResponse(data: any) {
  if (!data || typeof data !== 'object') {
    return { valid: false, reason: 'Response is not an object.' };
  }

  // In case data is wrapped inside a "property" key
  const property = data.property || data;

  // Log the structure of the data for debugging
  console.log('Zillow data structure:', {
    keys: Object.keys(property),
    dataType: typeof property,
    isArray: Array.isArray(property)
  });

  // If it's an array (like in propertyByCoordinates), use the first item
  // If it's a property object (from property-by-address endpoint), use it directly
  const propertyData = Array.isArray(property) ? property[0] : property;

  // If we still don't have valid data after unwrapping
  if (!propertyData || typeof propertyData !== 'object') {
    return {
      valid: false,
      reason: 'Could not find valid property data in response.',
      data: property // Return the original data for debugging
    };
  }

  // Define required fields - make this configurable based on use case
  const requiredFields = ['bedrooms', 'bathrooms'];
  const recommendedFields = ['livingArea', 'yearBuilt', 'homeType', 'propertyType'];

  // Check for required fields
  const missingRequiredFields = requiredFields.filter((key) => {
    // Check if the field exists and has a value
    return !(key in propertyData) || propertyData[key] === null || propertyData[key] === undefined;
  });

  // Check for recommended fields
  const missingRecommendedFields = recommendedFields.filter((key) => {
    // For recommended fields, we'll check pairs of alternative field names
    if (key === 'livingArea' && 'livingAreaSqFt' in propertyData) return false;
    if (key === 'homeType' && 'propertyType' in propertyData) return false;
    if (key === 'propertyType' && 'homeType' in propertyData) return false;

    return !(key in propertyData) || propertyData[key] === null || propertyData[key] === undefined;
  });

  // If missing required fields, return invalid
  if (missingRequiredFields.length > 0) {
    return {
      valid: false,
      reason: `Missing required fields: ${missingRequiredFields.join(', ')}`,
      data: propertyData,
      missingFields: missingRequiredFields
    };
  }

  // If missing recommended fields, return valid but with warnings
  if (missingRecommendedFields.length > 0) {
    return {
      valid: true,
      warning: `Missing recommended fields: ${missingRecommendedFields.join(', ')}`,
      data: propertyData,
      missingFields: missingRecommendedFields
    };
  }

  // All good!
  return {
    valid: true,
    data: propertyData
  };
}

/**
 * Extracts the most relevant property data from a Zillow API response
 *
 * @param data - The validated data from validateZillowResponse
 * @returns A cleaned and normalized property data object
 */
export function normalizeZillowData(data: any) {
  // Start with the validated data
  const property = data.data || data;

  // Extract resoFacts for nested properties
  const reso = property.resoFacts || {};

  // Log the structure for debugging
  console.log('Normalizing Zillow data:', {
    topLevelKeys: Object.keys(property),
    hasResoFacts: !!property.resoFacts,
    resoFactsKeys: reso ? Object.keys(reso) : 'none',
    address: property.address,
    streetAddress: property.streetAddress
  });

  // Create a normalized object with consistent field names
  return {
    // Basic property details
    bedrooms: property.bedrooms || reso.bedrooms,
    bathrooms: property.bathrooms || reso.bathrooms,
    livingArea: property.livingArea || property.livingAreaSqFt || reso.livingArea,
    yearBuilt: property.yearBuilt || reso.yearBuilt,
    propertyType: property.homeType || property.propertyTypeDimension || reso.propertyType,

    // Additional details
    lotSize: reso.lotSize || property.lotSize || property.lotSizeSqFt,
    price: property.price || property.zestimate,
    pricePerSqFt: property.pricePerSquareFoot || property.pricePerSqFt,
    mlsId: property.mlsid || property.mlsId || property.mlsNumber,

    // Address information
    address: property.address?.streetAddress || property.streetAddress,
    city: property.address?.city || property.city,
    state: property.address?.state || property.state,
    zip: property.address?.zipcode || property.zipcode,

    // Financial information
    hoaFees: reso.associationFee || property.monthlyHoaFee,

    // Features
    parking: Array.isArray(reso.parkingFeatures)
      ? reso.parkingFeatures
      : (Array.isArray(property.parking)
          ? property.parking
          : (property.parkingFeatures ? [property.parkingFeatures] : [])),

    appliances: Array.isArray(reso.appliances)
      ? reso.appliances
      : (Array.isArray(property.appliances)
          ? property.appliances
          : (property.homeFeatures ? [property.homeFeatures] : [])),

    // Additional data
    subdivision: reso.subdivisionName,
    taxHistory: property.taxHistory || {},
    priceHistory: property.priceHistory || [],

    // Images
    images: [property.imgSrc, ...(property.imageGallery || [])].filter(Boolean),

    // Additional details for AI context
    description: property.description,
    features: Array.isArray(reso.features)
      ? reso.features
      : (property.features ? [property.features] : []),

    // Original data for reference
    _original: property,

    // Flag for fallback data
    _fallback: property._fallback || false,

    // Include resoFacts for reference
    _resoFacts: reso
  };
}
