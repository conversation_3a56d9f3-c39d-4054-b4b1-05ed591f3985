import api from './api';

// Types
export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface Review {
  _id?: string;
  name: string;
  rating: number;
  comment: string;
  user: string;
  createdAt?: string;
}

export interface Property {
  _id?: string;
  user?: string;
  title: string;
  address: Address;
  description: string;
  price: number;
  images: string[];
  propertyType: 'House' | 'Apartment' | 'Condo' | 'Townhouse' | 'Land' | 'Commercial';
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  lotSize?: number;
  yearBuilt?: number;
  features: string[];
  reviews?: Review[];
  rating?: number;
  numReviews?: number;
  isFeatured?: boolean;
  status: 'For Sale' | 'For Rent' | 'Sold' | 'Pending';
  createdAt?: string;
  updatedAt?: string;
}

export interface PropertyListResponse {
  properties: Property[];
  page: number;
  pages: number;
}

export interface ReviewSubmit {
  rating: number;
  comment: string;
}

// Property API calls
const propertyService = {
  // Get all properties with pagination and search
  getProperties: async (
    keyword = '',
    pageNumber = 1
  ): Promise<PropertyListResponse> => {
    const { data } = await api.get(
      `/properties?keyword=${keyword}&pageNumber=${pageNumber}`
    );
    return data;
  },

  // Get property by ID
  getPropertyById: async (id: string): Promise<Property> => {
    const { data } = await api.get(`/properties/${id}`);
    return data;
  },

  // Create property (realtor only)
  createProperty: async (propertyData: Omit<Property, '_id'>): Promise<Property> => {
    const { data } = await api.post('/properties', propertyData);
    return data;
  },

  // Update property (realtor only)
  updateProperty: async (
    id: string,
    propertyData: Partial<Property>
  ): Promise<Property> => {
    const { data } = await api.put(`/properties/${id}`, propertyData);
    return data;
  },

  // Delete property (admin/realtor only)
  deleteProperty: async (id: string): Promise<{ message: string }> => {
    const { data } = await api.delete(`/properties/${id}`);
    return data;
  },

  // Create property review
  createPropertyReview: async (
    id: string,
    review: ReviewSubmit
  ): Promise<{ message: string }> => {
    const { data } = await api.post(`/properties/${id}/reviews`, review);
    return data;
  },

  // Get top rated properties
  getTopProperties: async (): Promise<Property[]> => {
    const { data } = await api.get('/properties/top');
    return data;
  },

  // Get featured properties
  getFeaturedProperties: async (): Promise<Property[]> => {
    const { data } = await api.get('/properties/featured');
    return data;
  },
};

export default propertyService;
