import express from 'express';
const router = express.Router();
import {
  authUser,
  registerUser,
  getUserProfile,
  updateUserProfile,
  getUsers,
  deleteUser,
  getUserById,
  updateUser,
  verifyEmail,
  sendVerificationEmail,
  requestVerificationCode,
  checkVerificationCode,
  clearAllUsers,
} from '../controllers/userController.js';
import { protect, admin } from '../middleware/authMiddleware.js';

router.route('/').post(registerUser).get(protect, admin, getUsers);
router.post('/login', authUser);
router.get('/verify/:token', verifyEmail);
router.post('/verify/send', sendVerificationEmail);
router.post('/verify/request-code', requestVerificationCode);
router.post('/verify/check-code', checkVerificationCode);

// Development only routes
router.delete('/clear-all', clearAllUsers);
router.get('/clear-all-test', clearAllUsers); // For testing with browser
router
  .route('/profile')
  .get(protect, getUserProfile)
  .put(protect, updateUserProfile);
router
  .route('/:id')
  .delete(protect, admin, deleteUser)
  .get(protect, admin, getUserById)
  .put(protect, admin, updateUser);

export default router;
