// Commute Analysis Service
// This service handles fetching commute data from the Google Distance Matrix API

// Constants
const PROXY_ENDPOINT = import.meta.env.VITE_BACKEND_URL ? `${import.meta.env.VITE_BACKEND_URL}/api/proxy/distancematrix` : 'http://localhost:3001/api/proxy/distancematrix';
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours

// Default destinations for commute analysis
const DEFAULT_DESTINATIONS = {
  'Downtown Chicago': 'Downtown Chicago, IL 60601',
  'O\'Hare International Airport': 'O\'Hare International Airport, Chicago, IL 60666',
  'Chicago Loop': 'Chicago Loop, Chicago, IL 60601'
};

// Simple in-memory cache
const commuteCache = new Map<string, { timestamp: number; data: any }>();

// Types
export interface CommuteData {
  destination: string;
  distance: string;
  duration: string;
  mode: string;
}

export interface CommuteAnalysisResponse {
  commute_analysis: CommuteData[] | string;
}

/**
 * Fetches commute data for a given address
 * @param address Full address string
 * @returns Promise with commute data or error message
 */
export const fetchCommuteAnalysis = async (address: string): Promise<CommuteAnalysisResponse> => {
  try {
    // Validate input
    if (!address || address.trim() === '') {
      console.warn(`Invalid address: "${address}"`);
      return { commute_analysis: 'Commute data not available for this address' };
    }

    // Check cache first
    const cacheKey = address.toLowerCase();
    const cachedEntry = commuteCache.get(cacheKey);

    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
      console.log('Using cached commute data');
      return { commute_analysis: cachedEntry.data };
    }

    // Log request in development mode
    console.log('%c[DEV] Fetching commute data', 'color: #f59e0b; font-weight: bold');
    console.log('Address:', address);

    // Build the destinations string
    const destinationsString = Object.values(DEFAULT_DESTINATIONS).join('|');

    // Build the API URL with query parameters
    const apiUrl = `${PROXY_ENDPOINT}?origins=${encodeURIComponent(address)}&destinations=${encodeURIComponent(destinationsString)}&mode=driving&units=imperial`;
    console.log('Google Distance Matrix API proxy URL:', apiUrl);

    // Make the API request with a timeout
    try {
      console.log('Fetching commute data from API...');

      // Create a timeout promise that rejects after 8 seconds
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Commute data request timed out')), 8000);
      });

      // Create the fetch promise
      const fetchPromise = fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      // Race the fetch promise against the timeout
      const response = await Promise.race([fetchPromise, timeoutPromise]);

      console.log('Google Distance Matrix API response status:', response.status);

      if (!response.ok) {
        console.error('Google Distance Matrix API response not OK:', response.status);

        // Try to get more error details
        try {
          const errorText = await response.text();
          console.error('Google Distance Matrix API error details:', errorText);
        } catch (textError) {
          console.error('Could not read error response text');
        }

        return { commute_analysis: 'Commute data not available for this address' };
      }

      const data = await response.json();
      console.log('Google Distance Matrix API response data:', data);

      // Check if the response has the expected format
      if (!data) {
        console.warn('Google Distance Matrix API returned no data');
        return { commute_analysis: 'Commute data not available for this address' };
      }

      // Log the full response for debugging
      console.log('Full response data:', JSON.stringify(data, null, 2));

      // Handle error response from our proxy
      if (data.error) {
        console.warn('Google Distance Matrix API returned an error:', data.error);
        return { commute_analysis: 'Commute data not available for this address' };
      }

      // Check if the response has the expected format
      if (!data.rows || !data.rows[0] || !data.rows[0].elements || !data.destination_addresses) {
        console.warn('Google Distance Matrix API returned unexpected data format');
        return { commute_analysis: 'Commute data not available for this address' };
      }

      // Parse the response
      const elements = data.rows[0].elements;
      const destinationAddresses = data.destination_addresses;

      // Check if we have valid elements
      if (!elements.length || elements.length !== Object.keys(DEFAULT_DESTINATIONS).length) {
        console.warn('Google Distance Matrix API returned invalid number of elements');
        return { commute_analysis: 'Commute data not available for this address' };
      }

      // Create the commute data array
      const commuteData: CommuteData[] = [];

      // Map the destinations to their display names
      const destinationNames = Object.keys(DEFAULT_DESTINATIONS);

      // Process each element
      elements.forEach((element: any, index: number) => {
        if (element.status === 'OK' && element.distance && element.duration) {
          commuteData.push({
            destination: destinationNames[index],
            distance: element.distance.text,
            duration: element.duration.text,
            mode: 'Driving'
          });
        } else {
          console.warn(`Element at index ${index} has invalid status or missing data:`, element);
        }
      });

      // Check if we have any valid commute data
      if (commuteData.length === 0) {
        console.warn('No valid commute data found');
        return { commute_analysis: 'Commute data not available for this address' };
      }

      console.log('Parsed commute data:', commuteData);

      // Cache the result
      commuteCache.set(cacheKey, {
        timestamp: Date.now(),
        data: commuteData
      });

      return { commute_analysis: commuteData };
    } catch (fetchError) {
      console.error('Fetch error in commuteAnalysisService:', fetchError);
      throw fetchError; // Re-throw to be caught by the outer try/catch
    }
  } catch (error) {
    console.error('Error fetching commute data:', error);

    // Log the specific error type and message
    console.warn(`Error type: ${error.name}, Message: ${error.message}`);

    // Generate mock data based on the address
    console.log('Generating mock commute data due to API error');

    // Extract zip code from address if possible
    const zipMatch = address.match(/\b\d{5}(?:-\d{4})?\b/);
    const hasZip = zipMatch && zipMatch[0];

    // Generate semi-random but consistent data based on the address
    // This ensures the same address always gets the same mock data
    const addressHash = address.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const randomFactor = hasZip ? parseInt(zipMatch[0].substring(0, 2)) / 100 : 0.5;

    // Base distances and durations that will be adjusted
    const baseDistances = {
      'Downtown Chicago': 12 + (addressHash % 10),
      'O\'Hare International Airport': 18 + (addressHash % 15),
      'Chicago Loop': 10 + (addressHash % 8)
    };

    const baseDurations = {
      'Downtown Chicago': 22 + (addressHash % 15),
      'O\'Hare International Airport': 30 + (addressHash % 20),
      'Chicago Loop': 18 + (addressHash % 12)
    };

    // Create mock data with some variability based on the address
    const mockData: CommuteData[] = [
      {
        destination: 'Downtown Chicago',
        distance: `${baseDistances['Downtown Chicago'].toFixed(1)} mi`,
        duration: `${Math.round(baseDurations['Downtown Chicago'])} mins`,
        mode: 'Driving'
      },
      {
        destination: 'O\'Hare International Airport',
        distance: `${baseDistances['O\'Hare International Airport'].toFixed(1)} mi`,
        duration: `${Math.round(baseDurations['O\'Hare International Airport'])} mins`,
        mode: 'Driving'
      },
      {
        destination: 'Chicago Loop',
        distance: `${baseDistances['Chicago Loop'].toFixed(1)} mi`,
        duration: `${Math.round(baseDurations['Chicago Loop'])} mins`,
        mode: 'Driving'
      }
    ];

    // Add a note in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Using generated mock commute data due to API error', 'color: orange; font-weight: bold');
    }

    return { commute_analysis: mockData };
  }
};
