import { Link } from 'react-router-dom';
import { FaMapMarkerAlt, FaArrowRight } from 'react-icons/fa';

const NeighborhoodButton = () => {
  return (
    <Link to="/neighborhood-compliance" className="glass-card p-5 transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden col-span-2 border-2 border-accent-cyan/30">
      <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

      <h3 className="font-bold mb-2 text-accent-cyan group-hover:text-white transition-colors relative z-10 flex items-center">
        <FaMapMarkerAlt className="w-4 h-4 mr-1.5 text-accent-cyan" />
        Neighborhood & Compliance Overview
      </h3>

      <p className="text-primary-200 text-sm leading-relaxed relative z-10">
        Generate comprehensive reports on <span className="text-accent-cyan font-medium">neighborhoods</span> and property compliance requirements.
      </p>
      
      <div className="flex items-center text-accent-cyan text-sm mt-3 group-hover:translate-x-1 transition-all duration-300 relative z-10">
        <span>Explore neighborhoods</span>
        <FaArrowRight className="ml-2 w-3 h-3" />
      </div>
    </Link>
  );
};

export default NeighborhoodButton;
