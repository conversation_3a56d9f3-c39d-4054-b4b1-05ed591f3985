// Custom Vite plugin for HMR
export default function hmrPlugin() {
  return {
    name: 'hmr-plugin',
    configureServer(server) {
      // Log when WebSocket connection is established
      server.ws.on('connection', () => {
        console.log('WebSocket connection established');
      });
      
      // Log when WebSocket connection is closed
      server.ws.on('close', () => {
        console.log('WebSocket connection closed');
      });
      
      // Log when WebSocket error occurs
      server.ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    }
  };
}
