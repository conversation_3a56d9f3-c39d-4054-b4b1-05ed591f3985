import asyncHandler from 'express-async-handler';
import Appointment from '../models/appointmentModel.js';
import User from '../models/userModel.js';
import Property from '../models/propertyModel.js';

// @desc    Create new appointment
// @route   POST /api/appointments
// @access  Private
const createAppointment = asyncHandler(async (req, res) => {
  const { realtorId, propertyId, date, time, notes } = req.body;

  // Check if realtor exists and is a realtor
  const realtor = await User.findById(realtorId);
  if (!realtor || !realtor.isRealtor) {
    res.status(400);
    throw new Error('Invalid realtor');
  }

  // Check if property exists
  const property = await Property.findById(propertyId);
  if (!property) {
    res.status(400);
    throw new Error('Property not found');
  }

  const appointment = new Appointment({
    user: req.user._id,
    realtor: realtorId,
    property: propertyId,
    date,
    time,
    notes,
    status: 'Pending',
  });

  const createdAppointment = await appointment.save();
  res.status(201).json(createdAppointment);
});

// @desc    Get all appointments for a user
// @route   GET /api/appointments
// @access  Private
const getUserAppointments = asyncHandler(async (req, res) => {
  const appointments = await Appointment.find({ user: req.user._id })
    .populate('realtor', 'name email')
    .populate('property', 'title address images');

  res.json(appointments);
});

// @desc    Get all appointments for a realtor
// @route   GET /api/appointments/realtor
// @access  Private/Realtor
const getRealtorAppointments = asyncHandler(async (req, res) => {
  const appointments = await Appointment.find({ realtor: req.user._id })
    .populate('user', 'name email')
    .populate('property', 'title address images');

  res.json(appointments);
});

// @desc    Update appointment status
// @route   PUT /api/appointments/:id/status
// @access  Private/Realtor
const updateAppointmentStatus = asyncHandler(async (req, res) => {
  const { status } = req.body;

  const appointment = await Appointment.findById(req.params.id);

  if (appointment) {
    // Check if user is the realtor for this appointment
    if (appointment.realtor.toString() === req.user._id.toString()) {
      appointment.status = status || appointment.status;

      const updatedAppointment = await appointment.save();
      res.json(updatedAppointment);
    } else {
      res.status(401);
      throw new Error('Not authorized to update this appointment');
    }
  } else {
    res.status(404);
    throw new Error('Appointment not found');
  }
});

// @desc    Get appointment by ID
// @route   GET /api/appointments/:id
// @access  Private
const getAppointmentById = asyncHandler(async (req, res) => {
  const appointment = await Appointment.findById(req.params.id)
    .populate('user', 'name email')
    .populate('realtor', 'name email')
    .populate('property', 'title address images');

  if (appointment) {
    // Check if user is the appointment creator or the realtor
    if (
      appointment.user.toString() === req.user._id.toString() ||
      appointment.realtor.toString() === req.user._id.toString() ||
      req.user.isAdmin
    ) {
      res.json(appointment);
    } else {
      res.status(401);
      throw new Error('Not authorized to view this appointment');
    }
  } else {
    res.status(404);
    throw new Error('Appointment not found');
  }
});

// @desc    Delete appointment
// @route   DELETE /api/appointments/:id
// @access  Private
const deleteAppointment = asyncHandler(async (req, res) => {
  const appointment = await Appointment.findById(req.params.id);

  if (appointment) {
    // Check if user is the appointment creator or admin
    if (
      appointment.user.toString() === req.user._id.toString() ||
      req.user.isAdmin
    ) {
      await appointment.remove();
      res.json({ message: 'Appointment removed' });
    } else {
      res.status(401);
      throw new Error('Not authorized to delete this appointment');
    }
  } else {
    res.status(404);
    throw new Error('Appointment not found');
  }
});

export {
  createAppointment,
  getUserAppointments,
  getRealtorAppointments,
  updateAppointmentStatus,
  getAppointmentById,
  deleteAppointment,
};
