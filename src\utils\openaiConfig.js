/**
 * OpenAI API configuration
 * This file contains the configuration for the OpenAI API client
 */

// Mock OpenAI client for browser compatibility
const mockOpenAI = {
  chat: {
    completions: {
      create: async (params) => {
        console.log('Using mock OpenAI API:', params);
        // Return a mock response
        return {
          choices: [
            {
              message: {
                content: JSON.stringify({
                  "buyer": {
                    "fullName": {
                      "pdfField": "Buyer_Name",
                      "label": "Buyer Name",
                      "type": "text",
                      "infer": false
                    },
                    "email": {
                      "pdfField": "Buyer_Email",
                      "label": "Buyer Email",
                      "type": "text",
                      "infer": false
                    }
                  },
                  "seller": {
                    "fullName": {
                      "pdfField": "Seller_Name",
                      "label": "Seller Name",
                      "type": "text",
                      "infer": false
                    }
                  },
                  "property": {
                    "address": {
                      "pdfField": "Property_Address",
                      "label": "Property Address",
                      "type": "textarea",
                      "infer": false
                    },
                    "unit": {
                      "pdfField": "Unit_Number",
                      "label": "Unit # (Optional)",
                      "type": "text",
                      "infer": false
                    },
                    "parkingIncluded": {
                      "pdfField": "Parking_Included",
                      "label": "Parking Included?",
                      "type": "checkbox",
                      "infer": false
                    }
                  }
                })
              }
            }
          ]
        };
      }
    }
  }
};

// Initialize OpenAI client
let openaiClient = null;

/**
 * Get the OpenAI API client
 * @returns {Object} - The OpenAI API client or mock client
 */
export function getOpenAIClient() {
  // For now, return the mock client
  // When the real OpenAI package is installed, this can be updated
  return mockOpenAI;
}

/**
 * Check if the OpenAI API is available
 * @returns {Promise<boolean>} - Whether the OpenAI API is available
 */
export async function isOpenAIAvailable() {
  try {
    // For now, return false to indicate that the real API is not available
    // When the real OpenAI package is installed, this can be updated
    return false;
  } catch (error) {
    console.error('Error checking OpenAI availability:', error);
    return false;
  }
}

export default {
  getOpenAIClient,
  isOpenAIAvailable
};
