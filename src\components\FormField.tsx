import React from 'react';
import ValidationMessage from './ValidationMessage';
import styles from '../pages/ListingGenerator.module.css';

interface FormFieldProps {
  id: string;
  name: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  placeholder?: string;
  type?: string;
  required?: boolean;
  error?: string;
  icon?: React.ReactNode;
  as?: 'input' | 'textarea' | 'select';
  options?: { value: string; label: string }[];
  rows?: number;
  className?: string;
}

const FormField: React.FC<FormFieldProps> = ({
  id,
  name,
  label,
  value,
  onChange,
  placeholder = '',
  type = 'text',
  required = false,
  error,
  icon,
  as = 'input',
  options = [],
  rows = 4,
  className = '',
}) => {
  return (
    <div className={`${styles.inputGroup} ${error ? styles.error : ''} ${className}`}>
      <label htmlFor={id} className={styles.inputLabel}>
        {icon && <span className={styles.inputIcon}>{icon}</span>}
        {label} {required ? <span className={styles.required}>*</span> : <span className={styles.optional}>(optional)</span>}
      </label>
      
      {as === 'input' && (
        <input
          type={type}
          id={id}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={styles.input}
          required={required}
        />
      )}
      
      {as === 'textarea' && (
        <textarea
          id={id}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={`${styles.input} ${styles.textarea}`}
          rows={rows}
          required={required}
        />
      )}
      
      {as === 'select' && (
        <select
          id={id}
          name={name}
          value={value}
          onChange={onChange}
          className={styles.input}
          required={required}
        >
          <option value="" disabled>{placeholder}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )}
      
      {error && (
        <div className="mt-2">
          <ValidationMessage type="error" message={error} />
        </div>
      )}
    </div>
  );
};

export default FormField;
