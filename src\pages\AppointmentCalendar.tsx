import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaCalendarAlt, FaPlus, FaEdit, FaTrash, FaCalendarDay, FaCalendarWeek, FaListAlt, FaBug, FaEraser } from 'react-icons/fa';
import { useApp } from '../context/SimpleAppContext';
import appointmentService from '../services/appointmentService';
import { Appointment } from '../models/Appointment';
import { Calendar, Views, dateFnsLocalizer } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay, parseISO } from 'date-fns';
import { enUS } from 'date-fns/locale';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import '../big-calendar-styles.css';
import { addTestAppointment, clearAllAppointments } from '../utils/testAppointment';

// Create a date-fns localizer for React Big Calendar
const locales = {
  'en-US': enUS,
};

// Create a localizer using date-fns
const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

// Event type for React Big Calendar
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  resource?: any; // Additional data
}

const AppointmentCalendar = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showAppointmentDetails, setShowAppointmentDetails] = useState(false);
  const [view, setView] = useState<any>('month');
  const [date, setDate] = useState(new Date());

  // Load appointments from database when user is logged in
  useEffect(() => {
    const loadAppointments = async () => {
      if (!state.user?.isAuthenticated) {
        console.log('User not authenticated, not loading appointments');
        setAppointments([]);
        return;
      }

      const userId = state.user?.id || 'guest';
      console.log('Loading appointments for user ID:', userId);

      try {
        // Get appointments for the current user from the database
        const userAppointments = await appointmentService.getClientAppointmentsForUser(userId);
        console.log('Found appointments:', userAppointments.length);

        setAppointments(userAppointments);
      } catch (error) {
        console.error('Error loading appointments:', error);
        setAppointments([]);
      }
    };

    loadAppointments();
  }, [state.user]);

  // Log when appointments change
  useEffect(() => {
    console.log('Appointments updated, count:', appointments.length);
  }, [appointments]);

  // Convert appointments to calendar events
  const calendarEvents = useMemo(() => {
    console.log('Converting appointments to calendar events, count:', appointments.length);

    if (appointments.length > 0) {
      console.log('Sample appointment data:', {
        id: appointments[0].id,
        date: appointments[0].date,
        time: appointments[0].time,
        userId: appointments[0].userId
      });
    }

    return appointments.map(appointment => {
      console.log(`Processing appointment ${appointment.id}:`, {
        date: appointment.date,
        time: appointment.time,
        clientName: appointment.clientName
      });

      // Parse the date and time
      let eventDate: Date;
      let eventEndDate: Date;

      try {
        // Try to parse the date string (e.g., "June 15, 2023")
        const dateMatch = appointment.date.match(/([A-Za-z]+)\s+(\d+),\s+(\d+)/);
        let dateObj: Date;

        if (dateMatch) {
          const [, month, day, year] = dateMatch;
          console.log(`Date match found: month=${month}, day=${day}, year=${year}`);
          dateObj = new Date(`${month} ${day}, ${year}`);
          console.log('Parsed date object:', dateObj.toString());
        } else {
          // Try to parse as a regular date string
          console.log('No date match found, trying direct Date parsing');
          dateObj = new Date(appointment.date);
          console.log('Direct parsed date:', dateObj.toString(), 'Valid:', !isNaN(dateObj.getTime()));
        }

        // Parse the time (e.g., "10:00 AM")
        const timeMatch = appointment.time.match(/(\d+):(\d+)\s+([AP]M)/);
        let hours = 0;
        let minutes = 0;

        if (timeMatch) {
          const [, hourStr, minuteStr, period] = timeMatch;
          hours = parseInt(hourStr);
          minutes = parseInt(minuteStr);
          console.log(`Time match found: hours=${hours}, minutes=${minutes}, period=${period}`);

          // Convert to 24-hour format
          if (period === 'PM' && hours < 12) {
            hours += 12;
          } else if (period === 'AM' && hours === 12) {
            hours = 0;
          }
          console.log(`Converted to 24-hour format: hours=${hours}`);
        } else {
          console.log('No time match found for pattern:', appointment.time);
        }

        // Set the hours and minutes
        dateObj.setHours(hours, minutes, 0, 0);
        eventDate = dateObj;
        console.log('Final event date:', eventDate.toString());

        // Create end date (1 hour after start)
        eventEndDate = new Date(dateObj);
        eventEndDate.setHours(hours + 1, minutes, 0, 0);
        console.log('Event end date:', eventEndDate.toString());
      } catch (error) {
        console.error('Error parsing appointment date/time:', error, 'for appointment:', appointment);
        // Fallback to current date/time
        eventDate = new Date();
        eventEndDate = new Date();
        eventEndDate.setHours(eventDate.getHours() + 1);
        console.log('Using fallback dates:', eventDate.toString());
      }

      const event = {
        id: appointment.id,
        title: `${appointment.clientName} - ${appointment.propertyAddress}`,
        start: eventDate,
        end: eventEndDate,
        resource: appointment, // Store the original appointment data
      };

      console.log('Created calendar event:', event);
      return event;
    });
  }, [appointments]);

  // Handle calendar navigation
  const handleNavigate = (newDate: Date) => {
    setDate(newDate);
  };

  // Handle view change
  const handleViewChange = (newView: any) => {
    setView(newView);
  };

  // Handle event selection
  const handleSelectEvent = (event: CalendarEvent) => {
    // The original appointment data is stored in the resource property
    const appointment = event.resource as Appointment;
    setSelectedAppointment(appointment);
    setShowAppointmentDetails(true);
  };

  // Close appointment details modal
  const closeAppointmentDetails = () => {
    setShowAppointmentDetails(false);
    setSelectedAppointment(null);
  };

  // Delete an appointment
  const deleteAppointment = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this appointment?')) {
      try {
        // Delete from database
        const success = await appointmentService.deleteClientAppointment(id);
        if (success) {
          // Refresh appointments from database
          const userId = state.user?.id || 'guest';
          try {
            const userAppointments = await appointmentService.getClientAppointmentsForUser(userId);
            setAppointments(userAppointments);
          } catch (error) {
            console.error('Error loading appointments after deletion:', error);
            // Fallback to local storage
            const localService = await import('../services/LocalAppointmentService').then(m => m.LocalAppointmentService);
            const localAppointments = localService.getUserAppointments(userId);
            setAppointments(localAppointments);
          }

          // Close the details modal if open
          if (selectedAppointment && selectedAppointment.id === id) {
            closeAppointmentDetails();
          }
        }
      } catch (error) {
        console.error('Error deleting appointment:', error);
        alert('Failed to delete appointment: ' + (error instanceof Error ? error.message : String(error)));
      }
    }
  };

  // Custom event styling
  const eventStyleGetter = (event: CalendarEvent) => {
    const appointment = event.resource as Appointment;
    let backgroundColor = '#00b9ff';

    // Different colors based on status
    if (appointment.status === 'confirmed') {
      backgroundColor = '#00b9ff';
    } else if (appointment.status === 'pending') {
      backgroundColor = '#f59e0b';
    } else if (appointment.status === 'cancelled') {
      backgroundColor = '#ef4444';
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.9,
        color: 'white',
        border: '0px',
        display: 'block',
        fontWeight: '500',
      }
    };
  };

  // Custom toolbar component
  const CustomToolbar = (toolbar: any) => {
    const goToBack = () => {
      toolbar.onNavigate('PREV');
    };

    const goToNext = () => {
      toolbar.onNavigate('NEXT');
    };

    const goToCurrent = () => {
      toolbar.onNavigate('TODAY');
    };

    const label = () => {
      const date = toolbar.date;
      return (
        <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-secondary-400 to-secondary-600">
          {format(date, 'MMMM yyyy')}
        </span>
      );
    };

    return (
      <div className="flex justify-between items-center mb-4 p-2">
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={goToBack}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            type="button"
            onClick={goToCurrent}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
          >
            Today
          </button>

          <button
            type="button"
            onClick={goToNext}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <div className="text-center">
          {label()}
        </div>

        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => toolbar.onView('month')}
            style={{
              background: view === 'month' ? 'linear-gradient(to right, #00b9ff, #9900ff)' : 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: view === 'month' ? 'none' : '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
          >
            <FaCalendarAlt className="w-5 h-5" />
          </button>

          <button
            type="button"
            onClick={() => toolbar.onView('week')}
            style={{
              background: view === 'week' ? 'linear-gradient(to right, #00b9ff, #9900ff)' : 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: view === 'week' ? 'none' : '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
          >
            <FaCalendarWeek className="w-5 h-5" />
          </button>

          <button
            type="button"
            onClick={() => toolbar.onView('day')}
            style={{
              background: view === 'day' ? 'linear-gradient(to right, #00b9ff, #9900ff)' : 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: view === 'day' ? 'none' : '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
          >
            <FaCalendarDay className="w-5 h-5" />
          </button>

          <button
            type="button"
            onClick={() => toolbar.onView('agenda')}
            style={{
              background: view === 'agenda' ? 'linear-gradient(to right, #00b9ff, #9900ff)' : 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: view === 'agenda' ? 'none' : '1px solid rgba(255, 255, 255, 0.2)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
          >
            <FaListAlt className="w-5 h-5" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="py-12 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/agent')}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              transition: 'all 0.3s ease',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            }}
            className="mb-4 transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-white mb-2">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-secondary-400 to-secondary-600">
              Appointment Calendar
            </span>
          </h1>
          <p className="text-primary-200 mb-4">
            View and manage all your scheduled appointments in one place.
          </p>
        </div>

        {/* Calendar Container */}
        <div className="glass-card p-6 rounded-xl shadow-lg border-glow mb-6">
          {/* React Big Calendar */}
          <div className="calendar-container" style={{ height: '700px' }}>
            {appointments.length === 0 && state.user?.isAuthenticated ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="text-center p-8 bg-dark-800/50 rounded-xl border border-primary-700/30 max-w-lg">
                  <FaCalendarAlt className="text-5xl text-primary-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">No Appointments Found</h3>
                  <p className="text-primary-300 mb-6">
                    You don't have any appointments scheduled yet. Click the button below to create your first appointment.
                  </p>
                  <button
                    onClick={() => navigate('/appointment-scheduler')}
                    style={{
                      background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                      color: 'white',
                      padding: '0.75rem 1.5rem',
                      borderRadius: '0.5rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: 'none',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      margin: '0 auto',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                  >
                    <FaPlus className="mr-2" />
                    Schedule New Appointment
                  </button>
                </div>
              </div>
            ) : !state.user?.isAuthenticated ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="text-center p-8 bg-dark-800/50 rounded-xl border border-primary-700/30 max-w-lg">
                  <FaCalendarAlt className="text-5xl text-primary-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Please Log In</h3>
                  <p className="text-primary-300 mb-6">
                    You need to be logged in to view and manage your appointments.
                  </p>
                  <button
                    onClick={() => navigate('/login')}
                    style={{
                      background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                      color: 'white',
                      padding: '0.75rem 1.5rem',
                      borderRadius: '0.5rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: 'none',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      margin: '0 auto',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                  >
                    Log In
                  </button>
                </div>
              </div>
            ) : (
              <Calendar
                localizer={localizer}
                events={calendarEvents}
                startAccessor="start"
                endAccessor="end"
                style={{ height: '100%' }}
                views={{
                  month: true,
                  week: true,
                  day: true,
                  agenda: true
                }}
                view={view}
                date={date}
                onNavigate={handleNavigate}
                onView={handleViewChange}
                eventPropGetter={eventStyleGetter}
                onSelectEvent={handleSelectEvent}
                components={{
                  toolbar: CustomToolbar,
                }}
                popup
                selectable
              />
            )}
          </div>

          {/* Add New Appointment Button - Only show when user is logged in */}
          {state.user?.isAuthenticated && (
            <div className="flex justify-center mt-6 space-x-4">
              <button
                onClick={() => navigate('/appointment-scheduler')}
                style={{
                  background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.5rem',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'all 0.3s ease',
                  border: 'none',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                }}
                className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
              >
                <FaPlus className="mr-2" />
                Schedule New Appointment
              </button>

              {/* Debug buttons - only visible in development */}
              {import.meta.env.DEV && (
                <>
                  <button
                    onClick={async () => {
                      // Force reload appointments from database
                      const userId = state.user?.id || 'guest';
                      console.log('Debug: Reloading appointments for user:', userId);

                      try {
                        const userAppointments = await appointmentService.getClientAppointmentsForUser(userId);
                        console.log('Debug: Found appointments in database:', userAppointments);
                        setAppointments(userAppointments);

                        // Also check localStorage as fallback
                        try {
                          const storedAppointments = localStorage.getItem('localAppointments');
                          console.log('Debug: Raw localStorage data:', storedAppointments);
                          if (storedAppointments) {
                            const parsed = JSON.parse(storedAppointments);
                            console.log('Debug: Parsed localStorage data:', parsed);
                          }
                        } catch (error) {
                          console.error('Debug: Error reading localStorage:', error);
                        }
                      } catch (error) {
                        console.error('Debug: Error loading appointments from database:', error);

                        // Fallback to local storage
                        try {
                          const localService = await import('../services/LocalAppointmentService').then(m => m.LocalAppointmentService);
                          const localAppointments = localService.getUserAppointments(userId);
                          console.log('Debug: Found appointments in localStorage:', localAppointments);
                          setAppointments(localAppointments);
                        } catch (localError) {
                          console.error('Debug: Error loading from localStorage:', localError);
                        }
                      }
                    }}
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      padding: '0.75rem 1.5rem',
                      borderRadius: '0.5rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                  >
                    Refresh Calendar
                  </button>

                  <button
                    onClick={async () => {
                      // Add a test appointment
                      const userId = state.user?.id || 'guest';
                      try {
                        const testAppointment = await addTestAppointment(userId);
                        if (testAppointment) {
                          // Reload appointments from database
                          try {
                            const userAppointments = await appointmentService.getClientAppointmentsForUser(userId);
                            setAppointments(userAppointments);
                            alert('Test appointment added successfully!');
                          } catch (error) {
                            console.error('Error loading appointments after adding test:', error);
                            // Fallback to local storage
                            const localService = await import('../services/LocalAppointmentService').then(m => m.LocalAppointmentService);
                            const localAppointments = localService.getUserAppointments(userId);
                            setAppointments(localAppointments);
                            alert('Test appointment added to local storage (database connection failed)');
                          }
                        }
                      } catch (error) {
                        console.error('Error adding test appointment:', error);
                        alert('Failed to add test appointment: ' + (error instanceof Error ? error.message : String(error)));
                      }
                    }}
                    style={{
                      background: 'rgba(59, 130, 246, 0.3)',
                      color: 'white',
                      padding: '0.75rem 1.5rem',
                      borderRadius: '0.5rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: '1px solid rgba(59, 130, 246, 0.4)',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                  >
                    <FaBug className="mr-2" />
                    Add Test Appointment
                  </button>

                  <button
                    onClick={async () => {
                      // Clear all appointments
                      if (window.confirm('Are you sure you want to clear all appointments? This cannot be undone.')) {
                        try {
                          await clearAllAppointments();
                          // Reload appointments
                          setAppointments([]);
                          alert('All appointments cleared successfully!');
                        } catch (error) {
                          console.error('Error clearing appointments:', error);
                          alert('Error clearing appointments: ' + (error instanceof Error ? error.message : String(error)));
                        }
                      }
                    }}
                    style={{
                      background: 'rgba(239, 68, 68, 0.3)',
                      color: 'white',
                      padding: '0.75rem 1.5rem',
                      borderRadius: '0.5rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: '1px solid rgba(239, 68, 68, 0.4)',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                  >
                    <FaEraser className="mr-2" />
                    Clear All Appointments
                  </button>
                </>
              )}
            </div>
          )}
        </div>

        {/* Appointment Details Modal */}
        {showAppointmentDetails && selectedAppointment && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
            <div className="glass-card p-6 rounded-xl shadow-lg border-glow max-w-2xl w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-white">
                  Appointment Details
                </h3>
                <button
                  onClick={closeAppointmentDetails}
                  className="text-primary-300 hover:text-white"
                >
                  &times;
                </button>
              </div>

              <div className="bg-dark-700/50 p-4 rounded-lg space-y-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-primary-400 text-sm">Client Name</p>
                    <p className="text-white font-semibold">{selectedAppointment.clientName}</p>
                  </div>
                  <div>
                    <p className="text-primary-400 text-sm">Client Email</p>
                    <p className="text-white">{selectedAppointment.clientEmail}</p>
                  </div>
                  <div>
                    <p className="text-primary-400 text-sm">Date</p>
                    <p className="text-white">{selectedAppointment.date}</p>
                  </div>
                  <div>
                    <p className="text-primary-400 text-sm">Time</p>
                    <p className="text-white">{selectedAppointment.time}</p>
                  </div>
                  <div>
                    <p className="text-primary-400 text-sm">Property Type</p>
                    <p className="text-white">{selectedAppointment.propertyType}</p>
                  </div>
                  <div>
                    <p className="text-primary-400 text-sm">Status</p>
                    <p className="text-white">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        selectedAppointment.status === 'confirmed' ? 'bg-green-600' :
                        selectedAppointment.status === 'pending' ? 'bg-yellow-600' :
                        'bg-red-600'
                      }`}>
                        {selectedAppointment.status.charAt(0).toUpperCase() + selectedAppointment.status.slice(1)}
                      </span>
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-primary-400 text-sm">Property Address</p>
                  <p className="text-white">{selectedAppointment.propertyAddress}</p>
                </div>

                {selectedAppointment.notes && (
                  <div>
                    <p className="text-primary-400 text-sm">Notes</p>
                    <p className="text-primary-200">{selectedAppointment.notes}</p>
                  </div>
                )}
              </div>

              <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-3 justify-end">
                <button
                  onClick={() => navigate(`/appointment-scheduler?id=${selectedAppointment.id}`)}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    padding: '0.5rem 1rem',
                    borderRadius: '0.5rem',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
                >
                  <FaEdit className="mr-2" />
                  Edit
                </button>

                <button
                  onClick={() => deleteAppointment(selectedAppointment.id)}
                  style={{
                    background: 'rgba(220, 38, 38, 0.2)',
                    color: 'white',
                    padding: '0.5rem 1rem',
                    borderRadius: '0.5rem',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(220, 38, 38, 0.3)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-red-800/50"
                >
                  <FaTrash className="mr-2" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppointmentCalendar;
