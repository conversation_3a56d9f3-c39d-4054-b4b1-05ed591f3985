import { Link } from 'react-router-dom';
import { useApp } from '../context/SimpleAppContext';

const SimpleAgentDashboard = () => {
  const { state } = useApp();

  const tools = [
    {
      id: 'listing',
      title: 'Listing Generator',
      description: 'Create compelling property listings with AI-generated descriptions and marketing tips.',
      path: '/listing-generator',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      color: 'from-primary-500 to-primary-600',
    },
    {
      id: 'appointment',
      title: 'Appointment Scheduler',
      description: 'Get AI recommendations for scheduling client appointments and viewing preparations.',
      path: '/appointment-scheduler',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: 'from-secondary-500 to-secondary-600',
    },
    {
      id: 'calendar',
      title: 'Appointment Calendar',
      description: 'View and manage all your scheduled appointments in a calendar view.',
      path: '/appointment-calendar',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: 'from-primary-600 to-secondary-400',
    },
    {
      id: 'analysis',
      title: 'Competitive Analysis',
      description: 'Generate detailed market analysis and competitive property comparisons.',
      path: '/comp-analyzer',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
        </svg>
      ),
      color: 'from-primary-500 to-secondary-500',
    },
    {
      id: 'inspection',
      title: 'Inspection Report Generator',
      description: 'Analyze inspection findings and generate client-friendly summary reports.',
      path: '/direct-inspection',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
      color: 'from-secondary-500 to-primary-500',
    },
    {
      id: 'lease',
      title: 'Lease Analysis',
      description: 'Review lease agreements and identify potential concerns or negotiation points.',
      path: '/agent/tools/lease',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      color: 'from-primary-600 to-secondary-600',
    },
  ];

  return (
    <div className="bg-dark-900 py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-12 animate-fade-in">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4 neon-text">Agent AI Tools</h1>
          <p className="text-primary-200 max-w-3xl">
            Welcome, <span className="font-semibold text-accent-cyan">{state.user.name}</span>! Leverage the power of <span className="text-secondary-400">AI</span> to streamline your real estate business. Our suite of <span className="text-accent-cyan">AI-powered tools</span> helps you save time,
            create professional content, and provide better service to your clients.
          </p>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {tools.map((tool, index) => (
            <Link
              key={tool.id}
              to={tool.path}
              className={`bg-dark-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group animate-fade-in border border-primary-800 hover:border-accent-cyan cyber-border`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="p-6">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${tool.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300 neon-glow`}>
                  {tool.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-accent-cyan transition-colors">{tool.title}</h3>
                <p className="text-primary-200 mb-4">{tool.description}</p>
                <div className="flex items-center text-accent-cyan font-medium group-hover:translate-x-1 transition-transform duration-300 border-b border-transparent group-hover:border-accent-cyan">
                  <span>Get started</span>
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="bg-gradient-neon rounded-xl p-8 text-white animate-fade-in cyber-border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="col-span-1 md:col-span-1">
              <h2 className="text-2xl font-bold mb-4 neon-text">Why Use AI Tools?</h2>
              <p className="text-white/90">
                Our <span className="text-accent-cyan">AI-powered tools</span> help real estate professionals save time, increase productivity, and deliver exceptional client service.
              </p>
            </div>
            <div className="col-span-1 md:col-span-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="bg-dark-800/80 backdrop-blur-md p-4 rounded-lg hover:bg-dark-800 transition-all duration-300 border border-primary-800 hover:border-accent-cyan cyber-border">
                  <h3 className="font-bold mb-2 text-accent-cyan">Save Time</h3>
                  <p className="text-primary-200">
                    Automate routine tasks and generate content in seconds instead of hours.
                  </p>
                </div>
                <div className="bg-dark-800/80 backdrop-blur-md p-4 rounded-lg hover:bg-dark-800 transition-all duration-300 border border-secondary-800 hover:border-secondary-400 cyber-border">
                  <h3 className="font-bold mb-2 text-secondary-400">Increase Consistency</h3>
                  <p className="text-primary-200">
                    Maintain professional quality across all client communications and materials.
                  </p>
                </div>
                <div className="bg-dark-800/80 backdrop-blur-md p-4 rounded-lg hover:bg-dark-800 transition-all duration-300 border border-primary-800 hover:border-accent-cyan cyber-border">
                  <h3 className="font-bold mb-2 text-accent-cyan">Enhance Client Service</h3>
                  <p className="text-primary-200">
                    Provide detailed information and insights that impress clients and build trust.
                  </p>
                </div>
                <div className="bg-dark-800/80 backdrop-blur-md p-4 rounded-lg hover:bg-dark-800 transition-all duration-300 border border-secondary-800 hover:border-secondary-400 cyber-border">
                  <h3 className="font-bold mb-2 text-secondary-400">Stay Competitive</h3>
                  <p className="text-primary-200">
                    Leverage cutting-edge technology to differentiate your services in the market.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleAgentDashboard;
