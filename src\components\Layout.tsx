import { ReactNode } from 'react';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';
import { useApp } from '../context/AppContext';

const Layout = () => {
  const { state } = useApp();
  
  return (
    <div className={`min-h-screen flex flex-col ${state.ui.theme === 'dark' ? 'dark' : ''}`}>
      <Header />
      <main className="flex-grow pt-20">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
