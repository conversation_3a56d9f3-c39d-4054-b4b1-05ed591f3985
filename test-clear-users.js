import axios from 'axios';

async function testClearUsers() {
  try {
    console.log('Testing clear all users functionality...');
    const response = await axios.delete('http://localhost:5000/api/users/clear-all');
    console.log('Response:', response.data);
    console.log('Success! All users and email addresses have been cleared.');
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

testClearUsers();
