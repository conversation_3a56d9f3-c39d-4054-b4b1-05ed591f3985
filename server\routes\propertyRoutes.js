import express from 'express';
const router = express.Router();
import {
  getProperties,
  getPropertyById,
  deleteProperty,
  createProperty,
  updateProperty,
  createPropertyReview,
  getTopProperties,
  getFeaturedProperties,
} from '../controllers/propertyController.js';
import { protect, admin, realtor } from '../middleware/authMiddleware.js';

router.route('/').get(getProperties).post(protect, realtor, createProperty);
router.route('/top').get(getTopProperties);
router.route('/featured').get(getFeaturedProperties);
router
  .route('/:id')
  .get(getPropertyById)
  .delete(protect, deleteProperty)
  .put(protect, realtor, updateProperty);
router.route('/:id/reviews').post(protect, createPropertyReview);

export default router;
