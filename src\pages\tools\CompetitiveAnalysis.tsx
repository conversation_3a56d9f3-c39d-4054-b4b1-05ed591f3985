import { useNavigate } from 'react-router-dom';
import { FaChartLine, FaArrowLeft } from 'react-icons/fa';
import { useApp } from '../../context/AppContext';

const CompetitiveAnalysis = () => {
  const navigate = useNavigate();
  const { state } = useApp();

  return (
    <div className="py-12 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button 
            onClick={() => navigate('/agent')}
            className="flex items-center text-primary-600 hover:text-primary-700 mb-4"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-dark-800 mb-2">Competitive Analysis</h1>
          <p className="text-dark-500">
            Generate detailed market analysis and competitive property comparisons.
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-card p-8 text-center">
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white mx-auto mb-6">
            <FaChartLine className="text-3xl" />
          </div>
          <h2 className="text-2xl font-bold text-dark-800 mb-4">Coming Soon</h2>
          <p className="text-dark-500 max-w-lg mx-auto mb-6">
            The Competitive Analysis tool is currently under development. Check back soon for this powerful feature that will help you generate detailed market analysis and competitive property comparisons.
          </p>
          <button 
            onClick={() => navigate('/agent')}
            className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompetitiveAnalysis;
