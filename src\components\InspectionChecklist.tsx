import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FaRegSquare } from 'react-icons/fa';

interface ChecklistItem {
  id: string;
  category: string;
  item: string;
  completed: boolean;
  relevantToReport: boolean;
}

interface InspectionChecklistProps {
  checklistItems: ChecklistItem[];
  onToggleItem: (id: string) => void;
  onClose: () => void;
}

const InspectionChecklist: React.FC<InspectionChecklistProps> = ({
  checklistItems,
  onToggleItem,
  onClose
}) => {
  // Group items by category
  const groupedItems: Record<string, ChecklistItem[]> = {};

  checklistItems.forEach(item => {
    if (!groupedItems[item.category]) {
      groupedItems[item.category] = [];
    }
    groupedItems[item.category].push(item);
  });

  // Calculate completion statistics
  const totalItems = checklistItems.length;
  const completedItems = checklistItems.filter(item => item.completed).length;
  const completionPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

  return (
    <div className="glass-card dark p-8 animate-fade-in">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <svg className="w-5 h-5 mr-2 text-accent-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Inspection Checklist
        </h2>
        <button
          onClick={onClose}
          style={{
            background: 'rgba(255, 255, 255, 0.1)',
            color: 'white',
            padding: '0.5rem',
            borderRadius: '9999px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.3s ease',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          }}
          className="transform hover:shadow-md hover:-translate-y-0.5 hover:scale-[1.03] transition-transform duration-300"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="glass-card light p-6 rounded-lg">
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-dark-700 font-medium">Inspection Progress</span>
            <span className="text-accent-cyan font-medium">{completionPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-gradient-to-r from-accent-cyan to-primary-400 h-2.5 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
          <div className="mt-2 text-xs text-gray-500 text-right">
            {completedItems} of {totalItems} items completed
          </div>
        </div>

        <div className="mb-6 p-4 bg-gradient-to-r from-accent-cyan/10 to-primary-400/10 rounded-lg border border-accent-cyan/20">
          <p className="text-dark-700">
            <span className="font-semibold text-accent-cyan">Instructions:</span> Use this checklist to track your inspection progress.
            <span className="ml-1 inline-flex items-center">
              <span className="inline-block w-4 h-4 mr-1 text-accent-cyan"><FaCheckSquare /></span>
              Items with filled checkboxes were found in your report.
            </span>
            <span className="ml-1 inline-flex items-center">
              <span className="inline-block w-4 h-4 mr-1 text-gray-400"><FaRegSquare /></span>
              Empty checkboxes indicate items not yet inspected.
            </span>
          </p>
        </div>

        {Object.keys(groupedItems).map(category => (
          <div key={category} className="mb-8">
            <h3 className="text-xl font-bold text-accent-cyan mb-4 pb-2 border-b border-accent-cyan/20">
              {category}
            </h3>
            <div className="space-y-3">
              {groupedItems[category].map(item => (
                <div
                  key={item.id}
                  className={`p-4 rounded-lg transition-all duration-200 ${
                    item.relevantToReport
                      ? 'bg-gradient-to-r from-accent-cyan/15 to-primary-400/15 border border-accent-cyan/30 shadow-md'
                      : 'bg-white border border-gray-100 hover:border-gray-200 hover:shadow-sm'
                  }`}
                >
                  <div className="flex items-start">
                    <div
                      className="flex-shrink-0 mr-3 cursor-pointer transition-all duration-200 text-lg"
                      onClick={() => onToggleItem(item.id)}
                    >
                      {item.relevantToReport ? (
                        <FaCheckSquare className="text-accent-cyan" />
                      ) : (
                        item.completed ? (
                          <FaCheckSquare className="text-gray-400" />
                        ) : (
                          <FaRegSquare className="text-gray-400 hover:text-accent-cyan" />
                        )
                      )}
                    </div>
                    <div className="flex-1">
                      <div
                        className={`text-dark-700 font-medium ${item.completed ? 'line-through opacity-70' : ''}`}
                      >
                        {item.item}
                      </div>
                      {item.relevantToReport && (
                        <div className="mt-1 text-sm text-accent-cyan">
                          <span className="font-medium">Note:</span> This item was identified in your inspection report.
                        </div>
                      )}
                    </div>
                    <div
                      className={`ml-2 flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center cursor-pointer ${
                        item.completed
                          ? 'bg-gradient-to-r from-accent-cyan to-primary-400 text-white'
                          : 'border-2 border-gray-300 hover:border-accent-cyan'
                      }`}
                      onClick={() => onToggleItem(item.id)}
                    >
                      {item.completed && <FaCheck className="w-3 h-3" />}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        <div className="mt-8 flex justify-center">
          <button
            onClick={onClose}
            style={{
              background: 'linear-gradient(to right, #00b9ff, #9900ff)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '9999px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              border: 'none',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
            className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 group"
          >
            <span className="inline-flex items-center justify-center whitespace-nowrap">
              <svg className="w-4 h-4 mr-1.5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
              </svg>
              Done
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default InspectionChecklist;
