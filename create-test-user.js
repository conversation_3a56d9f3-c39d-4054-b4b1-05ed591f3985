import axios from 'axios';

async function createTestUser() {
  try {
    console.log('Creating test user...');
    const response = await axios.post('http://localhost:5000/api/users', {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      verificationCode: '123456' // Using our fixed verification code
    });
    console.log('Response:', response.data);
    console.log('Success! Test user created.');
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

createTestUser();
