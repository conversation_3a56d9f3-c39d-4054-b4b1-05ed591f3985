// This is a fixed version of the extractAppointmentData function
// with corrected indentation and structure

const extractAppointmentData = async () => {
  if (!naturalLanguageInput.trim()) {
    setExtractionError('Please enter a description of the appointment');
    return;
  }

  setIsExtracting(true);
  setExtractionError('');
  setExtractionSuccess(false);

  // Check if the input contains relative dates
  const relativeDateCheck = checkForRelativeDates(naturalLanguageInput);
  console.log('Relative date check:', relativeDateCheck);

  // Log which LLM is being used in development mode
  if (import.meta.env.DEV) {
    console.log(`%c[DEV] Using ${llmProvider === 'openai' ? 'OpenAI' : 'Claude'} for appointment data extraction`, 
      `color: ${llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
  }

  try {
    let args;

    if (llmProvider === 'openai') {
      // Get OpenAI API key from environment variable
      let apiKey = import.meta.env.VITE_OPENAI_API_KEY;

      // If not available, try to get it from window.__ENV__
      if (!apiKey && typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_OPENAI_API_KEY) {
        apiKey = window.__ENV__.VITE_OPENAI_API_KEY;
      }

      // For development/testing, you can hardcode the key here (but remove before committing)
      if (!apiKey) {
        apiKey = '********************************************************************************************************************************************************************';
      }

      if (!apiKey) {
        throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
      }

      // Define the function schema for scheduling appointments
      const functionSchema = {
        name: 'scheduleAppointment',
        description: 'Schedule a real estate appointment with a client',
        parameters: {
          type: 'object',
          properties: {
            appointmentId: {
              type: 'string',
              description: 'Unique ID'
            },
            clientName: {
              type: 'string'
            },
            clientEmail: {
              type: 'string'
            },
            date: {
              type: 'string'
            },
            time: {
              type: 'string'
            },
            propertyAddress: {
              type: 'string'
            },
            status: {
              type: 'string',
              enum: ['confirmed', 'pending', 'cancelled']
            },
            notes: {
              type: 'string'
            }
          },
          required: ['appointmentId', 'clientName', 'clientEmail', 'date', 'time', 'propertyAddress', 'status']
        }
      };

      // Create the system message with instructions for the AI
      const systemMessage = `You are an AI assistant for real estate agents. Your task is to extract appointment details from the user's natural language input. Use the scheduleAppointment function to structure the appointment data.

For the time parameter, please format it as a standard time with AM/PM indicator (e.g., "10:00 AM", "2:30 PM").

For the date parameter, when the user mentions relative dates like "today", "tomorrow", or "next Monday", calculate the actual calendar date based on the current date (${new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}). Always provide a full date with month, day, and year (e.g., "June 15, 2023").

If the user doesn't specify a complete detail, make a reasonable assumption based on context. For example, if they only mention "tomorrow at 3", calculate the actual date for tomorrow (${new Date(Date.now() + 86400000).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}) and assume "3:00 PM" for the time.`;

      // Make the API request to OpenAI using axios
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemMessage },
            { role: 'user', content: naturalLanguageInput }
          ],
          functions: [functionSchema],
          function_call: { name: 'scheduleAppointment' },
          temperature: 0.7,
          max_tokens: 800
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'OpenAI-Beta': 'assistants=v1'
          }
        }
      );

      console.log('OpenAI API response status:', response.status);
      console.log('OpenAI API response data:', response.data);

      // Extract the function call arguments
      const functionCall = response.data.choices[0].message.function_call;

      if (functionCall && functionCall.name === 'scheduleAppointment') {
        try {
          args = JSON.parse(functionCall.arguments);
        } catch (parseError) {
          console.error('Error parsing function call arguments:', parseError);
          throw new Error('Failed to parse appointment data from OpenAI');
        }
      } else {
        throw new Error('Function call not returned by the OpenAI API');
      }
    } else {
      // Use Claude via backend API
      try {
        args = await extractAppointmentDataWithClaude(naturalLanguageInput);
        console.log('Claude API response data:', args);
      } catch (claudeError) {
        console.error('Error with Claude API:', claudeError);
        throw new Error(`Claude API error: ${claudeError instanceof Error ? claudeError.message : 'Unknown error'}`);
      }
    }

    // Continue with the extracted data
    console.log('Extracted appointment data:', args);
    
    // Format the date if it's in a different format
    let formattedDate = args.date;
    if (args.date) {
      try {
        console.log('Original date from OpenAI:', args.date);

        // Handle relative date terms directly
        const lowerDate = args.date.toLowerCase();
        const today = new Date();
        let dateObj;

        if (lowerDate.includes('today')) {
          console.log('Detected "today" in date string');
          dateObj = new Date(); // Today
        } else if (lowerDate.includes('tomorrow')) {
          console.log('Detected "tomorrow" in date string');
          dateObj = new Date(today.getTime() + 86400000); // Tomorrow (today + 1 day in ms)
        } else if (lowerDate.includes('day after tomorrow')) {
          console.log('Detected "day after tomorrow" in date string');
          dateObj = new Date(today.getTime() + 172800000); // Day after tomorrow (today + 2 days in ms)
        } else if (lowerDate.match(/next\s+(mon|tues|wednes|thurs|fri|satur|sun)day/)) {
          console.log('Detected "next [day of week]" in date string');
          // Calculate next occurrence of the specified day
          const dayMatch = lowerDate.match(/next\s+(mon|tues|wednes|thurs|fri|satur|sun)day/);
          if (dayMatch) {
            const dayPrefix = dayMatch[1];
            let targetDay;

            switch (dayPrefix) {
              case 'mon': targetDay = 1; break;
              case 'tues': targetDay = 2; break;
              case 'wednes': targetDay = 3; break;
              case 'thurs': targetDay = 4; break;
              case 'fri': targetDay = 5; break;
              case 'satur': targetDay = 6; break;
              case 'sun': targetDay = 0; break;
              default: targetDay = -1;
            }

            if (targetDay >= 0) {
              dateObj = new Date();
              const currentDay = dateObj.getDay();
              const daysToAdd = (targetDay + 7 - currentDay) % 7 || 7; // If today, then next week
              dateObj.setDate(dateObj.getDate() + daysToAdd);
            } else {
              // Fallback to regular parsing
              dateObj = new Date(args.date);
            }
          } else {
            // Fallback to regular parsing
            dateObj = new Date(args.date);
          }
        } else {
          // Try to parse the date normally
          dateObj = new Date(args.date);
        }

        // Format the date if we have a valid date object
        if (!isNaN(dateObj.getTime())) {
          const options = { year: 'numeric', month: 'long', day: 'numeric' };
          formattedDate = dateObj.toLocaleDateString('en-US', options);
          console.log('Formatted date:', formattedDate);
        } else {
          console.log('Date parsing failed, using original string');

          // Check if the date is in the expected format (e.g., "June 15, 2023")
          const dateFormatRegex = /^[A-Za-z]+ \d+, \d{4}$/;
          if (!dateFormatRegex.test(formattedDate)) {
            console.log('Date is not in the expected format, trying alternative parsing');

            // Try to extract date components using regex
            const dateMatch = args.date.match(/(\d{4})-(\d{2})-(\d{2})/);
            if (dateMatch) {
              const [, year, month, day] = dateMatch;
              const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
              const monthIndex = parseInt(month) - 1;
              formattedDate = `${monthNames[monthIndex]} ${parseInt(day)}, ${year}`;
              console.log('Reformatted date using regex:', formattedDate);
            }
          }
        }
      } catch (dateError) {
        console.error('Error formatting date:', dateError);
        // Keep the original date string if parsing fails
      }
    }

    // Format the time if needed
    let formattedTime = args.time || '';
    console.log('Original time from OpenAI:', formattedTime);

    // Check if time is in a valid format for our application
    // We expect times in format like "10:00 AM" or "2:30 PM"
    if (formattedTime && !formattedTime.match(/^\d{1,2}:\d{2} (AM|PM)$/)) {
      try {
        console.log('Time is not in the expected format, attempting to format');

        // Try to parse and format the time
        const timeDate = new Date(`2000-01-01 ${formattedTime}`);
        if (!isNaN(timeDate.getTime())) {
          formattedTime = timeDate.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          });
          console.log('Formatted time using Date object:', formattedTime);
        } else {
          console.log('Time parsing failed with Date object');

          // Try to extract time components using regex
          const timeMatch = formattedTime.match(/(\d{1,2}):(\d{2})(?::(\d{2}))?\s*(am|pm|AM|PM)?/);
          if (timeMatch) {
            const [, hours, minutes, , period] = timeMatch;
            const hour = parseInt(hours);
            const minute = parseInt(minutes);

            // Determine AM/PM
            let timePeriod = period ? period.toUpperCase() : (hour >= 12 ? 'PM' : 'AM');

            // Format the time
            formattedTime = `${hour}:${minutes.padStart(2, '0')} ${timePeriod}`;
            console.log('Reformatted time using regex:', formattedTime);
          }
        }
      } catch (timeError) {
        console.error('Error formatting time:', timeError);
        // Keep the original time string if parsing fails
      }
    }

    // Ensure the time is in our list of available time slots
    if (formattedTime && !timeSlots.includes(formattedTime)) {
      // Find the closest time slot
      const closestTime = findClosestTimeSlot(formattedTime);
      console.log(`Time "${formattedTime}" not in available slots, using closest: "${closestTime}"`);
      formattedTime = closestTime;
    }

    // Create the processed data object
    const processedData = {
      appointmentId: args.appointmentId || `appt-${Date.now()}`,
      clientName: args.clientName || '',
      clientEmail: args.clientEmail || '',
      clientPhone: '', // This might not be in the extracted data
      propertyAddress: args.propertyAddress || '',
      propertyType: 'House', // Default value
      date: formattedDate || '',
      time: formattedTime,
      status: args.status || 'pending',
      notes: args.notes || ''
    };

    // Store the extracted data for confirmation
    setExtractedData(processedData);

    // Update the form data with the extracted information
    setFormData({
      clientName: processedData.clientName,
      clientEmail: processedData.clientEmail,
      clientPhone: processedData.clientPhone,
      propertyAddress: processedData.propertyAddress,
      propertyType: processedData.propertyType,
      preferredDate: processedData.date,
      preferredTime: processedData.time,
      additionalNotes: processedData.notes
    });

    // Set the selected date for the calendar component
    if (formattedDate) {
      try {
        const dateObj = new Date(formattedDate);
        if (!isNaN(dateObj.getTime())) {
          setSelectedDate(dateObj);
        }
      } catch (dateError) {
        console.error('Error setting selected date:', dateError);
      }
    }

    setExtractionSuccess(true);
    setShowConfirmation(true);
    return processedData;
  } catch (error) {
    console.error('Error extracting appointment data:', error);

    // Provide more detailed error messages
    if (error.response) {
      if (error.response.status === 429) {
        setExtractionError(`${llmProvider.toUpperCase()} API rate limit exceeded. Please try again in a few minutes.`);
      } else if (error.response.status === 401 || error.response.status === 403) {
        setExtractionError(`Authentication error with ${llmProvider.toUpperCase()} API. Please check your API key.`);
      } else if (error.response.status >= 500) {
        setExtractionError(`${llmProvider.toUpperCase()} server error. Please try again later.`);
      } else {
        setExtractionError(`Error: ${error.response.data?.error?.message || 'Failed to extract appointment data'}`);
      }
    } else {
      setExtractionError(error.message || 'Failed to extract appointment data');
    }

    return null;
  } finally {
    setIsExtracting(false);
  }
};
