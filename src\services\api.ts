import axios from 'axios';

// Get backend URL from environment
const getBackendUrl = () => {
  // First check Vite environment
  if (import.meta.env.VITE_BACKEND_URL) {
    return import.meta.env.VITE_BACKEND_URL;
  }

  // Then check window environment (for dynamic configuration)
  if (typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_BACKEND_URL) {
    return window.__ENV__.VITE_BACKEND_URL;
  }

  // Fallback to environment-configured port
  return `http://localhost:${import.meta.env.VITE_PORT || 5000}`;
};

const API_URL = `${getBackendUrl()}/api`;

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('userToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export default api;
