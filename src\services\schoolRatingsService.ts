// School Ratings Service
// This service handles fetching school ratings data from the SchoolDigger API

// Constants
const PROXY_ENDPOINT = 'https://digital-realtor-api.onrender.com/api/proxy/schooldigger';
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours

// Simple in-memory cache
const schoolCache = new Map<string, { timestamp: number; data: any }>();

// Types
export interface SchoolData {
  name: string;
  city: string;
  state: string;
}

export interface SchoolRatingsResponse {
  school_ratings: SchoolData[] | string;
}

/**
 * Fetches school ratings data for a given state and city
 * @param state Two-letter state code (e.g., 'IL')
 * @param city City name (e.g., 'Chicago')
 * @returns Promise with school ratings data or error message
 */
export const fetchSchoolRatings = async (state: string, city: string): Promise<SchoolRatingsResponse> => {
  try {
    // Validate inputs
    if (!state || state.length !== 2) {
      console.warn(`Invalid state code: "${state}". Using IL as fallback.`);
      state = 'IL';
    }

    if (!city || city.trim() === '') {
      console.warn(`Invalid city: "${city}". Using Chicago as fallback.`);
      city = 'Chicago';
    }

    // Ensure state is uppercase and city is properly formatted
    const formattedState = state.toUpperCase();
    const formattedCity = city.trim();

    // Check cache first
    const cacheKey = `${formattedState}_${formattedCity.toLowerCase()}`;
    const cachedEntry = schoolCache.get(cacheKey);

    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
      console.log('Using cached school ratings data');
      return { school_ratings: cachedEntry.data };
    }

    // Log request in development mode
    console.log('%c[DEV] Fetching school ratings data', 'color: #4299e1; font-weight: bold');
    console.log('State:', formattedState);
    console.log('City:', formattedCity);

    // Build the API URL
    const apiUrl = `${PROXY_ENDPOINT}?st=${formattedState}&q=${encodeURIComponent(formattedCity)}`;
    console.log('SchoolDigger API URL:', apiUrl);

    // Make the API request through our proxy
    const response = await fetch(apiUrl);

    // Log the response status
    console.log('SchoolDigger API response status:', response.status);

    if (!response.ok) {
      console.error('SchoolDigger API response not OK:', response.status);

      // Try to get more error details
      try {
        const errorText = await response.text();
        console.error('SchoolDigger API error details:', errorText);
      } catch (textError) {
        console.error('Could not read error response text');
      }

      return { school_ratings: 'Data not available for this area' };
    }

    const data = await response.json();

    // Log the response data for debugging
    console.log('SchoolDigger API response data:', data);

    // Process the response
    if (!data.schoolMatches || data.schoolMatches.length === 0) {
      console.warn('No schools found for this area');
      return { school_ratings: 'Data not available for this area' };
    }

    // Extract the top 5 schools
    const schools: SchoolData[] = data.schoolMatches
      .slice(0, 5)
      .map((school: any) => ({
        name: school.schoolName,
        city: school.city,
        state: school.state
      }));

    console.log('Extracted school data:', schools);

    // Cache the result
    schoolCache.set(cacheKey, {
      timestamp: Date.now(),
      data: schools
    });

    return { school_ratings: schools };
  } catch (error) {
    console.error('Error fetching school ratings:', error);

    // Return a fallback response
    return { school_ratings: 'Data not available for this area' };
  }
};

/**
 * Extracts the state code from an address
 * @param address Full address string
 * @returns Two-letter state code or empty string
 */
export const extractStateFromAddress = (address: string): string => {
  if (!address) return '';

  // Convert to uppercase for consistent matching
  const upperAddress = address.toUpperCase();

  // First try: Look for a two-letter state code followed by a zip code
  const stateZipMatch = upperAddress.match(/\b([A-Z]{2})\s+\d{5}(?:-\d{4})?\b/);
  if (stateZipMatch && stateZipMatch[1]) {
    return stateZipMatch[1];
  }

  // Second try: Look for common state abbreviations
  const stateAbbreviations = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
  ];

  for (const state of stateAbbreviations) {
    // Look for the state code as a standalone word
    if (upperAddress.match(new RegExp(`\\b${state}\\b`))) {
      return state;
    }
  }

  // Third try: Check for full state names and convert to abbreviations
  const stateNameMap: Record<string, string> = {
    'ALABAMA': 'AL', 'ALASKA': 'AK', 'ARIZONA': 'AZ', 'ARKANSAS': 'AR', 'CALIFORNIA': 'CA',
    'COLORADO': 'CO', 'CONNECTICUT': 'CT', 'DELAWARE': 'DE', 'FLORIDA': 'FL', 'GEORGIA': 'GA',
    'HAWAII': 'HI', 'IDAHO': 'ID', 'ILLINOIS': 'IL', 'INDIANA': 'IN', 'IOWA': 'IA',
    'KANSAS': 'KS', 'KENTUCKY': 'KY', 'LOUISIANA': 'LA', 'MAINE': 'ME', 'MARYLAND': 'MD',
    'MASSACHUSETTS': 'MA', 'MICHIGAN': 'MI', 'MINNESOTA': 'MN', 'MISSISSIPPI': 'MS', 'MISSOURI': 'MO',
    'MONTANA': 'MT', 'NEBRASKA': 'NE', 'NEVADA': 'NV', 'NEW HAMPSHIRE': 'NH', 'NEW JERSEY': 'NJ',
    'NEW MEXICO': 'NM', 'NEW YORK': 'NY', 'NORTH CAROLINA': 'NC', 'NORTH DAKOTA': 'ND', 'OHIO': 'OH',
    'OKLAHOMA': 'OK', 'OREGON': 'OR', 'PENNSYLVANIA': 'PA', 'RHODE ISLAND': 'RI', 'SOUTH CAROLINA': 'SC',
    'SOUTH DAKOTA': 'SD', 'TENNESSEE': 'TN', 'TEXAS': 'TX', 'UTAH': 'UT', 'VERMONT': 'VT',
    'VIRGINIA': 'VA', 'WASHINGTON': 'WA', 'WEST VIRGINIA': 'WV', 'WISCONSIN': 'WI', 'WYOMING': 'WY'
  };

  for (const [stateName, stateCode] of Object.entries(stateNameMap)) {
    if (upperAddress.includes(stateName)) {
      return stateCode;
    }
  }

  // Fallback: If we can't find a state, use 'IL' as default for testing
  console.warn('Could not extract state from address, using IL as default');
  return 'IL';
};

/**
 * Extracts the city from an address
 * @param address Full address string
 * @returns City name or empty string
 */
export const extractCityFromAddress = (address: string): string => {
  if (!address) return '';

  // First try: Look for city in a standard address format (Street, City, State ZIP)
  const parts = address.split(',');

  if (parts.length >= 2) {
    // The city is typically the second part in a comma-separated address
    const cityStatePart = parts[1].trim();

    // If the city part contains a state code, extract just the city
    const cityMatch = cityStatePart.match(/^([^0-9]+?)(?:\s+[A-Z]{2}\s*\d{5}|\s+[A-Z]{2}|$)/i);
    if (cityMatch && cityMatch[1]) {
      return cityMatch[1].trim();
    }

    // Otherwise, just return the first word as a simple approximation
    return cityStatePart.split(' ')[0];
  }

  // Second try: Look for common city names in the address
  const commonCities = [
    'Chicago', 'New York', 'Los Angeles', 'Houston', 'Phoenix', 'Philadelphia',
    'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
    'Fort Worth', 'Columbus', 'Indianapolis', 'Charlotte', 'San Francisco',
    'Seattle', 'Denver', 'Washington', 'Boston', 'Nashville', 'Baltimore',
    'Oklahoma City', 'Louisville', 'Portland', 'Las Vegas', 'Milwaukee',
    'Albuquerque', 'Tucson', 'Fresno', 'Sacramento', 'Long Beach', 'Kansas City',
    'Mesa', 'Atlanta', 'Colorado Springs', 'Raleigh', 'Omaha', 'Miami', 'Oakland',
    'Minneapolis', 'Tulsa', 'Cleveland', 'Wichita', 'Arlington', 'New Orleans'
  ];

  for (const city of commonCities) {
    if (address.includes(city)) {
      return city;
    }
  }

  // Fallback: If we can't find a city, use 'Chicago' as default for testing
  console.warn('Could not extract city from address, using Chicago as default');
  return 'Chicago';
};
