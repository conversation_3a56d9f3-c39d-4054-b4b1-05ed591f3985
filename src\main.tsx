import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import './user-menu.css'
import './print-styles.css'
import './inspection-print-styles.css'
import './calendar-styles.css'
import './validation-messages.css'
// Import react-toastify styles
import 'react-toastify/dist/ReactToastify.css'
import App from './App.tsx'
import { AppProvider } from './context/SimpleAppContext'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AppProvider>
      <App />
    </AppProvider>
  </StrictMode>,
)
