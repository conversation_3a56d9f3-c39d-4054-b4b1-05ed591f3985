import { defineGraph, StateGraph } from 'langchain/graphs';
import { RunnableSequence } from 'langchain/schema/runnable';
import { ChatOpenAI } from 'langchain/chat_models/openai';
import { ChatAnthropic } from 'langchain/chat_models/anthropic';
import { StructuredOutputParser } from 'langchain/output_parsers';
import { z } from 'zod';
import { PromptTemplate } from 'langchain/prompts';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define the schema for the neighborhood data
const neighborhoodDataSchema = z.object({
  schools: z.object({
    rating: z.string(),
    topSchools: z.array(z.string())
  }),
  crimeRate: z.string(),
  amenities: z.array(z.string()),
  walkScore: z.number()
});

// Define the schema for the compliance data
const complianceDataSchema = z.object({
  zoningType: z.string(),
  hoaRules: z.array(z.string()),
  permitsRequired: z.array(z.string()),
  alerts: z.array(z.string())
});

// Define the schema for the final report
const reportSchema = z.object({
  neighborhoodSummary: neighborhoodDataSchema,
  complianceSummary: complianceDataSchema,
  generatedAt: z.string()
});

export class NeighborhoodComplianceService {
  private openaiApiKey: string;
  private claudeApiKey: string;

  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
    this.claudeApiKey = process.env.CLAUDE_API_KEY || '************************************************************************************************************';
  }

  /**
   * Generate a neighborhood and compliance report using LangGraph
   */
  async generateReport(propertyAddress: string, zipCode: string, llmProvider: string): Promise<any> {
    try {
      // Create the LLM based on the provider
      const llm = this.createLLM(llmProvider);

      // Create the LangGraph workflow
      const workflow = await this.createWorkflow(llm, llmProvider);

      // Execute the workflow
      const result = await workflow.invoke({
        propertyAddress,
        zipCode: zipCode || this.extractZipCode(propertyAddress)
      });

      return result;
    } catch (error) {
      console.error('Error in neighborhood compliance service:', error);
      throw error;
    }
  }

  /**
   * Create the LLM based on the provider
   */
  private createLLM(provider: string) {
    if (provider === 'openai') {
      return new ChatOpenAI({
        modelName: 'gpt-4o',
        temperature: 0.2,
        openAIApiKey: this.openaiApiKey
      });
    } else {
      return new ChatAnthropic({
        modelName: 'claude-3-opus-20240229',
        temperature: 0.2,
        anthropicApiKey: this.claudeApiKey
      });
    }
  }

  /**
   * Create the LangGraph workflow
   */
  private async createWorkflow(llm: any, llmProvider: string) {
    // Create the data collector agent
    const dataCollectorAgent = this.createDataCollectorAgent(llm, llmProvider);

    // Create the compliance agent
    const complianceAgent = this.createComplianceAgent(llm, llmProvider);

    // Create the summarizer agent
    const summarizerAgent = this.createSummarizerAgent(llm, llmProvider);

    // Create the report generator agent
    const reportGeneratorAgent = this.createReportGeneratorAgent(llm, llmProvider);

    // Define the graph
    const graph = defineGraph({
      nodes: {
        dataCollector: dataCollectorAgent,
        complianceAgent: complianceAgent,
        summarizer: summarizerAgent,
        reportGenerator: reportGeneratorAgent
      },
      edges: {
        dataCollector: ['complianceAgent'],
        complianceAgent: ['summarizer'],
        summarizer: ['reportGenerator']
      }
    });

    // Compile the graph
    const workflow = new StateGraph({
      channels: graph.channels,
      nodes: graph.nodes
    })
      .addEdge('dataCollector', 'complianceAgent')
      .addEdge('complianceAgent', 'summarizer')
      .addEdge('summarizer', 'reportGenerator')
      .compile();

    return workflow;
  }

  /**
   * Create the data collector agent
   */
  private createDataCollectorAgent(llm: any, llmProvider: string) {
    // Define the prompt template for the data collector agent
    const promptTemplate = new PromptTemplate({
      template: `You are a Data Collector Agent responsible for gathering neighborhood data for a property.

      Property Address: {propertyAddress}
      Zip Code: {zipCode}

      Your task is to collect the following information:
      1. School information (ratings, top schools in the area)
      2. Crime rate statistics
      3. Local amenities (parks, restaurants, shopping, etc.)
      4. Walkability score

      Use the available data to provide accurate information. If specific data is not available, make reasonable estimates based on similar neighborhoods.

      Return the data in a structured format that can be used by other agents in the workflow.`,
      inputVariables: ['propertyAddress', 'zipCode']
    });

    // Create the output parser
    const outputParser = StructuredOutputParser.fromZodSchema(neighborhoodDataSchema);

    // Create the runnable sequence
    return RunnableSequence.from([
      promptTemplate,
      llm,
      outputParser
    ]);
  }

  /**
   * Create the compliance agent
   */
  private createComplianceAgent(llm: any, llmProvider: string) {
    // Define the prompt template for the compliance agent
    const promptTemplate = new PromptTemplate({
      template: `You are a Compliance Agent responsible for identifying zoning laws, HOA rules, and permit requirements for a property.

      Property Address: {propertyAddress}
      Zip Code: {zipCode}
      Neighborhood Data: {neighborhoodData}

      Your task is to:
      1. Identify the zoning type for this property
      2. List any HOA rules that might apply
      3. Identify permits that would be required for common property modifications
      4. Highlight any compliance alerts or issues that potential buyers should be aware of

      Use the available data to provide accurate information. If specific data is not available, make reasonable estimates based on similar properties in the area.

      Return the data in a structured format that can be used by other agents in the workflow.`,
      inputVariables: ['propertyAddress', 'zipCode', 'neighborhoodData']
    });

    // Create the output parser
    const outputParser = StructuredOutputParser.fromZodSchema(complianceDataSchema);

    // Create the runnable sequence
    return RunnableSequence.from([
      {
        propertyAddress: (input) => input.propertyAddress,
        zipCode: (input) => input.zipCode,
        neighborhoodData: (input) => JSON.stringify(input.dataCollector)
      },
      promptTemplate,
      llm,
      outputParser
    ]);
  }

  /**
   * Create the summarizer agent
   */
  private createSummarizerAgent(llm: any, llmProvider: string) {
    // Define the prompt for OpenAI
    const openaiPrompt = new PromptTemplate({
      template: `You are a Summarizer Agent responsible for analyzing and summarizing neighborhood and compliance data for a property.

      Property Address: {propertyAddress}
      Zip Code: {zipCode}
      Neighborhood Data: {neighborhoodData}
      Compliance Data: {complianceData}

      Your task is to:
      1. Analyze the neighborhood data and compliance information
      2. Identify key insights and potential issues
      3. Prepare a comprehensive summary that highlights the most important information

      Focus on providing actionable insights that would be valuable to a potential buyer or real estate agent.

      Return your analysis in a detailed, well-structured format.`,
      inputVariables: ['propertyAddress', 'zipCode', 'neighborhoodData', 'complianceData']
    });

    // Define the prompt for Claude
    const claudePrompt = new PromptTemplate({
      template: `You are assisting a homebuyer by summarizing neighborhood and property compliance information. Be clear, concise, and highlight any important risks.

      Property Address: {propertyAddress}
      Zip Code: {zipCode}

      Data:
      Neighborhood Information: {neighborhoodData}
      Compliance Information: {complianceData}

      Respond with a comprehensive analysis covering:
      - School quality and educational options
      - Safety overview and crime statistics
      - Local amenities and convenience factors
      - Walkability and transportation options
      - Zoning or HOA restrictions that may affect property use
      - Permit requirements for common modifications
      - Any compliance alerts or issues that require attention

      Focus on providing actionable insights that would be valuable to a potential buyer or real estate agent.`,
      inputVariables: ['propertyAddress', 'zipCode', 'neighborhoodData', 'complianceData']
    });

    // Select the appropriate prompt based on the LLM provider
    const promptTemplate = llmProvider === 'openai' ? openaiPrompt : claudePrompt;

    // Create the runnable sequence
    return RunnableSequence.from([
      {
        propertyAddress: (input) => input.propertyAddress,
        zipCode: (input) => input.zipCode,
        neighborhoodData: (input) => JSON.stringify(input.dataCollector),
        complianceData: (input) => JSON.stringify(input.complianceAgent)
      },
      promptTemplate,
      llm
    ]);
  }

  /**
   * Create the report generator agent
   */
  private createReportGeneratorAgent(llm: any, llmProvider: string) {
    // Define the prompt template for the report generator agent
    const promptTemplate = new PromptTemplate({
      template: `You are a Report Generator Agent responsible for creating the final neighborhood and compliance report.

      Property Address: {propertyAddress}
      Zip Code: {zipCode}
      Neighborhood Data: {neighborhoodData}
      Compliance Data: {complianceData}
      Summary: {summary}

      Your task is to:
      1. Format all the collected data into a comprehensive report
      2. Ensure the report is well-structured and easy to understand
      3. Include all relevant information from the neighborhood data, compliance data, and summary

      The report should be in a format that can be easily displayed on a website and exported to PDF.

      Return the report in the following JSON structure:
      {
        "neighborhoodSummary": {
          "schools": {
            "rating": "9/10",
            "topSchools": ["Lincoln High", "Washington Elementary"]
          },
          "crimeRate": "Low",
          "amenities": ["Parks", "Restaurants", "Public Transit"],
          "walkScore": 78
        },
        "complianceSummary": {
          "zoningType": "Residential R3",
          "hoaRules": ["No exterior modifications"],
          "permitsRequired": ["Driveway expansion"],
          "alerts": ["Flood zone designation"]
        },
        "generatedAt": "2024-04-24T16:00:00Z"
      }`,
      inputVariables: ['propertyAddress', 'zipCode', 'neighborhoodData', 'complianceData', 'summary']
    });

    // Create the output parser
    const outputParser = StructuredOutputParser.fromZodSchema(reportSchema);

    // Create the runnable sequence
    return RunnableSequence.from([
      {
        propertyAddress: (input) => input.propertyAddress,
        zipCode: (input) => input.zipCode,
        neighborhoodData: (input) => JSON.stringify(input.dataCollector),
        complianceData: (input) => JSON.stringify(input.complianceAgent),
        summary: (input) => input.summarizer
      },
      promptTemplate,
      llm,
      outputParser
    ]);
  }

  /**
   * Extract zip code from address
   */
  private extractZipCode(address: string): string {
    // Simple regex to extract US zip code (5 digits, optionally followed by dash and 4 more digits)
    const zipCodeMatch = address.match(/\b\d{5}(?:-\d{4})?\b/);
    return zipCodeMatch ? zipCodeMatch[0] : '';
  }
}
