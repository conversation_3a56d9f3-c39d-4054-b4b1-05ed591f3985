import { Link } from 'react-router-dom';
import { FaMapMarkerAlt, FaImage } from 'react-icons/fa';
import NeighborhoodButton from '../components/NeighborhoodButton';

const SimpleHome = () => {

  return (
    <div>
      {/* Hero Section */}
      <section className="relative min-h-[350px] md:min-h-[400px] flex items-center overflow-hidden pt-0 mt-0">
        {/* Background gradient */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-cyber opacity-75"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-primary-900/90 to-secondary-900/90 shadow-lg"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-dark-900/95 via-dark-900/50 to-dark-900/70 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-accent-cyan/20 to-transparent opacity-70 mix-blend-overlay"></div>

          {/* Animated particles */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className="absolute rounded-full bg-accent-cyan/30 animate-float"
                style={{
                  width: `${Math.random() * 12 + 4}px`,
                  height: `${Math.random() * 12 + 4}px`,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animationDuration: `${Math.random() * 15 + 10}s`,
                  animationDelay: `${Math.random() * 5}s`,
                  opacity: Math.random() * 0.5 + 0.2
                }}
              ></div>
            ))}
          </div>

          {/* Grid overlay */}
          <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
        </div>

        {/* Content */}
        <div className="container mx-auto px-6 z-10 pt-0 mt-0 relative">
          <div className="max-w-3xl mx-auto md:mx-auto animate-slide-in-up">
            <div className="inline-block mb-3 bg-dark-800/50 backdrop-blur-md px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">AI-Powered Real Estate Platform</span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-3 leading-tight">
              <span className="block">Find Your Dream Home</span>
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent-cyan to-secondary-400 neon-text drop-shadow-glow">With AI Assistance</span>
            </h1>

            <p className="text-lg md:text-xl text-white/90 mb-4 max-w-2xl leading-relaxed">
              Our <span className="text-accent-cyan font-semibold">advanced AI</span> helps you discover the perfect property tailored to your unique needs and preferences.
            </p>

            {/* AI Tools Buttons */}
            <div className="inline-block mb-4 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">AI-Powered Tools</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <Link
                to="/direct-inspection"
                className="glass-card relative overflow-hidden group p-4 border border-accent-cyan/20 rounded-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-glow-accent hover:bg-accent-cyan/15 hover:border-accent-cyan/40"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-5 h-5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  </div>
                  <div className="flex items-center justify-center">
                    <svg className="w-4 h-4 mr-1.5 text-accent-cyan group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                    <span className="text-white font-medium group-hover:text-accent-cyan transition-colors relative z-10 group-hover:font-semibold">Inspection Report Generator</span>
                  </div>
                </div>
              </Link>
              <Link
                to="/neighborhood-compliance"
                className="glass-card relative overflow-hidden group p-4 border border-accent-cyan/20 rounded-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-glow-cyan hover:bg-accent-cyan/15 hover:border-accent-cyan/40"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <FaMapMarkerAlt className="w-5 h-5 group-hover:animate-pulse" />
                  </div>
                  <div className="flex items-center justify-center">
                    <FaMapMarkerAlt className="w-4 h-4 mr-1.5 text-accent-cyan group-hover:scale-110 transition-transform duration-300" />
                    <span className="text-white font-medium group-hover:text-accent-cyan transition-colors relative z-10 group-hover:font-semibold">Neighborhood & Compliance</span>
                  </div>
                </div>
              </Link>

              <Link
                to="/listing-generator"
                className="glass-card relative overflow-hidden group p-4 border border-accent-cyan/20 rounded-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-glow-cyan hover:bg-accent-cyan/15 hover:border-accent-cyan/40"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-5 h-5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="flex items-center justify-center">
                    <svg className="w-4 h-4 mr-1.5 text-accent-cyan group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="text-white font-medium group-hover:text-accent-cyan transition-colors relative z-10 group-hover:font-semibold">AI Listing Generator</span>
                  </div>
                </div>
              </Link>
              <Link
                to="/comp-analyzer"
                className="glass-card relative overflow-hidden group p-4 border border-secondary-400/20 rounded-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-glow-secondary hover:bg-secondary-400/15 hover:border-secondary-400/40"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-secondary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-secondary-400 to-secondary-600 flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-5 h-5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                  </div>
                  <div className="flex items-center justify-center">
                    <svg className="w-4 h-4 mr-1.5 text-secondary-400 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                    <span className="text-white font-medium group-hover:text-secondary-400 transition-colors relative z-10 group-hover:font-semibold">Comp Analyzer</span>
                  </div>
                </div>
              </Link>
              <Link
                to="/appointment-scheduler"
                className="glass-card relative overflow-hidden group p-4 border border-primary-400/20 rounded-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-glow-tertiary hover:bg-primary-400/15 hover:border-primary-400/40"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-primary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-5 h-5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="flex items-center justify-center">
                    <svg className="w-4 h-4 mr-1.5 text-primary-400 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-white font-medium group-hover:text-primary-400 transition-colors relative z-10 group-hover:font-semibold">Appointment Scheduler</span>
                  </div>
                </div>
              </Link>

              <Link
                to="/virtual-staging"
                className="glass-card relative overflow-hidden group p-4 border border-secondary-400/20 rounded-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-glow-secondary hover:bg-secondary-400/15 hover:border-secondary-400/40"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-secondary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-secondary-400 to-secondary-600 flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <FaImage className="w-5 h-5 group-hover:animate-pulse" />
                  </div>
                  <div className="flex items-center justify-center">
                    <FaImage className="w-4 h-4 mr-1.5 text-secondary-400 group-hover:scale-110 transition-transform duration-300" />
                    <span className="text-white font-medium group-hover:text-secondary-400 transition-colors relative z-10 group-hover:font-semibold">Virtual Staging</span>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          {/* Floating elements */}
          <div className="absolute top-1/4 right-10 w-32 h-32 bg-accent-cyan/5 rounded-full blur-3xl animate-pulse-slow hidden lg:block"></div>
          <div className="absolute bottom-1/4 right-1/4 w-40 h-40 bg-secondary-400/5 rounded-full blur-3xl animate-pulse-slow hidden lg:block"></div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-gradient-tech relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-10 animate-fade-in">
            <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">Cutting-Edge Technology</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 neon-text">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">AI-Powered Real Estate</span>
            </h2>

            <p className="text-lg text-primary-100 max-w-2xl mx-auto leading-relaxed">
              Our platform combines <span className="text-accent-cyan font-medium">cutting-edge AI technology</span> with real estate expertise to provide an unparalleled experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="glass-card p-8 transition-all duration-500 group animate-fade-in border-glow relative overflow-hidden hover:-translate-y-2">
              <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-primary-700 to-primary-900 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300 neon-glow relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-primary-100 mb-3 group-hover:text-accent-cyan transition-colors relative z-10">Smart Property Matching</h3>

              <p className="text-primary-200 mb-6 leading-relaxed relative z-10">
                Our <span className="text-accent-cyan font-medium">AI neural network</span> analyzes your preferences and needs to match you with properties that truly fit your lifestyle.
              </p>

              <div className="flex justify-between items-center relative z-10">
                <Link to="/properties" className="text-accent-cyan text-sm flex items-center group-hover:translate-x-1 transition-all duration-300">
                  <span>Explore properties</span>
                  <svg className="w-4 h-4 ml-1 group-hover:ml-2 transition-all duration-300" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>

                <div className="w-8 h-8 rounded-full bg-dark-800/80 border border-primary-700/50 flex items-center justify-center text-accent-cyan">
                  <span className="text-xs font-medium">01</span>
                </div>
              </div>
            </div>

            <div className="glass-card p-8 transition-all duration-500 group animate-fade-in delay-100 border-glow relative overflow-hidden hover:-translate-y-2">
              <div className="absolute top-0 right-0 w-32 h-32 bg-secondary-400/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-secondary-700 to-secondary-900 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300 neon-glow relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-primary-100 mb-3 group-hover:text-secondary-400 transition-colors relative z-10">AI Assistant</h3>

              <p className="text-primary-200 mb-6 leading-relaxed relative z-10">
                Get <span className="text-secondary-400 font-medium">instant answers</span> to your questions and personalized guidance throughout your real estate journey.
              </p>

              <div className="flex justify-between items-center relative z-10">
                <Link to="/login" className="text-secondary-400 text-sm flex items-center group-hover:translate-x-1 transition-all duration-300">
                  <span>Try the assistant</span>
                  <svg className="w-4 h-4 ml-1 group-hover:ml-2 transition-all duration-300" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>

                <div className="w-8 h-8 rounded-full bg-dark-800/80 border border-secondary-700/50 flex items-center justify-center text-secondary-400">
                  <span className="text-xs font-medium">02</span>
                </div>
              </div>
            </div>

            <div className="glass-card p-8 transition-all duration-500 group animate-fade-in delay-200 border-glow relative overflow-hidden hover:-translate-y-2">
              <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-primary-700 to-primary-900 w-16 h-16 rounded-full flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300 neon-glow relative z-10">
                <svg className="w-7 h-7" width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>

              <h3 className="text-xl font-bold text-primary-100 mb-3 group-hover:text-accent-cyan transition-colors relative z-10">Market Insights</h3>

              <p className="text-primary-200 mb-6 leading-relaxed relative z-10">
                Access <span className="text-accent-cyan font-medium">data-driven insights</span> on property values, neighborhood trends, and investment potential.
              </p>

              <div className="flex justify-between items-center relative z-10">
                <Link to="/login" className="text-accent-cyan text-sm flex items-center group-hover:translate-x-1 transition-all duration-300">
                  <span>View insights</span>
                  <svg className="w-4 h-4 ml-1 group-hover:ml-2 transition-all duration-300" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>

                <div className="w-8 h-8 rounded-full bg-dark-800/80 border border-primary-700/50 flex items-center justify-center text-accent-cyan">
                  <span className="text-xs font-medium">03</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Agent Tools Promo */}
      <section className="py-14 bg-gradient-neon text-white relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-accent-cyan/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
                <span className="text-accent-cyan text-sm font-medium">For Professionals</span>
              </div>

              <h2 className="text-4xl font-bold mb-6 neon-text">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Are You a Real Estate Agent?</span>
              </h2>

              <p className="text-white/90 mb-8 text-lg leading-relaxed max-w-xl">
                Access our suite of <span className="text-accent-cyan font-medium">AI-powered tools</span> designed specifically for real estate professionals. Generate listings, schedule appointments, analyze properties, and more with the power of AI.
              </p>

              <Link
                to="/login"
                className="tertiary-button button-rounded icon-button group"
              >
                <svg className="button-icon group-hover:animate-pulse" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Access Agent Tools
              </Link>
            </div>

            <div className="grid grid-cols-2 gap-6 animate-fade-in delay-100">
              <Link to="/listing-generator" className="glass-card p-5 transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                <h3 className="font-bold mb-2 text-accent-cyan group-hover:text-white transition-colors relative z-10 flex items-center">
                  <svg className="w-4 h-4 mr-1.5 text-accent-cyan" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  AI Listing Generator
                </h3>

                <p className="text-primary-200 text-sm leading-relaxed relative z-10">
                  Create compelling property listings with <span className="text-accent-cyan font-medium">AI-generated</span> descriptions.
                </p>


              </Link>

              <div className="glass-card p-5 transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-secondary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                <h3 className="font-bold mb-2 text-secondary-400 group-hover:text-white transition-colors relative z-10 flex items-center">
                  <svg className="w-4 h-4 mr-1.5 text-secondary-400" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Appointment Scheduler
                </h3>

                <p className="text-primary-200 text-sm leading-relaxed relative z-10">
                  Get <span className="text-secondary-400 font-medium">AI recommendations</span> for scheduling client appointments.
                </p>
              </div>

              <div className="glass-card p-5 transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                <h3 className="font-bold mb-2 text-accent-cyan group-hover:text-white transition-colors relative z-10 flex items-center">
                  <svg className="w-4 h-4 mr-1.5 text-accent-cyan" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                  </svg>
                  Competitive Analysis
                </h3>

                <p className="text-primary-200 text-sm leading-relaxed relative z-10">
                  Generate detailed market analysis and <span className="text-accent-cyan font-medium">property comparisons</span>.
                </p>
              </div>

              <div className="glass-card p-5 transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-secondary-400/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                <h3 className="font-bold mb-2 text-secondary-400 group-hover:text-white transition-colors relative z-10 flex items-center">
                  <svg className="w-4 h-4 mr-1.5 text-secondary-400" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                  Inspection Report Generator
                </h3>

                <p className="text-primary-200 text-sm leading-relaxed relative z-10">
                  Analyze inspection findings with <span className="text-secondary-400 font-medium">AI assistance</span>.
                </p>
              </div>

              <Link to="/virtual-staging" className="glass-card p-5 transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-accent-cyan/10 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                <h3 className="font-bold mb-2 text-accent-cyan group-hover:text-white transition-colors relative z-10 flex items-center">
                  <FaImage className="w-4 h-4 mr-1.5 text-accent-cyan" />
                  Virtual Staging Suggestions
                </h3>

                <p className="text-primary-200 text-sm leading-relaxed relative z-10">
                  Get <span className="text-accent-cyan font-medium">AI-powered</span> staging recommendations for empty rooms.
                </p>
              </Link>


            </div>
          </div>
        </div>
      </section>

      {/* AI Listing Generator Promo */}
      <section className="py-12 bg-gradient-to-br from-dark-900 to-primary-900">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="md:w-1/2 animate-fade-in">
              <div className="glass-card p-6 border-glow relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/10 rounded-full blur-3xl"></div>
                <h2 className="text-3xl font-bold text-white mb-4 relative z-10">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">AI-Powered Listing Generator</span>
                </h2>
                <p className="text-primary-200 mb-6 relative z-10">
                  Create professional, compelling property listings in seconds with our advanced agentic AI technology. Save time and impress clients with engaging, optimized content.
                </p>
                <div className="flex flex-wrap gap-4 mb-6 relative z-10">
                  <div className="bg-dark-800/50 px-3 py-1.5 rounded-full border border-accent-cyan/30 text-accent-cyan text-xs">
                    Agentic AI Technology
                  </div>
                  <div className="bg-dark-800/50 px-3 py-1.5 rounded-full border border-accent-cyan/30 text-accent-cyan text-xs">
                    Professional Listings
                  </div>
                  <div className="bg-dark-800/50 px-3 py-1.5 rounded-full border border-accent-cyan/30 text-accent-cyan text-xs">
                    Time-Saving
                  </div>
                </div>
                <Link
                  to="/listing-generator"
                  className="primary-button button-rounded icon-button relative z-10"
                >
                  <svg className="button-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Try Listing Generator
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 animate-fade-in delay-100">
              <div className="bg-dark-800/30 p-6 rounded-xl border border-primary-700/30 text-primary-100 relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-10"></div>
                <h3 className="text-xl font-bold text-accent-cyan mb-4">Sample Generated Listing</h3>
                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-white">Stunning 4-Bedroom Modern Home in Westlake Hills</h4>
                  <p className="text-sm text-primary-200">This exceptional property offers 4 spacious bedrooms and 3 elegant bathrooms within approximately 2,800 square feet of thoughtfully designed living space. Built in 2019, this home showcases premium hardwood floors, smart home technology, and an open concept kitchen.</p>
                  <div className="flex flex-wrap gap-2 text-xs">
                    <span className="bg-primary-800/50 px-2 py-1 rounded text-primary-300">4 Bedrooms</span>
                    <span className="bg-primary-800/50 px-2 py-1 rounded text-primary-300">3 Bathrooms</span>
                    <span className="bg-primary-800/50 px-2 py-1 rounded text-primary-300">2,800 sq ft</span>
                    <span className="bg-primary-800/50 px-2 py-1 rounded text-primary-300">Built in 2019</span>
                  </div>
                </div>
                <div className="mt-4 text-xs text-accent-cyan flex items-center">
                  <span>Generated in seconds with AI</span>
                  <svg className="w-3 h-3 ml-1 animate-pulse" width="12" height="12" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-14 bg-gradient-tech relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 text-center animate-fade-in relative z-10">
          <div className="glass-card p-10 border-glow relative overflow-hidden max-w-4xl mx-auto">
            <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
                <span className="text-accent-cyan text-sm font-medium">Start Your Journey</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 neon-text">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Ready to Find Your Dream Home?</span>
              </h2>

              <p className="text-lg text-primary-100 max-w-2xl mx-auto mb-8 leading-relaxed">
                Start your journey today with our <span className="text-accent-cyan font-medium">AI-powered platform</span> and discover properties that truly match your lifestyle.
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link
                  to="/properties"
                  className="primary-button button-lg button-rounded icon-button group"
                >
                  <svg className="button-icon group-hover:animate-pulse" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                  Get Started Now
                </Link>

                <Link
                  to="/login"
                  className="secondary-button button-lg button-rounded icon-button group"
                >
                  <svg className="button-icon group-hover:animate-pulse" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  Sign In
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SimpleHome;
