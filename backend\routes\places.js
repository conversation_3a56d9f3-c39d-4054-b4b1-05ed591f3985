const express = require('express');
const router = express.Router();
const axios = require('axios');

// Get Google API key from environment variables
// Use fallback key for now to avoid referrer restrictions
const GOOGLE_API_KEY = 'AIzaSyArpDdafA_tfQsn7soo1FuZijvvy5hFf78';

// Autocomplete endpoint
router.get('/autocomplete', async (req, res) => {
  const { input } = req.query;

  console.log('Received autocomplete request for input:', input);
  console.log('Google API Key available:', GOOGLE_API_KEY ? 'Yes' : 'No');

  if (!input) {
    console.log('Error: Missing input query parameter');
    return res.status(400).json({ error: 'Missing input query parameter' });
  }

  try {
    console.log('Making request to Google Places API...');
    console.log('Using API key:', GOOGLE_API_KEY ? `${GOOGLE_API_KEY.substring(0, 10)}...` : 'None');

    const response = await axios.get('https://maps.googleapis.com/maps/api/place/autocomplete/json', {
      params: {
        input,
        types: 'address',
        key: GOOGLE_API_KEY
      }
    });

    console.log('Google Places API response status:', response.data.status);
    console.log('Number of predictions:', response.data.predictions?.length || 0);

    if (response.data.status !== 'OK') {
      console.error('Google Places API error details:', response.data);
      if (response.data.error_message) {
        console.error('Error message:', response.data.error_message);
      }
    }

    res.json(response.data);
  } catch (error) {
    console.error('Places API Error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to fetch suggestions' });
  }
});

// Geocoding endpoint
router.get('/geocode', async (req, res) => {
  const { address } = req.query;

  if (!address) {
    return res.status(400).json({ error: 'Missing address query parameter' });
  }

  try {
    const response = await axios.get(
      'https://maps.googleapis.com/maps/api/geocode/json',
      {
        params: {
          address,
          key: GOOGLE_API_KEY
        }
      }
    );

    const result = response.data.results[0];
    if (!result) return res.status(404).json({ error: 'No coordinates found' });

    const { lat, lng } = result.geometry.location;
    res.json({ lat, lng, formatted_address: result.formatted_address });
  } catch (error) {
    console.error('Geocoding API Error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to fetch coordinates' });
  }
});

// Place details endpoint
router.get('/details', async (req, res) => {
  const { placeId } = req.query;

  if (!placeId) {
    return res.status(400).json({ error: 'Missing placeId query parameter' });
  }

  try {
    const response = await axios.get(
      'https://maps.googleapis.com/maps/api/place/details/json',
      {
        params: {
          place_id: placeId,
          fields: 'address_component,formatted_address,geometry,name',
          key: GOOGLE_API_KEY
        }
      }
    );

    if (!response.data.result) {
      return res.status(404).json({ error: 'Place details not found' });
    }

    res.json(response.data.result);
  } catch (error) {
    console.error('Place Details API Error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to fetch place details' });
  }
});

module.exports = router;
