import { PDFDocument } from 'pdf-lib';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function listPdfFormFields() {
  try {
    // Path to the contract template PDF
    const pdfPath = path.join(__dirname, '..', 'contract-template.pdf.pdf');

    // Check if the file exists
    if (!fs.existsSync(pdfPath)) {
      console.error(`PDF file not found at: ${pdfPath}`);
      return;
    }

    // Read the PDF file
    const pdfBytes = fs.readFileSync(pdfPath);

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Get the form from the PDF
    const form = pdfDoc.getForm();

    // Get all fields from the form
    const fields = form.getFields();

    console.log(`Total fields found: ${fields.length}`);

    // Map field details
    const fieldDetails = fields.map(field => {
      return `Field Name: ${field.getName()} | Type: ${field.constructor.name}`;
    });

    // Output to console
    console.log("=== PDF Form Fields ===");
    fieldDetails.forEach(detail => console.log(detail));

    // Create a simple array of field names for LLM input
    const fieldNames = fields.map(field => field.getName());
    console.log('\nField names for LLM input:');
    console.log(fieldNames.join(' '));

    // Create directory if it doesn't exist
    const utilsDir = path.join(__dirname, '..', 'src', 'utils');
    if (!fs.existsSync(utilsDir)) {
      fs.mkdirSync(utilsDir, { recursive: true });
    }

    // Save the field names to a JSON file
    const outputPath = path.join(utilsDir, 'pdfFieldNames.json');
    fs.writeFileSync(outputPath, JSON.stringify(fieldNames, null, 2));
    console.log(`\nField names saved to: ${outputPath}`);

    // Generate a field mapping template
    const fieldMap = {};
    fieldNames.forEach(name => {
      fieldMap[name] = '';
    });

    const fieldMapPath = path.join(utilsDir, 'fieldMapTemplate.json');
    fs.writeFileSync(fieldMapPath, JSON.stringify(fieldMap, null, 2));
    console.log(`Field map template saved to: ${fieldMapPath}`);

    // Write to file
    const textOutputPath = path.join(__dirname, 'pdf_fields_list.txt');
    fs.writeFileSync(textOutputPath, fieldDetails.join('\n'));

    console.log(`\nField list has been written to: ${textOutputPath}`);
  } catch (error) {
    console.error('Error extracting PDF fields:', error);
  }
}

// Run the function
listPdfFormFields();
