import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
// @ts-ignore - Import HMR plugin without TypeScript errors
import hmrPlugin from './vite-hmr-plugin'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')

  const isProd = mode === 'production'
  const isNetlify = !!process.env.NETLIFY_BUILD

  return {
    define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || mode),
      // Make sure environment variables are properly exposed
      'import.meta.env.VITE_OPENAI_API_KEY': JSON.stringify(process.env.VITE_OPENAI_API_KEY),
      'import.meta.env.VITE_CLAUDE_API_KEY': JSON.stringify(process.env.VITE_CLAUDE_API_KEY),
      'import.meta.env.VITE_GOOGLE_API_KEY': JSON.stringify(process.env.VITE_GOOGLE_API_KEY),
      // Add Microsoft Clarity ID
      'import.meta.env.VITE_CLARITY_ID': JSON.stringify('r7yjq54tfv')
    },
    plugins: [
      react({
        // Only apply minimal transformations for production
        babel: isProd ? {
          // Avoid console logs in production
          plugins: [
            'babel-plugin-transform-remove-console'
          ]
        } : {}
      }),
      // Only use HMR plugin in development
      !isProd && hmrPlugin()
    ].filter(Boolean),
    server: {
      hmr: {
        protocol: process.env.VITE_HMR_PROTOCOL || 'ws',
        host: process.env.VITE_HMR_HOST || 'localhost',
        port: 5175,
      },
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    build: {
      outDir: isNetlify ? 'prod-build' : 'build',
      minify: 'terser',
      sourcemap: false,
      terserOptions: {
        compress: {
          drop_console: isProd,
          drop_debugger: isProd
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            ui: ['react-icons'],
            forms: ['react-hook-form', '@hookform/resolvers', 'zod'],
            calendar: ['react-big-calendar', 'react-calendar', 'date-fns']
          },
          // Use hashed filenames for better caching
          entryFileNames: isProd ? 'assets/[name].[hash].js' : 'assets/[name].js',
          chunkFileNames: isProd ? 'assets/[name].[hash].js' : 'assets/[name].js',
          assetFileNames: isProd ? 'assets/[name].[hash].[ext]' : 'assets/[name].[ext]'
        }
      },
      // Ensure we don't exceed the free tier limits for Netlify
      chunkSizeWarningLimit: 1000, // 1000 kB
      assetsInlineLimit: 4096, // 4 kB
      // Generate a report of the bundle size
      reportCompressedSize: true
    },
    base: './',
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'react-icons',
        'react-hook-form',
        'zod',
        'date-fns'
      ]
    },
    // Add preview server configuration
    preview: {
      port: 5173,
      open: true
    }
  }
})
