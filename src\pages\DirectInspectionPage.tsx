import React, { useState, useRef } from 'react';
import { FaClipboardCheck, FaHome, FaCalendarAlt, FaUser, FaUserTie, FaBuilding, FaRulerCombined, FaFileAlt, FaFilePdf, FaSpinner, FaImage, FaClipboard, FaRobot } from 'react-icons/fa';
import { generateInspectionReport, analyzeImages, InspectionReportData } from '../services/inspectionReportService';
import { generateInspectionReportWithClaudeBackend, analyzeImagesWithClaudeBackend } from '../services/backendService';
import { generateInspectionChecklist } from '../services/checklistService';
import ImageUploader from '../components/ImageUploader';
import InspectionChecklist from '../components/InspectionChecklist';
import { jsPDF } from 'jspdf';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const DirectInspectionPage = () => {
  const reportRef = useRef<HTMLDivElement>(null);

  const [formData, setFormData] = useState({
    propertyAddress: '',
    inspectionDate: new Date().toISOString().split('T')[0],
    inspectorName: '',
    clientName: '',
    propertyType: '',
    yearBuilt: '',
    squareFootage: '',
    generalNotes: '',
    images: [] as string[],
    imageAnalysis: '',
    llmProvider: 'openai' // Default to OpenAI
  });

  const [showPreview, setShowPreview] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isAnalyzingImages, setIsAnalyzingImages] = useState(false);
  const [isGeneratingChecklist, setIsGeneratingChecklist] = useState(false);
  const [generatedReport, setGeneratedReport] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [showChecklist, setShowChecklist] = useState(false);
  const [checklistItems, setChecklistItems] = useState<Array<{
    id: string;
    category: string;
    item: string;
    completed: boolean;
    relevantToReport: boolean;
  }>>([]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImagesUploaded = async (images: string[]) => {
    setFormData(prev => ({
      ...prev,
      images
    }));

    // If images were removed, clear the analysis
    if (images.length === 0) {
      setFormData(prev => ({
        ...prev,
        imageAnalysis: ''
      }));
      return;
    }

    // Don't analyze if there are no images
    if (images.length === 0) return;

    try {
      setIsAnalyzingImages(true);
      setError(null);
      setStatusMessage(`Analyzing images with ${formData.llmProvider === 'openai' ? 'OpenAI' : 'Claude'}...`);

      // Get the appropriate API key based on the selected LLM provider
      let apiKey;
      let analysis;

      if (formData.llmProvider === 'openai') {
        // Use OpenAI API key
        apiKey = window.__ENV__?.VITE_OPENAI_API_KEY || '********************************************************************************************************************************************************************';
        // Analyze the images with OpenAI
        if (import.meta.env.DEV) console.log('%c[DEV] Using OpenAI for image analysis', 'color: #10a37f; font-weight: bold');
        analysis = await analyzeImages(apiKey, images);
      } else {
        // Use Claude via backend API
        // No API key needed as it's stored securely on the backend
        if (import.meta.env.DEV) console.log('%c[DEV] Using Claude for image analysis', 'color: #a27aff; font-weight: bold');
        analysis = await analyzeImagesWithClaudeBackend(images);
      }

      // Update the form data with the analysis
      setFormData(prev => ({
        ...prev,
        imageAnalysis: analysis
      }));

      setStatusMessage('Images analyzed successfully!');
    } catch (error) {
      console.error('Error analyzing images:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while analyzing the images. Please try again.');
    } finally {
      setIsAnalyzingImages(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate form data
    if (!formData.propertyAddress || !formData.inspectionDate || !formData.inspectorName || !formData.clientName) {
      setError('Please fill in all required fields');
      return;
    }

    // Reset states
    setError(null);
    setStatusMessage(null);
    setIsGenerating(true);
    setGeneratedReport(null);

    try {
      // Get the appropriate API key based on the selected LLM provider
      let apiKey;
      let report;

      // Set up console log interceptor to capture status messages
      const originalConsoleLog = console.log;
      console.log = function(...args) {
        originalConsoleLog.apply(console, args);
        const message = args.join(' ');

        if (message.includes('Using cached')) {
          setStatusMessage('Using cached report to optimize API usage');
        } else if (message.includes('Rate limiting')) {
          setStatusMessage(`Rate limiting in effect: Waiting before sending request`);
        } else if (message.includes('Rate limit hit')) {
          setStatusMessage(`Rate limit reached. Will retry once...`);
        } else if (message.includes('Rate limit error (429)')) {
          setStatusMessage(`API rate limit reached. Will try one more time...`);
        } else if (message.includes('Server requested retry after')) {
          const seconds = message.match(/\d+(\.\d+)?/)?.[0] || '?';
          setStatusMessage(`Server requested we wait ${seconds} seconds before retrying. Please be patient...`);
        } else if (message.includes('Using a 10-second delay')) {
          setStatusMessage(`Waiting 10 seconds before retrying request...`);
        } else if (message.includes('Retrying in')) {
          const seconds = message.match(/\d+(\.\d+)?/)?.[0] || '?';
          setStatusMessage(`Waiting ${seconds} seconds before retrying request...`);
        }
      };

      if (formData.llmProvider === 'openai') {
        // Use OpenAI API key
        apiKey = window.__ENV__?.VITE_OPENAI_API_KEY || '********************************************************************************************************************************************************************';
        // Generate the inspection report using OpenAI
        setStatusMessage('Generating report with OpenAI...');
        if (import.meta.env.DEV) {
          console.log('%c[DEV] Using OpenAI for report generation', 'color: #10a37f; font-weight: bold');
          console.log('Image analysis included:', formData.imageAnalysis ? 'Yes' : 'No');
        }
        report = await generateInspectionReport(apiKey, formData as InspectionReportData);
      } else {
        // Use Claude via backend API
        // No API key needed as it's stored securely on the backend
        setStatusMessage('Generating report with Claude...');
        if (import.meta.env.DEV) {
          console.log('%c[DEV] Using Claude for report generation', 'color: #a27aff; font-weight: bold');
          console.log('Image analysis included:', formData.imageAnalysis ? 'Yes' : 'No');
          console.log('Making request to backend endpoint: /api/claude/generate-report');
        }
        report = await generateInspectionReportWithClaudeBackend(formData as InspectionReportData);
      }

      // Restore original console.log
      console.log = originalConsoleLog;

      // Set the generated report
      setGeneratedReport(report);

      // Show the preview
      setShowPreview(true);

    } catch (error) {
      console.error('Error generating inspection report:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while generating the report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleReset = () => {
    // Reset form to initial state
    setFormData({
      propertyAddress: '',
      inspectionDate: new Date().toISOString().split('T')[0],
      inspectorName: '',
      clientName: '',
      propertyType: '',
      yearBuilt: '',
      squareFootage: '',
      generalNotes: '',
      images: [],
      imageAnalysis: '',
      llmProvider: 'openai' // Reset to OpenAI
    });
    setError(null);
    setStatusMessage(null);
    setChecklistItems([]);
    setShowChecklist(false);
  };

  const handleGenerateChecklist = async () => {
    if (!generatedReport || !formData.generalNotes) {
      setError('Please generate a report first before creating a checklist.');
      return;
    }

    try {
      setIsGeneratingChecklist(true);
      setError(null);
      setStatusMessage('Generating inspection checklist...');

      // Get the appropriate API key based on the selected LLM provider
      let apiKey;

      if (formData.llmProvider === 'openai') {
        // Use OpenAI API key
        apiKey = window.__ENV__?.VITE_OPENAI_API_KEY || '********************************************************************************************************************************************************************';
      } else {
        // Use Claude API key - but we'll still use OpenAI for the checklist generation
        // as we haven't implemented a Claude version of this function
        apiKey = window.__ENV__?.VITE_OPENAI_API_KEY || '********************************************************************************************************************************************************************';
        setStatusMessage('Note: Using OpenAI for checklist generation regardless of LLM selection');
      }

      // Generate the checklist
      const checklist = await generateInspectionChecklist(apiKey, formData.generalNotes);

      // Set the checklist items
      setChecklistItems(checklist);

      // Show the checklist
      setShowChecklist(true);
      setStatusMessage('Checklist generated successfully!');
    } catch (error) {
      console.error('Error generating checklist:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while generating the checklist. Please try again.');
    } finally {
      setIsGeneratingChecklist(false);
    }
  };

  const handleToggleChecklistItem = (id: string) => {
    setChecklistItems(prevItems =>
      prevItems.map(item =>
        item.id === id ? { ...item, completed: !item.completed } : item
      )
    );
  };

  const exportToPDF = () => {
    if (!reportRef.current) return;

    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set title
    const title = `Property Inspection Report - ${formData.propertyAddress}`;
    doc.setFontSize(18);
    doc.text(title, 105, 20, { align: 'center' });

    // Add inspection date
    doc.setFontSize(12);
    doc.text(`Inspection Date: ${new Date(formData.inspectionDate).toLocaleDateString()}`, 105, 30, { align: 'center' });

    // Add property information
    doc.setFontSize(14);
    doc.text('Property Information', 20, 45);
    doc.setFontSize(10);
    doc.text(`Address: ${formData.propertyAddress}`, 20, 55);
    doc.text(`Property Type: ${formData.propertyType || 'N/A'}`, 20, 60);
    doc.text(`Year Built: ${formData.yearBuilt || 'N/A'}`, 20, 65);
    doc.text(`Square Footage: ${formData.squareFootage || 'N/A'}`, 20, 70);
    doc.text(`Client: ${formData.clientName}`, 20, 75);
    doc.text(`Inspector: ${formData.inspectorName}`, 20, 80);

    // Add report content
    if (generatedReport) {
      doc.setFontSize(14);
      doc.text('Inspection Report', 20, 95);

      // Convert markdown to plain text for PDF
      const plainText = generatedReport.replace(/#+\s(.+)/g, '$1')
        .replace(/\*\*(.+?)\*\*/g, '$1')
        .replace(/\*(.+?)\*/g, '$1')
        .replace(/\[(.+?)\]\(.+?\)/g, '$1')
        .replace(/^\s*[-*+]\s+/gm, '• ');

      // Split text into lines and add to PDF with proper line breaks
      const textLines = doc.splitTextToSize(plainText, 170);
      doc.setFontSize(10);

      let yPos = 100;
      const lineHeight = 5;

      textLines.forEach((line: string) => {
        // Add a new page if we're near the bottom
        if (yPos > 280) {
          doc.addPage();
          yPos = 20;
        }

        doc.text(line, 20, yPos);
        yPos += lineHeight;
      });
    }

    // Add footer
    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Generated by Digital Realtor Inspection Tool - Page ${i} of ${totalPages}`, 105, 290, { align: 'center' });
    }

    // Save the PDF
    doc.save(`Inspection_Report_${formData.propertyAddress.replace(/\s+/g, '_')}.pdf`);
  };

  return (
    <div className="py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12 animate-fade-in">
          <h1 className="text-4xl font-bold text-white mb-4 neon-text">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Inspection Report Generator</span>
          </h1>
          <p className="text-primary-200 max-w-3xl mx-auto">
            Our advanced <span className="text-accent-cyan">agentic AI</span> technology creates detailed, professional inspection reports based on your input. Save time and deliver comprehensive reports in minutes.
          </p>

          {/* LLM Indicator - shows in all modes */}
          <div className="mt-4 py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
            <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                 style={{ backgroundColor: formData.llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
            <span className="text-xs">
              Powered by <span className="font-semibold"
                style={{ color: formData.llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                {formData.llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
              </span>
            </span>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/50 text-red-100 px-4 py-3 rounded-md mb-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Status Message */}
        {statusMessage && !error && (
          <div className={`px-4 py-3 rounded-md mb-4 ${statusMessage.includes('successfully') ? 'bg-green-500/20 border border-green-500/50 text-green-100' : 'bg-primary-500/20 border border-primary-500/50 text-primary-100'}`}>
            <div className="flex items-start">
              {statusMessage.includes('successfully') ? (
                <svg className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <span>{statusMessage}</span>

              {/* Dev Mode Active LLM Indicator - only shows in development when processing */}
              {import.meta.env.DEV && (statusMessage.includes('Analyzing') || statusMessage.includes('Generating')) && (
                <div className="ml-3 px-2 py-0.5 rounded bg-gray-800 border border-gray-700 inline-flex items-center">
                  <div className="mr-1.5 h-2 w-2 rounded-full animate-ping"
                       style={{ backgroundColor: statusMessage.includes('OpenAI') ? '#10a37f' : '#a27aff' }}></div>
                  <span className="text-xs font-mono"
                        style={{ color: statusMessage.includes('OpenAI') ? '#10a37f' : '#a27aff' }}>
                    {statusMessage.includes('OpenAI') ? 'OpenAI Active' : 'Claude Active'}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Loading Indicator */}
        {isGenerating && (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 mb-4 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <FaSpinner className="animate-spin h-10 w-10 text-accent-cyan" />
              </div>
              <div className="absolute inset-0 flex items-center justify-center animate-pulse opacity-50">
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-accent-cyan/20 to-primary-500/20"></div>
              </div>
            </div>
            <p className="text-primary-200 text-center">Generating your inspection report...</p>
            <p className="text-primary-400 text-sm text-center mt-2">Our AI is analyzing the inspection notes and creating a professional report.</p>
          </div>
        )}

        {showChecklist && (
          <InspectionChecklist
            checklistItems={checklistItems}
            onToggleItem={handleToggleChecklistItem}
            onClose={() => setShowChecklist(false)}
          />
        )}

        {showPreview && !isGenerating && !showChecklist ? (
          <div className="glass-card dark p-8 animate-fade-in">
            <div className="mb-6">
              <button
                onClick={() => setShowPreview(false)}
                style={{
                  background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '9999px',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'all 0.3s ease',
                  border: 'none',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                }}
                className="transform hover:shadow-md hover:-translate-y-0.5 hover:scale-[1.03] transition-transform duration-300 flex items-center justify-center group text-sm"
              >
                <svg className="w-4 h-4 mr-1.5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Report Generation
              </button>
            </div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center">
                <FaFilePdf className="mr-2 text-accent-cyan" />
                Report Preview
              </h2>
              <div className="flex space-x-4">
                <button
                  onClick={exportToPDF}
                  style={{
                    background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                    color: 'white',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 flex items-center group"
                >
                  <FaFilePdf className="mr-2 group-hover:animate-bounce" />
                  Export to PDF
                </button>
                <button
                  onClick={handlePrint}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                  </svg>
                  Print Report
                </button>

              </div>
            </div>

            <div id="report-content" ref={reportRef} className="glass-card light p-8 rounded-lg">
              {/* Report Header */}
              <div className="mb-8 text-center">
                <h1 className="text-3xl font-bold text-dark-800 mb-2">Property Inspection Report</h1>
                <p className="text-xl text-dark-600">{formData.propertyAddress}</p>
                <p className="text-gray-500">Inspection Date: {new Date(formData.inspectionDate).toLocaleDateString()}</p>
              </div>

              {/* Property Information */}
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">Property Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p><span className="font-semibold">Address:</span> {formData.propertyAddress}</p>
                    <p><span className="font-semibold">Property Type:</span> {formData.propertyType || 'N/A'}</p>
                    <p><span className="font-semibold">Year Built:</span> {formData.yearBuilt || 'N/A'}</p>
                  </div>
                  <div>
                    <p><span className="font-semibold">Square Footage:</span> {formData.squareFootage || 'N/A'}</p>
                    <p><span className="font-semibold">Client:</span> {formData.clientName}</p>
                    <p><span className="font-semibold">Inspector:</span> {formData.inspectorName}</p>
                  </div>
                </div>
              </div>

              {/* Property Images */}
              {formData.images && formData.images.length > 0 && (
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">Property Images</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Property image ${index + 1}`}
                          className="h-40 w-full object-cover rounded-lg border border-gray-200 shadow-sm"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* AI Generated Report */}
              {generatedReport ? (
                <div className="mb-8 prose prose-sm max-w-none">
                  <style>
                    {`
                      .prose table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 1.5em;
                        margin-bottom: 1.5em;
                        overflow-x: auto;
                        display: block;
                        border-radius: 8px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                      }
                      .prose table th {
                        background: linear-gradient(to right, #00b9ff, #9900ff);
                        color: white;
                        font-weight: 600;
                        text-align: left;
                        padding: 1rem;
                        border: none;
                        position: relative;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                        font-size: 0.85rem;
                      }
                      .prose table th::after {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        height: 2px;
                        background: rgba(255, 255, 255, 0.2);
                      }
                      .prose table th:first-child {
                        border-top-left-radius: 8px;
                      }
                      .prose table th:last-child {
                        border-top-right-radius: 8px;
                      }
                      .prose table td {
                        padding: 0.75rem 1rem;
                        border: 1px solid #e5e7eb;
                        border-top: none;
                        vertical-align: top;
                      }
                      .prose table tr:nth-child(even) {
                        background-color: rgba(0, 185, 255, 0.05);
                      }
                      .prose table tr:hover {
                        background-color: rgba(0, 185, 255, 0.1);
                      }
                      .prose table tr:last-child td:first-child {
                        border-bottom-left-radius: 8px;
                      }
                      .prose table tr:last-child td:last-child {
                        border-bottom-right-radius: 8px;
                      }
                      @media print {
                        .prose table {
                          display: table;
                          page-break-inside: avoid;
                          box-shadow: none;
                        }
                        .prose table th {
                          background: #00b9ff !important;
                          color: white !important;
                          -webkit-print-color-adjust: exact;
                          print-color-adjust: exact;
                        }
                      }
                    `}
                  </style>
                  <div className="inspection-report-container">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {generatedReport}
                    </ReactMarkdown>
                  </div>
                  <style>
                    {`
                      .inspection-report-container h2 {
                        color: #00b9ff;
                        border-bottom: 2px solid rgba(0, 185, 255, 0.2);
                        padding-bottom: 0.5rem;
                        margin-top: 2rem;
                        margin-bottom: 1rem;
                      }
                      .inspection-report-container h3 {
                        color: #9900ff;
                        margin-top: 1.5rem;
                        margin-bottom: 0.75rem;
                      }
                      .inspection-report-container ul {
                        padding-left: 1.5rem;
                      }
                      .inspection-report-container ul li {
                        margin-bottom: 0.5rem;
                      }
                      .inspection-report-container p {
                        margin-bottom: 1rem;
                        line-height: 1.6;
                      }
                      .inspection-report-container strong {
                        color: #00b9ff;
                      }
                    `}
                  </style>
                </div>
              ) : (
                /* General Notes (fallback if no AI report) */
                formData.generalNotes && (
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-dark-800 mb-4 pb-2 border-b border-gray-200">General Notes</h2>
                    <p className="whitespace-pre-line">{formData.generalNotes}</p>
                  </div>
                )
              )}

              {/* Generate Checklist Button */}
              <div className="mt-8 mb-8 flex justify-center">
                <button
                  onClick={handleGenerateChecklist}
                  disabled={isGeneratingChecklist}
                  style={{
                    background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                    color: 'white',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 group"
                >
                  {isGeneratingChecklist ? (
                    <span className="inline-flex items-center justify-center whitespace-nowrap">
                      <FaSpinner className="w-4 h-4 mr-1.5 animate-spin" />
                      Generating Checklist...
                    </span>
                  ) : (
                    <span className="inline-flex items-center justify-center whitespace-nowrap">
                      <FaClipboard className="w-4 h-4 mr-1.5 group-hover:animate-bounce" />
                      Generate Inspection Checklist
                    </span>
                  )}
                </button>
              </div>

              {/* Footer */}
              <div className="mt-12 pt-6 border-t border-gray-200 text-center text-gray-500 text-sm">
                <p>This report is generated by Digital Realtor Inspection Tool</p>
                <p>Report generated on {new Date().toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="glass-card dark p-8 animate-fade-in">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-accent-cyan to-primary-400 flex items-center justify-center text-white mr-4 shadow-lg">
                <FaClipboardCheck className="text-xl" />
              </div>
              <h2 className="text-2xl font-bold text-white">Enter Inspection Details</h2>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group">
                  <label htmlFor="propertyAddress" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaHome className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Property Address <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    id="propertyAddress"
                    name="propertyAddress"
                    value={formData.propertyAddress}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    required
                    placeholder="123 Main St, City, State, ZIP"
                  />
                </div>
                <div className="group">
                  <label htmlFor="inspectionDate" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaCalendarAlt className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Inspection Date <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="date"
                    id="inspectionDate"
                    name="inspectionDate"
                    value={formData.inspectionDate}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group">
                  <label htmlFor="inspectorName" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaUserTie className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Inspector Name <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    id="inspectorName"
                    name="inspectorName"
                    value={formData.inspectorName}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    required
                    placeholder="John Smith"
                  />
                </div>
                <div className="group">
                  <label htmlFor="clientName" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaUser className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Client Name <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    id="clientName"
                    name="clientName"
                    value={formData.clientName}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    required
                    placeholder="Jane Doe"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="group">
                  <label htmlFor="propertyType" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaBuilding className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Property Type
                  </label>
                  <select
                    id="propertyType"
                    name="propertyType"
                    value={formData.propertyType}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                  >
                    <option value="" className="bg-dark-900 text-primary-200">Select property type</option>
                    <option value="Single Family" className="bg-dark-900 text-primary-200">Single Family</option>
                    <option value="Condo" className="bg-dark-900 text-primary-200">Condo</option>
                    <option value="Townhouse" className="bg-dark-900 text-primary-200">Townhouse</option>
                    <option value="Multi-Family" className="bg-dark-900 text-primary-200">Multi-Family</option>
                    <option value="Commercial" className="bg-dark-900 text-primary-200">Commercial</option>
                  </select>
                </div>
                <div className="group">
                  <label htmlFor="yearBuilt" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaCalendarAlt className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Year Built
                  </label>
                  <input
                    type="text"
                    id="yearBuilt"
                    name="yearBuilt"
                    value={formData.yearBuilt}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    placeholder="2010"
                  />
                </div>
                <div className="group">
                  <label htmlFor="squareFootage" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaRulerCombined className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    Square Footage
                  </label>
                  <input
                    type="text"
                    id="squareFootage"
                    name="squareFootage"
                    value={formData.squareFootage}
                    onChange={handleChange}
                    className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                    placeholder="2,000"
                  />
                </div>
                <div className="group">
                  <label className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                    <FaRobot className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                    LLM Provider
                  </label>
                  <div className="bg-dark-800 p-1 rounded-lg flex">
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, llmProvider: 'openai'})}
                      style={{
                        background: formData.llmProvider === 'openai'
                          ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                          : 'transparent',
                        transition: 'all 0.3s ease',
                      }}
                      className={`px-4 py-2 rounded-lg transition-all transform ${formData.llmProvider === 'openai'
                        ? 'text-white shadow-lg hover:shadow-xl'
                        : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                    >
                      <span className="inline-flex items-center">
                        <FaRobot className="mr-2" style={{ color: '#10a37f' }} />
                        OpenAI
                      </span>
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, llmProvider: 'claude'})}
                      style={{
                        background: formData.llmProvider === 'claude'
                          ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                          : 'transparent',
                        transition: 'all 0.3s ease',
                      }}
                      className={`px-4 py-2 rounded-lg transition-all transform ${formData.llmProvider === 'claude'
                        ? 'text-white shadow-lg hover:shadow-xl'
                        : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                    >
                      <span className="inline-flex items-center">
                        <FaRobot className="mr-2" style={{ color: '#a27aff' }} />
                        Claude
                      </span>
                    </button>
                  </div>
                </div>
              </div>

              <div className="group">
                <label htmlFor="generalNotes" className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                  <FaFileAlt className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                  General Notes <span className="text-accent-cyan ml-1">*</span>
                </label>
                <div className="mb-2 text-xs text-primary-300">
                  <p>Provide detailed notes about issues found during inspection. Our AI will analyze these notes to generate a professional report with recommendations.</p>
                </div>
                <textarea
                  id="generalNotes"
                  name="generalNotes"
                  value={formData.generalNotes}
                  onChange={handleChange}
                  rows={6}
                  className="block w-full pl-4 pr-12 py-3 bg-dark-700 border border-primary-700 focus:border-accent-cyan rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan text-primary-100 text-base"
                  placeholder="Describe all issues found during inspection. For example: 'Found water damage in the basement, appears to be from a leaking pipe. Roof shingles are worn and need replacement in the next 1-2 years. HVAC system is functioning but making unusual noise during startup. Noticed cracks in the foundation on the east side of the house.'"
                  required
                />
                <div className="mt-2 text-xs text-primary-300 flex items-start">
                  <svg className="w-4 h-4 mr-1 text-accent-cyan flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>The more detailed your notes, the more comprehensive the AI-generated report will be.</span>
                </div>
              </div>

              <div className="mt-8 group">
                <label className="block text-sm font-medium text-primary-200 mb-1 flex items-center">
                  <FaImage className="mr-2 text-accent-cyan group-hover:text-accent-cyan-bright transition-colors" />
                  Property Images
                </label>
                <div className="mb-2 text-xs text-primary-300">
                  <p>Upload images of property issues or areas of concern. Our AI will analyze the images and identify potential problems.</p>
                </div>
                <ImageUploader
                  onImagesUploaded={handleImagesUploaded}
                  maxImages={5}
                  isAnalyzing={isAnalyzingImages}
                />
                {formData.imageAnalysis && (
                  <div className="mt-4 p-4 bg-dark-800/50 rounded-lg border border-primary-700/30">
                    <h4 className="text-accent-cyan font-medium mb-2">AI Image Analysis Results:</h4>
                    <div className="text-primary-200 text-sm whitespace-pre-line">
                      {formData.imageAnalysis}
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-8 flex justify-center space-x-4">
                <button
                  type="button"
                  onClick={handleReset}
                  style={{
                    background: 'white',
                    color: '#00b9ff',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                >
                  <span className="inline-flex items-center justify-center whitespace-nowrap">
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Reset Form
                  </span>
                </button>
                <button
                  type="submit"
                  style={{
                    background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                    color: 'white',
                    padding: '1rem 2rem',
                    borderRadius: '9999px',
                    fontWeight: '500',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  }}
                  className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 flex items-center justify-center group"
                  disabled={!formData.propertyAddress || !formData.inspectionDate || !formData.inspectorName || !formData.clientName || !formData.generalNotes || isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      Generate AI Report
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectInspectionPage;
