const jimp = require('jimp');
const path = require('path');
const fs = require('fs');

// Configuration
const outputDir = path.join(__dirname, 'output');
const logoFilename = 'digital-realtor-logo.png';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Create a simple logo (a blue circle with "DR" text)
async function createLogo() {
  try {
    // Create a new image (100x100 pixels, white background)
    const image = await new jimp(100, 100, 0xFFFFFFFF);

    // Load font
    const font = await jimp.loadFont(jimp.FONT_SANS_16_BLACK);

    // Draw a blue circle
    for (let x = 0; x < 100; x++) {
      for (let y = 0; y < 100; y++) {
        const distanceFromCenter = Math.sqrt(Math.pow(x - 50, 2) + Math.pow(y - 50, 2));
        if (distanceFromCenter <= 40) {
          image.setPixelColor(0x0066CCFF, x, y); // Blue color with full opacity
        }
      }
    }

    // Add text "DR" in white
    image.print(
      font,
      0,
      0,
      {
        text: 'DR',
        alignmentX: jimp.HORIZONTAL_ALIGN_CENTER,
        alignmentY: jimp.VERTICAL_ALIGN_MIDDLE
      },
      100,
      100
    );

    // Save the logo
    await image.writeAsync(path.join(outputDir, logoFilename));
    console.log(`Logo created successfully: ${path.join(outputDir, logoFilename)}`);

    return path.join(outputDir, logoFilename);
  } catch (err) {
    console.error('Error creating logo:', err);
    throw err;
  }
}

// Execute the function
createLogo()
  .then(() => console.log('Logo creation completed'))
  .catch(err => console.error('Logo creation failed:', err));
