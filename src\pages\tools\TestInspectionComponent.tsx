import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

const TestInspectionComponent = () => {
  const navigate = useNavigate();

  return (
    <div className="py-12 bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/agent')}
            className="flex items-center text-primary-600 hover:text-primary-700 mb-4"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-dark-800 mb-2">Test Inspection Component</h1>
          <p className="text-dark-500">
            This is a simplified test component to verify routing.
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-card p-8">
          <p>If you can see this, the routing is working correctly.</p>
        </div>
      </div>
    </div>
  );
};

export default TestInspectionComponent;
