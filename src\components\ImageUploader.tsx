import React, { useState, useRef } from 'react';
import { FaCamera, FaTrash, FaSpinner } from 'react-icons/fa';

interface ImageUploaderProps {
  onImagesUploaded: (images: string[]) => void;
  maxImages?: number;
  isAnalyzing?: boolean;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImagesUploaded,
  maxImages = 5,
  isAnalyzing = false
}) => {
  const [images, setImages] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);

      // Check if adding new files would exceed the maximum
      if (images.length + newFiles.length > maxImages) {
        alert(`You can only upload a maximum of ${maxImages} images.`);
        return;
      }

      // Process each file
      Promise.all(
        newFiles.map(file => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              if (event.target?.result) {
                resolve(event.target.result as string);
              }
            };
            reader.readAsDataURL(file);
          });
        })
      ).then(newImages => {
        const updatedImages = [...images, ...newImages];
        setImages(updatedImages);
        onImagesUploaded(updatedImages);
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files) {
      const droppedFiles = Array.from(e.dataTransfer.files).filter(
        file => file.type.startsWith('image/')
      );

      // Check if adding new files would exceed the maximum
      if (images.length + droppedFiles.length > maxImages) {
        alert(`You can only upload a maximum of ${maxImages} images.`);
        return;
      }

      // Process each file
      Promise.all(
        droppedFiles.map(file => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              if (event.target?.result) {
                resolve(event.target.result as string);
              }
            };
            reader.readAsDataURL(file);
          });
        })
      ).then(newImages => {
        const updatedImages = [...images, ...newImages];
        setImages(updatedImages);
        onImagesUploaded(updatedImages);
      });
    }
  };

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...images];
    updatedImages.splice(index, 1);
    setImages(updatedImages);
    onImagesUploaded(updatedImages);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 ${
          isDragging
            ? 'border-accent-cyan bg-accent-cyan/10'
            : 'border-primary-700 hover:border-accent-cyan hover:bg-dark-800/50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          multiple
          className="hidden"
          disabled={isAnalyzing}
        />
        <FaCamera className="mx-auto h-12 w-12 text-primary-400 mb-4" />
        <p className="text-primary-200 mb-4">
          Drag and drop images here
        </p>

        <button
          onClick={(e) => {
            e.preventDefault();
            handleBrowseClick();
          }}
          disabled={isAnalyzing}
          style={{
            background: isAnalyzing
              ? 'rgba(128, 128, 128, 0.3)'
              : 'linear-gradient(to right, #00b9ff, #9900ff)',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '9999px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.3s ease',
            border: 'none',
            boxShadow: isAnalyzing ? 'none' : '0 4px 6px rgba(0, 0, 0, 0.1)',
            margin: '0 auto',
            cursor: isAnalyzing ? 'not-allowed' : 'pointer',
          }}
          className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 flex items-center justify-center group mb-4"
        >
          {isAnalyzing ? (
            <>
              <FaSpinner className="w-5 h-5 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
              </svg>
              Browse Images
            </>
          )}
        </button>

        <p className="text-primary-400 text-sm">
          Upload up to {maxImages} images of property issues for AI analysis
        </p>
      </div>

      {isAnalyzing && (
        <div className="flex items-center justify-center py-4 text-accent-cyan">
          <FaSpinner className="w-4 h-4 animate-spin mr-2" />
          <span>Analyzing images with AI...</span>
        </div>
      )}

      {images.length > 0 && (
        <div>
          <h3 className="text-white font-medium mb-2">Uploaded Images ({images.length}/{maxImages})</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={image}
                  alt={`Uploaded image ${index + 1}`}
                  className="h-32 w-full object-cover rounded-lg border border-primary-700"
                />
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveImage(index);
                  }}
                  className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  disabled={isAnalyzing}
                >
                  <FaTrash className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
