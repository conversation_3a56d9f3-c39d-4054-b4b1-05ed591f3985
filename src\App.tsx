import { lazy, Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useApp } from './context/SimpleAppContext';
import './App.css';
import './futuristic-buttons.css';
import './futuristic-header.css';
import './login-styles.css';
import './background-styles.css';

// Import ToastContainer directly
import { ToastContainer } from 'react-toastify';

// Import the new ModernHome component
const ModernHome = lazy(() => import('./pages/ModernHome'));
const ContractGenerator = lazy(() => import('./pages/ContractGenerator'));

// Layout components
import SimpleLayout from './components/SimpleLayout';
import CookieConsent from './components/CookieConsent';
import ClarityScript from './components/ClarityScript';

// Eagerly loaded pages
import SimpleHome from './pages/SimpleHome';
import Properties from './pages/Properties';
import PropertyDetail from './pages/PropertyDetail';
import NotFound from './pages/NotFound';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import VerifyEmailPage from './pages/VerifyEmailPage';
import PrivacyPolicy from './pages/PrivacyPolicy';
import ListingGenerator from './pages/ListingGenerator';
import CompAnalyzer from './pages/CompAnalyzer';
import AppointmentScheduler from './pages/AppointmentScheduler';
import SimpleAppointmentScheduler from './pages/SimpleAppointmentScheduler';
import AppointmentCalendar from './pages/AppointmentCalendar';
import NeighborhoodComplianceOverview from './pages/NeighborhoodComplianceOverview';
import VirtualStagingTool from './pages/tools/VirtualStagingTool';

// Lazily loaded pages for better performance
import SimpleAbout from './pages/SimpleAbout';
import SimpleAgentDashboard from './pages/SimpleAgentDashboard';
import TestPage from './pages/TestPage';
import BasicInspectionTool from './pages/tools/BasicInspectionTool';
import DirectInspectionPage from './pages/DirectInspectionPage';
const Contact = lazy(() => import('./pages/Contact'));

// Agent tools
import SimpleTool from './pages/tools/SimpleTool';

// Auth guard for protected routes
const ProtectedRoute = ({ children }: { children: React.ReactElement }) => {
  const { state } = useApp();

  if (!state.user.isAuthenticated) {
    // Redirect to login if not authenticated
    return <Navigate to="/login" replace />;
  }

  // Check if user is an agent for agent-specific routes
  if (window.location.pathname.startsWith('/agent') && state.user.role !== 'agent') {
    return <Navigate to="/" replace />;
  }

  return children;
};

function App() {
  const { state } = useApp();

  return (
    <Router>
      <ClarityScript />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
      <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
        <CookieConsent />
        <Routes>
          {/* Login route outside of layout */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/verify/:token" element={<VerifyEmailPage />} />

          <Route path="/" element={<SimpleLayout />}>
            {/* Public routes */}
            <Route index element={<ModernHome />} />
            <Route path="properties" element={<Properties />} />
            <Route path="property/:id" element={<PropertyDetail />} />
            <Route path="about" element={<SimpleAbout />} />
            <Route path="contact" element={<Contact />} />
            <Route path="privacy-policy" element={<PrivacyPolicy />} />
            <Route path="listing-generator" element={<ListingGenerator />} />
            <Route path="comp-analyzer" element={<CompAnalyzer />} />
            <Route path="appointment-scheduler" element={<AppointmentScheduler />} />
            <Route path="appointment-calendar" element={<AppointmentCalendar />} />
            <Route path="neighborhood-compliance" element={<NeighborhoodComplianceOverview />} />
            <Route path="virtual-staging" element={<VirtualStagingTool />} />
            <Route path="contract-generator" element={<ContractGenerator />} />
            <Route path="test" element={<TestPage />} />
            <Route path="basic-inspection" element={<BasicInspectionTool />} />
            <Route path="inspection-report" element={<DirectInspectionPage />} />
            <Route path="direct-inspection" element={<DirectInspectionPage />} />

            {/* Agent routes */}
            <Route path="agent" element={
              <ProtectedRoute>
                <SimpleAgentDashboard />
              </ProtectedRoute>
            } />

            {/* Agent tools */}
            <Route path="agent/tools/:toolId" element={
              <ProtectedRoute>
                <SimpleTool />
              </ProtectedRoute>
            } />

            {/* Catch-all route for 404 */}
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </Suspense>
    </Router>
  );
}

export default App;
