/* Custom Toast Styles to match the app's design system */

/* Override react-toastify default styles */
.Toastify__toast-container {
  width: 320px;
}

.Toastify__toast {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(0, 185, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 185, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  color: #f0f4ff !important;
  font-family: inherit !important;
  min-height: 64px !important;
  padding: 16px !important;
  margin-bottom: 8px !important;
}

.Toastify__toast--success {
  border-color: rgba(16, 185, 129, 0.3) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(16, 185, 129, 0.1) !important;
}

.Toastify__toast--error {
  border-color: rgba(239, 68, 68, 0.3) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(239, 68, 68, 0.1) !important;
}

.Toastify__toast--info {
  border-color: rgba(0, 185, 255, 0.3) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 185, 255, 0.1) !important;
}

.Toastify__toast--warning {
  border-color: rgba(245, 158, 11, 0.3) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(245, 158, 11, 0.1) !important;
}

.Toastify__toast-body {
  padding: 0 !important;
  margin: 0 !important;
  color: #f0f4ff !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.Toastify__toast-icon {
  width: 24px !important;
  height: 24px !important;
  margin-right: 12px !important;
  margin-left: 0 !important;
  flex-shrink: 0 !important;
}

.Toastify__toast-icon > svg {
  width: 24px !important;
  height: 24px !important;
}

/* Success icon styling */
.Toastify__toast--success .Toastify__toast-icon > svg {
  fill: #10b981 !important;
}

/* Error icon styling */
.Toastify__toast--error .Toastify__toast-icon > svg {
  fill: #ef4444 !important;
}

/* Info icon styling */
.Toastify__toast--info .Toastify__toast-icon > svg {
  fill: #00b9ff !important;
}

/* Warning icon styling */
.Toastify__toast--warning .Toastify__toast-icon > svg {
  fill: #f59e0b !important;
}

.Toastify__close-button {
  color: rgba(240, 244, 255, 0.6) !important;
  opacity: 1 !important;
  align-self: flex-start !important;
  margin-top: 2px !important;
}

.Toastify__close-button:hover {
  color: #f0f4ff !important;
}

.Toastify__progress-bar {
  background: linear-gradient(to right, #00b9ff, #9900ff) !important;
  height: 3px !important;
}

.Toastify__progress-bar--success {
  background: linear-gradient(to right, #10b981, #059669) !important;
}

.Toastify__progress-bar--error {
  background: linear-gradient(to right, #ef4444, #dc2626) !important;
}

.Toastify__progress-bar--info {
  background: linear-gradient(to right, #00b9ff, #0ea5e9) !important;
}

.Toastify__progress-bar--warning {
  background: linear-gradient(to right, #f59e0b, #d97706) !important;
}

/* Animation improvements */
.Toastify__toast--animate-icon {
  animation: toastIconBounce 0.6s ease-out;
}

@keyframes toastIconBounce {
  0% {
    transform: scale(0.3) rotate(-10deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Custom toast content styling */
.custom-toast-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.custom-toast-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.custom-toast-icon--success {
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.custom-toast-icon--error {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.custom-toast-icon--info {
  background: rgba(0, 185, 255, 0.2);
  border: 1px solid rgba(0, 185, 255, 0.3);
}

.custom-toast-icon--warning {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.custom-toast-text {
  flex: 1;
  font-weight: 500;
  color: #f0f4ff;
}

.custom-toast-title {
  font-weight: 600;
  margin-bottom: 2px;
  color: #ffffff;
}

.custom-toast-message {
  font-size: 13px;
  color: rgba(240, 244, 255, 0.8);
  line-height: 1.4;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .Toastify__toast-container {
    width: 100vw;
    padding: 0 16px;
    left: 0;
    right: 0;
  }
  
  .Toastify__toast {
    margin-bottom: 8px;
    border-radius: 8px !important;
  }
}

/* Dark theme specific overrides */
.Toastify__toast-container--top-right {
  top: 24px;
  right: 24px;
}

.Toastify__toast-container--top-center {
  top: 24px;
}

.Toastify__toast-container--top-left {
  top: 24px;
  left: 24px;
}

.Toastify__toast-container--bottom-right {
  bottom: 24px;
  right: 24px;
}

.Toastify__toast-container--bottom-center {
  bottom: 24px;
}

.Toastify__toast-container--bottom-left {
  bottom: 24px;
  left: 24px;
}

/* Ensure proper z-index */
.Toastify__toast-container {
  z-index: 9999 !important;
}
