import { useState, useEffect, useRef } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { useApp } from '../context/SimpleAppContext';
import AppBackground from './AppBackground';

const SimpleLayout = () => {
  const { state, logout } = useApp();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobileFeaturesOpen, setIsMobileFeaturesOpen] = useState(false);
  const [isMobileToolsOpen, setIsMobileToolsOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isFeaturesOpen, setIsFeaturesOpen] = useState(false);
  const [isToolsOpen, setIsToolsOpen] = useState(false);
  const featuresDropdownRef = useRef<HTMLDivElement>(null);
  const toolsDropdownRef = useRef<HTMLLIElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsDropdownOpen(false);
    setIsToolsOpen(false);
    setIsMobileToolsOpen(false);
  }, [location]);

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
      if (featuresDropdownRef.current && !featuresDropdownRef.current.contains(event.target as Node)) {
        setIsFeaturesOpen(false);
      }
      if (toolsDropdownRef.current && !toolsDropdownRef.current.contains(event.target as Node)) {
        setIsToolsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="min-h-screen flex flex-col relative overflow-x-hidden w-full">
      <AppBackground />


      {/* Futuristic Header */}
      <header className="futuristic-header">
        <div className="header-bg"></div>
        <div className="header-container">
          <Link
            to="/"
            className="logo-link"
          >
            <svg className="logo-icon w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span className="logo-text">Digital Realtor</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="header-nav">
            <ul className="nav-list">
              <li className="nav-item">
                <Link
                  to="/"
                  className={`nav-link ${location.pathname === '/' ? 'active' : ''}`}
                >
                  <svg className="nav-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Home
                </Link>
              </li>

              <li className="nav-item">
                <Link
                  to="/about"
                  className={`nav-link ${location.pathname === '/about' ? 'active' : ''}`}
                >
                  <svg className="nav-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="whitespace-nowrap">About Us</span>
                </Link>
              </li>

              <li className="nav-item">
                <Link
                  to="/properties"
                  className={`nav-link ${location.pathname === '/properties' ? 'active' : ''}`}
                >
                  <svg className="nav-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Properties
                </Link>
              </li>

              <li className="nav-item">
                <Link
                  to="/contact"
                  className={`nav-link ${location.pathname === '/contact' ? 'active' : ''}`}
                >
                  <svg className="nav-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Contact
                </Link>
              </li>

              <li className="nav-item" ref={toolsDropdownRef}>
                <button
                  onClick={() => setIsToolsOpen(!isToolsOpen)}
                  className={`nav-link features-button ${isToolsOpen || location.pathname === '/listing-generator' || location.pathname === '/comp-analyzer' ? 'active' : ''}`}
                >
                  <svg className="nav-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                  </svg>
                  <span className="whitespace-nowrap">Tools</span>
                  <svg className={`ml-1 w-4 h-4 transition-transform duration-200 ${isToolsOpen ? 'rotate-180' : ''}`} width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isToolsOpen && (
                  <div className="dropdown-menu features-dropdown">
                    <Link
                      to="/listing-generator"
                      className={`menu-item ${location.pathname === '/listing-generator' ? 'active' : ''}`}
                      onClick={() => setIsToolsOpen(false)}
                    >
                      <svg className="menu-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-accent-cyan">AI Listing Generator</span>
                    </Link>
                    <Link
                      to="/comp-analyzer"
                      className={`menu-item ${location.pathname === '/comp-analyzer' ? 'active' : ''}`}
                      onClick={() => setIsToolsOpen(false)}
                    >
                      <svg className="menu-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                      </svg>
                      <span className="text-secondary-400">Comp Analyzer</span>
                    </Link>
                    <Link
                      to="/appointment-scheduler"
                      className={`menu-item ${location.pathname === '/appointment-scheduler' ? 'active' : ''}`}
                      onClick={() => setIsToolsOpen(false)}
                    >
                      <svg className="menu-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span className="text-accent-cyan">Appointment Scheduler</span>
                    </Link>
                    <Link
                      to="/direct-inspection"
                      className={`menu-item ${location.pathname === '/direct-inspection' ? 'active' : ''}`}
                      onClick={() => setIsToolsOpen(false)}
                    >
                      <svg className="menu-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                      <span className="text-primary-300">Inspection Report Generator</span>
                    </Link>
                    <Link
                      to="/neighborhood-compliance"
                      className={`menu-item ${location.pathname === '/neighborhood-compliance' ? 'active' : ''}`}
                      onClick={() => setIsToolsOpen(false)}
                    >
                      <svg className="menu-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-primary-400">Neighborhood & Compliance</span>
                    </Link>
                    <Link
                      to="/contract-generator"
                      className={`menu-item ${location.pathname === '/contract-generator' ? 'active' : ''}`}
                      onClick={() => setIsToolsOpen(false)}
                    >
                      <svg className="menu-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-secondary-400">Contract Generator</span>
                    </Link>
                    {/* Add more tools here in the future */}
                  </div>
                )}
              </li>

              {state.user.isAuthenticated && (
                <li className="nav-item">
                  <Link
                    to="/agent"
                    className={`nav-link ${location.pathname.startsWith('/agent') ? 'active' : ''}`}
                  >
                    <svg className="nav-icon" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Agent Tools
                  </Link>
                </li>
              )}
            </ul>
          </nav>

          {/* User Menu */}
          <div className="user-menu-container" ref={dropdownRef}>
            {state.user.isAuthenticated ? (
              <>
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="user-button"
                >
                  <div className="user-avatar">
                    <span>{state.user.name ? state.user.name.charAt(0).toUpperCase() : 'U'}</span>
                  </div>
                  <span className="hidden sm:block">
                    {state.user.name}
                  </span>
                  <svg className={`w-4 h-4 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isDropdownOpen && (
                  <div className="user-dropdown">
                    <div className="user-dropdown-header">
                      <div className="flex items-center space-x-3">
                        <div className="user-avatar user-avatar-large">
                          <span>{state.user.name ? state.user.name.charAt(0).toUpperCase() : 'U'}</span>
                        </div>
                        <div>
                          <div className="font-medium text-white">{state.user.name}</div>
                          <div className="text-xs text-primary-300 truncate max-w-[150px]">{state.user.email}</div>
                        </div>
                      </div>
                    </div>
                    <div className="user-dropdown-content">
                      <Link
                        to="/profile"
                        className="dropdown-menu-item"
                        onClick={() => setIsDropdownOpen(false)}
                      >
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        My Profile
                      </Link>
                      {state.user.isRealtor && (
                        <Link
                          to="/agent"
                          className="dropdown-menu-item"
                          onClick={() => setIsDropdownOpen(false)}
                        >
                          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          Agent Dashboard
                        </Link>
                      )}
                      <div className="dropdown-divider"></div>
                      <button
                        onClick={() => {
                          logout();
                          setIsDropdownOpen(false);
                        }}
                        className="dropdown-menu-item sign-out-button"
                      >
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/register"
                  className="secondary-button button-sm hidden sm:inline-flex"
                >
                  Register
                </Link>
                <Link
                  to="/login"
                  className="primary-button button-sm whitespace-nowrap"
                >
                  Sign In
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="mobile-menu-button md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
              </svg>
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="mobile-menu open">
            <div className="mobile-menu-header">
              <Link to="/" className="logo-link">
                <svg className="logo-icon w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
                    stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="logo-text">Digital Realtor</span>
              </Link>
              <button className="mobile-menu-close" onClick={() => setIsMobileMenuOpen(false)}>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <ul className="mobile-nav-list">
              <li className="mobile-nav-item">
                <Link
                  to="/"
                  className={`mobile-nav-link ${location.pathname === '/' ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Home
                </Link>
              </li>
              <li className="mobile-nav-item">
                <Link
                  to="/about"
                  className={`mobile-nav-link ${location.pathname === '/about' ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  About Us
                </Link>
              </li>

              <li className="mobile-nav-item">
                <Link
                  to="/properties"
                  className={`mobile-nav-link ${location.pathname === '/properties' ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Properties
                </Link>
              </li>

              <li className="mobile-nav-item">
                <Link
                  to="/contact"
                  className={`mobile-nav-link ${location.pathname === '/contact' ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Contact
                </Link>
              </li>

              <li className="mobile-nav-item mobile-features-dropdown">
                <button
                  onClick={() => setIsMobileToolsOpen(!isMobileToolsOpen)}
                  className={`mobile-nav-link flex items-center justify-between w-full ${(isMobileToolsOpen || location.pathname === '/listing-generator' || location.pathname === '/comp-analyzer') ? 'active' : ''}`}
                >
                  <span>Tools</span>
                  <svg className={`w-4 h-4 transition-transform duration-200 ${isMobileToolsOpen ? 'rotate-180' : ''}`} width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isMobileToolsOpen && (
                  <div className="mobile-features-submenu">
                    <Link
                      to="/listing-generator"
                      className={`mobile-submenu-link ${location.pathname === '/listing-generator' ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-accent-cyan">AI Listing Generator</span>
                    </Link>
                    <Link
                      to="/comp-analyzer"
                      className={`mobile-submenu-link ${location.pathname === '/comp-analyzer' ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-secondary-400">Comp Analyzer</span>
                    </Link>
                    <Link
                      to="/appointment-scheduler"
                      className={`mobile-submenu-link ${location.pathname === '/appointment-scheduler' ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-accent-cyan">Appointment Scheduler</span>
                    </Link>
                    <Link
                      to="/direct-inspection"
                      className={`mobile-submenu-link ${location.pathname === '/direct-inspection' ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-primary-300">Inspection Report Generator</span>
                    </Link>
                    <Link
                      to="/neighborhood-compliance"
                      className={`mobile-submenu-link ${location.pathname === '/neighborhood-compliance' ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-primary-400">Neighborhood & Compliance</span>
                    </Link>
                    <Link
                      to="/contract-generator"
                      className={`mobile-submenu-link ${location.pathname === '/contract-generator' ? 'active' : ''}`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-secondary-400">Contract Generator</span>
                    </Link>
                    {/* Add more tools here in the future */}
                  </div>
                )}
              </li>
              {state.user.isAuthenticated && (
                <li className="mobile-nav-item">
                  <Link
                    to="/agent"
                    className={`mobile-nav-link ${location.pathname.startsWith('/agent') ? 'active' : ''}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Agent Tools
                  </Link>
                </li>
              )}
            </ul>

            <div className="mt-auto pt-4 border-t border-primary-900/50 text-center">
              {state.user.isAuthenticated ? (
                <div className="px-4 py-2">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="user-avatar user-avatar-large">
                      <span>{state.user.name ? state.user.name.charAt(0).toUpperCase() : 'U'}</span>
                    </div>
                    <div className="text-left">
                      <div className="font-medium text-white">{state.user.name}</div>
                      <div className="text-xs text-primary-300 truncate max-w-[200px]">{state.user.email}</div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      to="/profile"
                      className="secondary-button button-sm flex-1"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    <button
                      onClick={() => {
                        logout();
                        setIsMobileMenuOpen(false);
                      }}
                      className="secondary-button button-sm flex-1 text-error border-error/30 hover:bg-error/10"
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col space-y-2 px-4 py-2">
                  <Link
                    to="/login"
                    className="primary-button button-sm"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    to="/register"
                    className="secondary-button button-sm"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Register
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </header>

      {/* Main Content */}
      <main className="flex-grow main-content overflow-x-hidden w-full">
        <Outlet />
      </main>

      {/* Footer */}
      <footer className="bg-transparent text-blue-300 pt-12 pb-8 relative z-10 backdrop-blur-md bg-dark-900/70 border-t border-primary-900/30 mt-20">
        <div className="app-background absolute inset-0 -z-10 opacity-30"></div>
        <div className="max-w-5xl mx-auto px-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm justify-center">
          {/* Column 1: Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  About
                </Link>
              </li>
              <li>
                <Link to="/properties" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Properties
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 2: Marketing Tools */}
          <div>
            <h3 className="text-white font-semibold mb-4">Marketing Tools</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/listing-generator" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Listing Generator
                </Link>
              </li>
              <li>
                <Link to="/virtual-staging" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Virtual Staging
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 3: Analysis & Compliance Tools */}
          <div>
            <h3 className="text-white font-semibold mb-4">Analysis & Compliance Tools</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/comp-analyzer" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Comp Analyzer
                </Link>
              </li>
              <li>
                <Link to="/neighborhood-compliance" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Neighborhood & Compliance
                </Link>
              </li>
              <li>
                <Link to="/contract-generator" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Contract Generator
                </Link>
              </li>
              <li>
                <Link to="/appointment-scheduler" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Appointment Scheduler
                </Link>
              </li>
              <li>
                <Link to="/inspection-report" className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow">
                  Inspection Report Generator
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 4: Contact Us */}
          <div>
            <h3 className="text-white font-semibold mb-4">Contact Us</h3>
            <a
              href="mailto:<EMAIL>"
              className="text-blue-400 hover:underline hover:text-cyan-300 transition-all duration-300 hover:translate-x-1 hover:shadow-glow inline-block"
            >
              <EMAIL>
            </a>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="text-center text-xs text-gray-400 mt-10">
          © 2025 <span className="text-blue-400">Digital Realtor</span>. All rights reserved.
        </div>
      </footer>
    </div>
  );
};

export default SimpleLayout;
