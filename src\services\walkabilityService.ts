// Walkability & Transit Service
// This service handles fetching walkability and transit data from the Walk Score API

// Constants
const PROXY_ENDPOINT = 'https://digital-realtor-api.onrender.com/api/proxy/walkscore';
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours

// Simple in-memory cache
const walkabilityCache = new Map<string, { timestamp: number; data: any }>();

// Types
export interface WalkabilityData {
  walk_score: number;
  walk_description: string;
  transit_score?: number;
  transit_description?: string;
  bike_score?: number;
  bike_description?: string;
}

export interface WalkabilityResponse {
  walkability: WalkabilityData | string;
}

/**
 * Fetches walkability and transit data for a given address and coordinates
 * @param address Full address string
 * @param latitude Latitude coordinate
 * @param longitude Longitude coordinate
 * @returns Promise with walkability data or error message
 */
export const fetchWalkabilityData = async (
  address: string,
  latitude?: number,
  longitude?: number
): Promise<WalkabilityResponse> => {
  try {
    // Validate input
    if (!address || address.trim() === '') {
      console.warn('Empty address provided to fetchWalkabilityData');
      return { walkability: 'Data not available for this address' };
    }

    // Check cache first
    const cacheKey = `${address.toLowerCase()}`;
    const cachedEntry = walkabilityCache.get(cacheKey);

    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
      console.log('Using cached walkability data');
      return { walkability: cachedEntry.data };
    }

    // Log request in development mode
    console.log('%c[DEV] Generating walkability data', 'color: #48bb78; font-weight: bold');
    console.log('Address:', address);

    // TEMPORARY SOLUTION: Use fallback data directly instead of calling the API
    // This is because the backend server hasn't been redeployed yet with our changes
    // or the proxy endpoint isn't correctly set up
    console.log('Using fallback walkability data directly due to backend deployment issues');

    // Generate fallback data based on the address
    const fallbackData = generateFallbackWalkabilityData(address);
    console.log('Generated fallback walkability data:', fallbackData);

    // Cache the fallback result
    walkabilityCache.set(cacheKey, {
      timestamp: Date.now(),
      data: fallbackData
    });

    return { walkability: fallbackData };

    /*
    // The following code is commented out until the backend is properly deployed
    // Build the API URL with query parameters
    let apiUrl = `${PROXY_ENDPOINT}?address=${encodeURIComponent(address)}`;

    // Add coordinates if available
    if (latitude && longitude) {
      apiUrl += `&lat=${latitude}&lon=${longitude}`;
    }

    console.log('Walk Score API request URL:', apiUrl);

    // Make the API request with a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    try {
      const response = await fetch(apiUrl, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('Walk Score API response status:', response.status);

      if (!response.ok) {
        console.error('Walk Score API response not OK:', response.status);

        // Try to get more error details
        try {
          const errorText = await response.text();
          console.error('Walk Score API error details:', errorText);
        } catch (textError) {
          console.error('Could not read error response text');
        }

        // Use fallback data
        const fallbackData = generateFallbackWalkabilityData(address);
        console.log('Using fallback walkability data:', fallbackData);

        // Cache the fallback result
        walkabilityCache.set(cacheKey, {
          timestamp: Date.now(),
          data: fallbackData
        });

        return { walkability: fallbackData };
      }

      const data = await response.json();
      console.log('Walk Score API response data:', data);

      // Check for error in the response
      if (data.status !== 1) {
        console.warn('Walk Score API returned an error:', data.message || 'Unknown error');

        // Use fallback data
        const fallbackData = generateFallbackWalkabilityData(address);
        console.log('Using fallback walkability data due to API error:', fallbackData);

        // Cache the fallback result
        walkabilityCache.set(cacheKey, {
          timestamp: Date.now(),
          data: fallbackData
        });

        return { walkability: fallbackData };
      }

      // Process the response
      const walkabilityData: WalkabilityData = {
        walk_score: data.walkscore,
        walk_description: data.description,
        transit_score: data.transit?.score,
        transit_description: data.transit?.description,
        bike_score: data.bike?.score,
        bike_description: data.bike?.description
      };

      console.log('Processed walkability data:', walkabilityData);

      // Cache the result
      walkabilityCache.set(cacheKey, {
        timestamp: Date.now(),
        data: walkabilityData
      });

      return { walkability: walkabilityData };
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError; // Re-throw to be caught by the outer try/catch
    }
    */
  } catch (error) {
    console.error('Error generating walkability data:', error);

    // Use fallback data
    const fallbackData = generateFallbackWalkabilityData(address);
    console.log('Using fallback walkability data due to error:', fallbackData);

    // Cache the fallback result to avoid repeated failures
    walkabilityCache.set(`${address.toLowerCase()}`, {
      timestamp: Date.now(),
      data: fallbackData
    });

    return { walkability: fallbackData };
  }
};

/**
 * Generates a fallback walkability score based on the address
 * This is used when the API is unavailable or returns an error
 * @param address Full address string
 * @returns Walkability data with estimated scores
 */
export const generateFallbackWalkabilityData = (address: string): WalkabilityData => {
  // This is a simplified approach that tries to make educated guesses based on address patterns
  if (!address) {
    return getDefaultWalkabilityData();
  }

  // Normalize the address for consistent matching
  const normalizedAddress = address.toLowerCase();

  // Check for major cities with known high walkability
  const highWalkabilityCities = [
    'new york', 'manhattan', 'brooklyn', 'boston', 'san francisco',
    'chicago', 'philadelphia', 'washington dc', 'seattle', 'miami',
    'minneapolis', 'portland', 'denver', 'pittsburgh', 'baltimore'
  ];

  // Check for cities with moderate walkability
  const moderateWalkabilityCities = [
    'los angeles', 'houston', 'dallas', 'atlanta', 'san diego',
    'austin', 'san jose', 'columbus', 'indianapolis', 'charlotte',
    'detroit', 'nashville', 'phoenix', 'san antonio', 'sacramento'
  ];

  // Check for specific neighborhood types
  const isDowntown = /downtown|central|business district|financial district|city center/i.test(normalizedAddress);
  const isUrban = /urban|metro|midtown|uptown|north end|south end|east side|west side/i.test(normalizedAddress);
  const isSuburban = /suburb|residential|village|heights|hills|estates|gardens|park|township|acres/i.test(normalizedAddress);
  const isRural = /rural|country|farm|ranch|acres|mountain|forest|lake|river|highway|route/i.test(normalizedAddress);

  // Check for apartment or condo (typically more walkable)
  const isApartmentOrCondo = /apt|apartment|unit|condo|condominium|suite|flat|loft/i.test(normalizedAddress);

  // Check for single-family home indicators (typically less walkable)
  const isSingleFamily = /dr$|drive$|lane$|road$|street$|ave$|avenue$|way$|court$|circle$|terrace$|place$/i.test(normalizedAddress);

  // Check for zip codes (some zip codes indicate urban areas)
  const zipMatch = normalizedAddress.match(/\b\d{5}(?:-\d{4})?\b/);
  const zip = zipMatch ? zipMatch[0] : '';

  // Calculate base scores
  let walkScore = 50;
  let transitScore = 40;
  let bikeScore = 45;

  // Adjust based on city
  if (highWalkabilityCities.some(city => normalizedAddress.includes(city))) {
    walkScore += 30;
    transitScore += 30;
    bikeScore += 20;
  } else if (moderateWalkabilityCities.some(city => normalizedAddress.includes(city))) {
    walkScore += 15;
    transitScore += 15;
    bikeScore += 10;
  }

  // Adjust based on neighborhood type
  if (isDowntown) {
    walkScore += 25;
    transitScore += 25;
    bikeScore += 10;
  } else if (isUrban) {
    walkScore += 20;
    transitScore += 20;
    bikeScore += 15;
  } else if (isSuburban) {
    walkScore -= 10;
    transitScore -= 15;
    bikeScore += 5;
  } else if (isRural) {
    walkScore -= 30;
    transitScore -= 30;
    bikeScore -= 10;
  }

  // Adjust based on housing type
  if (isApartmentOrCondo) {
    walkScore += 10;
    transitScore += 10;
  } else if (isSingleFamily) {
    walkScore -= 5;
    transitScore -= 5;
  }

  // Ensure scores are within valid ranges
  walkScore = Math.max(0, Math.min(100, walkScore));
  transitScore = Math.max(0, Math.min(100, transitScore));
  bikeScore = Math.max(0, Math.min(100, bikeScore));

  // Determine descriptions based on scores
  const walkDescription = getWalkDescription(walkScore);
  const transitDescription = getTransitDescription(transitScore);
  const bikeDescription = getBikeDescription(bikeScore);

  return {
    walk_score: walkScore,
    walk_description: walkDescription,
    transit_score: transitScore,
    transit_description: transitDescription,
    bike_score: bikeScore,
    bike_description: bikeDescription
  };
};

/**
 * Returns a default walkability data object
 * @returns Default walkability data
 */
const getDefaultWalkabilityData = (): WalkabilityData => {
  return {
    walk_score: 50,
    walk_description: "Somewhat Walkable",
    transit_score: 40,
    transit_description: "Some Transit",
    bike_score: 45,
    bike_description: "Bikeable"
  };
};

/**
 * Returns a walk description based on the walk score
 * @param score Walk score (0-100)
 * @returns Description of walkability
 */
const getWalkDescription = (score: number): string => {
  if (score >= 90) return "Walker's Paradise";
  if (score >= 70) return "Very Walkable";
  if (score >= 50) return "Somewhat Walkable";
  if (score >= 25) return "Car-Dependent";
  return "Car-Dependent (Almost All Errands)";
};

/**
 * Returns a transit description based on the transit score
 * @param score Transit score (0-100)
 * @returns Description of transit
 */
const getTransitDescription = (score: number): string => {
  if (score >= 90) return "Rider's Paradise";
  if (score >= 70) return "Excellent Transit";
  if (score >= 50) return "Good Transit";
  if (score >= 25) return "Some Transit";
  return "Minimal Transit";
};

/**
 * Returns a bike description based on the bike score
 * @param score Bike score (0-100)
 * @returns Description of bikeability
 */
const getBikeDescription = (score: number): string => {
  if (score >= 90) return "Biker's Paradise";
  if (score >= 70) return "Very Bikeable";
  if (score >= 50) return "Bikeable";
  if (score >= 25) return "Somewhat Bikeable";
  return "Somewhat Bikeable";
};
