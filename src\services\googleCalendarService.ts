import axios from 'axios';

// Google Calendar API configuration
const API_KEY = import.meta.env.VITE_GOOGLE_API_KEY || '';
const CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || '';
const DISCOVERY_DOCS = ['https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest'];
const SCOPES = 'https://www.googleapis.com/auth/calendar';

// Interface for appointment data
interface AppointmentData {
  id: string;
  clientName: string;
  clientEmail: string;
  date: string;
  time: string;
  propertyAddress: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes?: string;
}

// Interface for calendar event
interface CalendarEvent {
  id?: string;
  summary: string;
  location: string;
  description: string;
  start: {
    dateTime: string;
    timeZone: string;
  };
  end: {
    dateTime: string;
    timeZone: string;
  };
  attendees?: Array<{
    email: string;
    name?: string;
    responseStatus?: string;
  }>;
  reminders?: {
    useDefault: boolean;
    overrides?: Array<{
      method: string;
      minutes: number;
    }>;
  };
}

// Initialize Google API client
const initGoogleApi = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Helper function to initialize the client
    const initClient = async () => {
      try {
        if (!window.gapi || !window.gapi.client) {
          throw new Error('Google API client not available');
        }

        await window.gapi.client.init({
          apiKey: API_KEY,
          clientId: CLIENT_ID,
          discoveryDocs: DISCOVERY_DOCS,
          scope: SCOPES,
        });

        console.log('Google API client initialized successfully');
        resolve();
      } catch (error) {
        console.error('Error initializing Google API client:', error);
        reject(error);
      }
    };

    // Check if the API is already loaded
    if (window.isGoogleApiLoaded && window.isGoogleApiLoaded()) {
      console.log('Google API already loaded, initializing client');
      initClient();
      return;
    }

    // Check if the API is currently loading
    if (window.isGoogleApiLoading && window.isGoogleApiLoading()) {
      console.log('Google API is loading, waiting for it to complete');
      window.addEventListener('google-api-loaded', () => {
        console.log('Received google-api-loaded event, initializing client');
        initClient();
      }, { once: true });

      window.addEventListener('google-api-load-error', () => {
        console.error('Received google-api-load-error event');
        reject(new Error('Failed to load Google API'));
      }, { once: true });

      return;
    }

    // If the API is not loaded or loading, try to load it manually
    if (!window.gapi) {
      console.log('Google API not loaded, attempting to load it manually');

      // Create a script element to load the API
      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.async = true;
      script.defer = true;

      script.onload = () => {
        console.log('Google API script loaded manually');
        if (window.gapi) {
          window.gapi.load('client:auth2', async () => {
            console.log('Google API client and auth2 libraries loaded manually');
            initClient();
          });
        } else {
          reject(new Error('Google API not available after manual load'));
        }
      };

      script.onerror = () => {
        console.error('Error loading Google API script manually');
        reject(new Error('Failed to load Google API manually'));
      };

      document.head.appendChild(script);
      return;
    }

    // If gapi is available but not initialized
    if (window.gapi && !window.gapi.client) {
      console.log('Google API available but not initialized, loading client');
      window.gapi.load('client:auth2', async () => {
        initClient();
      });
      return;
    }

    // If we get here, something unexpected happened
    reject(new Error('Unable to initialize Google API client'));
  });
};

// Check if user is signed in
const isSignedIn = (): boolean => {
  try {
    return window.gapi &&
           window.gapi.auth2 &&
           window.gapi.auth2.getAuthInstance() &&
           window.gapi.auth2.getAuthInstance().isSignedIn.get();
  } catch (error) {
    console.error('Error checking if user is signed in:', error);
    return false;
  }
};

// Ensure Google API is initialized
const ensureInitialized = async (): Promise<void> => {
  if (!window.gapi || !window.gapi.auth2 || !window.gapi.auth2.getAuthInstance()) {
    console.log('Google API not fully initialized, initializing now');
    await initGoogleApi();
  }
};

// Sign in to Google
const signIn = async (): Promise<void> => {
  try {
    await ensureInitialized();

    if (!window.gapi || !window.gapi.auth2 || !window.gapi.auth2.getAuthInstance()) {
      throw new Error('Google API client not initialized properly');
    }

    await window.gapi.auth2.getAuthInstance().signIn({
      prompt: 'select_account' // Always show the account selection dialog
    });

    return Promise.resolve();
  } catch (error) {
    console.error('Error signing in to Google:', error);
    return Promise.reject(error);
  }
};

// Sign out from Google
const signOut = async (): Promise<void> => {
  try {
    await ensureInitialized();

    if (!window.gapi || !window.gapi.auth2 || !window.gapi.auth2.getAuthInstance()) {
      throw new Error('Google API client not initialized properly');
    }

    await window.gapi.auth2.getAuthInstance().signOut();
    return Promise.resolve();
  } catch (error) {
    console.error('Error signing out from Google:', error);
    return Promise.reject(error);
  }
};

// Convert appointment data to Google Calendar event
const appointmentToCalendarEvent = (appointment: AppointmentData): CalendarEvent => {
  // Parse date and time
  const [month, day, year] = appointment.date.match(/(\w+) (\d+), (\d+)/)?.slice(1) || [];
  const monthIndex = new Date(`${month} 1, 2000`).getMonth();

  // Create date objects for start and end times
  const startDate = new Date(parseInt(year), monthIndex, parseInt(day));
  const endDate = new Date(parseInt(year), monthIndex, parseInt(day));

  // Parse time (e.g., "10:00 AM")
  const [hours, minutes, period] = appointment.time.match(/(\d+):(\d+) (\w+)/)?.slice(1) || [];
  let hour = parseInt(hours);
  if (period.toUpperCase() === 'PM' && hour < 12) {
    hour += 12;
  } else if (period.toUpperCase() === 'AM' && hour === 12) {
    hour = 0;
  }

  // Set hours and minutes
  startDate.setHours(hour, parseInt(minutes), 0, 0);
  endDate.setHours(hour + 1, parseInt(minutes), 0, 0); // Default to 1-hour appointment

  // Create the event object
  const event: CalendarEvent = {
    summary: `Property Viewing: ${appointment.propertyAddress}`,
    location: appointment.propertyAddress,
    description: `
      Appointment with ${appointment.clientName} (${appointment.clientEmail})
      Property Address: ${appointment.propertyAddress}
      Status: ${appointment.status}
      Appointment ID: ${appointment.id}
      ${appointment.notes ? `Notes: ${appointment.notes}` : ''}
    `,
    start: {
      dateTime: startDate.toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    end: {
      dateTime: endDate.toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    attendees: [
      {
        email: appointment.clientEmail,
        name: appointment.clientName,
      },
    ],
    reminders: {
      useDefault: false,
      overrides: [
        { method: 'email', minutes: 24 * 60 }, // 1 day before
        { method: 'popup', minutes: 60 }, // 1 hour before
      ],
    },
  };

  return event;
};

// Create a calendar event
const createCalendarEvent = async (appointment: AppointmentData): Promise<string> => {
  try {
    await ensureInitialized();

    if (!window.gapi || !window.gapi.client) {
      throw new Error('Google API client not initialized properly');
    }

    if (!isSignedIn()) {
      await signIn();
    }

    // Check again after sign in
    if (!isSignedIn()) {
      throw new Error('User did not complete sign in process');
    }

    // Make sure the calendar API is loaded
    if (!window.gapi.client.calendar) {
      console.log('Calendar API not loaded, loading now');
      await window.gapi.client.load('calendar', 'v3');
    }

    const event = appointmentToCalendarEvent(appointment);

    console.log('Creating calendar event with data:', event);

    const response = await window.gapi.client.calendar.events.insert({
      calendarId: 'primary',
      resource: event,
      sendUpdates: 'all', // Send email notifications to attendees
    });

    console.log('Calendar event created successfully:', response.result);

    return response.result.id || '';
  } catch (error) {
    console.error('Error creating calendar event:', error);
    throw error;
  }
};

// Get a calendar event by ID
const getCalendarEvent = async (eventId: string): Promise<CalendarEvent> => {
  try {
    await ensureInitialized();

    if (!window.gapi || !window.gapi.client) {
      throw new Error('Google API client not initialized properly');
    }

    if (!isSignedIn()) {
      await signIn();
    }

    // Make sure the calendar API is loaded
    if (!window.gapi.client.calendar) {
      console.log('Calendar API not loaded, loading now');
      await window.gapi.client.load('calendar', 'v3');
    }

    const response = await window.gapi.client.calendar.events.get({
      calendarId: 'primary',
      eventId: eventId,
    });

    return response.result;
  } catch (error) {
    console.error('Error getting calendar event:', error);
    throw error;
  }
};

// Update a calendar event
const updateCalendarEvent = async (eventId: string, appointment: AppointmentData): Promise<string> => {
  try {
    await ensureInitialized();

    if (!window.gapi || !window.gapi.client) {
      throw new Error('Google API client not initialized properly');
    }

    if (!isSignedIn()) {
      await signIn();
    }

    // Make sure the calendar API is loaded
    if (!window.gapi.client.calendar) {
      console.log('Calendar API not loaded, loading now');
      await window.gapi.client.load('calendar', 'v3');
    }

    const event = appointmentToCalendarEvent(appointment);

    const response = await window.gapi.client.calendar.events.update({
      calendarId: 'primary',
      eventId: eventId,
      resource: event,
      sendUpdates: 'all', // Send email notifications to attendees
    });

    return response.result.id || '';
  } catch (error) {
    console.error('Error updating calendar event:', error);
    throw error;
  }
};

// Delete a calendar event
const deleteCalendarEvent = async (eventId: string): Promise<void> => {
  try {
    await ensureInitialized();

    if (!window.gapi || !window.gapi.client) {
      throw new Error('Google API client not initialized properly');
    }

    if (!isSignedIn()) {
      await signIn();
    }

    // Make sure the calendar API is loaded
    if (!window.gapi.client.calendar) {
      console.log('Calendar API not loaded, loading now');
      await window.gapi.client.load('calendar', 'v3');
    }

    await window.gapi.client.calendar.events.delete({
      calendarId: 'primary',
      eventId: eventId,
      sendUpdates: 'all', // Send email notifications to attendees
    });

    return Promise.resolve();
  } catch (error) {
    console.error('Error deleting calendar event:', error);
    throw error;
  }
};

// Export the service
const googleCalendarService = {
  initGoogleApi,
  ensureInitialized,
  isSignedIn,
  signIn,
  signOut,
  createCalendarEvent,
  getCalendarEvent,
  updateCalendarEvent,
  deleteCalendarEvent,
};

export default googleCalendarService;

// TypeScript declarations are now in src/types/global.d.ts
