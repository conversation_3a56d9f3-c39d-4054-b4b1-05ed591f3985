/* User Menu Styles */
.user-menu-container {
  position: relative;
  margin-left: 1rem;
}

/* User <PERSON><PERSON> */
.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: var(--primary-100);
  transition: color 0.2s ease;
}

.user-button:hover {
  color: var(--accent-cyan);
}

/* User Avatar */
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid rgba(0, 247, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.user-avatar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--accent-cyan);
  opacity: 0.5;
}

.user-avatar-large {
  width: 40px;
  height: 40px;
  font-size: 16px;
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 240px;
  background-color: rgba(13, 21, 38, 0.95);
  border-radius: 8px;
  border: 1px solid rgba(0, 247, 255, 0.2);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 247, 255, 0.15);
  z-index: 50;
  overflow: hidden;
  transform-origin: top right;
  animation: dropdown-appear 0.2s ease-out forwards;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dropdown Header */
.user-dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(74, 107, 255, 0.2);
}

/* Dropdown Content */
.user-dropdown-content {
  padding: 8px;
}

/* Dropdown Menu Items */
.dropdown-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: var(--primary-200);
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin-bottom: 4px;
  font-size: 14px;
}

.dropdown-menu-item:hover {
  background-color: rgba(74, 107, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

.dropdown-menu-item svg {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: var(--accent-cyan);
  flex-shrink: 0;
}

/* Dropdown Divider */
.dropdown-divider {
  height: 1px;
  background-color: rgba(74, 107, 255, 0.15);
  margin: 8px 4px;
}

/* Sign Out Button */
.sign-out-button {
  color: var(--error) !important;
}

.sign-out-button:hover {
  background-color: rgba(255, 51, 102, 0.1) !important;
}

.sign-out-button svg {
  color: var(--error) !important;
}
