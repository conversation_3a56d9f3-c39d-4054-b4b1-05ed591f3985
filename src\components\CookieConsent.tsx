import { useState, useEffect } from 'react';

const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(false);

  useEffect(() => {
    // Check if user has already given consent
    const hasConsent = localStorage.getItem('cookie-consent');
    if (!hasConsent) {
      // Show the consent banner after a short delay
      const timer = setTimeout(() => {
        setShowConsent(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    // Store consent in localStorage
    localStorage.setItem('cookie-consent', 'true');

    // Notify Clarity about consent if it's available
    if (window.clarity && typeof window.clarity === 'function') {
      try {
        window.clarity('consent');
        console.log('Clarity consent set successfully');
      } catch (error) {
        console.error('Error setting Clarity consent:', error);
      }
    }

    // Hide the consent banner
    setShowConsent(false);
  };

  const handleDecline = () => {
    // Store decline in localStorage
    localStorage.setItem('cookie-consent', 'false');
    // Hide the consent banner
    setShowConsent(false);
  };

  if (!showConsent) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-dark-800/90 backdrop-blur-md z-50 p-4 shadow-lg border-t border-accent-cyan/30">
      <div className="container mx-auto flex flex-col md:flex-row items-center justify-between gap-4">
        <div className="text-white text-sm md:text-base">
          <p>
            We use cookies to enhance your experience on our website. By continuing to use our site, you consent to our use of cookies.
            <a href="/privacy-policy" className="text-accent-cyan ml-1 hover:underline">
              Learn more
            </a>
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={handleDecline}
            className="secondary-button button-rounded text-sm py-2 px-4"
          >
            Decline
          </button>
          <button
            onClick={handleAccept}
            className="primary-button button-rounded text-sm py-2 px-4"
          >
            Accept
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
