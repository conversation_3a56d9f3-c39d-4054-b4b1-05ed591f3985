const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

// Configuration
const appUrl = 'https://aidigitalrealtor.com/'; // Correct production URL
const outputDir = path.join(__dirname, 'output');
const pngFilename = 'digital-realtor-qrcode.png';
const svgFilename = 'digital-realtor-qrcode.svg';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Options for QR code generation
const qrOptions = {
  errorCorrectionLevel: 'H', // High error correction capability
  type: 'image/png',
  quality: 0.92,
  margin: 1,
  color: {
    dark: '#000000', // Black dots
    light: '#ffffff' // White background
  }
};

// Generate PNG QR code
QRCode.toFile(
  path.join(outputDir, pngFilename),
  appUrl,
  qrOptions,
  function (err) {
    if (err) {
      console.error('Error generating PNG QR code:', err);
    } else {
      console.log(`PNG QR code generated successfully: ${path.join(outputDir, pngFilename)}`);
    }
  }
);

// Generate SVG QR code
QRCode.toString(
  appUrl,
  { type: 'svg', errorCorrectionLevel: 'H', margin: 1 },
  function (err, svgString) {
    if (err) {
      console.error('Error generating SVG QR code:', err);
    } else {
      fs.writeFileSync(path.join(outputDir, svgFilename), svgString);
      console.log(`SVG QR code generated successfully: ${path.join(outputDir, svgFilename)}`);
    }
  }
);

// Also generate a terminal QR code for quick testing
QRCode.toString(appUrl, { type: 'terminal' }, function (err, terminalCode) {
  if (err) {
    console.error('Error generating terminal QR code:', err);
  } else {
    console.log('\nQR Code for quick testing (scan with your phone camera):');
    console.log(terminalCode);
  }
});
