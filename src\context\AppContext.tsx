import { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import userService, { User as UserType } from '../services/userService';

// Define types for our state and actions
export type ToolType = 'listing' | 'appointment' | 'analysis' | 'inspection' | 'lease';

interface AppState {
  // User state
  user: {
    isAuthenticated: boolean;
    _id: string | null;
    name: string | null;
    email: string | null;
    isAdmin: boolean;
    isRealtor: boolean;
    isVerified: boolean;
    token: string | null;
    role: string | null;
  };

  // UI state
  ui: {
    theme: 'light' | 'dark';
    sidebarOpen: boolean;
    loading: boolean;
    error: string | null;
  };

  // Agent tools state
  agentTools: {
    activeTool: ToolType | null;
    isProcessing: boolean;
    results: Record<ToolType, any>;
    error: string | null;
  };

  // API keys
  openAiApiKey: string;
}

type AppAction =
  // User actions
  | { type: 'LOGIN_SUCCESS'; payload: UserType }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER_PROFILE'; payload: UserType }

  // UI actions
  | { type: 'TOGGLE_THEME' }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }

  // Agent tools actions
  | { type: 'SET_ACTIVE_TOOL'; payload: ToolType }
  | { type: 'START_PROCESSING_TOOL' }
  | { type: 'SET_TOOL_RESULTS'; payload: { tool: ToolType; data: any } }
  | { type: 'SET_TOOL_ERROR'; payload: string }
  | { type: 'RESET_TOOL' };

// Initial state
const initialState: AppState = {
  user: {
    isAuthenticated: false,
    _id: null,
    name: null,
    email: null,
    isAdmin: false,
    isRealtor: false,
    isVerified: false,
    token: null,
    role: null,
  },
  ui: {
    theme: 'dark',
    sidebarOpen: false,
    loading: false,
    error: null,
  },
  agentTools: {
    activeTool: null,
    isProcessing: false,
    results: {
      listing: null,
      appointment: null,
      analysis: null,
      inspection: null,
      lease: null,
    },
    error: null,
  },
  // Use environment variable or a default API key for development
  openAiApiKey: 'sk-Gy9Yx9Yx9Yx9Yx9Yx9Yx9Yx9Yx9Yx9Yx9Yx9Yx9Y',
};

// Create reducer function
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    // User actions
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: {
          isAuthenticated: true,
          _id: action.payload._id,
          name: action.payload.name,
          email: action.payload.email,
          isAdmin: action.payload.isAdmin,
          isRealtor: action.payload.isRealtor,
          isVerified: action.payload.isVerified !== undefined ? action.payload.isVerified : true,
          token: action.payload.token || null,
          role: action.payload.isRealtor ? 'agent' : 'user',
        },
      };
    case 'LOGOUT':
      return {
        ...state,
        user: {
          isAuthenticated: false,
          _id: null,
          name: null,
          email: null,
          isAdmin: false,
          isRealtor: false,
          isVerified: false,
          token: null,
          role: null,
        },
      };
    case 'UPDATE_USER_PROFILE':
      return {
        ...state,
        user: {
          isAuthenticated: true,
          _id: action.payload._id,
          name: action.payload.name,
          email: action.payload.email,
          isAdmin: action.payload.isAdmin,
          isRealtor: action.payload.isRealtor,
          isVerified: action.payload.isVerified !== undefined ? action.payload.isVerified : state.user.isVerified,
          token: action.payload.token || state.user.token,
          role: action.payload.isRealtor ? 'agent' : 'user',
        },
      };

    // UI actions
    case 'TOGGLE_THEME':
      return {
        ...state,
        ui: {
          ...state.ui,
          theme: state.ui.theme === 'light' ? 'dark' : 'light',
        },
      };
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        ui: {
          ...state.ui,
          sidebarOpen: !state.ui.sidebarOpen,
        },
      };
    case 'SET_SIDEBAR':
      return {
        ...state,
        ui: {
          ...state.ui,
          sidebarOpen: action.payload,
        },
      };
    case 'SET_LOADING':
      return {
        ...state,
        ui: {
          ...state.ui,
          loading: action.payload,
        },
      };
    case 'SET_ERROR':
      return {
        ...state,
        ui: {
          ...state.ui,
          error: action.payload,
        },
      };

    // Agent tools actions
    case 'SET_ACTIVE_TOOL':
      return {
        ...state,
        agentTools: {
          ...state.agentTools,
          activeTool: action.payload,
          error: null,
        },
      };
    case 'START_PROCESSING_TOOL':
      return {
        ...state,
        agentTools: {
          ...state.agentTools,
          isProcessing: true,
          error: null,
        },
      };
    case 'SET_TOOL_RESULTS':
      return {
        ...state,
        agentTools: {
          ...state.agentTools,
          isProcessing: false,
          results: {
            ...state.agentTools.results,
            [action.payload.tool]: action.payload.data,
          },
        },
      };
    case 'SET_TOOL_ERROR':
      return {
        ...state,
        agentTools: {
          ...state.agentTools,
          isProcessing: false,
          error: action.payload,
        },
      };
    case 'RESET_TOOL':
      return {
        ...state,
        agentTools: {
          ...state.agentTools,
          isProcessing: false,
          error: null,
          activeTool: null,
        },
      };
    default:
      return state;
  }
};

// Create context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;

  // Helper functions
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  updateProfile: (userData: { name?: string; email?: string; password?: string }) => Promise<void>;
  toggleTheme: () => void;
  toggleSidebar: () => void;
  setSidebar: (isOpen: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Agent tools helpers
  setActiveTool: (tool: ToolType) => void;
  processToolRequest: (tool: ToolType, data: any) => Promise<void>;
  resetTool: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Create provider component
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider = ({ children }: AppProviderProps) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Check for saved user on mount
  useEffect(() => {
    const userInfoString = localStorage.getItem('userInfo');
    if (userInfoString) {
      try {
        const userInfo = JSON.parse(userInfoString) as UserType;
        dispatch({ type: 'LOGIN_SUCCESS', payload: userInfo });
      } catch (error) {
        console.error('Error parsing user info:', error);
        localStorage.removeItem('userInfo');
        localStorage.removeItem('userToken');
      }
    }
  }, []);

  // User helper functions
  const login = async (email: string, password: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      const userData = await userService.login({ email, password });

      // Check if the user's email is verified
      if (userData.isVerified === false) {
        throw new Error('Email not verified. Please check your inbox for the verification email.');
      }

      dispatch({ type: 'LOGIN_SUCCESS', payload: userData });
    } catch (error) {
      console.error('Login error:', error);
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'An error occurred during login'
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const register = async (name: string, email: string, password: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Register the user but don't automatically log them in
      await userService.register({ name, email, password });

      // Send verification email
      await userService.sendVerificationEmail(email);

      // Return success without logging in
      return { success: true, message: 'Registration successful. Please check your email for verification.' };
    } catch (error) {
      console.error('Registration error:', error);
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'An error occurred during registration'
      });
      return { success: false, message: error instanceof Error ? error.message : 'Registration failed' };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = () => {
    userService.logout();
    dispatch({ type: 'LOGOUT' });
  };

  const updateProfile = async (userData: { name?: string; email?: string; password?: string }) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      const updatedUser = await userService.updateProfile(userData);
      dispatch({ type: 'UPDATE_USER_PROFILE', payload: updatedUser });
    } catch (error) {
      console.error('Update profile error:', error);
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'An error occurred while updating profile'
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // UI helper functions
  const toggleTheme = () => {
    dispatch({ type: 'TOGGLE_THEME' });
  };

  const toggleSidebar = () => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  };

  const setSidebar = (isOpen: boolean) => {
    dispatch({ type: 'SET_SIDEBAR', payload: isOpen });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setError = (error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  // Agent tools helper functions
  const setActiveTool = (tool: ToolType) => {
    dispatch({ type: 'SET_ACTIVE_TOOL', payload: tool });
  };

  const processToolRequest = async (tool: ToolType, data: any) => {
    dispatch({ type: 'START_PROCESSING_TOOL' });

    try {
      // In a real application, this would be an API call to an AI service
      // For now, we'll simulate a response after a delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      let result;

      switch (tool) {
        case 'listing':
          result = generateListingResponse(data);
          break;
        case 'appointment':
          result = generateAppointmentResponse(data);
          break;
        case 'analysis':
          result = generateAnalysisResponse(data);
          break;
        case 'inspection':
          result = generateInspectionResponse(data);
          break;
        case 'lease':
          result = generateLeaseResponse(data);
          break;
        default:
          throw new Error('Invalid tool type');
      }

      dispatch({
        type: 'SET_TOOL_RESULTS',
        payload: { tool, data: result }
      });
    } catch (error) {
      dispatch({
        type: 'SET_TOOL_ERROR',
        payload: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  };

  const resetTool = () => {
    dispatch({ type: 'RESET_TOOL' });
  };

  return (
    <AppContext.Provider
      value={{
        state,
        dispatch,
        login,
        register,
        logout,
        updateProfile,
        toggleTheme,
        toggleSidebar,
        setSidebar,
        setLoading,
        setError,
        setActiveTool,
        processToolRequest,
        resetTool
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

// Custom hook for using the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Mock response generators
const generateListingResponse = (data: any) => {
  return {
    title: `Beautiful ${data.bedrooms} Bedroom ${data.propertyType} in ${data.location}`,
    description: `This stunning ${data.propertyType.toLowerCase()} features ${data.bedrooms} bedrooms and ${data.bathrooms} bathrooms with approximately ${data.squareFeet} square feet of living space. Located in the desirable ${data.location} area, this property offers ${data.features.join(', ')}. Perfect for ${data.targetBuyer}. Don't miss this opportunity to own your dream home!`,
    keyPoints: [
      `${data.bedrooms} bedrooms, ${data.bathrooms} bathrooms`,
      `Approximately ${data.squareFeet} square feet`,
      `Located in ${data.location}`,
      `Features: ${data.features.join(', ')}`,
      `Ideal for: ${data.targetBuyer}`,
    ],
    suggestedPrice: `$${(parseInt(data.estimatedValue) * 1.05).toLocaleString()}`,
    marketingTips: [
      'Schedule professional photography to highlight the property\'s best features',
      'Create a virtual tour to attract remote buyers',
      'Host an open house within the first week of listing',
      'Promote on social media targeting the ideal buyer demographic',
    ],
  };
};

const generateAppointmentResponse = (data: any) => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const dayAfterTomorrow = new Date(today);
  dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' });
  };

  return {
    client: {
      name: data.clientName,
      email: data.clientEmail,
      phone: data.clientPhone,
    },
    property: {
      address: data.propertyAddress,
      type: data.propertyType,
    },
    suggestedSlots: [
      {
        date: formatDate(tomorrow),
        slots: ['10:00 AM', '2:00 PM', '4:30 PM'],
      },
      {
        date: formatDate(dayAfterTomorrow),
        slots: ['9:30 AM', '1:00 PM', '3:30 PM'],
      },
    ],
    preparationTips: [
      'Review property details and recent comparable sales before the meeting',
      'Prepare answers to common questions about the neighborhood',
      'Have financing options ready to discuss',
      'Bring property disclosure documents',
    ],
    followUpReminder: `Send a follow-up email to ${data.clientName} within 24 hours of the appointment`,
  };
};

const generateAnalysisResponse = (data: any) => {
  return {
    subjectProperty: {
      address: data.propertyAddress,
      price: `$${parseInt(data.propertyPrice).toLocaleString()}`,
      pricePerSqFt: `$${Math.round(parseInt(data.propertyPrice) / parseInt(data.propertySqFt))}`,
      daysOnMarket: Math.floor(Math.random() * 30) + 1,
    },
    comparableProperties: [
      {
        address: '123 Nearby Street',
        price: `$${(parseInt(data.propertyPrice) * 0.95).toLocaleString()}`,
        pricePerSqFt: `$${Math.round(parseInt(data.propertyPrice) * 0.95 / parseInt(data.propertySqFt))}`,
        daysOnMarket: Math.floor(Math.random() * 45) + 1,
        distanceFromSubject: '0.3 miles',
        keyDifferences: 'Similar size, one fewer bathroom, updated kitchen',
      },
      {
        address: '456 Close Avenue',
        price: `$${(parseInt(data.propertyPrice) * 1.05).toLocaleString()}`,
        pricePerSqFt: `$${Math.round(parseInt(data.propertyPrice) * 1.05 / parseInt(data.propertySqFt))}`,
        daysOnMarket: Math.floor(Math.random() * 60) + 1,
        distanceFromSubject: '0.5 miles',
        keyDifferences: 'Larger lot size, similar interior, older roof',
      },
      {
        address: '789 Nearby Boulevard',
        price: `$${(parseInt(data.propertyPrice) * 1.02).toLocaleString()}`,
        pricePerSqFt: `$${Math.round(parseInt(data.propertyPrice) * 1.02 / parseInt(data.propertySqFt))}`,
        daysOnMarket: Math.floor(Math.random() * 30) + 1,
        distanceFromSubject: '0.7 miles',
        keyDifferences: 'Recently renovated, smaller square footage, better school district',
      },
    ],
    marketTrends: {
      averageDaysOnMarket: Math.floor(Math.random() * 40) + 20,
      medianSalePrice: `$${(parseInt(data.propertyPrice) * 1.01).toLocaleString()}`,
      priceChangeLastYear: `+${Math.floor(Math.random() * 8) + 2}%`,
      inventoryLevel: `${Math.floor(Math.random() * 3) + 1} months`,
    },
    pricingRecommendation: {
      suggestedListPrice: `$${(parseInt(data.propertyPrice) * 1.03).toLocaleString()}`,
      rationale: 'Based on recent comparable sales, current market conditions, and property features',
      negotiationStrategy: 'Price slightly above market to allow room for negotiation while remaining competitive',
    },
  };
};

const generateInspectionResponse = (data: any) => {
  return {
    propertyOverview: {
      address: data.propertyAddress,
      yearBuilt: data.yearBuilt,
      squareFootage: data.squareFootage,
      propertyType: data.propertyType,
    },
    inspectionSummary: {
      overallCondition: data.overallCondition,
      majorIssues: data.majorIssues.length,
      minorIssues: data.minorIssues.length,
      safetyHazards: data.safetyHazards.length,
    },
    keyFindings: {
      structural: {
        condition: data.structuralCondition,
        issues: data.structuralIssues,
        recommendations: [
          'Schedule a follow-up evaluation with a structural engineer for any major concerns',
          'Monitor foundation for any signs of movement or cracking',
          'Ensure proper drainage away from the foundation',
        ],
      },
      electrical: {
        condition: data.electricalCondition,
        issues: data.electricalIssues,
        recommendations: [
          'Have a licensed electrician address any outdated wiring or safety concerns',
          'Consider upgrading electrical panel if at capacity',
          'Install GFCI outlets in wet areas if not already present',
        ],
      },
      plumbing: {
        condition: data.plumbingCondition,
        issues: data.plumbingIssues,
        recommendations: [
          'Address any active leaks immediately',
          'Consider replacing older pipes if showing signs of corrosion',
          'Check water pressure and drainage throughout the home',
        ],
      },
      roofing: {
        condition: data.roofingCondition,
        issues: data.roofingIssues,
        recommendations: [
          'Schedule regular roof inspections, especially after severe weather',
          'Clear gutters and downspouts regularly',
          'Address any missing or damaged shingles promptly',
        ],
      },
    },
    estimatedRepairCosts: {
      immediateRepairs: `$${Math.floor(Math.random() * 5000) + 1000}`,
      shortTermRepairs: `$${Math.floor(Math.random() * 10000) + 5000}`,
      longTermRepairs: `$${Math.floor(Math.random() * 20000) + 10000}`,
    },
    negotiationPoints: [
      'Request credit for immediate safety concerns and major repairs',
      'Consider asking for warranty coverage for major systems',
      'Prioritize structural and water-related issues in negotiations',
    ],
  };
};

const generateLeaseResponse = (data: any) => {
  return {
    leaseOverview: {
      propertyAddress: data.propertyAddress,
      leaseType: data.leaseType,
      leaseTerm: data.leaseTerm,
      monthlyRent: `$${parseInt(data.monthlyRent).toLocaleString()}`,
    },
    keyTerms: {
      securityDeposit: `$${parseInt(data.securityDeposit).toLocaleString()}`,
      petPolicy: data.petPolicy,
      latePaymentTerms: data.latePaymentTerms,
      renewalOptions: data.renewalOptions,
    },
    potentialConcerns: [
      {
        clause: 'Early Termination',
        issue: 'High penalty for early termination without clear conditions',
        recommendation: 'Negotiate more flexible terms or specific conditions for early termination',
      },
      {
        clause: 'Maintenance Responsibilities',
        issue: 'Tenant responsible for all maintenance including major systems',
        recommendation: 'Clarify landlord responsibility for major systems and structural elements',
      },
      {
        clause: 'Rent Increases',
        issue: 'Allows for unlimited rent increases with 30-day notice',
        recommendation: 'Negotiate cap on annual increases or longer notice period',
      },
    ],
    marketComparison: {
      averageRent: `$${Math.round(parseInt(data.monthlyRent) * 0.98).toLocaleString()}`,
      rentTrend: `+${Math.floor(Math.random() * 5) + 1}% annually`,
      typicalTerms: '12-month lease with 60-day renewal notice',
      competitiveAdvantages: 'Close to amenities, newer appliances, in-unit laundry',
    },
    recommendations: [
      'Request clarification on maintenance responsibilities',
      'Negotiate cap on annual rent increases',
      'Add specific conditions for security deposit return',
      'Include right to sublease with landlord approval',
    ],
  };
};
