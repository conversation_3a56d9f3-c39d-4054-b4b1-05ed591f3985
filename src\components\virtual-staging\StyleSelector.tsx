import React from 'react';
import { DesignStyle } from '../../pages/tools/VirtualStagingTool';
import { <PERSON>aCheck, FaPalette, FaTree, FaGem, FaMinusSquare, FaMountain } from 'react-icons/fa';

// Define keyframes for badge pulse animation
const badgePulseKeyframes = `
@keyframes badgePulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
`;

interface StyleSelectorProps {
  selectedStyle: DesignStyle;
  onStyleChange: (style: DesignStyle) => void;
  disabled?: boolean;
}

const StyleSelector: React.FC<StyleSelectorProps> = ({
  selectedStyle,
  onStyleChange,
  disabled = false
}) => {
  // Add the animation styles to the document when the component mounts
  React.useEffect(() => {
    // Create a style element to inject the keyframes
    const styleElement = document.createElement('style');
    styleElement.textContent = badgePulseKeyframes;
    document.head.appendChild(styleElement);

    // Clean up when component unmounts
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  // Define the available styles with descriptions and icons
  const styles: {
    value: DesignStyle;
    label: string;
    description: string;
    icon: React.ReactNode;
    textColor: string;
    borderColor: string;
    bgColor: string;
    ringColor: string;
    gradientFrom: string;
    gradientTo: string;
    shadowColor: string;
  }[] = [
    {
      value: 'Modern',
      label: 'Modern',
      description: 'Clean lines, minimal decoration, neutral colors with bold accents',
      icon: <FaPalette />,
      textColor: 'text-accent-cyan',
      borderColor: 'border-accent-cyan',
      bgColor: 'bg-accent-cyan',
      ringColor: 'ring-accent-cyan/60',
      gradientFrom: 'from-accent-cyan/30',
      gradientTo: 'to-accent-cyan/10',
      shadowColor: 'rgba(56, 189, 248, 0.6)'
    },
    {
      value: 'Rustic',
      label: 'Rustic',
      description: 'Natural materials, warm colors, cozy lived-in feel',
      icon: <FaTree />,
      textColor: 'text-green-500',
      borderColor: 'border-green-500',
      bgColor: 'bg-green-500',
      ringColor: 'ring-green-500/60',
      gradientFrom: 'from-green-600/30',
      gradientTo: 'to-green-500/10',
      shadowColor: 'rgba(34, 197, 94, 0.6)'
    },
    {
      value: 'Luxury',
      label: 'Luxury',
      description: 'Elegant high-end finishes with rich textures and sophisticated details',
      icon: <FaGem />,
      textColor: 'text-purple-500',
      borderColor: 'border-purple-500',
      bgColor: 'bg-purple-500',
      ringColor: 'ring-purple-500/60',
      gradientFrom: 'from-purple-600/30',
      gradientTo: 'to-purple-500/10',
      shadowColor: 'rgba(168, 85, 247, 0.6)'
    },
    {
      value: 'Minimalist',
      label: 'Minimalist',
      description: 'Simplicity, functionality, and a "less is more" approach',
      icon: <FaMinusSquare />,
      textColor: 'text-gray-400',
      borderColor: 'border-gray-400',
      bgColor: 'bg-gray-400',
      ringColor: 'ring-gray-400/60',
      gradientFrom: 'from-gray-500/30',
      gradientTo: 'to-gray-400/10',
      shadowColor: 'rgba(156, 163, 175, 0.6)'
    },
    {
      value: 'Scandinavian',
      label: 'Scandinavian',
      description: 'Light airy spaces with natural elements and functional simplicity',
      icon: <FaMountain />,
      textColor: 'text-blue-400',
      borderColor: 'border-blue-400',
      bgColor: 'bg-blue-400',
      ringColor: 'ring-blue-400/60',
      gradientFrom: 'from-blue-500/30',
      gradientTo: 'to-blue-400/10',
      shadowColor: 'rgba(96, 165, 250, 0.6)'
    }
  ];

  return (
    <div className="grid grid-cols-1 gap-4" role="radiogroup" aria-label="Design Style Selection">
      {styles.map((style) => {
        const isSelected = selectedStyle === style.value;

        return (
          <div
            key={style.value}
            onClick={() => !disabled && onStyleChange(style.value)}
            className={`group relative p-5 rounded-xl border-2 transition-all duration-300 ${
              disabled
                ? 'opacity-70 cursor-not-allowed'
                : 'cursor-pointer hover:shadow-lg hover:translate-y-[-2px]'
            } ${
              isSelected
                ? `${style.borderColor} border-3 bg-gradient-to-br ${style.gradientFrom} ${style.gradientTo} ring-2 ${style.ringColor} ring-offset-2 ring-offset-dark-900 scale-[1.05] shadow-[0_0_15px_${style.shadowColor}]`
                : 'border-primary-700/30 bg-dark-800/50 hover:border-gray-400'
            }`}
            style={{
              boxShadow: isSelected ? `0 0 15px ${style.shadowColor}` : '',
              transform: isSelected ? 'scale(1.05)' : 'scale(1)'
            }}
            role="radio"
            aria-checked={isSelected}
            tabIndex={disabled ? -1 : 0}
            onKeyDown={(e) => {
              if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                onStyleChange(style.value);
              }
            }}
          >
            {/* Hidden actual radio input for accessibility */}
            <input
              type="radio"
              name="designStyle"
              value={style.value}
              checked={isSelected}
              onChange={() => !disabled && onStyleChange(style.value)}
              className="sr-only"
              disabled={disabled}
              aria-label={`${style.label} style`}
            />

            {/* Left border accent for selected card */}
            {isSelected && (
              <div className={`absolute left-0 top-0 bottom-0 w-2 ${style.bgColor} rounded-l-xl`} aria-hidden="true"></div>
            )}

            {/* Selected badge */}
            {isSelected && (
              <div
                className={`absolute top-3 right-3 ${style.bgColor} text-white text-xs px-3 py-1.5 rounded-full flex items-center shadow-lg`}
                style={{
                  boxShadow: `0 2px 8px ${style.shadowColor}`,
                  animation: 'badgePulse 2s infinite'
                }}
                aria-hidden="true"
              >
                <FaCheck className="text-white text-xs mr-2" />
                <span className="font-semibold">Selected</span>
              </div>
            )}

            {/* Add a subtle glow effect for selected cards */}
            {isSelected && (
              <div
                className="absolute inset-0 rounded-xl pointer-events-none"
                style={{
                  background: `radial-gradient(circle at center, ${style.shadowColor} 0%, transparent 70%)`,
                  opacity: 0.15,
                  mixBlendMode: 'screen'
                }}
                aria-hidden="true"
              ></div>
            )}

            <div className="flex items-center">
              {/* Style icon - consistent size and alignment */}
              <div
                className={`flex-shrink-0 w-14 h-14 rounded-full ${
                  isSelected
                    ? `${style.bgColor} shadow-lg`
                    : 'bg-dark-700/50'
                } flex items-center justify-center mr-5 transition-all duration-300 group-hover:scale-110`}
                style={{
                  boxShadow: isSelected ? `0 4px 12px ${style.shadowColor}` : '',
                  transform: isSelected ? 'translateY(-2px)' : ''
                }}
              >
                <span className={`text-2xl transition-colors duration-300 ${
                  isSelected
                    ? 'text-white'
                    : 'text-gray-400 group-hover:text-gray-300'
                }`}>
                  {style.icon}
                </span>
              </div>

              <div className="flex-1">
                <h4 className={`transition-all duration-300 ${
                  isSelected
                    ? `${style.textColor} text-xl font-bold tracking-wide`
                    : 'text-white text-base font-medium group-hover:text-gray-100'
                }`}
                style={{
                  textShadow: isSelected ? `0 0 8px ${style.shadowColor}` : ''
                }}
                >
                  {style.label}
                </h4>
                <p className={`text-sm leading-snug transition-colors duration-300 ${
                  isSelected
                    ? 'text-white'
                    : 'text-primary-300 group-hover:text-primary-200'
                }`}>
                  {style.description}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StyleSelector;
