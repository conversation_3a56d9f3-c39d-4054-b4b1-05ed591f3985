import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { FaUser, FaLock, FaSignInAlt } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import userService from '../services/userService';

const Login = () => {
  const { login } = useApp();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [unverifiedEmail, setUnverifiedEmail] = useState<string | null>(null);
  const [resendingVerification, setResendingVerification] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setUnverifiedEmail(null);
    setIsLoading(true);

    try {
      // In a real app, this would be an API call to authenticate
      // For demo purposes, we'll simulate a successful login
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if the email is verified (in a real app, this would be checked on the server)
      // For demo purposes, we'll simulate verification check
      const isVerified = !email.includes('unverified');

      if (!isVerified) {
        setUnverifiedEmail(email);
        setIsLoading(false);
        return;
      }

      // Check if email is for an agent (simple demo logic)
      const isAgent = email.includes('agent');
      const role = isAgent ? 'agent' : 'client';
      const name = email.split('@')[0]; // Use part of email as name for demo

      // Call login from context
      await login(email, password);

      // Navigate to appropriate page
      if (isAgent) {
        navigate('/agent');
      } else {
        navigate('/');
      }
    } catch (err) {
      setError('Invalid email or password');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (!unverifiedEmail) return;

    setResendingVerification(true);
    try {
      // In a real app, this would call the API to resend verification email
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Simulate sending verification email
      await userService.sendVerificationEmail(unverifiedEmail);
      setError('');
      setUnverifiedEmail(null);
      alert(`Verification email sent to ${unverifiedEmail}. Please check your inbox.`);
    } catch (err) {
      setError('Failed to resend verification email. Please try again.');
    } finally {
      setResendingVerification(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">Sign in to your account</h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <a href="#" className="font-medium text-primary-600 hover:text-primary-500">
              create a new account
            </a>
          </p>
        </div>

        <div className="bg-white p-8 rounded-xl shadow-md">
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
              {error}
            </div>
          )}

          {unverifiedEmail && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg">
              <h3 className="font-medium mb-2">Email not verified</h3>
              <p className="text-sm mb-3">
                Your email address <span className="font-medium">{unverifiedEmail}</span> has not been verified.
                Please check your inbox for the verification email.
              </p>
              <button
                onClick={handleResendVerification}
                disabled={resendingVerification}
                className="text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 font-medium py-1 px-3 rounded inline-flex items-center transition-colors"
              >
                {resendingVerification ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sending...
                  </>
                ) : (
                  'Resend verification email'
                )}
              </button>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <div className="flex items-center mb-1" style={{ display: 'flex', alignItems: 'center' }}>
                <FaUser style={{ width: '16px', height: '16px', marginRight: '8px', color: '#9ca3af', flexShrink: 0 }} />
                <label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email address
                </label>
              </div>
              <div className="mt-1 relative rounded-md shadow-sm">

                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Email address"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Hint: Use an email with "agent" to login as an agent (e.g., <EMAIL>).<br/>
                Use an email with "unverified" to simulate an unverified account.
              </p>
            </div>

            <div>
              <div className="flex items-center mb-1" style={{ display: 'flex', alignItems: 'center' }}>
                <FaLock style={{ width: '16px', height: '16px', marginRight: '8px', color: '#9ca3af', flexShrink: 0 }} />
                <label htmlFor="password" className="text-sm font-medium text-gray-700">
                  Password
                </label>
              </div>
              <div className="mt-1 relative rounded-md shadow-sm">

                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Password"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Any password will work for this demo
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <a href="#" className="font-medium text-primary-600 hover:text-primary-500">
                  Forgot your password?
                </a>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent rounded-md text-white bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <FaSignInAlt className="mr-2" />
                    Sign in
                  </span>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
