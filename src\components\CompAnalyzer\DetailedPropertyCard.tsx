import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>uler<PERSON><PERSON><PERSON>, FaCalendarAlt, FaStar, FaCheck, FaTimes } from 'react-icons/fa';

interface PropertyDetails {
  address: string;
  price: string;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  yearBuilt?: number;
  distanceFromTarget?: string;
  soldDate?: string;
  similarityScore?: number;
  adjustments?: string;
  zillowLink?: string;
  source?: string;
  included?: boolean;
  exclusionReason?: string | null;
}

interface DetailedPropertyCardProps {
  property: PropertyDetails;
}

const DetailedPropertyCard: React.FC<DetailedPropertyCardProps> = ({ property }) => {
  // Calculate similarity score breakdown (example calculation)
  const getSimilarityBreakdown = () => {
    if (!property.similarityScore) return null;
    
    // These are example weights - in a real implementation, you'd use the actual weights from the LLM
    const locationWeight = 0.4;
    const propertyWeight = 0.3;
    const recencyWeight = 0.2;
    const marketWeight = 0.1;
    
    // Calculate component scores (this is simplified - in reality would be more complex)
    const locationScore = property.distanceFromTarget 
      ? 100 - Math.min(parseFloat(property.distanceFromTarget.replace(' miles', '')) * 20, 50)
      : 80;
      
    const propertyScore = property.bedrooms && property.bathrooms && property.squareFeet
      ? 100 - (Math.abs(property.bedrooms - 3) * 10 + Math.abs(property.bathrooms - 2) * 10)
      : 75;
      
    const recencyScore = property.soldDate
      ? 100 - (new Date().getMonth() - new Date(property.soldDate).getMonth()) * 5
      : 85;
      
    const marketScore = 85; // Placeholder
    
    return {
      location: Math.round(locationScore),
      property: Math.round(propertyScore),
      recency: Math.round(recencyScore),
      market: Math.round(marketScore),
      weights: {
        location: locationWeight,
        property: propertyWeight,
        recency: recencyWeight,
        market: marketWeight
      }
    };
  };
  
  const similarityBreakdown = getSimilarityBreakdown();
  
  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 75) return 'text-blue-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };
  
  // Get background color based on score
  const getScoreBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-900/20';
    if (score >= 75) return 'bg-blue-900/20';
    if (score >= 60) return 'bg-yellow-900/20';
    return 'bg-red-900/20';
  };
  
  // Get border color based on score
  const getScoreBorderColor = (score: number) => {
    if (score >= 90) return 'border-green-700/30';
    if (score >= 75) return 'border-blue-700/30';
    if (score >= 60) return 'border-yellow-700/30';
    return 'border-red-700/30';
  };

  return (
    <div
      className={`bg-dark-800/50 rounded-xl p-4 border hover:shadow-lg transition-all duration-200 ${
        property.included === false
          ? 'border-red-700/30 hover:border-red-700/50'
          : property.included === true
            ? 'border-green-700/30 hover:border-green-700/50'
            : 'border-primary-700/30 hover:border-primary-700/50'
      }`}
    >
      {/* Price and Source/Status Badges */}
      <div className="flex justify-between items-center mb-2">
        <div className="text-accent-cyan font-bold">{property.price}</div>
        <div className="flex space-x-1">
          {property.source && (
            <span className="px-2 py-0.5 bg-dark-700 rounded-full text-xs text-primary-300">
              {property.source}
            </span>
          )}
          {property.included === true && (
            <span className="px-2 py-0.5 bg-green-900/30 rounded-full text-xs text-green-400 border border-green-700/30 flex items-center">
              <FaCheck size={8} className="mr-1" />
              Included
            </span>
          )}
          {property.included === false && (
            <span className="px-2 py-0.5 bg-red-900/30 rounded-full text-xs text-red-400 border border-red-700/30 flex items-center">
              <FaTimes size={8} className="mr-1" />
              Excluded
            </span>
          )}
        </div>
      </div>

      {/* Address */}
      <div className="text-white text-sm font-medium mb-3 truncate">{property.address}</div>

      {/* Property Details Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
        <div className="flex items-center">
          <FaBed className="text-primary-400 mr-1.5" />
          <span className="text-white">{property.bedrooms}</span>
        </div>
        <div className="flex items-center">
          <FaBath className="text-primary-400 mr-1.5" />
          <span className="text-white">{property.bathrooms}</span>
        </div>
        <div className="flex items-center">
          <FaRulerCombined className="text-primary-400 mr-1.5" />
          <span className="text-white">{property.squareFeet} sqft</span>
        </div>
        <div className="flex items-center">
          <FaCalendarAlt className="text-primary-400 mr-1.5" />
          <span className="text-white">{property.yearBuilt}</span>
        </div>
      </div>

      {/* Distance and Sold Date */}
      <div className="flex flex-wrap gap-2 mb-2 text-xs">
        <div className="bg-dark-900/60 px-2 py-1 rounded text-primary-200">
          Distance: {property.distanceFromTarget}
        </div>
        {property.soldDate && (
          <div className="bg-dark-900/60 px-2 py-1 rounded text-primary-200">
            Sold: {property.soldDate}
          </div>
        )}
      </div>

      {/* Similarity Score with Breakdown */}
      {property.similarityScore && (
        <div className="mb-3">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-primary-300">Similarity Score:</span>
            <span className={`text-sm font-bold ${getScoreColor(property.similarityScore)}`}>
              {property.similarityScore}%
            </span>
          </div>
          
          {similarityBreakdown && (
            <div className="grid grid-cols-2 gap-1 text-xs">
              <div className="flex justify-between items-center">
                <span className="text-primary-400">Location:</span>
                <span className={getScoreColor(similarityBreakdown.location)}>
                  {similarityBreakdown.location}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-primary-400">Property:</span>
                <span className={getScoreColor(similarityBreakdown.property)}>
                  {similarityBreakdown.property}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-primary-400">Recency:</span>
                <span className={getScoreColor(similarityBreakdown.recency)}>
                  {similarityBreakdown.recency}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-primary-400">Market:</span>
                <span className={getScoreColor(similarityBreakdown.market)}>
                  {similarityBreakdown.market}%
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Adjustments */}
      {property.adjustments && (
        <div className="text-xs text-primary-200 mb-2 bg-dark-900/40 p-2 rounded">
          <span className="text-primary-300">Adjustments: </span>
          {property.adjustments}
        </div>
      )}

      {/* Exclusion Reason */}
      {property.exclusionReason && (
        <div className="text-xs text-red-400 mb-2 bg-red-900/10 p-2 rounded border border-red-900/20">
          <span className="text-red-300">Exclusion Reason: </span>
          {property.exclusionReason}
        </div>
      )}

      {/* Zillow Link */}
      {property.zillowLink && (
        <div className="mt-3 flex justify-end">
          <a
            href={property.zillowLink}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-3 py-1 bg-blue-600/20 hover:bg-blue-600/40 text-blue-400 text-xs rounded-full border border-blue-600/30 transition-all duration-200 hover:shadow-glow-secondary"
            title="This link will search for the property on Zillow"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Search on Zillow
          </a>
        </div>
      )}
    </div>
  );
};

export default DetailedPropertyCard;
