const express = require('express');
const router = express.Router();
const { PDFDocument } = require('pdf-lib');
const fs = require('fs');
const path = require('path');
const { Configuration, OpenAIApi } = require('openai');

// Initialize OpenAI API
const configuration = new Configuration({
  apiKey: process.env.OPENAI_API_KEY,
});
const openai = new OpenAIApi(configuration);

// Path to the contract template PDF
const CONTRACT_TEMPLATE_PATH = path.join(__dirname, '../../contract-template.pdf');

// Path to save generated PDFs
const GENERATED_PDFS_DIR = path.join(__dirname, '../../public/generated-contracts');

// Ensure the directory exists
if (!fs.existsSync(GENERATED_PDFS_DIR)) {
  fs.mkdirSync(GENERATED_PDFS_DIR, { recursive: true });
}

/**
 * Use OpenAI to infer missing contract details
 */
async function inferMissingDetails(formData) {
  try {
    const prompt = `
You are a real estate contract assistant. Based on the following user-provided details, suggest inferred values for missing fields such as earnest money, typical closing dates, HOA info, or included fixtures. Use Chicago real estate norms where applicable.

User Data:
${JSON.stringify(formData, null, 2)}

Response Format:
{
  "earnestMoney": number,
  "closingDate": "YYYY-MM-DD",
  "hoa": boolean,
  "monthlyFees": number,
  "fixturesIncluded": ["Fixture1", "Fixture2", ...]
}
`;

    const response = await openai.createCompletion({
      model: "gpt-4-turbo",
      prompt,
      max_tokens: 500,
      temperature: 0.7,
    });

    const inferredData = JSON.parse(response.data.choices[0].text.trim());
    return inferredData;
  } catch (error) {
    console.error('Error inferring missing details:', error);
    return {};
  }
}

/**
 * Fill PDF form fields with user data
 */
async function fillPdfForm(formData) {
  try {
    // Read the PDF template
    const pdfBytes = fs.readFileSync(CONTRACT_TEMPLATE_PATH);
    
    // Load the PDF document
    const pdfDoc = await PDFDocument.load(pdfBytes);
    
    // Get the form from the PDF
    const form = pdfDoc.getForm();
    
    // Map form data to PDF fields
    // Note: These field names should match the actual field names in the PDF
    // You'll need to update these based on the output of the extractPdfFields.js script
    
    // Buyer Information
    if (formData.buyer.fullName) {
      const buyerField = form.getTextField('Buyer_Name');
      if (buyerField) buyerField.setText(formData.buyer.fullName);
    }
    
    // Seller Information
    if (formData.seller.fullName) {
      const sellerField = form.getTextField('Seller_Name');
      if (sellerField) sellerField.setText(formData.seller.fullName);
    }
    
    // Property Information
    if (formData.property.address) {
      const addressField = form.getTextField('Property_Address');
      if (addressField) addressField.setText(formData.property.address);
      
      if (formData.property.unit) {
        const unitField = form.getTextField('Property_Unit');
        if (unitField) unitField.setText(formData.property.unit);
      }
    }
    
    // Parking Information
    if (formData.property.parkingIncluded) {
      const parkingField = form.getCheckBox('Parking_Included');
      if (parkingField) parkingField.check();
      
      if (formData.property.parkingDetails) {
        const parkingDetailsField = form.getTextField('Parking_Details');
        if (parkingDetailsField) parkingDetailsField.setText(formData.property.parkingDetails);
      }
    }
    
    // Financial Information
    if (formData.financials.purchasePrice) {
      const priceField = form.getTextField('Purchase_Price');
      if (priceField) priceField.setText(`$${formData.financials.purchasePrice}`);
    }
    
    if (formData.financials.earnestMoney) {
      const earnestMoneyField = form.getTextField('Earnest_Money');
      if (earnestMoneyField) earnestMoneyField.setText(`$${formData.financials.earnestMoney}`);
    }
    
    if (formData.financials.closingDate) {
      const closingDateField = form.getTextField('Closing_Date');
      if (closingDateField) {
        const date = new Date(formData.financials.closingDate);
        const formattedDate = date.toLocaleDateString('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric'
        });
        closingDateField.setText(formattedDate);
      }
    }
    
    // HOA Information
    if (formData.associations.hoa) {
      const hoaField = form.getCheckBox('HOA_Exists');
      if (hoaField) hoaField.check();
      
      if (formData.associations.monthlyFees) {
        const feesField = form.getTextField('HOA_Fees');
        if (feesField) feesField.setText(`$${formData.associations.monthlyFees}`);
      }
    }
    
    // Fixtures
    if (formData.fixturesIncluded && formData.fixturesIncluded.length > 0) {
      const fixturesField = form.getTextField('Fixtures_Included');
      if (fixturesField) fixturesField.setText(formData.fixturesIncluded.join(', '));
    }
    
    // Additional Terms
    if (formData.additionalTerms) {
      const termsField = form.getTextField('Additional_Terms');
      if (termsField) termsField.setText(formData.additionalTerms);
    }
    
    // Flatten the form (makes it non-editable)
    form.flatten();
    
    // Save the filled PDF
    const pdfBytes = await pdfDoc.save();
    
    // Generate a unique filename
    const timestamp = Date.now();
    const filename = `contract_${timestamp}.pdf`;
    const filePath = path.join(GENERATED_PDFS_DIR, filename);
    
    // Write the PDF to disk
    fs.writeFileSync(filePath, pdfBytes);
    
    return `/generated-contracts/${filename}`;
  } catch (error) {
    console.error('Error filling PDF form:', error);
    throw error;
  }
}

/**
 * POST /api/contract-generator
 * Generate a contract based on user input
 */
router.post('/', async (req, res) => {
  try {
    let formData = req.body;
    
    // Infer missing details using OpenAI
    const inferredData = await inferMissingDetails(formData);
    
    // Merge inferred data with user data (only for missing fields)
    if (!formData.financials.earnestMoney && inferredData.earnestMoney) {
      formData.financials.earnestMoney = inferredData.earnestMoney;
    }
    
    if (!formData.financials.closingDate && inferredData.closingDate) {
      formData.financials.closingDate = inferredData.closingDate;
    }
    
    if (!formData.associations.hoa && inferredData.hoa !== undefined) {
      formData.associations.hoa = inferredData.hoa;
    }
    
    if (!formData.associations.monthlyFees && inferredData.monthlyFees) {
      formData.associations.monthlyFees = inferredData.monthlyFees;
    }
    
    if (formData.fixturesIncluded.length === 0 && inferredData.fixturesIncluded) {
      formData.fixturesIncluded = inferredData.fixturesIncluded;
    }
    
    // Fill the PDF form with the data
    const pdfUrl = await fillPdfForm(formData);
    
    // Return the URL to the generated PDF
    res.json({ success: true, pdfUrl });
  } catch (error) {
    console.error('Error generating contract:', error);
    res.status(500).json({ success: false, error: 'Failed to generate contract' });
  }
});

module.exports = router;
