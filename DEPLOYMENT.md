# Digital Realtor - Deployment Guide

This document provides instructions for deploying the Digital Realtor application to production.

## Prerequisites

- Node.js (v18 or higher)
- npm (v8 or higher)
- MongoDB Atlas account (or other MongoDB hosting)
- A hosting service (Heroku, Vercel, Netlify, AWS, etc.)

## Building for Production

### Option 1: Using the Production Build Script

1. Run the production build script:
   ```bash
   npm run build:prod
   ```

2. This will:
   - Clean any previous build
   - Install dependencies
   - Run linting
   - Build the application
   - Copy server files to the build directory
   - Copy necessary configuration files

3. The production-ready application will be available in the `build` directory.

### Option 2: Manual Build

1. Install dependencies:
   ```bash
   npm install
   ```

2. Build the application:
   ```bash
   NODE_ENV=production npm run build
   ```

3. Copy server files to the build directory:
   ```bash
   mkdir -p build/server
   cp -r server/* build/server/
   cp package.json build/
   cp .env.production build/.env
   ```

## Deployment Options

### Option 1: Traditional Hosting (VPS, Dedicated Server)

1. Transfer the `build` directory to your server.

2. Install production dependencies:
   ```bash
   cd build
   npm install --production
   ```

3. Start the server:
   ```bash
   npm start
   ```

4. For production environments, it's recommended to use a process manager like PM2:
   ```bash
   npm install -g pm2
   pm2 start server/server.js --name "digital-realtor"
   ```

### Option 2: Heroku

1. Create a new Heroku app:
   ```bash
   heroku create digital-realtor
   ```

2. Set environment variables:
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set MONGO_URI=your_mongo_uri
   heroku config:set JWT_SECRET=your_jwt_secret
   ```

3. Deploy to Heroku:
   ```bash
   git add .
   git commit -m "Prepare for Heroku deployment"
   git push heroku main
   ```

### Option 3: Vercel (Frontend) + MongoDB Atlas (Database)

1. Configure Vercel to use the `build` directory as the source.

2. Set up environment variables in the Vercel dashboard.

3. Deploy using the Vercel CLI or GitHub integration.

## Environment Variables

Ensure the following environment variables are set in your production environment:

- `NODE_ENV`: Set to `production`
- `PORT`: The port on which the server will run (often set by the hosting provider)
- `MONGO_URI`: Your MongoDB connection string
- `JWT_SECRET`: Secret key for JWT token generation

## Security Considerations

1. Ensure your MongoDB connection string and JWT secret are kept secure.
2. Use HTTPS for all production traffic.
3. Implement rate limiting for API endpoints.
4. Regularly update dependencies to patch security vulnerabilities.

## Monitoring and Maintenance

1. Set up logging with a service like Loggly, Papertrail, or ELK Stack.
2. Configure monitoring with tools like New Relic, Datadog, or Prometheus.
3. Set up automated backups for your MongoDB database.
4. Implement a CI/CD pipeline for seamless updates.

## Troubleshooting

If you encounter issues during deployment:

1. Check server logs for error messages.
2. Verify all environment variables are correctly set.
3. Ensure the MongoDB connection is working.
4. Check for port conflicts if running on a shared server.

For additional help, refer to the project documentation or contact the development team.
