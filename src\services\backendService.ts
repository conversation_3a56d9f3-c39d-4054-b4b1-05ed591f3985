import { InspectionReportData } from './inspectionReportService';

// Get backend URL from environment
const getBackendUrl = () => {
  // First check Vite environment
  if (import.meta.env.VITE_BACKEND_URL) {
    return import.meta.env.VITE_BACKEND_URL;
  }

  // Then check window environment (for dynamic configuration)
  if (typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_BACKEND_URL) {
    return window.__ENV__.VITE_BACKEND_URL;
  }

  // Fallback to environment-configured port
  return `http://localhost:${import.meta.env.VITE_PORT || 5000}`;
};

const API_BASE_URL = `${getBackendUrl()}/api`;

// Function to analyze images using Claude via backend
export const analyzeImagesWithClaudeBackend = async (
  images: string[]
): Promise<string> => {
  if (!images || images.length === 0) {
    return '';
  }

  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to <PERSON> backend for image analysis', 'color: #a27aff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/claude/analyze-images`);
      console.log('Number of images:', images.length);
    }

    const response = await fetch(`${API_BASE_URL}/claude/analyze-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ images }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from Claude backend (image analysis)', 'color: #a27aff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Analysis length:', data.analysis?.length || 0);
    }

    return data.analysis;
  } catch (error) {
    console.error('Error analyzing images with Claude backend:', error);
    throw error;
  }
};

// Function to generate an inspection report using Claude via backend
export const generateInspectionReportWithClaudeBackend = async (
  inspectionData: InspectionReportData
): Promise<string> => {
  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to Claude backend for report generation', 'color: #a27aff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/claude/generate-report`);
      console.log('Property Address:', inspectionData.propertyAddress);
      console.log('Image Analysis Included:', inspectionData.imageAnalysis ? 'Yes' : 'No');
    }

    const response = await fetch(`${API_BASE_URL}/claude/generate-report`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(inspectionData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from Claude backend (report generation)', 'color: #a27aff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Report length:', data.report?.length || 0);
    }

    return data.report;
  } catch (error) {
    console.error('Error generating inspection report with Claude backend:', error);
    throw error;
  }
};
