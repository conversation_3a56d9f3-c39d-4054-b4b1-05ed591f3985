import { useState, useEffect, useRef } from 'react';
import { fetchPlaceSuggestions } from '../services/placesApi';
import { FaMapMarkerAlt } from 'react-icons/fa';

/**
 * AddressAutocomplete component
 * Provides a search input with autocomplete suggestions for addresses
 *
 * @param {Object} props - Component props
 * @param {Function} props.onSelect - Callback function when an address is selected
 * @param {string} props.initialValue - Initial value for the input field
 * @param {string} props.placeholder - Placeholder text for the input field
 * @param {string} props.className - Additional CSS classes for the component
 * @param {string} props.inputClassName - Additional CSS classes for the input element
 * @param {string} props.dropdownClassName - Additional CSS classes for the dropdown
 * @returns {JSX.Element} AddressAutocomplete component
 */
export default function AddressAutocomplete({
  onSelect,
  initialValue = '',
  placeholder = 'Start typing an address...',
  className = '',
  inputClassName = '',
  dropdownClassName = ''
}) {
  const [input, setInput] = useState(initialValue);
  const [suggestions, setSuggestions] = useState([]);
  const [activeIndex, setActiveIndex] = useState(-1);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const timeoutRef = useRef(null);

  // Handle input change with debounce
  const handleInputChange = (e) => {
    const val = e.target.value;
    setInput(val);
    setShowDropdown(true);
    setActiveIndex(-1);

    // Clear any existing timeout
    if (timeoutRef.current) clearTimeout(timeoutRef.current);

    // Set loading state if input is long enough
    if (val.length > 2) {
      setLoading(true);

      // Debounce API call
      timeoutRef.current = setTimeout(async () => {
        const results = await fetchPlaceSuggestions(val);
        setSuggestions(results);
        setLoading(false);
      }, 300);
    } else {
      setSuggestions([]);
      setLoading(false);
    }
  };

  // Handle selection of an address
  const handleSelect = (val) => {
    setInput(val);
    setShowDropdown(false);
    setSuggestions([]);
    if (onSelect) onSelect(val);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setActiveIndex((prev) => (prev + 1) % suggestions.length);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setActiveIndex((prev) => (prev - 1 + suggestions.length) % suggestions.length);
    } else if (e.key === 'Enter' && activeIndex >= 0) {
      e.preventDefault();
      handleSelect(suggestions[activeIndex]);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setShowDropdown(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (!e.target.closest('.address-autocomplete-container')) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, []);

  return (
    <div className={`address-autocomplete-container relative w-full ${className}`}>
      <div className="relative">
        <input
          type="text"
          value={input}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => input.length > 2 && setShowDropdown(true)}
          placeholder={placeholder}
          className={`w-full px-4 py-3 bg-dark-800/80 border border-primary-700/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent ${inputClassName}`}
          aria-label="Address search"
          autoComplete="off"
        />
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-primary-400"></div>
          </div>
        )}
      </div>

      {showDropdown && suggestions.length > 0 && (
        <ul className={`absolute z-50 mt-1 w-full bg-dark-800/95 backdrop-blur-sm border border-primary-700/50 rounded-lg shadow-lg max-h-64 overflow-auto ${dropdownClassName}`}>
          {suggestions.map((suggestion, idx) => (
            <li
              key={idx}
              className={`py-3 pr-4 cursor-pointer text-sm flex items-center transition-all duration-200 group ${
                idx === activeIndex
                  ? 'bg-green-600/40 text-white border-l-4 border-green-400 pl-3'
                  : 'text-white pl-4 hover:bg-green-600/30 hover:border-l-4 hover:border-green-400 hover:pl-3'
              }`}
              onMouseDown={() => handleSelect(suggestion)}
              onMouseEnter={() => setActiveIndex(idx)}
            >
              <FaMapMarkerAlt className={`mr-2 flex-shrink-0 group-hover:text-green-400 ${idx === activeIndex ? 'text-green-400' : 'text-green-500/70'}`} />
              <span className={`truncate group-hover:text-green-300 ${idx === activeIndex ? 'text-green-300' : ''}`}>{suggestion}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
