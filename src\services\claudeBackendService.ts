// Claude Backend Service
// This service handles all communication with the Claude API through the backend

// Get backend URL from environment (check both Vite env and window env)
const getBackendUrl = () => {
  // First check Vite environment
  if (import.meta.env.VITE_BACKEND_URL) {
    return import.meta.env.VITE_BACKEND_URL;
  }

  // Then check window environment (for dynamic configuration)
  if (typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_BACKEND_URL) {
    return window.__ENV__.VITE_BACKEND_URL;
  }

  // Fallback to localhost:5000 for development
  return 'http://localhost:5000';
};

// Get API base URL dynamically
const getApiBaseUrl = () => {
  const backendUrl = getBackendUrl();
  const apiUrl = `${backendUrl}/api`;

  // Debug logging for development
  if (import.meta.env.DEV) {
    console.log('%c[DEV] Claude Backend Service - Dynamic URL Resolution', 'color: #a27aff; font-weight: bold');
    console.log('Vite VITE_BACKEND_URL:', import.meta.env.VITE_BACKEND_URL);
    console.log('Window VITE_BACKEND_URL:', typeof window !== 'undefined' && window.__ENV__ ? window.__ENV__.VITE_BACKEND_URL : 'undefined');
    console.log('Resolved Backend URL:', backendUrl);
    console.log('Final API URL:', apiUrl);
  }

  return apiUrl;
};

// Function to generate a property listing using Claude via backend
export const generatePropertyListingWithClaude = async (propertyData: any): Promise<string> => {
  try {
    // Validate and format required fields
    const formattedData = {
      propertyType: propertyData.propertyType || '',
      bedrooms: propertyData.bedrooms ? String(propertyData.bedrooms) : '',
      bathrooms: propertyData.bathrooms ? String(propertyData.bathrooms) : '',
      squareFeet: propertyData.squareFeet ? String(propertyData.squareFeet) : '',
      location: propertyData.location || '',
      price: propertyData.price ? String(propertyData.price).replace(/,/g, '') : '',
      yearBuilt: propertyData.yearBuilt || '',
      features: propertyData.features || '',
      description: propertyData.description || '',
      llmProvider: propertyData.llmProvider || 'claude',
      listingType: propertyData.listingType || 'sale'
    };

    // Validate required fields before sending
    const requiredFields = ['propertyType', 'bedrooms', 'bathrooms', 'squareFeet', 'location', 'price'];
    const missingFields = requiredFields.filter(field => !formattedData[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to Claude backend for property listing generation', 'color: #a27aff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/claude/generate-listing`);
      console.log('Property data:', formattedData);
    }

    const response = await fetch(`${API_BASE_URL}/claude/generate-listing`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formattedData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from Claude backend (property listing)', 'color: #a27aff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Listing length:', data.listing?.length || 0);
    }

    return data.listing;
  } catch (error) {
    console.error('Error generating property listing with Claude backend:', error);
    throw error;
  }
};

// Function to extract property data from natural language using Claude via backend
export const extractPropertyDataWithClaude = async (naturalLanguageInput: string): Promise<any> => {
  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to Claude backend for property data extraction', 'color: #a27aff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/claude/extract-property-data`);
      console.log('Input length:', naturalLanguageInput.length);
    }

    const response = await fetch(`${API_BASE_URL}/claude/extract-property-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ naturalLanguageInput }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from Claude backend (property data extraction)', 'color: #a27aff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Extracted data:', data.extractedData);
    }

    return data.extractedData;
  } catch (error) {
    console.error('Error extracting property data with Claude backend:', error);
    throw error;
  }
};

// Function to analyze comparable properties using Claude via backend
export const analyzeCompsWithClaude = async (
  address: string,
  propertyDetails?: {
    squareFeet?: number;
    bedrooms?: number;
    bathrooms?: number;
    lotSize?: number;
    yearBuilt?: number;
    condition?: string;
    uniqueFeatures?: string[];
    searchRadius?: number;
    filterMode?: 'strict' | 'soft';
  },
  marketTrends?: {
    averagePricePerSqFt?: string;
    medianSalePrice?: string;
    averageDaysOnMarket?: number;
    priceChangeLastYear?: string;
    inventoryLevel?: string;
    marketType?: string;
  }
): Promise<any> => {
  try {
    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to Claude backend for comp analysis', 'color: #a27aff; font-weight: bold');
      console.log('Endpoint:', `${API_BASE_URL}/claude/analyze-comps`);
      console.log('Address:', address);
      if (propertyDetails) console.log('Property Details:', propertyDetails);
      if (marketTrends) console.log('Market Trends:', marketTrends);
    }

    const response = await fetch(`${API_BASE_URL}/claude/analyze-comps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address, propertyDetails, marketTrends }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from Claude backend (comp analysis)', 'color: #a27aff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Analysis data:', data);
    }

    return data;
  } catch (error) {
    console.error('Error analyzing comps with Claude backend:', error);
    throw error;
  }
};

// Function to extract appointment data from natural language using Claude via backend
export const extractAppointmentDataWithClaude = async (naturalLanguageInput: string): Promise<any> => {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const endpoint = `${apiBaseUrl}/claude/extract-appointment-data`;

    // Log request in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Sending request to Claude backend for appointment data extraction', 'color: #a27aff; font-weight: bold');
      console.log('Endpoint:', endpoint);
      console.log('Input length:', naturalLanguageInput.length);
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ naturalLanguageInput }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Backend API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    // Log response in development mode
    if (import.meta.env.DEV) {
      console.log('%c[DEV] Received response from Claude backend (appointment data extraction)', 'color: #a27aff; font-weight: bold');
      console.log('Response status:', response.status);
      console.log('Extracted data:', data.extractedData);
    }

    return data.extractedData;
  } catch (error) {
    console.error('Error extracting appointment data with Claude backend:', error);
    throw error;
  }
};
