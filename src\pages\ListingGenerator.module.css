/* ListingGenerator.module.css */
.pageContainer {
  padding: 2rem 1rem;
}

@media (min-width: 768px) {
  .pageContainer {
    padding: 3rem 2rem;
  }
}

.pageTitle {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(to right, #00ffff, #00b9ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.pageSubtitle {
  font-size: 1.125rem;
  color: var(--primary-200);
  margin-bottom: 2rem;
  max-width: 800px;
}

.card {
  background: rgba(13, 21, 38, 0.7);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.cardHeader {
  padding: 1.75rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  background: rgba(13, 21, 38, 0.5);
}

.cardTitle {
  font-size: 1.625rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.875rem;
}

.cardIcon {
  color: var(--accent-cyan);
  font-size: 1.375rem;
}

.cardBody {
  padding: 2rem;
}

@media (min-width: 768px) {
  .cardBody {
    padding: 2.5rem;
  }
}

@media (max-width: 639px) {
  .cardBody {
    padding: 1.5rem;
  }

  .cardHeader {
    padding: 1.5rem;
  }
}

.formGrid {
  display: grid;
  gap: 2rem;
}

.formRow {
  display: grid;
  gap: 1.75rem;
  grid-template-columns: 1fr;
  margin-bottom: 0.5rem;
}

@media (min-width: 640px) {
  .formRow.cols2 {
    grid-template-columns: 1fr 1fr;
  }

  .formRow.cols3 {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media (max-width: 639px) {
  .formRow {
    gap: 1.5rem;
  }
}

.inputGroup {
  margin-bottom: 1.75rem;
}

.inputLabel {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  margin-bottom: 0.625rem;
  line-height: 1.4;
}

.inputIcon {
  color: var(--accent-cyan);
  margin-right: 0.625rem;
  flex-shrink: 0;
  font-size: 1rem;
}

.required {
  color: var(--accent-cyan);
  margin-left: 0.375rem;
  font-weight: bold;
}

.optional {
  color: var(--primary-400);
  font-weight: normal;
  margin-left: 0.375rem;
  font-size: 0.75rem;
  opacity: 0.9;
}

.input {
  width: 100%;
  padding: 0.875rem 1rem;
  min-height: 3.25rem;
  background-color: rgba(42, 63, 102, 0.3);
  border: 1px solid var(--primary-700);
  border-radius: 0.5rem;
  color: white;
  font-size: 1rem;
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
  appearance: none;
}

.input:focus {
  outline: none;
  border-color: var(--accent-cyan);
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.input::placeholder {
  color: var(--primary-400);
  opacity: 0.8;
}

.error .input {
  border-color: var(--error);
}

/* Select styling */
select.input {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2300ffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

select.input option {
  background-color: rgba(13, 21, 38, 1);
  color: white;
}

.textarea {
  min-height: 140px;
  resize: vertical;
  padding-top: 1rem;
  padding-bottom: 1rem;
  line-height: 1.6;
}

.buttonGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  margin-top: 2.5rem;
  justify-content: center;
}

.primaryButton {
  background: linear-gradient(to right, #00b9ff, #9900ff);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 9999px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  min-height: 3.25rem;
  cursor: pointer;
  font-size: 1rem;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.primaryButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.secondaryButton {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 9999px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  min-height: 3.25rem;
  cursor: pointer;
  font-size: 1rem;
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.secondaryButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.toggleContainer {
  display: flex;
  background: rgba(25, 36, 64, 0.6);
  border-radius: 9999px;
  padding: 0.375rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toggleButton {
  flex: 1;
  padding: 0.875rem 1.5rem;
  border-radius: 9999px;
  border: none;
  background: transparent;
  color: var(--primary-300);
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  min-height: 3rem;
  cursor: pointer;
}

.toggleButton:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
  color: var(--primary-200);
}

.toggleButton.active {
  background: linear-gradient(to right, #00b9ff, #9900ff);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  font-weight: 600;
  transform: translateY(-1px);
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.imageContainer {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  aspect-ratio: 16/9;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.imageContainer img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.imageContainer:hover img {
  transform: scale(1.05);
}

.removeButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(220, 38, 38, 0.8);
  color: white;
  border: none;
  border-radius: 9999px;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.imageContainer:hover .removeButton {
  opacity: 1;
}

.uploadButton {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.875rem 1.5rem;
  background: rgba(42, 63, 102, 0.3);
  border: 1px solid var(--primary-700);
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  min-height: 3.25rem;
  cursor: pointer;
}

.uploadButton:hover {
  border-color: var(--accent-cyan);
  background: rgba(42, 63, 102, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.1);
}

.uploadButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  border-color: var(--primary-700);
  background: rgba(42, 63, 102, 0.2);
}

.uploadIcon {
  color: var(--accent-cyan);
  font-size: 1rem;
}

.tipCard {
  background: rgba(25, 36, 64, 0.6);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin-bottom: 1.5rem;
}

.tipTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tipIcon {
  color: var(--accent-cyan);
}

.tipList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tipItem {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-200);
}

.tipItemIcon {
  color: var(--accent-cyan);
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.aiCard {
  background: rgba(25, 36, 64, 0.6);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

.aiTitle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.aiIconContainer {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  background: linear-gradient(to right, #00b9ff, #9900ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.aiTitleText {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.aiModel {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  background: rgba(0, 255, 255, 0.1);
  color: var(--accent-cyan);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.aiDescription {
  color: var(--primary-200);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.aiFeatures {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.aiFeatureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-300);
  font-size: 0.75rem;
}

.aiFeatureIcon {
  color: var(--accent-cyan);
  flex-shrink: 0;
}

.generatedContent {
  margin-top: 3rem;
  animation: fadeIn 0.5s ease;
}

.generatedHeader {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.generatedTitle {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(to right, #00ffff, #00b9ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.generatedBadge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.openAiBadge {
  background: rgba(0, 255, 255, 0.1);
  color: var(--accent-cyan);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.cachedBadge {
  background: rgba(0, 242, 195, 0.1);
  color: var(--success);
  border: 1px solid rgba(0, 242, 195, 0.2);
}

.generatedDivider {
  flex-grow: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin-left: 0.5rem;
}

.generatedContainer {
  background: rgba(25, 36, 64, 0.4);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

@media (min-width: 768px) {
  .generatedContainer {
    padding: 2rem;
  }
}

.propertyHeader {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.propertyPrice {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-cyan);
  margin-bottom: 0.5rem;
}

.propertyAddress {
  font-size: 1.25rem;
  color: white;
  margin-bottom: 1rem;
}

.propertyDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.propertyDetailItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-300);
}

.propertyDetailIcon {
  color: var(--primary-400);
}

.carousel {
  position: relative;
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  overflow: hidden;
}

.carouselImage {
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
  border-radius: 0.75rem;
  transition: transform 0.5s ease;
}

.carouselImage:hover {
  transform: scale(1.03);
}

.carouselArrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  z-index: 10;
  transition: all 0.2s ease;
}

.carouselArrow:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.05);
}

.carouselArrowLeft {
  left: 1rem;
}

.carouselArrowRight {
  right: 1rem;
  background: linear-gradient(to right, #00b9ff, #9900ff);
}

.carouselIndicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.carouselIndicator {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.carouselIndicator.active {
  background: linear-gradient(to right, #00b9ff, #9900ff);
}

.carouselIndicator:hover {
  background: rgba(255, 255, 255, 0.2);
}

.actionButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: flex-start;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loadingSpinner {
  position: relative;
  width: 4rem;
  height: 4rem;
  margin-bottom: 1.5rem;
}

.loadingCircle {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: var(--accent-cyan);
  animation: spin 1s linear infinite;
}

.loadingPulse {
  position: absolute;
  inset: -0.5rem;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

.loadingText {
  text-align: center;
  color: var(--primary-200);
  margin-bottom: 0.5rem;
}

.loadingSubtext {
  text-align: center;
  color: var(--primary-400);
  font-size: 0.875rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .buttonGroup {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .primaryButton, .secondaryButton {
    width: 100%;
    min-width: 100%;
  }

  .toggleContainer {
    flex-direction: column;
    border-radius: 0.75rem;
    gap: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .toggleButton {
    width: 100%;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
  }

  .toggleButton.active {
    transform: none;
  }

  .propertyDetails {
    flex-direction: column;
    gap: 0.75rem;
  }

  .carouselArrow {
    width: 2.5rem;
    height: 2.5rem;
  }

  .input, .textarea, .uploadButton {
    padding: 0.75rem 1rem;
  }

  .formGrid {
    gap: 1.5rem;
  }

  .inputGroup {
    margin-bottom: 1.25rem;
  }

  .cardTitle {
    font-size: 1.375rem;
  }
}

@media (min-width: 641px) and (max-width: 767px) {
  .buttonGroup {
    justify-content: center;
    flex-wrap: wrap;
  }

  .primaryButton, .secondaryButton {
    flex: 1;
    min-width: 180px;
  }

  .formGrid {
    gap: 1.75rem;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .buttonGroup {
    justify-content: flex-start;
  }

  .toggleContainer {
    margin-bottom: 1.75rem;
  }
}
