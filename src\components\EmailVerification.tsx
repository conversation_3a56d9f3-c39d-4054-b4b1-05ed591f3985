import { useState } from 'react';
import { Link } from 'react-router-dom';
import userService from '../services/userService';

interface EmailVerificationProps {
  email: string;
  onVerified: (verificationCode: string) => void;
  onCancel: () => void;
}

const EmailVerification = ({ email, onVerified, onCancel }: EmailVerificationProps) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }

    setIsLoading(true);

    try {
      // Verify the code
      await userService.checkVerificationCode(email, verificationCode);

      // If successful, call the onVerified callback with the code
      onVerified(verificationCode);
    } catch (err: any) {
      console.error('Verification error:', err);
      setError(err.response?.data?.message || 'Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setError('');
    setIsResending(true);

    try {
      // Request a new verification code
      await userService.requestVerificationCode(email);
      // Show success message
      alert('A new verification code has been sent to your email');
    } catch (err: any) {
      console.error('Error resending code:', err);
      setError(err.response?.data?.message || 'Failed to resend verification code. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="glass-card p-8 rounded-xl shadow-lg border-glow">
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-br from-accent-cyan to-primary-500 mb-4">
          <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Verify Your Email</span>
        </h3>
        <p className="text-primary-200 mb-6">
          We've sent a verification code to <span className="text-accent-cyan">{email}</span>.
          Please check your email and enter the code below to continue.
        </p>
        <div className="mb-4 p-3 bg-primary-800/50 border border-primary-700/50 rounded-lg text-sm text-primary-200">
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-2 mt-0.5 text-accent-cyan flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>
              <strong>For testing:</strong> Use verification code <span className="text-accent-cyan font-mono">123456</span>
            </span>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-3 bg-error/20 text-error rounded-lg w-full max-w-sm mx-auto">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto">
          <div>
            <label htmlFor="verificationCode" className="sr-only">Verification Code</label>
            <input
              id="verificationCode"
              name="verificationCode"
              type="text"
              required
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              className="block w-full px-4 py-3 bg-dark-700 border border-primary-700 text-primary-100 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-cyan focus:border-accent-cyan text-center text-lg tracking-widest"
              placeholder="Enter verification code"
            />
          </div>

          <div className="flex flex-col space-y-4">
            <button
              type="submit"
              disabled={isLoading}
              style={{
                background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                color: 'white',
                padding: '1rem 2rem',
                borderRadius: '9999px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease',
                border: 'none',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              }}
              className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
            >
              {isLoading ? (
                <span className="inline-flex items-center justify-center whitespace-nowrap">
                  <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </span>
              ) : (
                <span className="inline-flex items-center justify-center whitespace-nowrap">
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  Verify & Continue
                </span>
              )}
            </button>

            <button
              type="button"
              onClick={handleResendCode}
              disabled={isResending}
              style={{
                background: 'white',
                color: '#00b9ff',
                padding: '0.75rem 2rem',
                borderRadius: '9999px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease',
                border: 'none',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              }}
              className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
            >
              {isResending ? (
                <span className="inline-flex items-center justify-center whitespace-nowrap">
                  <svg className="animate-spin mr-2 h-4 w-4 text-accent-cyan" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Sending...
                </span>
              ) : (
                <span className="inline-flex items-center justify-center whitespace-nowrap">
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Resend Code
                </span>
              )}
            </button>

            <button
              type="button"
              onClick={onCancel}
              className="text-primary-300 hover:text-white focus:outline-none text-sm mt-2 flex items-center justify-center"
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Registration
            </button>
          </div>
        </form>

        <p className="text-primary-300 text-sm mt-6">
          If you don't see the email, check your spam folder or try again.
        </p>
      </div>
    </div>
  );
};

export default EmailVerification;
