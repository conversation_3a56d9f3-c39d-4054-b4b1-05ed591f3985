import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { analyzeComps, CompAnalysisResult, generateEnhancedCMA, validateZillowData } from '../services/openaiService';
import { analyzeCompsWithClaude } from '../services/claudeBackendService';
import { useApp } from '../context/SimpleAppContext';
import { FaRobot, FaBuilding, FaRulerCombined, FaBed, FaBath, FaCalendarAlt, FaStar, FaMapMarkerAlt, FaExclamationTriangle } from 'react-icons/fa';
import AddressAutocomplete from '../components/AddressAutocomplete';
import EnhancedConfidenceScore from '../components/CompAnalyzer/EnhancedConfidenceScore';
import DetailedPropertyCard from '../components/CompAnalyzer/DetailedPropertyCard';
import EnhancedAnalysisResults from '../components/CompAnalyzer/EnhancedAnalysisResults';
import { fetchLatLngForAddress } from '../services/placesApi';
import { fetchPropertyData, fetchPropertyDataByAddress } from '../services/zillowApi';
import {
  getPropertyDetailsFromZillow,
  getComparablePropertiesFromZillow,
  getComparablePropertiesFromRealtor,
  getMarketTrendsFromZillow,
  getAverageMarketTrends,
  calculateRecommendedPrice,
  PropertyDetails,
  ComparableProperty,
  MarketTrends
} from '../services/realEstateDataService';

const CompAnalyzer = () => {
  const { state } = useApp();
  const [address, setAddress] = useState('');
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const [loading, setLoading] = useState(false);
  const [llmProvider, setLlmProvider] = useState<'openai' | 'claude'>('openai'); // Default to OpenAI
  const [useRealEstateApis, setUseRealEstateApis] = useState(true); // Use real estate APIs by default
  const [propertyDetails, setPropertyDetails] = useState<PropertyDetails | null>(null);
  const [comparableProperties, setComparableProperties] = useState<ComparableProperty[]>([]);
  const [marketTrends, setMarketTrends] = useState<MarketTrends | null>(null);
  const [showPropertyDetailsForm, setShowPropertyDetailsForm] = useState(false);
  const [propertyDetailsForm, setPropertyDetailsForm] = useState({
    squareFeet: '',
    bedrooms: '',
    bathrooms: '',
    lotSize: '',
    yearBuilt: '',
    homeType: '',
    condition: 'Good',
    uniqueFeatures: '',
    searchRadius: '1.5', // Default search radius is 1.5 miles
    useSoftFilter: false, // Default to strict filtering
    statusType: 'RecentlySold' // Default to recently sold properties
  });
  const [result, setResult] = useState<null | {
    recommendedPrice: string;
    recommendedListingPrice?: string; // New field for the enhanced prompt
    priceRange?: string;
    confidenceScore?: number;
    confidenceExplanation?: string;
    priceJustification?: string; // New field for the enhanced prompt
    keyCompsUsed?: string[]; // New field for the enhanced prompt
    comparableProperties: Array<{
      address: string;
      price: string;
      bedrooms?: number;
      bathrooms?: number;
      squareFeet?: number;
      yearBuilt?: number;
      distanceFromTarget?: string;
      soldDate?: string; // New field for the enhanced prompt
      similarityScore?: number;
      adjustments?: string;
      zillowLink?: string;
      source?: string;
      included?: boolean; // New field for the enhanced prompt
    }>;
    marketTrends?: {
      averagePricePerSqFt: string;
      medianSalePrice: string;
      averageDaysOnMarket: number;
      priceChangeLastYear: string;
      inventoryLevel?: string;
      marketType?: string;
    };
  }>(null);
  const [error, setError] = useState<string | null>(null);
  const [dataSourcesLoading, setDataSourcesLoading] = useState({
    zillow: false,
    realtor: false,
    marketTrends: false
  });
  const [retrievingFromZillow, setRetrievingFromZillow] = useState(false);

  // Enhanced analysis state
  const [enhancedAnalysisResult, setEnhancedAnalysisResult] = useState<any>(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // Reset form when address changes to a new address
  useEffect(() => {
    // Reset form when address changes (but keep current values if it's the same address)
    if (address && propertyDetails && propertyDetails.address !== address) {
      setPropertyDetailsForm({
        squareFeet: '',
        bedrooms: '',
        bathrooms: '',
        lotSize: '',
        yearBuilt: '',
        homeType: '',
        condition: 'Good',
        uniqueFeatures: '',
        searchRadius: '1.5',
        useSoftFilter: false,
        statusType: 'RecentlySold'
      });
    }
  }, [address]);

  // Simple cache to prevent repeated API calls
  const [propertyDataCache, setPropertyDataCache] = useState<{[key: string]: any}>({});

  // Function to clear cached analysis data
  const clearAnalysisCache = () => {
    localStorage.removeItem('lastAnalysis');
    localStorage.removeItem('lastEnhancedAnalysis');
    setPropertyDataCache({});
    setResult(null);
    setEnhancedAnalysisResult(null);
    console.log('Analysis cache cleared');
  };

  // Check for and clear any cached mock data on component mount
  useEffect(() => {
    const checkAndClearMockCache = () => {
      try {
        const lastAnalysis = localStorage.getItem('lastAnalysis');
        if (lastAnalysis) {
          const parsed = JSON.parse(lastAnalysis);
          const containsMockData = parsed?.comparableProperties?.some((comp: any) =>
            comp.address?.includes('Example St') ||
            comp.address?.includes('Sample Ave') ||
            comp.address?.includes('Test Blvd') ||
            comp.source === 'Mock' ||
            comp._mock === true
          );

          if (containsMockData) {
            console.log('Found cached mock data - clearing cache');
            clearAnalysisCache();
          }
        }
      } catch (error) {
        console.warn('Error checking cached analysis data:', error);
      }
    };

    checkAndClearMockCache();
  }, []);

  // Function to fetch property details from Zillow
  const fetchPropertyDetails = async (address: string) => {
    try {
      setDataSourcesLoading(prev => ({ ...prev, zillow: true }));
      const details = await getPropertyDetailsFromZillow(address);
      setPropertyDetails(details);
      return details;
    } catch (error) {
      console.error('Error fetching property details:', error);
      return null;
    } finally {
      setDataSourcesLoading(prev => ({ ...prev, zillow: false }));
    }
  };

  // Function to fetch comparable properties from multiple sources
  const fetchComparableProperties = async (address: string) => {
    try {
      // Set loading state for all data sources
      setDataSourcesLoading(prev => ({
        ...prev,
        zillow: true,
        realtor: true
      }));

      // Get search radius from property details or use default
      const searchRadius = propertyDetails?.searchRadius || 1.5;
      const useSoftFilter = propertyDetails?.useSoftFilter || false;

      if (import.meta.env.DEV) {
        console.log(`%c[DEV] Fetching comps with search radius: ${searchRadius} miles`, 'color: #00b9ff; font-weight: bold');
        console.log(`%c[DEV] Using ${useSoftFilter ? 'soft' : 'strict'} filtering`, 'color: #00b9ff; font-weight: bold');
      }

      // Fetch comparable properties from all sources in parallel
      const [zillowComps, realtorComps] = await Promise.all([
        getComparablePropertiesFromZillow(address, searchRadius, {
          bedrooms: propertyDetails?.bedrooms,
          bathrooms: propertyDetails?.bathrooms,
          squareFeet: propertyDetails?.squareFeet,
          homeType: propertyDetails?.homeType,
          statusType: propertyDetails?.statusType
        }),
        getComparablePropertiesFromRealtor(address, searchRadius)
      ]);

      // Combine all comparable properties
      let allComps = [...zillowComps, ...realtorComps];

      // Apply filtering based on property details if available
      if (propertyDetails && propertyDetails.squareFeet && propertyDetails.bedrooms && propertyDetails.bathrooms) {
        if (useSoftFilter) {
          // Soft filtering - calculate similarity scores and sort by score
          allComps = allComps.map(comp => {
            let score = 100;

            // Bed/bath differences (up to -10 points each)
            if (propertyDetails.bedrooms) {
              score -= Math.min(10, Math.abs(comp.bedrooms - propertyDetails.bedrooms) * 5);
            }

            if (propertyDetails.bathrooms) {
              score -= Math.min(10, Math.abs(comp.bathrooms - propertyDetails.bathrooms) * 5);
            }

            // Square footage difference (up to -15 points)
            if (propertyDetails.squareFeet && comp.squareFeet) {
              const sqftDiff = Math.abs(comp.squareFeet - propertyDetails.squareFeet) / propertyDetails.squareFeet;
              score -= Math.min(15, sqftDiff * 100);
            }

            // Year built difference (up to -10 points)
            if (propertyDetails.yearBuilt && comp.yearBuilt) {
              const yearDiff = Math.abs(comp.yearBuilt - propertyDetails.yearBuilt);
              score -= Math.min(10, yearDiff * 0.5);
            }

            // Distance penalty if beyond search radius
            const distance = parseFloat(comp.distanceFromTarget?.split(' ')[0] || '0');
            if (distance > searchRadius) {
              score -= Math.min(10, Math.max(0, distance - searchRadius) * 5);
            }

            return {
              ...comp,
              similarityScore: Math.max(0, Math.floor(score))
            };
          }).sort((a, b) => (b.similarityScore || 0) - (a.similarityScore || 0));
        } else {
          // Strict filtering - only include properties that match within specific ranges
          allComps = allComps.filter(comp => {
            // Must have exact same number of bedrooms
            if (comp.bedrooms !== propertyDetails.bedrooms) return false;

            // Must have exact same number of bathrooms
            if (comp.bathrooms !== propertyDetails.bathrooms) return false;

            // Must be within ±10% of square footage
            if (propertyDetails.squareFeet && comp.squareFeet) {
              const sqftDiff = Math.abs(comp.squareFeet - propertyDetails.squareFeet) / propertyDetails.squareFeet;
              if (sqftDiff > 0.1) return false;
            }

            // Must be within ±5 years of year built
            if (propertyDetails.yearBuilt && comp.yearBuilt) {
              if (Math.abs(comp.yearBuilt - propertyDetails.yearBuilt) > 5) return false;
            }

            // Must be within search radius
            const distance = parseFloat(comp.distanceFromTarget?.split(' ')[0] || '0');
            if (distance > searchRadius) return false;

            return true;
          });
        }
      }

      if (import.meta.env.DEV) {
        console.log(`%c[DEV] Found ${allComps.length} comparable properties after filtering`, 'color: #00b9ff; font-weight: bold');
      }

      setComparableProperties(allComps);
      return allComps;
    } catch (error) {
      console.error('Error fetching comparable properties:', error);
      return [];
    } finally {
      // Reset loading state for all data sources
      setDataSourcesLoading(prev => ({
        ...prev,
        zillow: false,
        realtor: false
      }));
    }
  };

  // Function to fetch market trends
  const fetchMarketTrends = async (address: string) => {
    try {
      setDataSourcesLoading(prev => ({ ...prev, marketTrends: true }));

      // Extract zip code from address (simplified)
      const zipCodeMatch = address.match(/\b\d{5}\b/);
      const zipCode = zipCodeMatch ? zipCodeMatch[0] : '90210'; // Default to Beverly Hills if no zip code found

      const trends = await getAverageMarketTrends(zipCode);
      setMarketTrends(trends);
      return trends;
    } catch (error) {
      console.error('Error fetching market trends:', error);
      return null;
    } finally {
      setDataSourcesLoading(prev => ({ ...prev, marketTrends: false }));
    }
  };

  // Function to handle address selection from autocomplete
  const handleAddressSelect = async (selectedAddress: string) => {
    setAddress(selectedAddress);
    setError(null);

    try {
      // Fetch coordinates for the selected address
      const location = await fetchLatLngForAddress(selectedAddress);

      if (location) {
        setCoordinates({ lat: location.lat, lng: location.lng });

        // If we have a formatted address from the geocoding API, use it
        if (location.formatted_address) {
          setAddress(location.formatted_address);
        }

        if (import.meta.env.DEV) {
          console.log(`%c[DEV] Address geocoded: ${location.formatted_address}`, 'color: #00b9ff; font-weight: bold');
          console.log(`%c[DEV] Coordinates: ${location.lat}, ${location.lng}`, 'color: #00b9ff; font-weight: bold');
        }
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
    }
  };

  // Function to retrieve property details from Zillow
  const handleRetrieveFromZillow = async () => {
    if (!address.trim()) {
      setError('Please enter a valid address first');
      return;
    }

    setRetrievingFromZillow(true);
    setError(null);

    try {
      // Call the Zillow API to get property details
      const zillowData = await fetchPropertyData(address);

      if (zillowData && !zillowData._fallback) {
        // Map Zillow fields to our form fields, ensuring proper string conversion
        const updatedForm = {
          ...propertyDetailsForm,
          squareFeet: String(zillowData.livingAreaValue || zillowData.livingAreaUnits || zillowData.livingArea || zillowData.livingAreaSqFt || ''),
          bedrooms: String(zillowData.bedrooms || ''),
          bathrooms: String(zillowData.bathrooms || ''),
          yearBuilt: String(zillowData.yearBuilt || ''),
          homeType: String(zillowData.homeType || '')
        };

        console.log('🔄 Updating form with Zillow data:', updatedForm);
        setPropertyDetailsForm(updatedForm);
        updatePropertyDetails(updatedForm);

        console.log('✅ Property details saved from Zillow retrieval');

        // Show success message
        console.log('✅ Successfully retrieved property details from Zillow:', {
          squareFeet: updatedForm.squareFeet,
          bedrooms: updatedForm.bedrooms,
          bathrooms: updatedForm.bathrooms,
          yearBuilt: updatedForm.yearBuilt,
          homeType: updatedForm.homeType
        });
      } else {
        setError('Unable to retrieve property details from Zillow. Please enter the details manually.');
      }
    } catch (error) {
      console.error('Error retrieving property details from Zillow:', error);
      setError('Failed to retrieve property details from Zillow. Please try again or enter the details manually.');
    } finally {
      setRetrievingFromZillow(false);
    }
  };

  // Function to auto-save property details when form changes
  const updatePropertyDetails = (updatedForm: typeof propertyDetailsForm) => {
    // Convert form values to appropriate types
    const details: PropertyDetails = {
      address,
      squareFeet: updatedForm.squareFeet ? parseInt(updatedForm.squareFeet) : undefined,
      bedrooms: updatedForm.bedrooms ? parseInt(updatedForm.bedrooms) : undefined,
      bathrooms: updatedForm.bathrooms ? parseFloat(updatedForm.bathrooms) : undefined,
      lotSize: updatedForm.lotSize ? parseFloat(updatedForm.lotSize) : undefined,
      yearBuilt: updatedForm.yearBuilt ? parseInt(updatedForm.yearBuilt) : undefined,
      homeType: updatedForm.homeType || undefined,
      condition: updatedForm.condition as 'Excellent' | 'Good' | 'Fair' | 'Poor',
      uniqueFeatures: updatedForm.uniqueFeatures ? updatedForm.uniqueFeatures.split(',').map(f => f.trim()) : [],
      searchRadius: updatedForm.searchRadius ? parseFloat(updatedForm.searchRadius) : 1.5,
      useSoftFilter: updatedForm.useSoftFilter,
      statusType: updatedForm.statusType || 'RecentlySold'
    };

    if (import.meta.env.DEV) {
      console.log('🔄 Updating property details:', details);
    }

    setPropertyDetails(details);
  };

  // Function to handle property details form changes with auto-save
  const handlePropertyDetailsChange = (field: string, value: any) => {
    const updatedForm = { ...propertyDetailsForm, [field]: value };
    setPropertyDetailsForm(updatedForm);
    updatePropertyDetails(updatedForm);
  };

  // Enhanced analysis functions
  const getCurrentPropertyData = async () => {
    // Try to get actual property data from Zillow first
    let zillowPropertyData = null;

    // Check cache first to avoid repeated API calls
    const cacheKey = address.toLowerCase().trim();
    if (propertyDataCache[cacheKey]) {
      console.log('Using cached property data for:', address);
      zillowPropertyData = propertyDataCache[cacheKey];
    } else {
      try {
        // Parse address into components for the new API
        const addressParts = address.split(',');
        const streetAddress = addressParts[0]?.trim() || '';
        const citystatezip = addressParts.slice(1).join(',').trim() || '';

        if (streetAddress && citystatezip) {
          console.log('Fetching property data with:', { streetAddress, citystatezip });
          zillowPropertyData = await fetchPropertyDataByAddress(streetAddress, citystatezip);

          // Cache the result
          setPropertyDataCache(prev => ({
            ...prev,
            [cacheKey]: zillowPropertyData
          }));
        } else {
          console.warn('Could not parse address components for Zillow API');
        }
      } catch (error) {
        console.warn('Could not fetch Zillow property data for enhanced analysis:', error);
        // Check if it's a rate limiting error
        if (error.message?.includes('429') || error.message?.includes('Too Many Requests')) {
          console.warn('Rate limiting detected - using property details form data instead');
        }
      }
    }

    return {
      address,
      price: zillowPropertyData?.price || zillowPropertyData?.zestimate || 0,
      zestimate: zillowPropertyData?.zestimate || 0,
      bedrooms: propertyDetails?.bedrooms || zillowPropertyData?.bedrooms || 0,
      bathrooms: propertyDetails?.bathrooms || zillowPropertyData?.bathrooms || 0,
      sqft: propertyDetails?.squareFeet || zillowPropertyData?.livingAreaValue || zillowPropertyData?.livingArea || 0,
      yearBuilt: propertyDetails?.yearBuilt || zillowPropertyData?.yearBuilt || null,
      propertyType: propertyDetails?.homeType || zillowPropertyData?.homeType || 'Unknown',
      lotSize: propertyDetails?.lotSize || zillowPropertyData?.lotSize || null,
      condition: propertyDetails?.condition || 'Good',
      uniqueFeatures: propertyDetails?.uniqueFeatures || [],
      daysOnMarket: zillowPropertyData?.daysOnZillow || 0,
      rentEstimate: zillowPropertyData?.rentZestimate || 0,
      taxHistory: zillowPropertyData?.taxHistory || []
    };
  };

  const getComparableProperties = () => {
    const parsePrice = (price: any): number => {
      if (!price) return 0;
      if (typeof price === 'number') return price;
      if (typeof price === 'string') {
        return parseFloat(price.replace(/[$,]/g, '')) || 0;
      }
      return 0;
    };

    return comparableProperties
      .filter(comp => {
        const parsedPrice = parsePrice(comp.price);
        return parsedPrice > 0;
      })
      .map(comp => {
        const parsedPrice = parsePrice(comp.price);
        return {
          address: comp.address,
          price: parsedPrice,
          bedrooms: comp.bedrooms || 0,
          bathrooms: comp.bathrooms || 0,
          sqft: comp.squareFeet || 0,
          yearBuilt: comp.yearBuilt || null,
          daysOnMarket: comp.daysOnMarket || 0,
          homeStatus: comp.homeStatus || 'Unknown',
          lastSoldDate: comp.soldDate || 'Unknown',
          lastSoldPrice: parsedPrice,
          pricePerSqft: comp.squareFeet ? Math.round(parsedPrice / comp.squareFeet) : 0,
          distanceFromTarget: comp.distanceFromTarget || 'Unknown',
          source: comp.source || 'Unknown'
        };
      });
  };

  // Enhanced analysis handler with validation
  const handleAnalyzeCompsWithValidation = async () => {
    try {
      setAnalysisLoading(true);
      setAnalysisError(null);

      // Ensure we have comparable properties first
      if (!comparableProperties || comparableProperties.length === 0) {
        // Try to fetch comparable properties if we don't have any
        if (address.trim()) {
          await fetchComparableProperties(address);
        }
      }

      const currentProperty = await getCurrentPropertyData();
      const comparablePropertiesData = getComparableProperties();

      console.log('Current property data for analysis:', currentProperty);
      console.log('Comparable properties data for analysis:', comparablePropertiesData);

      const validation = validateZillowData(currentProperty, comparablePropertiesData);

      if (!validation.isValid) {
        setAnalysisError(`Cannot generate analysis: ${validation.errors.join(', ')}`);
        setAnalysisLoading(false);
        return;
      }

      if (validation.warnings.length > 0) {
        // Show warning but continue
        console.warn('Analysis warnings:', validation.warnings);
      }

      // Proceed with enhanced analysis
      await handleEnhancedAnalyzeComps(currentProperty, comparablePropertiesData);
    } catch (error) {
      console.error('Error in enhanced analysis validation:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Failed to prepare analysis data');
      setAnalysisLoading(false);
    }
  };

  // Enhanced analysis function
  const handleEnhancedAnalyzeComps = async (currentProperty?: any, comparablePropertiesData?: any) => {
    try {
      // Use provided data or fetch fresh data
      const propertyData = currentProperty || await getCurrentPropertyData();
      const compsData = comparablePropertiesData || getComparableProperties();

      // Calculate market metrics if not already available
      const marketMetrics = marketTrends ? {
        avgPrice: parseFloat(marketTrends.formattedMedianSalePrice.replace(/[$,]/g, '')) || 0,
        avgPricePerSqft: parseFloat(marketTrends.formattedAveragePricePerSqFt.replace(/[$,]/g, '')) || 0,
        priceRange: { min: 0, max: 0 },
        avgDaysOnMarket: marketTrends.averageDaysOnMarket || 0
      } : undefined;

      console.log('Generating enhanced analysis with:', {
        property: propertyData,
        comps: compsData.length,
        marketMetrics
      });

      // Generate enhanced analysis
      const analysisResult = await generateEnhancedCMA(
        propertyData,
        compsData,
        marketMetrics
      );

      // Update analysis state
      setEnhancedAnalysisResult(analysisResult);

      // Check if analysis contains mock data before caching
      const containsMockData = analysisResult?.comparableProperties?.some((comp: any) =>
        comp.address?.includes('Example St') ||
        comp.address?.includes('Sample Ave') ||
        comp.address?.includes('Test Blvd') ||
        comp.source === 'Mock' ||
        comp._mock === true
      );

      if (!containsMockData) {
        // Only save to local storage if it doesn't contain mock data
        localStorage.setItem('lastAnalysis', JSON.stringify({
          ...analysisResult,
          propertyAddress: propertyData.address,
          timestamp: Date.now()
        }));
      } else {
        console.warn('Analysis contains mock data - not caching to prevent contamination');
      }

    } catch (error) {
      console.error('Enhanced analysis failed:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Analysis failed');
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Calculate market metrics from comparable properties
  const calculateMarketMetricsFromComps = (comparables: any[]) => {
    if (!comparables || comparables.length === 0) {
      return {
        avgPrice: 0,
        medianPrice: 0,
        avgPricePerSqft: 0,
        priceRange: { min: 0, max: 0 },
        avgDaysOnMarket: 0
      };
    }

    const validPrices = comparables.filter(c => c.price > 0).map(c => c.price);
    const validPricePerSqft = comparables.filter(c => c.pricePerSqft > 0).map(c => c.pricePerSqft);
    const validDaysOnMarket = comparables.filter(c => c.daysOnMarket >= 0).map(c => c.daysOnMarket);

    // Calculate median price
    const sortedPrices = [...validPrices].sort((a, b) => a - b);
    const medianPrice = sortedPrices.length > 0 ?
      sortedPrices.length % 2 === 0
        ? (sortedPrices[sortedPrices.length / 2 - 1] + sortedPrices[sortedPrices.length / 2]) / 2
        : sortedPrices[Math.floor(sortedPrices.length / 2)]
      : 0;

    return {
      avgPrice: validPrices.length ? validPrices.reduce((sum, price) => sum + price, 0) / validPrices.length : 0,
      medianPrice,
      avgPricePerSqft: validPricePerSqft.length ? validPricePerSqft.reduce((sum, price) => sum + price, 0) / validPricePerSqft.length : 0,
      priceRange: {
        min: validPrices.length ? Math.min(...validPrices) : 0,
        max: validPrices.length ? Math.max(...validPrices) : 0
      },
      avgDaysOnMarket: validDaysOnMarket.length ? validDaysOnMarket.reduce((sum, days) => sum + days, 0) / validDaysOnMarket.length : 0
    };
  };

  // Main form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!address.trim()) {
      setError('Please enter a valid address');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Log which LLM is being used in development mode
      if (import.meta.env.DEV) {
        console.log(`%c[DEV] Using ${llmProvider === 'openai' ? 'OpenAI' : 'Claude'} for comp analysis`,
          `color: ${llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
        console.log(`%c[DEV] Using real estate APIs: ${useRealEstateApis ? 'Yes' : 'No'}`,
          `color: #00b9ff; font-weight: bold`);
      }

      // Always use current form values for property details to ensure consistency
      const currentPropertyDetails: PropertyDetails = {
        address,
        squareFeet: propertyDetailsForm.squareFeet ? parseInt(propertyDetailsForm.squareFeet) : undefined,
        bedrooms: propertyDetailsForm.bedrooms ? parseInt(propertyDetailsForm.bedrooms) : undefined,
        bathrooms: propertyDetailsForm.bathrooms ? parseFloat(propertyDetailsForm.bathrooms) : undefined,
        lotSize: propertyDetailsForm.lotSize ? parseFloat(propertyDetailsForm.lotSize) : undefined,
        yearBuilt: propertyDetailsForm.yearBuilt ? parseInt(propertyDetailsForm.yearBuilt) : undefined,
        homeType: propertyDetailsForm.homeType || undefined,
        condition: propertyDetailsForm.condition as 'Excellent' | 'Good' | 'Fair' | 'Poor',
        uniqueFeatures: propertyDetailsForm.uniqueFeatures ? propertyDetailsForm.uniqueFeatures.split(',').map(f => f.trim()) : [],
        searchRadius: propertyDetailsForm.searchRadius ? parseFloat(propertyDetailsForm.searchRadius) : 1.5,
        useSoftFilter: propertyDetailsForm.useSoftFilter,
        statusType: propertyDetailsForm.statusType || 'RecentlySold'
      };

      if (import.meta.env.DEV) {
        console.log('📋 Current form values:', propertyDetailsForm);
        console.log('🎯 Property details for analysis:', currentPropertyDetails);
      }

      // Update the propertyDetails state to stay in sync
      setPropertyDetails(currentPropertyDetails);

      let propertyDetailsData = currentPropertyDetails;
      let comparablePropertiesData: ComparableProperty[] = [];
      let marketTrendsData = marketTrends;

      // Fetch data from real estate APIs if enabled
      if (useRealEstateApis) {
        // Fetch all data in parallel
        const [details, comps, trends] = await Promise.all([
          fetchPropertyDetails(address),
          fetchComparableProperties(address),
          fetchMarketTrends(address)
        ]);

        propertyDetailsData = details || propertyDetailsData;
        comparablePropertiesData = comps;
        marketTrendsData = trends || marketTrendsData;
      }

      // Call the appropriate LLM service
      let analysisResult;
      if (llmProvider === 'openai') {
        // Get the OpenAI API key from the app state
        const apiKey = state.openAiApiKey;

        if (!apiKey) {
          setError('OpenAI API key is not configured. Please check your settings.');
          return;
        }

        // Call the OpenAI service to analyze comps with additional data
        analysisResult = await analyzeComps(
          apiKey,
          address,
          {
            ...propertyDetailsData,
            searchRadius: propertyDetailsData?.searchRadius || 1.5,
            filterMode: propertyDetailsData?.useSoftFilter ? 'soft' : 'strict'
          } || undefined,
          marketTrendsData ? {
            averagePricePerSqFt: marketTrendsData.formattedAveragePricePerSqFt,
            medianSalePrice: marketTrendsData.formattedMedianSalePrice,
            averageDaysOnMarket: marketTrendsData.averageDaysOnMarket,
            priceChangeLastYear: marketTrendsData.formattedPriceChangeLastYear,
            inventoryLevel: marketTrendsData.inventoryLevel,
            marketType: marketTrendsData.marketType
          } : undefined
        );
      } else {
        // Call the Claude backend service to analyze comps with additional data
        analysisResult = await analyzeCompsWithClaude(
          address,
          {
            ...propertyDetailsData,
            searchRadius: propertyDetailsData?.searchRadius || 1.5,
            filterMode: propertyDetailsData?.useSoftFilter ? 'soft' : 'strict'
          } || undefined,
          marketTrendsData ? {
            averagePricePerSqFt: marketTrendsData.formattedAveragePricePerSqFt,
            medianSalePrice: marketTrendsData.formattedMedianSalePrice,
            averageDaysOnMarket: marketTrendsData.averageDaysOnMarket,
            priceChangeLastYear: marketTrendsData.formattedPriceChangeLastYear,
            inventoryLevel: marketTrendsData.inventoryLevel,
            marketType: marketTrendsData.marketType
          } : undefined,
          comparablePropertiesData // Pass the polygon search results
        );

        // Ensure keyCompsUsed field is present for Claude responses
        if (analysisResult && analysisResult.comparableProperties && Array.isArray(analysisResult.comparableProperties)) {
          // If keyCompsUsed is missing or empty, generate it from included properties
          if (!analysisResult.keyCompsUsed || !Array.isArray(analysisResult.keyCompsUsed) || analysisResult.keyCompsUsed.length === 0) {
            analysisResult.keyCompsUsed = analysisResult.comparableProperties
              .filter(comp => comp.included === true || (comp.included === undefined && comp.similarityScore >= 80))
              .map(comp => `${comp.address} (${comp.price}, ${comp.bedrooms}bd/${comp.bathrooms}ba, ${comp.squareFeet} sqft)`);
          }

          // Mark properties as included/excluded based on similarity score if not already marked
          analysisResult.comparableProperties = analysisResult.comparableProperties.map(comp => {
            if (comp.included === undefined) {
              // If similarity score is available, use it to determine inclusion
              if (comp.similarityScore !== undefined) {
                return {
                  ...comp,
                  included: comp.similarityScore >= 70
                };
              }
              // Default to included if no similarity score
              return {
                ...comp,
                included: true
              };
            }
            return comp;
          });
        }
      }

      // Format the calculated price
      const formatPrice = (price: number) => {
        return '$' + price.toLocaleString();
      };

      // If we have real estate data, enhance the result with it
      if (useRealEstateApis && propertyDetailsData && comparablePropertiesData.length > 0) {
        // Calculate a recommended price using our own algorithm
        const calculatedPrice = calculateRecommendedPrice(propertyDetailsData, comparablePropertiesData, marketTrendsData);

        // Calculate market metrics from actual Zillow polygon search results
        const realMarketMetrics = calculateMarketMetricsFromComps(comparablePropertiesData);

        // Create an enhanced result that synchronizes LLM analysis with real Zillow data
        // This will be further enhanced with comprehensive market analysis data when available
        const enhancedResult = {
          // Use LLM's recommended price if available, otherwise use calculated price
          recommendedPrice: analysisResult.recommendedPrice ||
            (calculatedPrice.recommendedPrice > 0 ?
              formatPrice(calculatedPrice.recommendedPrice) :
              formatPrice(realMarketMetrics.avgPrice)),
          recommendedListingPrice: analysisResult.recommendedListingPrice || analysisResult.recommendedPrice,
          priceRange: analysisResult.priceRange ||
            (calculatedPrice.priceRange[0] > 0 ?
              `${formatPrice(calculatedPrice.priceRange[0])} - ${formatPrice(calculatedPrice.priceRange[1])}` :
              `${formatPrice(realMarketMetrics.priceRange.min)} - ${formatPrice(realMarketMetrics.priceRange.max)}`),
          // Use LLM's confidence score if available, otherwise use calculated
          confidenceScore: analysisResult.confidenceScore || calculatedPrice.confidenceScore || 75,
          confidenceExplanation: analysisResult.confidenceExplanation ||
            `Based on ${comparablePropertiesData.length} comparable properties from Zillow polygon search`,
          priceJustification: analysisResult.priceJustification ||
            `Price calculated from ${comparablePropertiesData.length} recent comparable sales within the search area`,
          // Use LLM's key comps if available, otherwise use top 5 polygon search results
          keyCompsUsed: analysisResult.keyCompsUsed?.length > 0 ? analysisResult.keyCompsUsed :
            comparablePropertiesData.slice(0, 5).map(comp =>
              `${comp.address} (${comp.formattedPrice}, ${comp.bedrooms}BR/${comp.bathrooms}BA, ${comp.squareFeet} sqft)`
            ),
          excludedCompsAndWhy: analysisResult.excludedCompsAndWhy?.length > 0 ? analysisResult.excludedCompsAndWhy :
            (comparablePropertiesData.length > 6 ?
              [`${comparablePropertiesData.length - 6} properties excluded due to lower similarity scores`] : []),
          // Use ONLY polygon search results for comparable properties, enhanced with LLM analysis
          comparableProperties: comparablePropertiesData.map(comp => {
            // Find matching LLM analysis for this property
            const llmComp = analysisResult.comparableProperties?.find((ac: any) =>
              ac.address?.toLowerCase().includes(comp.address?.toLowerCase().split(',')[0] || '') ||
              comp.address?.toLowerCase().includes(ac.address?.toLowerCase().split(',')[0] || '')
            );

            return {
              address: comp.address,
              price: comp.formattedPrice,
              bedrooms: comp.bedrooms,
              bathrooms: comp.bathrooms,
              squareFeet: comp.squareFeet,
              yearBuilt: comp.yearBuilt,
              distanceFromTarget: comp.distanceFromTarget,
              similarityScore: llmComp?.similarityScore || comp.similarityScore,
              adjustments: llmComp?.adjustments || comp.adjustments || 'No adjustments needed',
              zillowLink: comp.zillowLink,
              source: comp.source,
              included: llmComp?.included !== false, // Use LLM's inclusion decision if available
              exclusionReason: llmComp?.exclusionReason
            };
          }),
          // ALWAYS use calculated market trends from real comparable properties (most precise)
          marketTrends: {
            averagePricePerSqFt: `$${realMarketMetrics.avgPricePerSqft.toFixed(0)}`,
            medianSalePrice: formatPrice(realMarketMetrics.medianPrice),
            averageDaysOnMarket: Math.round(realMarketMetrics.avgDaysOnMarket),
            priceChangeLastYear: marketTrendsData?.formattedPriceChangeLastYear || '5.0%',
            inventoryLevel: marketTrendsData?.inventoryLevel || (realMarketMetrics.avgDaysOnMarket < 30 ? 'Low' : realMarketMetrics.avgDaysOnMarket < 60 ? 'Medium' : 'High'),
            marketType: marketTrendsData?.marketType || (realMarketMetrics.avgDaysOnMarket < 30 ? 'Seller\'s Market' : realMarketMetrics.avgDaysOnMarket < 60 ? 'Balanced' : 'Buyer\'s Market')
          }
        };

        setResult(enhancedResult);
      } else {
        // If no real estate data, enhance LLM result with better structure
        const enhancedLLMResult = {
          ...analysisResult,
          confidenceScore: analysisResult.confidenceScore || 65,
          confidenceExplanation: analysisResult.confidenceExplanation || 'Analysis based on LLM-generated comparable properties',
          priceJustification: analysisResult.priceJustification || 'Price recommendation based on AI analysis of market conditions',
          keyCompsUsed: analysisResult.keyCompsUsed || [],
          excludedCompsAndWhy: analysisResult.excludedCompsAndWhy || [],
          // Ensure comparable properties have consistent structure
          comparableProperties: (analysisResult.comparableProperties || []).map((comp: any) => ({
            ...comp,
            included: comp.included !== false, // Default to included unless explicitly excluded
            source: comp.source || 'LLM Generated'
          }))
        };
        setResult(enhancedLLMResult);
      }

      // Generate Enhanced Analysis automatically after main analysis
      try {
        setAnalysisLoading(true);
        setAnalysisError(null);

        const currentProperty = await getCurrentPropertyData();

        // Use real market metrics calculated from Zillow polygon search results
        const realMarketMetrics = calculateMarketMetricsFromComps(comparablePropertiesData || []);
        const marketMetrics = {
          avgPrice: realMarketMetrics.medianPrice,
          avgPricePerSqft: realMarketMetrics.avgPricePerSqft,
          priceRange: realMarketMetrics.priceRange,
          avgDaysOnMarket: realMarketMetrics.avgDaysOnMarket
        };

        const apiKey = llmProvider === 'openai' ? state.openAiApiKey : undefined;

        const enhancedAnalysisResult = await generateEnhancedCMA(
          currentProperty,
          comparablePropertiesData,
          marketMetrics,
          llmProvider,
          apiKey
        );

        setEnhancedAnalysisResult(enhancedAnalysisResult);

        // Update the main result with enhanced analysis data for unified UI
        if (result && enhancedAnalysisResult) {
          const updatedResult = {
            ...result,
            // Update confidence score and explanation with enhanced analysis data
            confidenceScore: enhancedAnalysisResult.confidence,
            confidenceExplanation: `Enhanced Analysis - Data Quality: ${enhancedAnalysisResult.dataQuality.quality}. ${result.confidenceExplanation || ''}`,
            // Keep existing price data but add enhanced justification if available
            priceJustification: result.priceJustification || 'Price recommendation based on comprehensive market analysis',
            // Update key comps to show top 5 from enhanced analysis
            keyCompsUsed: result.keyCompsUsed?.slice(0, 5) || []
          };
          setResult(updatedResult);
        }

        // Cache the enhanced analysis
        localStorage.setItem('lastEnhancedAnalysis', JSON.stringify({
          result: enhancedAnalysisResult,
          propertyAddress: currentProperty.address,
          timestamp: Date.now()
        }));

      } catch (enhancedError) {
        console.warn('Enhanced Analysis failed:', enhancedError);
        setAnalysisError(enhancedError instanceof Error ? enhancedError.message : 'Enhanced Analysis failed');
      } finally {
        setAnalysisLoading(false);
      }

    } catch (err) {
      setError(`An error occurred while analyzing the property. Please try again.`);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-dark-900">
      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-tech overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-8 animate-fade-in">
            <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">AI-Powered Analysis</span>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 neon-text">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Comparative Market Analysis</span>
            </h1>

            <p className="text-lg text-primary-100 max-w-2xl mx-auto leading-relaxed">
              Our <span className="text-accent-cyan font-medium">AI-powered Comp Analyzer</span> evaluates similar properties in the area to recommend the optimal listing price for your property.
            </p>

            {/* LLM Indicator - shows in all modes */}
            <div className="mt-4 py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
              <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                   style={{ backgroundColor: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
              <span className="text-xs">
                Powered by <span className="font-semibold"
                  style={{ color: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                  {llmProvider === 'openai' ? 'OpenAI GPT-4o' : 'Claude 4'}
                </span>
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="glass-card p-10 border-glow relative overflow-hidden mb-10 max-w-3xl mx-auto">
              <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/5 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl"></div>

              <div className="relative z-10">
                <h2 className="text-2xl font-bold text-white mb-8 text-center">Enter Property Address</h2>

                <form onSubmit={handleSubmit} className="space-y-8 max-w-2xl mx-auto">
                  {/* LLM Provider Toggle */}
                  <div className="flex justify-center mb-6">
                    <div className="bg-dark-800 p-1 rounded-lg flex">
                      <button
                        type="button"
                        onClick={() => setLlmProvider('openai')}
                        style={{
                          background: llmProvider === 'openai'
                            ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                            : 'transparent',
                          transition: 'all 0.3s ease',
                        }}
                        className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'openai'
                          ? 'text-white shadow-lg hover:shadow-xl'
                          : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                      >
                        <span className="inline-flex items-center">
                          <FaRobot className="mr-2" style={{ color: '#10a37f' }} />
                          OpenAI
                        </span>
                      </button>
                      <button
                        type="button"
                        onClick={() => setLlmProvider('claude')}
                        style={{
                          background: llmProvider === 'claude'
                            ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                            : 'transparent',
                          transition: 'all 0.3s ease',
                        }}
                        className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'claude'
                          ? 'text-white shadow-lg hover:shadow-xl'
                          : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                      >
                        <span className="inline-flex items-center">
                          <FaRobot className="mr-2" style={{ color: '#a27aff' }} />
                          Claude
                        </span>
                      </button>
                    </div>
                  </div>

                  {/* Real Estate API Toggle */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center">
                      <label className="inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={useRealEstateApis}
                          onChange={() => setUseRealEstateApis(!useRealEstateApis)}
                          className="sr-only peer"
                        />
                        <div className="relative w-11 h-6 bg-dark-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-accent-cyan/50 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-cyan"></div>
                        <span className="ms-3 text-sm font-medium text-primary-100">Use Real Estate Data APIs</span>
                      </label>
                    </div>
                    <p className="text-xs text-center text-primary-300 mt-1">
                      Enhances analysis with data from Zillow and Realtor.com
                    </p>
                  </div>

                  <div className="relative">
                    <label htmlFor="address" className="block text-primary-100 mb-3 text-sm font-medium">Property Address</label>
                    <div className="relative group">
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-accent-cyan/30 to-secondary-400/30 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-300"></div>
                      <div className="relative">
                        <AddressAutocomplete
                          initialValue={address}
                          onSelect={handleAddressSelect}
                          placeholder="Start typing an address..."
                          inputClassName="bg-white/95 backdrop-blur-sm border border-primary-700/30 text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-cyan/50 focus:border-accent-cyan/50 shadow-sm transition-all duration-300"
                          dropdownClassName="bg-dark-800/95 backdrop-blur-sm border border-primary-700/50 shadow-glow-sm"
                        />
                        {coordinates && (
                          <div className="mt-2 text-xs text-primary-300 flex items-center">
                            <FaMapMarkerAlt className="mr-1 text-accent-cyan" />
                            <span>Coordinates: {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    {error && <p className="mt-2 text-red-400 text-sm">{error}</p>}
                  </div>

                  {/* Property Details Button */}
                  <div className="flex justify-center">
                    <button
                      type="button"
                      onClick={() => setShowPropertyDetailsForm(!showPropertyDetailsForm)}
                      className="secondary-button flex items-center"
                    >
                      {showPropertyDetailsForm ? 'Hide Property Details' : 'Add Property Details'}
                      <FaBuilding className="ml-2" />
                    </button>
                  </div>

                  {/* Property Details Form */}
                  {showPropertyDetailsForm && (
                    <div className="bg-dark-800/80 p-6 rounded-xl border border-primary-700/30 animate-fade-in">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-bold text-white">Property Details</h3>
                        <button
                          onClick={handleRetrieveFromZillow}
                          disabled={retrievingFromZillow || !address.trim()}
                          className="tertiary-button flex items-center gap-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {retrievingFromZillow ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                              Retrieving...
                            </>
                          ) : (
                            <>
                              <FaBuilding className="text-sm" />
                              Retrieve from Zillow
                            </>
                          )}
                        </button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="squareFeet" className="block text-primary-100 mb-1 text-sm">Square Feet</label>
                          <input
                            type="number"
                            id="squareFeet"
                            value={propertyDetailsForm.squareFeet}
                            onChange={(e) => handlePropertyDetailsChange('squareFeet', e.target.value)}
                            placeholder="e.g. 1500"
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          />
                        </div>
                        <div>
                          <label htmlFor="bedrooms" className="block text-primary-100 mb-1 text-sm">Bedrooms</label>
                          <input
                            type="number"
                            id="bedrooms"
                            value={propertyDetailsForm.bedrooms}
                            onChange={(e) => handlePropertyDetailsChange('bedrooms', e.target.value)}
                            placeholder="e.g. 3"
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          />
                        </div>
                        <div>
                          <label htmlFor="bathrooms" className="block text-primary-100 mb-1 text-sm">Bathrooms</label>
                          <input
                            type="number"
                            id="bathrooms"
                            value={propertyDetailsForm.bathrooms}
                            onChange={(e) => handlePropertyDetailsChange('bathrooms', e.target.value)}
                            placeholder="e.g. 2.5"
                            step="0.5"
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          />
                        </div>
                        <div>
                          <label htmlFor="lotSize" className="block text-primary-100 mb-1 text-sm">Lot Size (acres)</label>
                          <input
                            type="number"
                            id="lotSize"
                            value={propertyDetailsForm.lotSize}
                            onChange={(e) => handlePropertyDetailsChange('lotSize', e.target.value)}
                            placeholder="e.g. 0.25"
                            step="0.01"
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          />
                        </div>
                        <div>
                          <label htmlFor="yearBuilt" className="block text-primary-100 mb-1 text-sm">Year Built</label>
                          <input
                            type="number"
                            id="yearBuilt"
                            value={propertyDetailsForm.yearBuilt}
                            onChange={(e) => handlePropertyDetailsChange('yearBuilt', e.target.value)}
                            placeholder="e.g. 2005"
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          />
                        </div>
                        <div>
                          <label htmlFor="homeType" className="block text-primary-100 mb-1 text-sm">Home Type</label>
                          <input
                            type="text"
                            id="homeType"
                            value={propertyDetailsForm.homeType}
                            onChange={(e) => handlePropertyDetailsChange('homeType', e.target.value)}
                            placeholder="e.g. Single Family, Condo, Townhouse"
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          />
                        </div>
                        <div>
                          <label htmlFor="statusType" className="block text-primary-100 mb-1 text-sm">Status Type</label>
                          <select
                            id="statusType"
                            value={propertyDetailsForm.statusType}
                            onChange={(e) => handlePropertyDetailsChange('statusType', e.target.value)}
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          >
                            <option value="RecentlySold">Recently Sold</option>
                            <option value="ForSale">For Sale</option>
                          </select>
                        </div>
                        <div>
                          <label htmlFor="condition" className="block text-primary-100 mb-1 text-sm">Condition</label>
                          <select
                            id="condition"
                            value={propertyDetailsForm.condition}
                            onChange={(e) => handlePropertyDetailsChange('condition', e.target.value)}
                            className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          >
                            <option value="Excellent">Excellent</option>
                            <option value="Good">Good</option>
                            <option value="Fair">Fair</option>
                            <option value="Poor">Poor</option>
                          </select>
                        </div>
                      </div>
                      <div className="mt-4">
                        <label htmlFor="uniqueFeatures" className="block text-primary-100 mb-1 text-sm">Unique Features (comma separated)</label>
                        <textarea
                          id="uniqueFeatures"
                          value={propertyDetailsForm.uniqueFeatures}
                          onChange={(e) => handlePropertyDetailsChange('uniqueFeatures', e.target.value)}
                          placeholder="e.g. Hardwood floors, Granite countertops, Fireplace"
                          className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          rows={3}
                        />
                      </div>

                      <div className="mt-4">
                        <label htmlFor="searchRadius" className="block text-primary-100 mb-1 text-sm font-medium">Search Radius (miles)</label>
                        <input
                          type="number"
                          id="searchRadius"
                          value={propertyDetailsForm.searchRadius || 1.5}
                          onChange={(e) => handlePropertyDetailsChange('searchRadius', Math.max(0.1, Math.min(50, parseFloat(e.target.value))))}
                          min="0.1"
                          max="50"
                          step="0.1"
                          required
                          className="w-full bg-dark-700 border border-primary-700/30 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent-cyan/50 focus:border-accent-cyan/50"
                          onInvalid={(e) => (e.target as HTMLInputElement).setCustomValidity("Please enter a value between 0.1 and 50.")}
                          onInput={(e) => (e.target as HTMLInputElement).setCustomValidity("")}
                        />
                        <p className="text-xs text-primary-300 mt-1">Distance range for finding comparable properties (0.1-50 miles)</p>
                      </div>

                      <div className="mt-4">
                        <label className="flex items-center space-x-3 text-sm text-primary-100">
                          <input
                            type="checkbox"
                            checked={propertyDetailsForm.useSoftFilter}
                            onChange={(e) => handlePropertyDetailsChange('useSoftFilter', e.target.checked)}
                            className="form-checkbox h-4 w-4 text-accent-cyan bg-dark-700 border-primary-700/30 rounded focus:ring-accent-cyan/50"
                          />
                          <span>Use Soft Filters (Ranked Similarity)</span>
                        </label>
                        <p className="text-xs text-primary-300 mt-1 ml-7">
                          When enabled, shows a broader range of properties ranked by similarity score.
                          When disabled, only shows properties that exactly match bedrooms/bathrooms, within ±10% sqft, ±5 years built.
                        </p>
                      </div>


                    </div>
                  )}

                  {/* Data Sources Loading Indicators */}
                  {(dataSourcesLoading.zillow || dataSourcesLoading.realtor || dataSourcesLoading.marketTrends) && (
                    <div className="bg-dark-800/50 p-4 rounded-lg border border-primary-700/30">
                      <h4 className="text-sm font-medium text-primary-100 mb-2">Fetching Real Estate Data</h4>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <div className={`h-2 w-2 rounded-full mr-2 ${dataSourcesLoading.zillow ? 'bg-accent-cyan animate-pulse' : 'bg-green-500'}`}></div>
                          <span className="text-xs text-primary-200">Zillow Property Data</span>
                        </div>
                        <div className="flex items-center">
                          <div className={`h-2 w-2 rounded-full mr-2 ${dataSourcesLoading.realtor ? 'bg-accent-cyan animate-pulse' : 'bg-green-500'}`}></div>
                          <span className="text-xs text-primary-200">Realtor.com Comparable Properties</span>
                        </div>
                        <div className="flex items-center">
                          <div className={`h-2 w-2 rounded-full mr-2 ${dataSourcesLoading.marketTrends ? 'bg-accent-cyan animate-pulse' : 'bg-green-500'}`}></div>
                          <span className="text-xs text-primary-200">Market Trends Data</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="pt-2">
                    <button
                      type="submit"
                      disabled={loading}
                      className="primary-button button-lg w-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {loading ? (
                        <>
                          <span className="text-white">Processing...</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 mr-2" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                          </svg>
                          Analyze Comps
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {loading && (
              <div className="glass-card p-8 border-glow relative overflow-hidden animate-fade-in mb-8" aria-busy="true">
                <div className="flex flex-col items-center justify-center w-full h-64 p-6 rounded-xl bg-gradient-to-br from-indigo-800 via-blue-800 to-purple-800 shadow-lg animate-pulse">
                  <div className="relative w-16 h-16 mb-4">
                    <div className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"></div>
                    <div className="absolute inset-0 rounded-full border-4 border-transparent border-b-accent-cyan animate-spin animate-delay-150" style={{ animationDuration: '1.5s' }}></div>
                  </div>
                  <p className="text-blue-200 text-sm font-medium tracking-wide mb-2">Analyzing Property Details…</p>
                  <p className="text-blue-300/70 text-xs max-w-md text-center">
                    Our AI is analyzing comparable properties, market trends, and your property details to determine the optimal listing price.
                  </p>
                </div>
              </div>
            )}

            {result && (
              <div className="glass-card p-8 border-glow relative overflow-hidden animate-fade-in">
                <div className="absolute top-0 right-0 w-64 h-64 bg-secondary-400/5 rounded-full blur-3xl"></div>

                <div className="relative z-10">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-2">Analysis Results</h2>
                      <p className="text-primary-200">Based on {result.comparableProperties.length} comparable properties</p>
                      {enhancedAnalysisResult && (
                        <div className="flex items-center mt-2">
                          <FaRobot className="text-accent-cyan mr-2" />
                          <span className="text-sm text-accent-cyan">Enhanced with Comprehensive Market Analysis</span>
                        </div>
                      )}
                    </div>
                    <EnhancedConfidenceScore
                      score={enhancedAnalysisResult?.confidence || result.confidenceScore || 85}
                      explanation={enhancedAnalysisResult?.dataQuality ?
                        `${result.confidenceExplanation || ''} Data Quality: ${enhancedAnalysisResult.dataQuality.quality}` :
                        result.confidenceExplanation}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div className="bg-dark-800/50 rounded-xl p-6 border border-primary-700/30">
                      <h3 className="text-xl font-bold text-white mb-4">Recommended Price</h3>
                      <div className="text-3xl font-bold text-accent-cyan mb-2">{result.recommendedListingPrice || result.recommendedPrice}</div>
                      <p className="text-primary-200">Suggested range: {result.priceRange}</p>
                      {result.priceJustification && (
                        <div className="mt-4 p-3 bg-dark-900/50 rounded-lg border border-primary-700/20">
                          <p className="text-sm text-primary-100">{result.priceJustification}</p>
                        </div>
                      )}
                    </div>

                    <div className="bg-dark-800/50 rounded-xl p-6 border border-primary-700/30">
                      <h3 className="text-xl font-bold text-white mb-4">Market Trends</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-primary-200">Avg. Price/SqFt:</span>
                          <span className="text-white font-medium">{result.marketTrends?.averagePricePerSqFt}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-primary-200">Median Sale Price:</span>
                          <span className="text-white font-medium">{result.marketTrends?.medianSalePrice}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-primary-200">Avg. Days on Market:</span>
                          <span className="text-white font-medium">{result.marketTrends?.averageDaysOnMarket} days</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-primary-200">YoY Price Change:</span>
                          <span className="text-accent-cyan font-medium">{result.marketTrends?.priceChangeLastYear}</span>
                        </div>
                        {result.marketTrends?.inventoryLevel && (
                          <div className="flex justify-between">
                            <span className="text-primary-200">Inventory Level:</span>
                            <span className="text-white font-medium">{result.marketTrends.inventoryLevel}</span>
                          </div>
                        )}
                        {result.marketTrends?.marketType && (
                          <div className="flex justify-between">
                            <span className="text-primary-200">Market Type:</span>
                            <span className="text-white font-medium">{result.marketTrends.marketType}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Key Comps Section */}
                  {result.keyCompsUsed?.length > 0 && (
                    <div className="mb-8">
                      <div className="bg-dark-800/50 rounded-xl p-6 border border-primary-700/30">
                        <h3 className="text-lg font-bold text-white mb-4">Key Comps Used in Analysis</h3>
                        <ul className="space-y-2">
                          {result.keyCompsUsed.map((comp, index) => (
                            <li key={index} className="flex items-start">
                              <div className="h-5 w-5 rounded-full bg-green-500/20 flex items-center justify-center mr-2 mt-0.5">
                                <span className="text-green-400 text-xs">✓</span>
                              </div>
                              <span className="text-primary-100">{comp}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  {/* Warning for mock data */}
                  {result.comparableProperties?.some((comp: any) =>
                    comp.address?.includes('Example St') ||
                    comp.address?.includes('Sample Ave') ||
                    comp.address?.includes('Test Blvd') ||
                    comp.source === 'Mock' ||
                    comp._mock === true
                  ) && (
                    <div className="mb-6 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span className="text-yellow-400 font-medium">Warning: Mock Data Detected</span>
                      </div>
                      <p className="text-yellow-200 text-sm mt-2">
                        Some properties in this analysis appear to be mock/sample data. Please clear the cache and run a new analysis for accurate results.
                      </p>
                    </div>
                  )}

                  <h3 className="text-xl font-bold text-white mb-4">Comparable Properties</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {result.comparableProperties.map((property, index) => (
                      <DetailedPropertyCard key={index} property={property} />
                    ))}
                  </div>

                  <div className="mt-8 flex justify-between items-center">
                    <div className="flex gap-4">
                      <button
                        onClick={() => setResult(null)}
                        className="secondary-button"
                      >
                        New Analysis
                      </button>
                      <button
                        onClick={clearAnalysisCache}
                        className="tertiary-button"
                        title="Clear cached analysis data to prevent mock data contamination"
                      >
                        Clear Cache
                      </button>
                    </div>

                    <div className="flex gap-4">
                      <button
                        className="tertiary-button"
                        onClick={() => {
                        // Create a simple report text
                        const reportText = `
COMPARATIVE MARKET ANALYSIS REPORT
=================================
Property Address: ${address}
Generated: ${new Date().toLocaleDateString()}

RECOMMENDED PRICE: ${result.recommendedListingPrice || result.recommendedPrice}
Price Range: ${result.priceRange}
Confidence Score: ${result.confidenceScore || 85}%
${result.confidenceExplanation ? `Confidence Explanation: ${result.confidenceExplanation}` : ''}
${result.priceJustification ? `Price Justification: ${result.priceJustification}` : ''}

MARKET TRENDS
------------
Average Price Per Sq Ft: ${result.marketTrends?.averagePricePerSqFt || 'N/A'}
Median Sale Price: ${result.marketTrends?.medianSalePrice || 'N/A'}
Average Days on Market: ${result.marketTrends?.averageDaysOnMarket || 'N/A'} days
Year-over-Year Price Change: ${result.marketTrends?.priceChangeLastYear || 'N/A'}
${result.marketTrends?.inventoryLevel ? `Inventory Level: ${result.marketTrends.inventoryLevel}` : ''}
${result.marketTrends?.marketType ? `Market Type: ${result.marketTrends.marketType}` : ''}

${result.keyCompsUsed?.length ? `
KEY COMPS USED IN ANALYSIS
-------------------------
${result.keyCompsUsed.map(comp => `- ${comp}`).join('\n')}
` : ''}

COMPARABLE PROPERTIES
-------------------
${result.comparableProperties.map((property, index) => `
Property ${index + 1}: ${property.address}
Price: ${property.price}
Bedrooms: ${property.bedrooms || 'N/A'}
Bathrooms: ${property.bathrooms || 'N/A'}
Square Feet: ${property.squareFeet || 'N/A'}
Year Built: ${property.yearBuilt || 'N/A'}
Distance: ${property.distanceFromTarget || 'N/A'}
${property.soldDate ? `Sold Date: ${property.soldDate}` : ''}
Similarity Score: ${property.similarityScore || 'N/A'}%
${property.adjustments ? `Adjustments: ${property.adjustments}` : ''}
${property.included !== undefined ? `Included in Analysis: ${property.included ? 'Yes' : 'No'}` : ''}
${property.source ? `Source: ${property.source}` : ''}
${property.zillowLink ? `Zillow Link: ${property.zillowLink}` : ''}
`).join('\n')}

This report was generated by AI Digital Realtor's Comp Analyzer tool.
Data sources include ${useRealEstateApis ? 'Zillow, Realtor.com, and ' : ''}${llmProvider === 'openai' ? 'OpenAI GPT-4o' : 'Claude 4'} AI.
`;

                        // Create a blob and download it
                        const blob = new Blob([reportText], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `comp-analysis-${address.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.txt`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                      }}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Export Report
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Analysis Results */}
            <EnhancedAnalysisResults
              analysisResult={enhancedAnalysisResult}
              loading={analysisLoading}
              error={analysisError}
              onRetry={() => {
                setAnalysisError(null);
                // Retry by running the main analysis again
                handleSubmit(new Event('submit') as any);
              }}
            />
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-gradient-to-br from-dark-900 to-primary-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">How It Works</span>
            </h2>
            <p className="text-primary-100 max-w-2xl mx-auto">
              Our AI-powered Comp Analyzer uses advanced algorithms to find the most relevant comparable properties and determine the optimal listing price.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <div className="glass-card p-6 border-glow relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-primary-700 to-primary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                <span className="text-lg font-bold">1</span>
              </div>

              <h3 className="text-lg font-bold text-white mb-2 relative z-10">Multi-Source Data</h3>
              <p className="text-primary-200 text-sm relative z-10">
                Our system collects data from multiple real estate sources including Zillow and Realtor.com to provide comprehensive market insights.
              </p>
            </div>

            <div className="glass-card p-6 border-glow relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-secondary-400/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-secondary-700 to-secondary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                <span className="text-lg font-bold">2</span>
              </div>

              <h3 className="text-lg font-bold text-white mb-2 relative z-10">AI Analysis</h3>
              <p className="text-primary-200 text-sm relative z-10">
                Advanced AI algorithms powered by OpenAI GPT-4o or Claude 4 analyze comparable properties and calculate their relevance to your property with detailed adjustments.
              </p>
            </div>

            <div className="glass-card p-6 border-glow relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-primary-700 to-primary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                <span className="text-lg font-bold">3</span>
              </div>

              <h3 className="text-lg font-bold text-white mb-2 relative z-10">Market Trends</h3>
              <p className="text-primary-200 text-sm relative z-10">
                Our system analyzes current market conditions including inventory levels, days on market, and price trends to provide context for your property valuation.
              </p>
            </div>

            <div className="glass-card p-6 border-glow relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-secondary-400/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

              <div className="bg-gradient-to-br from-secondary-700 to-secondary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                <span className="text-lg font-bold">4</span>
              </div>

              <h3 className="text-lg font-bold text-white mb-2 relative z-10">Confidence-Based Pricing</h3>
              <p className="text-primary-200 text-sm relative z-10">
                Our system provides a recommended price range with a confidence score based on the quality and quantity of comparable properties and market stability.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-tech relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="glass-card p-8 border-glow relative overflow-hidden max-w-3xl mx-auto">
            <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <h2 className="text-3xl font-bold text-white mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Ready to Try More AI Tools?</span>
              </h2>

              <p className="text-lg text-primary-100 mb-8 max-w-xl mx-auto">
                Explore our full suite of AI-powered tools designed to help real estate professionals work more efficiently and effectively.
              </p>

              <Link
                to="/"
                className="primary-button button-lg"
              >
                Explore All Tools
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CompAnalyzer;
