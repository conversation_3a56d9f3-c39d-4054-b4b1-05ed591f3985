import { useState, useEffect, useRef, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useApp } from '../context/SimpleAppContext';
import { FaRobot, FaMapMarkerAlt, FaSchool, FaShieldAlt, FaStore, FaWalking, FaFileAlt, FaBuilding, FaExclamationTriangle, FaDownload, FaSpinner, FaBus, FaBicycle, FaChartBar, FaCar } from 'react-icons/fa';
import { generateNeighborhoodComplianceReport } from '../services/neighborhoodComplianceService';
import { fetchNeighborhoodData, mergeReportWithRealTimeData } from '../services/neighborhoodDataService';
import AddressAutocomplete from '../components/AddressAutocomplete';
import { fetchLatLngForAddress } from '../services/placesApi';

// Add type definitions for Google Maps API
declare global {
  interface Window {
    google: any;
    __ENV__?: {
      VITE_GOOGLE_API_KEY?: string;
    };
  }
}

// Define the report data structure
interface NeighborhoodData {
  schools: {
    rating: string;
    topSchools: string[];
  };
  crimeRate: string;
  amenities: string[];
  walkScore: number;
}

interface ComplianceData {
  zoningType: string;
  hoaRules: string[];
  permitsRequired: string[];
  alerts: string[];
}

interface ReportData {
  neighborhoodSummary: NeighborhoodData;
  complianceSummary: ComplianceData;
  generatedAt: string;
  realTimeData?: {
    neighborhood: {
      school_ratings: SchoolData[] | string;
      walkability: WalkabilityData | string;
      demographics: DemographicsData | string;
      commute_analysis: CommuteData[] | string;
    }
  };
}

// Define the demographics data structure
interface DemographicsData {
  population: number;
  median_age: number;
  median_household_income?: number;
  median_home_value?: number;
}

// Define the commute data structure
interface CommuteData {
  destination: string;
  distance: string;
  duration: string;
  mode: string;
}

// Define the school data structure
interface SchoolData {
  name: string;
  city: string;
  state: string;
}

// Define the walkability data structure
interface WalkabilityData {
  walk_score: number;
  walk_description: string;
  transit_score?: number;
  transit_description?: string;
  bike_score?: number;
  bike_description?: string;
}

const NeighborhoodComplianceOverview = () => {
  const { state } = useApp();
  const [address, setAddress] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [llmProvider, setLlmProvider] = useState<'openai' | 'claude'>('openai'); // Default to OpenAI
  const [report, setReport] = useState<ReportData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Handle address selection from the autocomplete component
  const handleAddressSelect = async (selectedAddress: string) => {
    if (!selectedAddress) return;

    setAddress(selectedAddress);

    try {
      // Get coordinates and formatted address using the geocoding service
      const geocodeResult = await fetchLatLngForAddress(selectedAddress);

      if (geocodeResult) {
        // Extract zip code from the formatted address
        const addressParts = geocodeResult.formatted_address.split(',');
        const zipCodeMatch = addressParts[addressParts.length - 2]?.trim().match(/\d{5}(-\d{4})?/);

        if (zipCodeMatch) {
          setZipCode(zipCodeMatch[0]);
        }
      }
    } catch (error) {
      console.error('Error geocoding selected address:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!address.trim()) {
      setError('Please enter a valid address');
      return;
    }

    setLoading(true);
    setError(null);

    // Set a timeout to prevent the report generation from hanging indefinitely
    const timeoutId = setTimeout(() => {
      if (loading) {
        console.warn('Report generation timed out after 30 seconds');
        setLoading(false);
        setError('Report generation timed out. Please try again or try a different address.');
      }
    }, 30000); // 30 second timeout

    try {
      // Log which LLM is being used in development mode
      if (import.meta.env.DEV) {
        console.log(`%c[DEV] Using ${llmProvider === 'openai' ? 'OpenAI' : 'Claude'} for neighborhood & compliance analysis`,
          `color: ${llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
      }

      // Get the API key from the app state or environment variables
      let apiKey;

      if (llmProvider === 'openai') {
        apiKey = state.openAiApiKey || import.meta.env.VITE_OPENAI_API_KEY || '********************************************************************************************************************************************************************';
      } else {
        apiKey = state.claudeApiKey || import.meta.env.VITE_CLAUDE_API_KEY || '************************************************************************************************************';
      }

      if (!apiKey) {
        setError(`${llmProvider.toUpperCase()} API key is not configured. Please check your settings.`);
        return;
      }

      // Log the API key (first 10 characters) in development mode
      if (import.meta.env.DEV) {
        console.log(`Using ${llmProvider} API key: ${apiKey.substring(0, 10)}...`);
      }

      // Get coordinates from the geocoding service
      let latitude: number | undefined;
      let longitude: number | undefined;

      try {
        const geocodeResult = await fetchLatLngForAddress(address);
        if (geocodeResult) {
          latitude = geocodeResult.lat;
          longitude = geocodeResult.lng;
        }
      } catch (error) {
        console.warn('Could not get coordinates from geocoding service:', error);
      }

      // Call the service to generate the AI report
      const aiReportData = await generateNeighborhoodComplianceReport(
        address,
        zipCode,
        llmProvider,
        apiKey
      );

      // Fetch real-time neighborhood data in parallel
      const neighborhoodData = await fetchNeighborhoodData(
        address,
        latitude,
        longitude
      );

      // Merge the AI report with the real-time data
      const mergedReport = mergeReportWithRealTimeData(aiReportData, neighborhoodData);

      setReport(mergedReport);
    } catch (err) {
      setError(`An error occurred while generating the report with ${llmProvider.toUpperCase()}. Please try again.`);
      console.error(err);
    } finally {
      clearTimeout(timeoutId);
      setLoading(false);
    }
  };

  const handleDownloadPDF = () => {
    if (!report) return;

    // In a real implementation, this would call a backend endpoint to generate a PDF
    // For now, we'll just show an alert
    alert('PDF download functionality will be implemented in the next phase.');
  };

  // Render the report section
  const renderReport = () => {
    if (!report) return null;

    return (
      <div className="glass-card p-8 border-glow relative overflow-hidden animate-fade-in">
        <div className="absolute top-0 right-0 w-64 h-64 bg-secondary-400/5 rounded-full blur-3xl"></div>

        <div className="relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">Neighborhood & Compliance Report</h2>
              <p className="text-primary-200">{address}</p>
              <p className="text-primary-300 text-sm">Generated on {new Date(report.generatedAt).toLocaleDateString()}</p>

              {/* LLM Indicator */}
              <div className="mt-2 py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
                <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                     style={{ backgroundColor: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
                <span className="text-xs">
                  Powered by <span className="font-semibold"
                    style={{ color: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                    {llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
                  </span>
                </span>
              </div>
            </div>
            <button
              onClick={handleDownloadPDF}
              className="mt-4 md:mt-0 bg-dark-800/80 backdrop-blur-sm px-4 py-2 rounded-lg border border-primary-700/50 text-accent-cyan hover:bg-dark-700/80 transition-colors flex items-center"
            >
              <FaDownload className="mr-2" />
              Download PDF
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Neighborhood Summary */}
            <div className="bg-dark-800/50 rounded-xl p-6 border border-primary-700/30">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <FaMapMarkerAlt className="mr-2 text-accent-cyan" />
                Neighborhood Summary
              </h3>

              <div className="space-y-6">
                {/* Schools */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaSchool className="mr-2 text-primary-400" />
                    Schools
                  </h4>
                  <p className="text-primary-200 mb-2">Rating: <span className="text-accent-cyan">{report.neighborhoodSummary.schools.rating}</span></p>

                  {/* AI-Generated Schools */}
                  <div className="bg-dark-700/50 rounded-lg p-3 mb-3">
                    <p className="text-primary-300 text-sm mb-1">AI-Generated Top Schools:</p>
                    <ul className="list-disc list-inside text-primary-200">
                      {report.neighborhoodSummary.schools.topSchools.map((school, index) => (
                        <li key={index}>{school}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Real-Time School Data */}
                  {report.realTimeData?.neighborhood.school_ratings && (
                    <div className="bg-dark-700/50 rounded-lg p-3 border border-accent-cyan/30">
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-primary-300 text-sm">Real-Time School Data:</p>
                        <span className="text-xs bg-accent-cyan/20 text-accent-cyan px-2 py-0.5 rounded-full">Live Data</span>
                      </div>
                      {typeof report.realTimeData.neighborhood.school_ratings === 'string' ? (
                        <p className="text-primary-200 text-sm italic">{report.realTimeData.neighborhood.school_ratings}</p>
                      ) : (
                        <ul className="list-disc list-inside text-primary-200">
                          {report.realTimeData.neighborhood.school_ratings.map((school, index) => (
                            <li key={index}>{school.name} ({school.city}, {school.state})</li>
                          ))}
                        </ul>
                      )}
                    </div>
                  )}
                </div>

                {/* Crime Rate */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaShieldAlt className="mr-2 text-primary-400" />
                    Safety
                  </h4>
                  <p className="text-primary-200">
                    Crime Rate: <span className={`font-medium ${
                      report.neighborhoodSummary.crimeRate.toLowerCase().includes('low') ? 'text-green-400' :
                      report.neighborhoodSummary.crimeRate.toLowerCase().includes('moderate') ? 'text-yellow-400' :
                      'text-red-400'
                    }`}>{report.neighborhoodSummary.crimeRate}</span>
                  </p>
                </div>

                {/* Amenities */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaStore className="mr-2 text-primary-400" />
                    Local Amenities
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {report.neighborhoodSummary.amenities.map((amenity, index) => (
                      <span key={index} className="bg-dark-700 px-3 py-1 rounded-full text-primary-200 text-sm">
                        {amenity}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Demographics */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaChartBar className="mr-2 text-primary-400" />
                    Demographics
                  </h4>

                  {/* Real-Time Demographics Data */}
                  {report.realTimeData?.neighborhood.demographics && (
                    <div className="bg-dark-700/50 rounded-lg p-3 border border-accent-cyan/30">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-primary-300 text-sm">Census Data:</p>
                        <span className="text-xs bg-accent-cyan/20 text-accent-cyan px-2 py-0.5 rounded-full">Live Data</span>
                      </div>

                      {typeof report.realTimeData.neighborhood.demographics === 'string' ? (
                        <p className="text-primary-200 text-sm italic">{report.realTimeData.neighborhood.demographics}</p>
                      ) : (
                        <div className="space-y-3">
                          {/* Population */}
                          <div className="flex items-center justify-between">
                            <span className="text-primary-200">Population:</span>
                            <span className="text-accent-cyan font-bold">
                              {report.realTimeData.neighborhood.demographics.population.toLocaleString()}
                            </span>
                          </div>

                          {/* Median Age */}
                          <div className="flex items-center justify-between">
                            <span className="text-primary-200">Median Age:</span>
                            <span className="text-accent-cyan font-bold">
                              {report.realTimeData.neighborhood.demographics.median_age.toFixed(1)} years
                            </span>
                          </div>

                          {/* Median Household Income */}
                          {report.realTimeData.neighborhood.demographics.median_household_income ? (
                            <div className="flex items-center justify-between">
                              <span className="text-primary-200">Median Household Income:</span>
                              <span className="text-accent-cyan font-bold">
                                ${report.realTimeData.neighborhood.demographics.median_household_income.toLocaleString()}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center justify-between">
                              <span className="text-primary-200">Median Household Income:</span>
                              <span className="text-primary-300 italic">Data not available</span>
                            </div>
                          )}

                          {/* Median Home Value */}
                          {report.realTimeData.neighborhood.demographics.median_home_value ? (
                            <div className="flex items-center justify-between">
                              <span className="text-primary-200">Median Home Value:</span>
                              <span className="text-accent-cyan font-bold">
                                ${report.realTimeData.neighborhood.demographics.median_home_value.toLocaleString()}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center justify-between">
                              <span className="text-primary-200">Median Home Value:</span>
                              <span className="text-primary-300 italic">Data not available</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Commute Analysis - Temporarily commented out
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaCar className="mr-2 text-primary-400" />
                    Commute Analysis
                  </h4>

                  {/* Real-Time Commute Data *//*}
                  {report.realTimeData?.neighborhood.commute_analysis && (
                    <div className="bg-dark-700/50 rounded-lg p-3 border border-accent-cyan/30">
                      {/* This div is now handled in the conditional rendering below *//*}

                      {typeof report.realTimeData.neighborhood.commute_analysis === 'string' ? (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <p className="text-primary-300 text-sm">Travel Times:</p>
                          </div>
                          <p className="text-primary-200 text-sm italic">{report.realTimeData.neighborhood.commute_analysis}</p>
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <div className="flex items-center justify-between mb-2">
                            <p className="text-primary-300 text-sm">Travel Times:</p>
                            {/* Check if the data is likely mock data by looking at the pattern *//*}
                            {report.realTimeData.neighborhood.commute_analysis.some(item =>
                              item.destination === 'Downtown Chicago' &&
                              item.distance.match(/^\d+\.\d+ mi$/) &&
                              item.duration.match(/^\d+ mins$/)
                            ) && (
                              <span className="text-xs bg-yellow-600/30 text-yellow-400 px-2 py-0.5 rounded-full">
                                Estimated Data
                              </span>
                            )}
                          </div>
                          <table className="min-w-full text-sm">
                            <thead>
                              <tr className="border-b border-dark-600">
                                <th className="text-left py-2 text-primary-300">Destination</th>
                                <th className="text-left py-2 text-primary-300">Distance</th>
                                <th className="text-left py-2 text-primary-300">Est. Time</th>
                                <th className="text-left py-2 text-primary-300">Mode</th>
                              </tr>
                            </thead>
                            <tbody>
                              {report.realTimeData.neighborhood.commute_analysis.map((commute, index) => (
                                <tr key={index} className={index % 2 === 0 ? 'bg-dark-800/30' : ''}>
                                  <td className="py-2 text-primary-200">{commute.destination}</td>
                                  <td className="py-2 text-accent-cyan">{commute.distance}</td>
                                  <td className="py-2 text-accent-cyan">{commute.duration}</td>
                                  <td className="py-2 text-primary-200">{commute.mode}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                */}

                {/* Walkability */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaWalking className="mr-2 text-primary-400" />
                    Walkability & Transit
                  </h4>

                  {/* AI-Generated Walkability */}
                  <div className="mb-3">
                    <p className="text-primary-300 text-sm mb-1">AI-Generated Walkability:</p>
                    <div className="flex items-center">
                      <div className="w-full bg-dark-700 rounded-full h-4 mr-4">
                        <div
                          className="bg-gradient-to-r from-accent-cyan to-primary-500 h-4 rounded-full"
                          style={{ width: `${report.neighborhoodSummary.walkScore}%` }}
                        ></div>
                      </div>
                      <span className="text-accent-cyan font-bold">{report.neighborhoodSummary.walkScore}</span>
                    </div>
                    <p className="text-primary-300 text-sm mt-1">
                      {report.neighborhoodSummary.walkScore >= 70 ? 'Very Walkable' :
                       report.neighborhoodSummary.walkScore >= 50 ? 'Somewhat Walkable' :
                       'Car-Dependent'}
                    </p>
                  </div>

                  {/* Real-Time Walkability Data */}
                  {report.realTimeData?.neighborhood.walkability && (
                    <div className="bg-dark-700/50 rounded-lg p-3 border border-accent-cyan/30">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-primary-300 text-sm">Real-Time Walkability Data:</p>
                        <span className="text-xs bg-accent-cyan/20 text-accent-cyan px-2 py-0.5 rounded-full">Live Data</span>
                      </div>

                      {typeof report.realTimeData.neighborhood.walkability === 'string' ? (
                        <p className="text-primary-200 text-sm italic">{report.realTimeData.neighborhood.walkability}</p>
                      ) : (
                        <div className="space-y-3">
                          {/* Walk Score */}
                          <div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <FaWalking className="text-accent-cyan mr-2" />
                                <span className="text-primary-200 text-sm">Walk Score:</span>
                              </div>
                              <span className="text-accent-cyan font-bold">{report.realTimeData.neighborhood.walkability.walk_score}</span>
                            </div>
                            <div className="w-full bg-dark-700 rounded-full h-2 mt-1">
                              <div
                                className="bg-gradient-to-r from-accent-cyan to-primary-500 h-2 rounded-full"
                                style={{ width: `${report.realTimeData.neighborhood.walkability.walk_score}%` }}
                              ></div>
                            </div>
                            <p className="text-primary-300 text-xs mt-1">{report.realTimeData.neighborhood.walkability.walk_description}</p>
                          </div>

                          {/* Transit Score */}
                          {report.realTimeData.neighborhood.walkability.transit_score && (
                            <div>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <FaBus className="text-accent-cyan mr-2" />
                                  <span className="text-primary-200 text-sm">Transit Score:</span>
                                </div>
                                <span className="text-accent-cyan font-bold">{report.realTimeData.neighborhood.walkability.transit_score}</span>
                              </div>
                              <div className="w-full bg-dark-700 rounded-full h-2 mt-1">
                                <div
                                  className="bg-gradient-to-r from-accent-cyan to-primary-500 h-2 rounded-full"
                                  style={{ width: `${report.realTimeData.neighborhood.walkability.transit_score}%` }}
                                ></div>
                              </div>
                              <p className="text-primary-300 text-xs mt-1">{report.realTimeData.neighborhood.walkability.transit_description}</p>
                            </div>
                          )}

                          {/* Bike Score */}
                          {report.realTimeData.neighborhood.walkability.bike_score && (
                            <div>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <FaBicycle className="text-accent-cyan mr-2" />
                                  <span className="text-primary-200 text-sm">Bike Score:</span>
                                </div>
                                <span className="text-accent-cyan font-bold">{report.realTimeData.neighborhood.walkability.bike_score}</span>
                              </div>
                              <div className="w-full bg-dark-700 rounded-full h-2 mt-1">
                                <div
                                  className="bg-gradient-to-r from-accent-cyan to-primary-500 h-2 rounded-full"
                                  style={{ width: `${report.realTimeData.neighborhood.walkability.bike_score}%` }}
                                ></div>
                              </div>
                              <p className="text-primary-300 text-xs mt-1">{report.realTimeData.neighborhood.walkability.bike_description}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Compliance Summary */}
            <div className="bg-dark-800/50 rounded-xl p-6 border border-primary-700/30">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <FaFileAlt className="mr-2 text-accent-cyan" />
                Compliance Overview
              </h3>

              <div className="space-y-6">
                {/* Zoning */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <FaBuilding className="mr-2 text-primary-400" />
                    Zoning
                  </h4>
                  <p className="text-primary-200">Type: <span className="text-accent-cyan">{report.complianceSummary.zoningType}</span></p>
                </div>

                {/* HOA Rules */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">HOA Rules</h4>
                  {report.complianceSummary.hoaRules.length > 0 ? (
                    <ul className="list-disc list-inside text-primary-200">
                      {report.complianceSummary.hoaRules.map((rule, index) => (
                        <li key={index}>{rule}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-primary-200">No HOA rules found for this property.</p>
                  )}
                </div>

                {/* Permits Required */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Required Permits</h4>
                  {report.complianceSummary.permitsRequired.length > 0 ? (
                    <ul className="list-disc list-inside text-primary-200">
                      {report.complianceSummary.permitsRequired.map((permit, index) => (
                        <li key={index}>{permit}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-primary-200">No specific permits required.</p>
                  )}
                </div>

                {/* Alerts */}
                {report.complianceSummary.alerts.length > 0 && (
                  <div>
                    <h4 className="text-lg font-semibold text-red-400 mb-2 flex items-center">
                      <FaExclamationTriangle className="mr-2" />
                      Compliance Alerts
                    </h4>
                    <div className="bg-red-900/20 border border-red-700/30 rounded-lg p-4">
                      <ul className="space-y-2">
                        {report.complianceSummary.alerts.map((alert, index) => (
                          <li key={index} className="flex items-start">
                            <FaExclamationTriangle className="text-red-400 mr-2 mt-1 flex-shrink-0" />
                            <span className="text-red-300">{alert}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-dark-900">
      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-tech overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-8 animate-fade-in">
            <div className="inline-block mb-3 bg-dark-800/80 backdrop-blur-sm px-4 py-1.5 rounded-full border border-accent-cyan/30">
              <span className="text-accent-cyan text-sm font-medium">AI-Powered Analysis</span>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 neon-text">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Neighborhood & Compliance Overview</span>
            </h1>

            <p className="text-lg text-primary-200 max-w-3xl mx-auto">
              Get comprehensive insights about any neighborhood and property compliance requirements using our advanced AI analysis.
            </p>

            {/* LLM Indicator */}
            <div className="mt-4 py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
              <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                   style={{ backgroundColor: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
              <span className="text-xs">
                Powered by <span className="font-semibold"
                  style={{ color: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                  {llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
                </span>
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="glass-card p-10 border-glow relative overflow-hidden mb-10 max-w-3xl mx-auto">
              <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/5 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl"></div>

              <div className="relative z-10">
                <h2 className="text-2xl font-bold text-white mb-8 text-center">Enter Property Address</h2>

                <form onSubmit={handleSubmit} className="space-y-8 max-w-2xl mx-auto">
                  {/* LLM Provider Toggle */}
                  <div className="flex flex-col items-center mb-6">
                    <div className="bg-dark-800 p-1 rounded-lg flex mb-3">
                      <button
                        type="button"
                        onClick={() => setLlmProvider('openai')}
                        style={{
                          background: llmProvider === 'openai'
                            ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                            : 'transparent',
                          transition: 'all 0.3s ease',
                        }}
                        className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'openai'
                          ? 'text-white shadow-lg hover:shadow-xl'
                          : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                      >
                        <span className="inline-flex items-center">
                          <FaRobot className="mr-2" style={{ color: '#10a37f' }} />
                          OpenAI
                        </span>
                      </button>
                      <button
                        type="button"
                        onClick={() => setLlmProvider('claude')}
                        style={{
                          background: llmProvider === 'claude'
                            ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                            : 'transparent',
                          transition: 'all 0.3s ease',
                        }}
                        className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'claude'
                          ? 'text-white shadow-lg hover:shadow-xl'
                          : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                      >
                        <span className="inline-flex items-center">
                          <FaRobot className="mr-2" style={{ color: '#a27aff' }} />
                          Claude
                        </span>
                      </button>
                    </div>

                    {/* LLM Indicator */}
                    <div className="py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
                      <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                           style={{ backgroundColor: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
                      <span className="text-xs">
                        Powered by <span className="font-semibold"
                          style={{ color: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                          {llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
                        </span>
                      </span>
                    </div>
                  </div>

                  <div className="relative">
                    <label htmlFor="address" className="block text-primary-100 mb-3 text-sm font-medium">Property Address</label>
                    <div className="relative group">
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-accent-cyan/30 to-secondary-400/30 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-300"></div>
                      <div className="relative">
                        <AddressAutocomplete
                          initialValue={address}
                          onSelect={handleAddressSelect}
                          placeholder="Start typing an address..."
                          inputClassName="bg-white/95 backdrop-blur-sm border border-primary-700/30 text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-cyan/50 focus:border-accent-cyan/50 shadow-sm transition-all duration-300"
                          dropdownClassName="bg-dark-800/95 backdrop-blur-sm border border-primary-700/50 shadow-glow-sm"
                        />
                        {address && zipCode && (
                          <div className="mt-2 text-xs text-primary-300 flex items-center">
                            <FaMapMarkerAlt className="mr-1 text-accent-cyan" />
                            <span>Selected: {address} ({zipCode})</span>
                          </div>
                        )}
                      </div>
                    </div>
                    {error && <p className="mt-2 text-red-400 text-sm">{error}</p>}
                  </div>

                  <div className="pt-2">
                    <button
                      type="submit"
                      disabled={loading}
                      className="primary-button button-lg w-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {loading ? (
                        <>
                          <FaSpinner className="animate-spin mr-3 h-5 w-5 text-white" />
                          Generating Report...
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          Generate Report
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {report && renderReport()}

            {/* How It Works Section */}
            <div className="mt-16">
              <h2 className="text-3xl font-bold text-white text-center mb-10">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">How It Works</span>
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div className="glass-card p-6 border-glow relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                  <div className="bg-gradient-to-br from-primary-700 to-primary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                    <span className="text-lg font-bold">1</span>
                  </div>

                  <h3 className="text-lg font-bold text-white mb-2 relative z-10">Data Collection</h3>
                  <p className="text-primary-200 text-sm relative z-10">
                    Our AI agents collect comprehensive data about the neighborhood, including schools, crime statistics, amenities, and walkability scores.
                  </p>
                </div>

                <div className="glass-card p-6 border-glow relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-secondary-400/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                  <div className="bg-gradient-to-br from-secondary-700 to-secondary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                    <span className="text-lg font-bold">2</span>
                  </div>

                  <h3 className="text-lg font-bold text-white mb-2 relative z-10">Compliance Analysis</h3>
                  <p className="text-primary-200 text-sm relative z-10">
                    Our system analyzes zoning laws, HOA rules, and permit requirements specific to the property address to identify potential compliance issues.
                  </p>
                </div>

                <div className="glass-card p-6 border-glow relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-accent-cyan/5 rounded-full blur-xl transform transition-all duration-500 group-hover:scale-150 opacity-0 group-hover:opacity-70"></div>

                  <div className="bg-gradient-to-br from-primary-700 to-primary-900 w-12 h-12 rounded-full flex items-center justify-center text-white mb-4 relative z-10">
                    <span className="text-lg font-bold">3</span>
                  </div>

                  <h3 className="text-lg font-bold text-white mb-2 relative z-10">Comprehensive Report</h3>
                  <p className="text-primary-200 text-sm relative z-10">
                    Get a detailed report with actionable insights about the neighborhood and property compliance requirements, highlighting any potential issues.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 relative bg-gradient-tech overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent-cyan/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="glass-card p-8 border-glow relative overflow-hidden max-w-3xl mx-auto">
            <div className="absolute top-0 right-0 w-64 h-64 bg-accent-cyan/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <h2 className="text-3xl font-bold text-white mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-cyan to-primary-400">Ready to Try More AI Tools?</span>
              </h2>

              <p className="text-lg text-primary-100 mb-8 max-w-xl mx-auto">
                Explore our full suite of AI-powered tools designed to help real estate professionals work more efficiently and effectively.
              </p>

              <Link
                to="/"
                className="primary-button button-lg"
              >
                Explore All Tools
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NeighborhoodComplianceOverview;
