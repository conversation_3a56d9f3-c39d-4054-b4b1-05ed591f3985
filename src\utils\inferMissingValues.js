// OpenAI API integration
import { getOpenAIClient } from './openaiConfig';

// Sample inferred values
const sampleInferredValues = {
  "financials.earnestMoney": 40000,
  "financials.closingDate": "2025-06-30",
  "fixturesIncluded": ["Refrigerator", "Dishwasher", "Washer/Dryer"],
  "property.parkingDetails": "Assigned space #42",
  "associations.monthlyFees": 350
};

/**
 * Infer missing values in the form data using OpenAI
 * @param {Object} partialData - The partial form data
 * @param {Object} schema - The contract schema
 * @returns {Promise<Object>} - The inferred values
 */
export async function inferMissingValues(partialData, schema) {
  try {
    // Identify which fields can be inferred
    const inferrableFields = [];

    // Process each section in the schema
    Object.entries(schema).forEach(([section, fields]) => {
      // Handle nested fields
      if (typeof fields === 'object' && !Array.isArray(fields)) {
        Object.entries(fields).forEach(([fieldName, fieldInfo]) => {
          if (fieldInfo.infer === true) {
            const path = `${section}.${fieldName}`;
            const value = getNestedValue(partialData, path);

            if (value === undefined || value === null || value === '') {
              inferrableFields.push({
                path,
                type: fieldInfo.type,
                label: fieldInfo.label
              });
            }
          }
        });
      }
      // Handle direct fields
      else if (fields.infer === true) {
        const value = partialData[section];

        if (value === undefined || value === null || value === '') {
          inferrableFields.push({
            path: section,
            type: fields.type,
            label: fields.label
          });
        }
      }
    });

    // If there are no inferrable fields, return the original data
    if (inferrableFields.length === 0) {
      return partialData;
    }

    const prompt = `
You are a real estate contract assistant. Based on the following partial contract data, suggest values for the missing fields.
Use Chicago real estate norms and standards for condominiums where applicable.

Partial Contract Data:
${JSON.stringify(partialData, null, 2)}

Missing Fields to Infer:
${inferrableFields.map(field => `- ${field.label} (${field.path}): ${field.type}`).join('\\n')}

Please respond with a JSON object containing only the inferred values for the missing fields. Use the field paths as keys.
For example:
{
  "financials.earnestMoney": 40000,
  "financials.closingDate": "2025-06-30",
  "fixturesIncluded": ["Refrigerator", "Oven", "Washer/Dryer"]
}
`;

    // Get the OpenAI client
    const openai = getOpenAIClient();

    if (!openai) {
      console.warn('OpenAI client not available, using mock implementation');
      throw new Error('OpenAI client not available');
    }

    // Call the OpenAI API
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        { role: "system", content: "You are a helpful assistant that infers missing values for real estate contracts." },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });

    // Parse the response
    const inferredValuesText = response.choices[0].message.content.trim();

    // Extract JSON from the response (in case there's any additional text)
    const jsonMatch = inferredValuesText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Could not extract JSON from the response');
    }

    const inferredValues = JSON.parse(jsonMatch[0]);

    // Merge the inferred values with the partial data
    const mergedData = { ...partialData };

    Object.entries(inferredValues).forEach(([path, value]) => {
      setNestedValue(mergedData, path, value);
    });

    return mergedData;
  } catch (error) {
    console.error('Error inferring missing values:', error);

    // In case of error, return the original data with some default inferred values
    console.log('Using default inferred values as fallback');
    const mergedData = { ...partialData };

    // Add some default values for common fields
    if (partialData.financials && !partialData.financials.earnestMoney && partialData.financials.purchasePrice) {
      // Default earnest money is 5% of purchase price
      const earnestMoney = Math.round(parseFloat(partialData.financials.purchasePrice) * 0.05);
      setNestedValue(mergedData, 'financials.earnestMoney', earnestMoney);
    }

    if (partialData.financials && !partialData.financials.closingDate) {
      // Default closing date is 45 days from today
      const today = new Date();
      const closingDate = new Date(today.setDate(today.getDate() + 45));
      const formattedDate = closingDate.toISOString().split('T')[0];
      setNestedValue(mergedData, 'financials.closingDate', formattedDate);
    }

    if (!partialData.fixturesIncluded || partialData.fixturesIncluded.length === 0) {
      // Default fixtures
      setNestedValue(mergedData, 'fixturesIncluded', ['Refrigerator', 'Oven/Range', 'Dishwasher', 'Microwave']);
    }

    return mergedData;
  }
}

/**
 * Get a nested value from an object using a dot-notation path
 * @param {Object} obj - The object to get the value from
 * @param {string} path - The path to the value (e.g., 'financials.earnestMoney')
 * @returns {*} - The value at the path, or undefined if not found
 */
function getNestedValue(obj, path) {
  const keys = path.split('.');
  let value = obj;

  for (const key of keys) {
    if (value === undefined || value === null) {
      return undefined;
    }

    value = value[key];
  }

  return value;
}

/**
 * Set a nested value in an object using a dot-notation path
 * @param {Object} obj - The object to set the value in
 * @param {string} path - The path to the value (e.g., 'financials.earnestMoney')
 * @param {*} value - The value to set
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];

    if (current[key] === undefined || current[key] === null) {
      current[key] = {};
    }

    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
}

/**
 * Main function to test inferring missing values
 */
export async function testInferMissingValues() {
  try {
    // In a browser environment, we can't load from a file
    // Instead, we'll use a sample schema
    const sampleSchema = {
      "buyer": {
        "fullName": { "infer": false },
        "email": { "infer": false }
      },
      "seller": {
        "fullName": { "infer": false }
      },
      "property": {
        "address": { "infer": false },
        "parkingIncluded": { "infer": false },
        "parkingDetails": { "infer": true }
      },
      "financials": {
        "purchasePrice": { "infer": false },
        "earnestMoney": { "infer": true },
        "closingDate": { "infer": true }
      },
      "associations": {
        "hoa": { "infer": false },
        "monthlyFees": { "infer": true }
      },
      "fixturesIncluded": { "infer": true }
    };

    // Create a partial data object
    const partialData = {
      buyer: { fullName: "John Doe" },
      seller: { fullName: "Jane Smith" },
      property: {
        address: "1260 W Washington Blvd Apt 408, Chicago, IL 60607",
        parkingIncluded: true
      },
      financials: { purchasePrice: 800000 },
      associations: { hoa: true }
    };

    // Infer the missing values
    const mergedData = await inferMissingValues(partialData, sampleSchema);

    // In a browser environment, we can't save to a file
    // Instead, we'll log the result to the console
    console.log('Inferred values:', mergedData);

    return mergedData;
  } catch (error) {
    console.error('Error testing infer missing values:', error);
    return null;
  }
}

export default {
  inferMissingValues,
  testInferMissingValues
};
