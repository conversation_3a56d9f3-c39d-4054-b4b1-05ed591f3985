/* Background Styles */
:root {
  --bg-overlay-opacity: 0.85;
  --bg-overlay-color: rgba(10, 12, 26, var(--bg-overlay-opacity));
}

.app-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-image: url('./Background.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -2;
}

.app-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay-color);
  z-index: -1;
}

/* Static background */
.app-background {
  transition: opacity 0.5s ease;
}

/* Add a subtle glow effect to the background */
.bg-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: radial-gradient(circle at 50% 50%, rgba(0, 247, 255, 0.05), transparent 70%);
  z-index: -1;
  opacity: 0.6;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

/* Add animated particles effect */
.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

/* Content container with glass effect */
.glass-container {
  background: rgba(10, 12, 26, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 247, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Grid pattern background */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(74, 107, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(74, 107, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Adjust the main content area to work with the background */
.main-content {
  position: relative;
  z-index: 1;
}

/* Dark overlay for modal backgrounds */
.modal-bg-overlay {
  background-color: rgba(5, 6, 13, 0.8);
  backdrop-filter: blur(5px);
}

/* Media query for mobile devices */
@media (max-width: 768px) {
  :root {
    --bg-overlay-opacity: 0.9;
  }

  .app-background {
    background-position: 70% center;
  }
}
