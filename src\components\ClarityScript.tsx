import { useEffect } from 'react';

const CLARITY_ID = 'r7yjq54tfv';

/**
 * Component that injects the Microsoft Clarity tracking script into the document head
 * This approach ensures the script is loaded exactly as provided by Microsoft
 */
const ClarityScript = () => {
  useEffect(() => {
    // Only load Clarity in production environment
    if (process.env.NODE_ENV !== 'production') {
      console.log('Microsoft Clarity not loaded in development environment');
      return;
    }

    try {
      // Check if the script is already loaded
      if (document.querySelector(`script[src*="clarity.ms/tag/${CLARITY_ID}"]`)) {
        console.log('Microsoft Clarity script already loaded');
        return;
      }

      // Create the script element
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.innerHTML = `
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "${CLARITY_ID}");
      `;
      
      // Append the script to the document head
      document.head.appendChild(script);
      console.log('Microsoft Clarity script loaded successfully');
    } catch (error) {
      console.error('Failed to load Microsoft Clarity script:', error);
    }
  }, []);

  // This component doesn't render anything
  return null;
};

export default ClarityScript;
