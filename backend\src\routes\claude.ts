import express from 'express';
import Anthropic from '@anthropic-ai/sdk';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || '************************************************************************************************************',
});

// Define interfaces for request bodies
interface AnalyzeImagesRequest {
  images: string[];
}

interface GenerateReportRequest {
  propertyAddress: string;
  inspectionDate: string;
  inspectorName: string;
  clientName: string;
  propertyType?: string;
  yearBuilt?: string;
  squareFootage?: string;
  generalNotes: string;
  imageAnalysis?: string;
}

// Endpoint to analyze images
router.post('/analyze-images', async (req, res) => {
  try {
    const { images } = req.body as AnalyzeImagesRequest;

    if (!images || images.length === 0) {
      return res.status(400).json({ error: 'No images provided' });
    }

    // Prepare the message content
    let messageContent = "Please analyze these property images and identify any issues or defects. For each issue, provide the Item/Area, Issue description, Analysis, and Recommendation in a structured format that can be easily converted to a table:";

    // Add images to the message content
    const imageContents = images.map(image => {
      // Determine the media type from the data URL
      const mediaType = image.match(/^data:image\\/(jpeg|png|gif|webp);base64,/)?.[1] || 'jpeg';

      return {
        type: "image" as const,
        source: {
          type: "base64" as const,
          media_type: `image/${mediaType}` as "image/jpeg" | "image/png" | "image/gif" | "image/webp",
          data: image.replace(/^data:image\\/\\w+;base64,/, "")
        }
      };
    });

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional property inspector analyzing images of a property.
    Identify any visible issues, defects, or concerns in the provided images that would be relevant for a property inspection report.
    Focus on structural issues, water damage, electrical problems, HVAC issues, plumbing problems, safety hazards, and other significant defects.

    For each issue identified, provide the following information in a structured format:
    1. Item/Area: The specific item or area visible in the photo (e.g., "Bathroom Ceiling", "Kitchen Sink", "Basement Wall")
    2. Issue: A clear description of the problem
    3. Analysis: The potential implications or risks, including severity
    4. Recommendation: Specific actions to address the issue

    Format your response as a structured list where each issue is clearly separated and includes all four elements above. This will be used to create a table in the final report.`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1000,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: messageContent },
            ...imageContents
          ]
        }
      ]
    });

    // Extract the analysis
    const content = response.content[0];
    if (content.type === 'text') {
      return res.status(200).json({ analysis: content.text });
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error analyzing images with Claude:', error);
    return res.status(500).json({ error: 'Failed to analyze images with Claude. Please try again.' });
  }
});

// Endpoint to generate inspection report
router.post('/generate-report', async (req, res) => {
  try {
    const inspectionData = req.body as GenerateReportRequest;

    // Validate required fields
    if (!inspectionData.propertyAddress || !inspectionData.inspectionDate ||
        !inspectionData.inspectorName || !inspectionData.clientName || !inspectionData.generalNotes) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional property inspector. Create a detailed inspection report based on the provided information.
    The report should include:

    1. A professional header with property details
    2. A summary of findings
    3. Detailed analysis of issues found - THIS MUST BE FORMATTED AS A MARKDOWN TABLE with the following columns:
       - Item: The specific item or area with the issue
       - Issue: A clear description of the problem
       - Analysis: Your professional analysis of the issue, its severity, and potential implications
       - Recommendation: Specific actions to address the issue
    4. If image analysis is provided, include a section titled "Issues Identified from Photos" with the findings
       - THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns as above:
       - Item: The specific item or area visible in the photo
       - Issue: A clear description of the problem identified in the photo
       - Analysis: Your professional analysis of the issue, its severity, and potential implications
       - Recommendation: Specific actions to address the issue
    5. A conclusion

    Format the report using Markdown with appropriate headers, bullet points, and sections. BOTH the detailed analysis section AND the issues identified from photos section MUST be formatted as markdown tables with the columns specified above. Be specific, professional, and thorough.`;

    // Create the user message with the inspection details
    const userMessage = `Property Inspection Details:
    - Property Address: ${inspectionData.propertyAddress}
    - Inspection Date: ${new Date(inspectionData.inspectionDate).toLocaleDateString()}
    - Inspector: ${inspectionData.inspectorName}
    - Client: ${inspectionData.clientName}
    - Property Type: ${inspectionData.propertyType || 'Not specified'}
    - Year Built: ${inspectionData.yearBuilt || 'Not specified'}
    - Square Footage: ${inspectionData.squareFootage || 'Not specified'}

    General Notes and Issues Found:
    ${inspectionData.generalNotes}

    ${inspectionData.imageAnalysis ? `
    Image Analysis Results:
    ${inspectionData.imageAnalysis}
    ` : ''}

    Please analyze these notes${inspectionData.imageAnalysis ? ' and image analysis results' : ''}, identify all issues mentioned, and provide professional recommendations for addressing each issue. Format the report professionally with clear sections.

    IMPORTANT: The "Detailed Analysis of Issues" section MUST be formatted as a markdown table with these exact columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.
    ${inspectionData.imageAnalysis ? 'Include a separate section titled "Issues Identified from Photos" that summarizes the findings from the image analysis. THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.' : ''}`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1500,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: userMessage }
          ]
        }
      ]
    });

    // Extract the generated report
    const content = response.content[0];
    if (content.type === 'text') {
      return res.status(200).json({ report: content.text });
    } else {
      console.error('Unexpected response format from Claude');
      return res.status(500).json({ error: 'Unexpected response format from Claude' });
    }
  } catch (error) {
    console.error('Error generating inspection report with Claude:', error);
    return res.status(500).json({ error: 'Failed to generate inspection report with Claude. Please try again.' });
  }
});

export default router;
