/* Custom styles for React Big Calendar */

/* Calendar container */
.rbc-calendar {
  background-color: rgba(17, 24, 39, 0.7);
  border-radius: 0.75rem;
  overflow: hidden;
  font-family: inherit;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.1);
}

/* Header toolbar */
.rbc-toolbar {
  padding: 1rem;
  background: linear-gradient(to right, rgba(17, 24, 39, 0.9), rgba(31, 41, 55, 0.9));
  color: white;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.rbc-toolbar button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.rbc-toolbar button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.rbc-toolbar button.rbc-active {
  background: linear-gradient(to right, #00b9ff, #9900ff);
  color: white;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.rbc-toolbar button.rbc-active:hover {
  background: linear-gradient(to right, #00a5e0, #8800e0);
}

.rbc-toolbar-label {
  font-weight: bold;
  font-size: 1.25rem;
  background: linear-gradient(to right, #00b9ff, #9900ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Month view */
.rbc-month-view {
  border: none;
  background-color: rgba(17, 24, 39, 0.5);
}

.rbc-month-header {
  background-color: rgba(31, 41, 55, 0.8);
}

.rbc-header {
  padding: 0.75rem 0;
  font-weight: 600;
  color: #a5b4fc;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.rbc-header + .rbc-header {
  border-left: 1px solid rgba(59, 130, 246, 0.1);
}

/* Day cells */
.rbc-day-bg {
  background-color: rgba(17, 24, 39, 0.5);
  transition: background-color 0.2s ease;
}

.rbc-day-bg:hover {
  background-color: rgba(31, 41, 55, 0.7);
}

.rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid rgba(59, 130, 246, 0.1);
}

.rbc-off-range-bg {
  background-color: rgba(17, 24, 39, 0.3);
}

.rbc-today {
  background-color: rgba(79, 70, 229, 0.2);
}

/* Date cells */
.rbc-date-cell {
  padding: 0.5rem;
  text-align: right;
  color: #e2e8f0;
}

.rbc-date-cell.rbc-now {
  font-weight: bold;
  color: #a5b4fc;
}

/* Events */
.rbc-event {
  background: linear-gradient(to right, rgba(0, 185, 255, 0.8), rgba(153, 0, 255, 0.8));
  border: none;
  border-radius: 0.25rem;
  color: white;
  padding: 0.25rem 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.rbc-event:hover {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.rbc-event-label {
  font-size: 0.75rem;
  font-weight: bold;
}

.rbc-event-content {
  font-size: 0.875rem;
}

/* Selected slot */
.rbc-selected-cell {
  background-color: rgba(79, 70, 229, 0.3);
}

/* Time slots (for week/day view) */
.rbc-time-view {
  background-color: rgba(17, 24, 39, 0.7);
  border: none;
}

.rbc-time-header {
  background-color: rgba(31, 41, 55, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.rbc-time-content {
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.rbc-time-slot {
  color: #a5b4fc;
}

.rbc-time-gutter {
  background-color: rgba(31, 41, 55, 0.8);
}

.rbc-timeslot-group {
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

/* Agenda view */
.rbc-agenda-view {
  background-color: rgba(17, 24, 39, 0.7);
  color: #e2e8f0;
}

.rbc-agenda-table {
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.rbc-agenda-table th {
  background-color: rgba(31, 41, 55, 0.8);
  color: #a5b4fc;
  padding: 0.75rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.rbc-agenda-table td {
  padding: 0.75rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.rbc-agenda-time-cell {
  color: #a5b4fc;
}

.rbc-agenda-event-cell {
  color: white;
}

/* Overlay (for event popup) */
.rbc-overlay {
  background-color: rgba(17, 24, 39, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  padding: 0.5rem;
}

.rbc-overlay-header {
  color: #a5b4fc;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 0.5rem;
}
