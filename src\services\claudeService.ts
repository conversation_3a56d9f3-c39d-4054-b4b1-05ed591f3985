import Anthropic from '@anthropic-ai/sdk';

// Define the interface for the inspection report data
import { InspectionReportData } from './inspectionReportService';

// Rate limiting variables
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 3000; // 3 seconds between requests

// Simple in-memory cache for reports
interface CacheEntry {
  timestamp: number;
  report: string;
}

const reportCache = new Map<string, CacheEntry>();
const CACHE_TTL = 1000 * 60 * 60; // 1 hour in milliseconds

// Helper function to create a cache key from inspection data
const createCacheKey = (inspectionData: InspectionReportData): string => {
  return JSON.stringify(inspectionData);
};

// Helper function to implement delay
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Function to analyze images using <PERSON>'s vision capabilities
export const analyzeImagesWithClaude = async (
  apiKey: string,
  images: string[]
): Promise<string> => {
  if (!images || images.length === 0) {
    return '';
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  // Update last request time
  lastRequestTime = Date.now();

  try {
    // Initialize Anthropic client
    const anthropic = new Anthropic({
      apiKey: apiKey || '************************************************************************************************************',
      dangerouslyAllowBrowser: true, // Allow running in browser environment
    });

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional property inspector analyzing images of a property.
    Identify any visible issues, defects, or concerns in the provided images that would be relevant for a property inspection report.
    Focus on structural issues, water damage, electrical problems, HVAC issues, plumbing problems, safety hazards, and other significant defects.

    For each issue identified, provide the following information in a structured format:
    1. Item/Area: The specific item or area visible in the photo (e.g., "Bathroom Ceiling", "Kitchen Sink", "Basement Wall")
    2. Issue: A clear description of the problem
    3. Analysis: The potential implications or risks, including severity
    4. Recommendation: Specific actions to address the issue

    Format your response as a structured list where each issue is clearly separated and includes all four elements above. This will be used to create a table in the final report.`;

    // Prepare the message content
    let messageContent = "Please analyze these property images and identify any issues or defects. For each issue, provide the Item/Area, Issue description, Analysis, and Recommendation in a structured format that can be easily converted to a table:";

    // Add images to the message content
    const imageContents = images.map(image => {
      // Determine the media type from the data URL
      const mediaType = image.match(/^data:image\/(jpeg|png|gif|webp);base64,/)?.[1] || 'jpeg';

      return {
        type: "image" as const,
        source: {
          type: "base64" as const,
          media_type: `image/${mediaType}` as "image/jpeg" | "image/png" | "image/gif" | "image/webp",
          data: image.replace(/^data:image\/\w+;base64,/, "")
        }
      };
    });

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1000,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: messageContent },
            ...imageContents
          ]
        }
      ]
    });

    // Extract the analysis
    const content = response.content[0];
    if (content.type === 'text') {
      return content.text;
    } else {
      console.error('Unexpected response format from Claude');
      return 'Error: Unexpected response format from Claude';
    }

  } catch (error) {
    console.error('Error analyzing images with Claude:', error);
    throw new Error('Failed to analyze images with Claude. Please try again.');
  }
};

// Create a function to generate an inspection report using Claude
export const generateInspectionReportWithClaude = async (
  apiKey: string,
  inspectionData: InspectionReportData
): Promise<string> => {
  // Check cache first
  const cacheKey = createCacheKey(inspectionData);
  const cachedEntry = reportCache.get(cacheKey);

  if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_TTL) {
    console.log('Using cached inspection report');
    return cachedEntry.report;
  }

  // Implement rate limiting
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await delay(waitTime);
  }

  // Update last request time
  lastRequestTime = Date.now();

  try {
    // Initialize Anthropic client
    const anthropic = new Anthropic({
      apiKey: apiKey || '************************************************************************************************************',
      dangerouslyAllowBrowser: true, // Allow running in browser environment
    });

    // Create the system message with instructions for the AI
    const systemMessage = `You are a professional property inspector. Create a detailed inspection report based on the provided information.
    The report should include:

    1. A professional header with property details
    2. A summary of findings
    3. Detailed analysis of issues found - THIS MUST BE FORMATTED AS A MARKDOWN TABLE with the following columns:
       - Item: The specific item or area with the issue
       - Issue: A clear description of the problem
       - Analysis: Your professional analysis of the issue, its severity, and potential implications
       - Recommendation: Specific actions to address the issue
    4. If image analysis is provided, include a section titled "Issues Identified from Photos" with the findings
       - THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns as above:
       - Item: The specific item or area visible in the photo
       - Issue: A clear description of the problem identified in the photo
       - Analysis: Your professional analysis of the issue, its severity, and potential implications
       - Recommendation: Specific actions to address the issue
    5. A conclusion

    Format the report using Markdown with appropriate headers, bullet points, and sections. BOTH the detailed analysis section AND the issues identified from photos section MUST be formatted as markdown tables with the columns specified above. Be specific, professional, and thorough.`;

    // Create the user message with the inspection details
    const userMessage = `Property Inspection Details:
    - Property Address: ${inspectionData.propertyAddress}
    - Inspection Date: ${new Date(inspectionData.inspectionDate).toLocaleDateString()}
    - Inspector: ${inspectionData.inspectorName}
    - Client: ${inspectionData.clientName}
    - Property Type: ${inspectionData.propertyType || 'Not specified'}
    - Year Built: ${inspectionData.yearBuilt || 'Not specified'}
    - Square Footage: ${inspectionData.squareFootage || 'Not specified'}

    General Notes and Issues Found:
    ${inspectionData.generalNotes}

    ${inspectionData.imageAnalysis ? `
    Image Analysis Results:
    ${inspectionData.imageAnalysis}
    ` : ''}

    Please analyze these notes${inspectionData.imageAnalysis ? ' and image analysis results' : ''}, identify all issues mentioned, and provide professional recommendations for addressing each issue. Format the report professionally with clear sections.

    IMPORTANT: The "Detailed Analysis of Issues" section MUST be formatted as a markdown table with these exact columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.
    ${inspectionData.imageAnalysis ? 'Include a separate section titled "Issues Identified from Photos" that summarizes the findings from the image analysis. THIS SECTION MUST ALSO BE FORMATTED AS A MARKDOWN TABLE with the same columns: Item | Issue | Analysis | Recommendation. Use proper markdown table syntax with headers and dividers.' : ''}`;

    // Make the API request
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1500,
      system: systemMessage,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: userMessage }
          ]
        }
      ]
    });

    // Extract the generated report
    const content = response.content[0];
    let report = '';

    if (content.type === 'text') {
      report = content.text;
    } else {
      console.error('Unexpected response format from Claude');
      report = 'Error: Unexpected response format from Claude';
    }

    // Cache the result
    reportCache.set(cacheKey, {
      timestamp: Date.now(),
      report
    });

    // Return the generated report
    return report;

  } catch (error) {
    console.error('Error generating inspection report with Claude:', error);
    throw new Error('Failed to generate inspection report with Claude. Please try again.');
  }
};
