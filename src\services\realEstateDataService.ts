import axios from 'axios';
import { fetchPropertyDataByAddress, fetchPropertyDataByCoordinates, fetchPropertyDataByPolygon } from './zillowApi';
import { backendUrl } from './api';

// Helper function to calculate distance between two coordinates
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// Helper function to generate polygon coordinates from center point and radius
const generatePolygonFromRadius = (centerLat: number, centerLng: number, radiusMiles: number): number[][] => {
  const points = 8; // 8-sided polygon for good approximation
  const radiusInDegrees = radiusMiles / 69; // Approximate conversion: 1 degree ≈ 69 miles
  const polygon: number[][] = [];

  for (let i = 0; i < points; i++) {
    const angle = (i * 2 * Math.PI) / points;
    const lat = centerLat + radiusInDegrees * Math.cos(angle);
    const lng = centerLng + radiusInDegrees * Math.sin(angle) / Math.cos(centerLat * Math.PI / 180);
    polygon.push([lat, lng]);
  }

  // Close the polygon by adding the first point at the end
  polygon.push(polygon[0]);
  return polygon;
};



// Interface for property details
export interface PropertyDetails {
  address: string;
  city?: string;
  state?: string;
  zipCode?: string;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  lotSize?: number;
  yearBuilt?: number;
  homeType?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor';
  uniqueFeatures?: string[];
  searchRadius?: number; // Search radius in miles
  useSoftFilter?: boolean; // Whether to use soft filtering
}

// Interface for comparable property
export interface ComparableProperty {
  address: string;
  price: number;
  formattedPrice: string;
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  lotSize?: number;
  yearBuilt?: number;
  distanceFromTarget?: string;
  similarityScore?: number;
  zillowLink?: string;
  source: string;
}

// Interface for market trends
export interface MarketTrends {
  averagePricePerSqFt: number;
  formattedAveragePricePerSqFt: string;
  medianSalePrice: number;
  formattedMedianSalePrice: string;
  averageDaysOnMarket: number;
  priceChangeLastYear: number;
  formattedPriceChangeLastYear: string;
  inventoryLevel?: 'Low' | 'Medium' | 'High';
  marketType?: 'Buyer' | 'Seller' | 'Neutral';
  source: string;
}

// Function to format price as a string with commas
const formatPrice = (price: number): string => {
  return '$' + price.toLocaleString('en-US');
};

// Function to get property details from Zillow API
export const getPropertyDetailsFromZillow = async (address: string): Promise<PropertyDetails | null> => {
  try {
    // In a real implementation, this would call the actual Zillow API
    // For now, we'll simulate a response
    console.log(`Getting property details from Zillow for address: ${address}`);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Parse the address to extract components (this is a simplified example)
    const addressParts = address.split(',').map(part => part.trim());
    const street = addressParts[0];
    const cityStateZip = addressParts[1]?.split(' ') || [];
    const zipCode = cityStateZip.pop() || '';
    const state = cityStateZip.pop() || '';
    const city = cityStateZip.join(' ');

    // Return simulated property details
    return {
      address,
      city,
      state,
      zipCode,
      bedrooms: Math.floor(Math.random() * 3) + 2, // 2-4 bedrooms
      bathrooms: Math.floor(Math.random() * 2) + 1.5, // 1.5-3.5 bathrooms
      squareFeet: Math.floor(Math.random() * 1500) + 1000, // 1000-2500 sq ft
      lotSize: Math.floor(Math.random() * 0.25 * 100) / 100 + 0.1, // 0.1-0.35 acres
      yearBuilt: Math.floor(Math.random() * 40) + 1980, // 1980-2020
      condition: ['Excellent', 'Good', 'Fair', 'Poor'][Math.floor(Math.random() * 3)] as 'Excellent' | 'Good' | 'Fair' | 'Poor',
      uniqueFeatures: [
        'Updated Kitchen',
        'Hardwood Floors',
        'Granite Countertops',
        'Stainless Steel Appliances',
        'Open Floor Plan',
        'Fireplace',
        'Deck/Patio',
        'Fenced Yard'
      ].sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 4) + 1)
    };
  } catch (error) {
    console.error('Error getting property details from Zillow:', error);
    return null;
  }
};

// Function to get comparable properties from Zillow API using polygon search
export const getComparablePropertiesFromZillow = async (
  address: string,
  radius: number = 1,
  propertyDetails?: {
    bedrooms?: number;
    bathrooms?: number;
    squareFeet?: number;
    homeType?: string;
  }
): Promise<ComparableProperty[]> => {
  try {
    console.log(`Getting comparable properties from Zillow for address: ${address} within ${radius} miles`);

    // First, get the coordinates for the target address
    const addressParts = address.split(',');
    const citystatezip = addressParts.slice(1).join(',').trim();

    // Get property data for the target address to get coordinates
    const targetProperty = await fetchPropertyDataByAddress(address, citystatezip);

    if (!targetProperty || !targetProperty.latitude || !targetProperty.longitude) {
      console.warn('Could not get coordinates for target address');
      return [];
    }

    // Generate polygon coordinates based on center point and radius
    const polygon = generatePolygonFromRadius(
      targetProperty.latitude,
      targetProperty.longitude,
      radius
    );

    // Prepare search filters based on property details
    const filters: any = {};

    if (propertyDetails?.bedrooms) {
      // Allow ±1 bedroom range
      filters.bedroomsMin = Math.max(1, propertyDetails.bedrooms - 1);
      filters.bedroomsMax = propertyDetails.bedrooms + 1;
    }

    if (propertyDetails?.bathrooms) {
      // Allow ±0.5 bathroom range
      filters.bathroomsMin = Math.max(1, propertyDetails.bathrooms - 0.5);
      filters.bathroomsMax = propertyDetails.bathrooms + 0.5;
    }

    if (propertyDetails?.squareFeet) {
      // Allow ±20% square footage range
      const sqftVariance = propertyDetails.squareFeet * 0.2;
      filters.sqftMin = Math.round(propertyDetails.squareFeet - sqftVariance);
      filters.sqftMax = Math.round(propertyDetails.squareFeet + sqftVariance);
    }

    if (propertyDetails?.homeType) {
      // Map home type to Zillow API format
      if (propertyDetails.homeType === 'SINGLE_FAMILY') {
        filters.homeType = 'Houses';
      } else if (propertyDetails.homeType === 'CONDO') {
        filters.homeType = 'Condos';
      }
      // If homeType doesn't match our mappings, don't include it in filters
    }

    // Only include RecentlySold properties
    filters.status_type = 'RecentlySold';

    console.log('Polygon search filters:', filters);
    console.log('Polygon coordinates:', polygon);

    // Use polygon-based search to find comparable properties
    const polygonProperties = await fetchPropertyDataByPolygon(polygon, filters);

    if (!polygonProperties || !polygonProperties.results) {
      console.warn('No properties found from Zillow polygon API');
      return [];
    }

    // Convert Zillow API response to ComparableProperty format
    const comps: ComparableProperty[] = polygonProperties.results
      .filter((prop: any) => {
        // Filter out the target property itself and properties without essential data
        return prop.address !== address &&
               prop.price &&
               prop.bedrooms &&
               prop.bathrooms &&
               prop.livingArea;
      })
      .slice(0, 8) // Limit to 8 comparable properties
      .map((prop: any) => {
        const distance = calculateDistance(
          targetProperty.latitude,
          targetProperty.longitude,
          prop.latitude || 0,
          prop.longitude || 0
        );

        return {
          address: prop.address || prop.streetAddress || '',
          price: prop.price || prop.zestimate || 0,
          formattedPrice: formatPrice(prop.price || prop.zestimate || 0),
          bedrooms: prop.bedrooms || prop.beds || 0,
          bathrooms: prop.bathrooms || prop.baths || 0,
          squareFeet: prop.livingArea || prop.finishedSqFt || prop.sqft || 0,
          yearBuilt: prop.yearBuilt || null,
          distanceFromTarget: `${distance.toFixed(1)} miles`,
          similarityScore: 85, // Default similarity score, could be calculated based on property features
          zillowLink: prop.url || `https://www.zillow.com/homes/${encodeURIComponent(prop.address || '')}_rb/`,
          source: 'Zillow'
        };
      });

    console.log(`Found ${comps.length} comparable properties from Zillow polygon API`);
    return comps;

  } catch (error) {
    console.error('Error getting comparable properties from Zillow:', error);
    // Return empty array instead of mock data to avoid confusion
    return [];
  }
};

// Function to get comparable properties from Redfin API
export const getComparablePropertiesFromRedfin = async (address: string, radius: number = 1): Promise<ComparableProperty[]> => {
  try {
    // Redfin API integration not implemented yet
    // Return empty array to focus on Zillow data
    console.log(`Getting comparable properties from Redfin for address: ${address} within ${radius} miles`);
    console.log('Redfin API integration not implemented - returning empty array');
    return [];
  } catch (error) {
    console.error('Error getting comparable properties from Redfin:', error);
    return [];
  }
};

// Function to get comparable properties from Realtor.com API
export const getComparablePropertiesFromRealtor = async (address: string, radius: number = 1): Promise<ComparableProperty[]> => {
  try {
    // Realtor.com API integration not implemented yet
    // Return empty array to focus on Zillow data
    console.log(`Getting comparable properties from Realtor.com for address: ${address} within ${radius} miles`);
    console.log('Realtor.com API integration not implemented - returning empty array');
    return [];
  } catch (error) {
    console.error('Error getting comparable properties from Realtor.com:', error);
    return [];
  }
};

// Function to get market trends from Zillow API
export const getMarketTrendsFromZillow = async (zipCode: string): Promise<MarketTrends | null> => {
  try {
    // In a real implementation, this would call the actual Zillow API
    // For now, we'll simulate a response
    console.log(`Getting market trends from Zillow for zip code: ${zipCode}`);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 600));

    // Generate random market trends
    const averagePricePerSqFt = Math.floor(Math.random() * 200) + 150; // $150-$350 per sq ft
    const medianSalePrice = Math.floor(Math.random() * 300000) + 300000; // $300k-$600k
    const averageDaysOnMarket = Math.floor(Math.random() * 30) + 15; // 15-45 days
    const priceChangeLastYear = Math.floor(Math.random() * 10) + 1; // 1-10% increase

    return {
      averagePricePerSqFt,
      formattedAveragePricePerSqFt: formatPrice(averagePricePerSqFt),
      medianSalePrice,
      formattedMedianSalePrice: formatPrice(medianSalePrice),
      averageDaysOnMarket,
      priceChangeLastYear,
      formattedPriceChangeLastYear: `${priceChangeLastYear}%`,
      inventoryLevel: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)] as 'Low' | 'Medium' | 'High',
      marketType: ['Buyer', 'Seller', 'Neutral'][Math.floor(Math.random() * 3)] as 'Buyer' | 'Seller' | 'Neutral',
      source: 'Zillow'
    };
  } catch (error) {
    console.error('Error getting market trends from Zillow:', error);
    return null;
  }
};

// Function to get market trends from Redfin API
export const getMarketTrendsFromRedfin = async (zipCode: string): Promise<MarketTrends | null> => {
  try {
    // In a real implementation, this would call the actual Redfin API
    // For now, we'll simulate a response
    console.log(`Getting market trends from Redfin for zip code: ${zipCode}`);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 550));

    // Generate random market trends
    const averagePricePerSqFt = Math.floor(Math.random() * 200) + 150; // $150-$350 per sq ft
    const medianSalePrice = Math.floor(Math.random() * 300000) + 300000; // $300k-$600k
    const averageDaysOnMarket = Math.floor(Math.random() * 30) + 15; // 15-45 days
    const priceChangeLastYear = Math.floor(Math.random() * 10) + 1; // 1-10% increase

    return {
      averagePricePerSqFt,
      formattedAveragePricePerSqFt: formatPrice(averagePricePerSqFt),
      medianSalePrice,
      formattedMedianSalePrice: formatPrice(medianSalePrice),
      averageDaysOnMarket,
      priceChangeLastYear,
      formattedPriceChangeLastYear: `${priceChangeLastYear}%`,
      inventoryLevel: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)] as 'Low' | 'Medium' | 'High',
      marketType: ['Buyer', 'Seller', 'Neutral'][Math.floor(Math.random() * 3)] as 'Buyer' | 'Seller' | 'Neutral',
      source: 'Redfin'
    };
  } catch (error) {
    console.error('Error getting market trends from Redfin:', error);
    return null;
  }
};

// Function to get all comparable properties from multiple sources
export const getAllComparableProperties = async (address: string, radius: number = 1.5): Promise<ComparableProperty[]> => {
  try {
    console.log(`Getting all comparable properties for address: ${address} within ${radius} miles`);

    // Get comparable properties from all sources in parallel
    const [zillowComps, redfinComps, realtorComps] = await Promise.all([
      getComparablePropertiesFromZillow(address, radius),
      getComparablePropertiesFromRedfin(address, radius),
      getComparablePropertiesFromRealtor(address, radius)
    ]);

    // Combine all comparable properties
    return [...zillowComps, ...redfinComps, ...realtorComps];
  } catch (error) {
    console.error('Error getting all comparable properties:', error);
    return [];
  }
};

// Function to get market trends from multiple sources
export const getAllMarketTrends = async (zipCode: string): Promise<MarketTrends[]> => {
  try {
    // Get market trends from all sources in parallel
    const [zillowTrends, redfinTrends] = await Promise.all([
      getMarketTrendsFromZillow(zipCode),
      getMarketTrendsFromRedfin(zipCode)
    ]);

    // Combine all market trends
    return [zillowTrends, redfinTrends].filter(Boolean) as MarketTrends[];
  } catch (error) {
    console.error('Error getting all market trends:', error);
    return [];
  }
};

// Function to calculate the average of market trends from multiple sources
export const getAverageMarketTrends = async (zipCode: string): Promise<MarketTrends | null> => {
  try {
    const allTrends = await getAllMarketTrends(zipCode);

    if (allTrends.length === 0) {
      return null;
    }

    // Calculate average values
    const averagePricePerSqFt = allTrends.reduce((sum, trend) => sum + trend.averagePricePerSqFt, 0) / allTrends.length;
    const medianSalePrice = allTrends.reduce((sum, trend) => sum + trend.medianSalePrice, 0) / allTrends.length;
    const averageDaysOnMarket = allTrends.reduce((sum, trend) => sum + trend.averageDaysOnMarket, 0) / allTrends.length;
    const priceChangeLastYear = allTrends.reduce((sum, trend) => sum + trend.priceChangeLastYear, 0) / allTrends.length;

    // Determine inventory level and market type based on majority
    const inventoryLevelCounts = allTrends.reduce((counts, trend) => {
      if (trend.inventoryLevel) {
        counts[trend.inventoryLevel] = (counts[trend.inventoryLevel] || 0) + 1;
      }
      return counts;
    }, {} as Record<string, number>);

    const marketTypeCounts = allTrends.reduce((counts, trend) => {
      if (trend.marketType) {
        counts[trend.marketType] = (counts[trend.marketType] || 0) + 1;
      }
      return counts;
    }, {} as Record<string, number>);

    const inventoryLevel = Object.entries(inventoryLevelCounts).sort((a, b) => b[1] - a[1])[0]?.[0] as 'Low' | 'Medium' | 'High';
    const marketType = Object.entries(marketTypeCounts).sort((a, b) => b[1] - a[1])[0]?.[0] as 'Buyer' | 'Seller' | 'Neutral';

    return {
      averagePricePerSqFt,
      formattedAveragePricePerSqFt: formatPrice(Math.round(averagePricePerSqFt)),
      medianSalePrice,
      formattedMedianSalePrice: formatPrice(Math.round(medianSalePrice)),
      averageDaysOnMarket: Math.round(averageDaysOnMarket),
      priceChangeLastYear,
      formattedPriceChangeLastYear: `${priceChangeLastYear.toFixed(1)}%`,
      inventoryLevel,
      marketType,
      source: 'Combined Sources'
    };
  } catch (error) {
    console.error('Error getting average market trends:', error);
    return null;
  }
};

// Function to calculate a recommended price based on comparable properties
export const calculateRecommendedPrice = (
  propertyDetails: PropertyDetails,
  comparableProperties: ComparableProperty[],
  marketTrends: MarketTrends | null
): { recommendedPrice: number; priceRange: [number, number]; confidenceScore: number } => {
  // If no comparable properties, return a default value
  if (comparableProperties.length === 0) {
    return {
      recommendedPrice: 0,
      priceRange: [0, 0],
      confidenceScore: 0
    };
  }

  // Calculate the average price per square foot from comparable properties
  const totalPricePerSqFt = comparableProperties.reduce((sum, comp) => {
    return sum + (comp.price / comp.squareFeet);
  }, 0);
  const avgPricePerSqFt = totalPricePerSqFt / comparableProperties.length;

  // Calculate the base price based on the subject property's square footage
  let basePrice = (propertyDetails.squareFeet || 0) * avgPricePerSqFt;

  // Apply adjustments based on property attributes
  // Bedrooms adjustment
  if (propertyDetails.bedrooms) {
    const avgBedrooms = comparableProperties.reduce((sum, comp) => sum + comp.bedrooms, 0) / comparableProperties.length;
    const bedroomAdjustment = (propertyDetails.bedrooms - avgBedrooms) * 10000; // $10k per bedroom difference
    basePrice += bedroomAdjustment;
  }

  // Bathrooms adjustment
  if (propertyDetails.bathrooms) {
    const avgBathrooms = comparableProperties.reduce((sum, comp) => sum + comp.bathrooms, 0) / comparableProperties.length;
    const bathroomAdjustment = (propertyDetails.bathrooms - avgBathrooms) * 5000; // $5k per bathroom difference
    basePrice += bathroomAdjustment;
  }

  // Year built adjustment
  if (propertyDetails.yearBuilt) {
    const avgYearBuilt = comparableProperties.reduce((sum, comp) => sum + (comp.yearBuilt || 0), 0) /
                         comparableProperties.filter(comp => comp.yearBuilt).length;
    const yearBuiltAdjustment = (propertyDetails.yearBuilt - avgYearBuilt) * 500; // $500 per year difference
    basePrice += yearBuiltAdjustment;
  }

  // Condition adjustment
  if (propertyDetails.condition) {
    const conditionValues = {
      'Excellent': 3,
      'Good': 2,
      'Fair': 1,
      'Poor': 0
    };
    const conditionValue = conditionValues[propertyDetails.condition];
    const conditionAdjustment = conditionValue * 15000; // Up to $45k for excellent condition
    basePrice += conditionAdjustment;
  }

  // Market trends adjustment
  if (marketTrends) {
    // Adjust based on price change last year
    const marketTrendAdjustment = basePrice * (marketTrends.priceChangeLastYear / 100);
    basePrice += marketTrendAdjustment;

    // Adjust based on market type
    if (marketTrends.marketType === 'Seller') {
      basePrice *= 1.02; // 2% premium in a seller's market
    } else if (marketTrends.marketType === 'Buyer') {
      basePrice *= 0.98; // 2% discount in a buyer's market
    }
  }

  // Round to nearest $1,000
  const recommendedPrice = Math.round(basePrice / 1000) * 1000;

  // Calculate price range (±5%)
  const priceRange: [number, number] = [
    Math.round((recommendedPrice * 0.95) / 1000) * 1000,
    Math.round((recommendedPrice * 1.05) / 1000) * 1000
  ];

  // Calculate confidence score based on number and similarity of comps
  const avgSimilarity = comparableProperties.reduce((sum, comp) => sum + (comp.similarityScore || 0), 0) /
                        comparableProperties.filter(comp => comp.similarityScore).length;

  // Confidence score factors:
  // 1. Number of comps (more is better)
  // 2. Average similarity score (higher is better)
  // 3. Recency of comps (not implemented in this mock)
  // 4. Consistency of prices (lower standard deviation is better)

  const numCompsScore = Math.min(comparableProperties.length / 10, 1) * 40; // Up to 40 points for number of comps
  const similarityScore = (avgSimilarity / 100) * 40; // Up to 40 points for similarity

  // Calculate price consistency (standard deviation relative to mean)
  const avgPrice = comparableProperties.reduce((sum, comp) => sum + comp.price, 0) / comparableProperties.length;
  const priceVariance = comparableProperties.reduce((sum, comp) => sum + Math.pow(comp.price - avgPrice, 2), 0) / comparableProperties.length;
  const priceStdDev = Math.sqrt(priceVariance);
  const priceConsistencyScore = Math.max(0, 20 - ((priceStdDev / avgPrice) * 100)); // Up to 20 points for consistency

  // Final confidence score (0-100)
  const confidenceScore = Math.min(Math.round(numCompsScore + similarityScore + priceConsistencyScore), 100);

  return {
    recommendedPrice,
    priceRange,
    confidenceScore
  };
};
