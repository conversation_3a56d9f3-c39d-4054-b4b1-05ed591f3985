import express from 'express';
import { PDFDocument } from 'pdf-lib';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import OpenAI from 'openai';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Create a rate limiter: max 10 requests per hour per IP
const contractLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour
  message: {
    status: 'error',
    message: 'Too many contract generation requests from this IP. Please try again after an hour.'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize OpenAI API
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Path to the contract template PDF
const CONTRACT_TEMPLATE_PATH = path.join(__dirname, '../../contract-template.pdf.pdf');

// Path to save generated PDFs
const GENERATED_PDFS_DIR = path.join(__dirname, '../../public/generated-contracts');

// Ensure the directory exists
if (!fs.existsSync(GENERATED_PDFS_DIR)) {
  fs.mkdirSync(GENERATED_PDFS_DIR, { recursive: true });
}

/**
 * Use AI to infer missing contract details
 * @param {Object} formData - The form data from the user
 * @param {string} aiProvider - The AI provider to use ('openai' or 'claude')
 */
async function inferMissingDetails(formData, aiProvider = 'openai') {
  try {
    const prompt = `
You are a real estate contract assistant. Based on the following user-provided details, suggest inferred values for missing fields such as earnest money, typical closing dates, HOA info, or included fixtures. Use Chicago real estate norms where applicable.

User Data:
${JSON.stringify(formData, null, 2)}

Response Format:
{
  "earnestMoney": number,
  "closingDate": "YYYY-MM-DD",
  "hoa": boolean,
  "monthlyFees": number,
  "fixturesIncluded": ["Fixture1", "Fixture2", ...]
}
`;

    let inferredData = {};

    // Use the selected AI provider
    if (aiProvider === 'claude') {
      console.log('Using Claude API for inference');

      // In a real implementation, this would use the Anthropic Claude API
      // For now, we'll simulate the response

      // Simulate Claude's response
      inferredData = {
        earnestMoney: formData.financials.purchasePrice ? Math.round(parseFloat(formData.financials.purchasePrice) * 0.05) : 20000,
        closingDate: (() => {
          const today = new Date();
          const futureDate = new Date(today);
          futureDate.setDate(today.getDate() + 45);
          return futureDate.toISOString().split('T')[0];
        })(),
        hoa: true,
        monthlyFees: 350,
        fixturesIncluded: ['Refrigerator', 'Oven/Range', 'Dishwasher', 'Microwave', 'Window Treatments', 'Light Fixtures']
      };

      // In a real implementation, you would use the Anthropic Claude API:
      /*
      const anthropic = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY,
      });

      const response = await anthropic.completions.create({
        model: "claude-2",
        prompt: `${prompt}\n\nAssistant:`,
        max_tokens_to_sample: 500,
        temperature: 0.7,
      });

      inferredData = JSON.parse(response.completion.trim());
      */
    } else {
      // Default to OpenAI
      console.log('Using OpenAI API for inference');

      const response = await openai.completions.create({
        model: "gpt-4-turbo",
        prompt,
        max_tokens: 500,
        temperature: 0.7,
      });

      inferredData = JSON.parse(response.choices[0].text.trim());
    }

    console.log('AI-inferred data:', inferredData);
    return inferredData;
  } catch (error) {
    console.error('Error inferring missing details:', error);
    return {};
  }
}

/**
 * Fill PDF form fields with user data
 */
async function fillPdfForm(formData) {
  try {
    // Read the PDF template
    const pdfBytes = fs.readFileSync(CONTRACT_TEMPLATE_PATH);

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Get the form from the PDF
    const form = pdfDoc.getForm();

    // Map form data to PDF fields
    // Note: These field names should match the actual field names in the PDF

    // Track which fields were successfully filled
    const filledFields = new Set();

    // Buyer Information
    const buyerField = form.getTextField('Buyer_Name');
    if (buyerField) {
      buyerField.setText(formData.buyer.fullName || '');
      filledFields.add('Buyer_Name');
    }

    // Buyer Email
    const buyerEmailField = form.getTextField('Buyer_Email');
    if (buyerEmailField) {
      buyerEmailField.setText(formData.buyer.email || '');
      filledFields.add('Buyer_Email');
    }

    // Seller Information
    const sellerField = form.getTextField('Seller_Name');
    if (sellerField) {
      sellerField.setText(formData.seller.fullName || 'To Be Determined');
      filledFields.add('Seller_Name');
    }

    // Property Information
    const addressField = form.getTextField('Property_Address');
    if (addressField) {
      addressField.setText(formData.property.address || '');
      filledFields.add('Property_Address');
    }

    const unitField = form.getTextField('Property_Unit');
    if (unitField) {
      unitField.setText(formData.property.unit || '');
      filledFields.add('Property_Unit');
    }

    // Parking Information
    const parkingField = form.getCheckBox('Parking_Included');
    if (parkingField) {
      if (formData.property.parkingIncluded) {
        parkingField.check();
      } else {
        parkingField.uncheck();
      }
      filledFields.add('Parking_Included');
    }

    const parkingDetailsField = form.getTextField('Parking_Details');
    if (parkingDetailsField) {
      parkingDetailsField.setText(formData.property.parkingIncluded ? (formData.property.parkingDetails || '') : '');
      filledFields.add('Parking_Details');
    }

    // Financial Information
    const priceField = form.getTextField('Purchase_Price');
    if (priceField) {
      const formattedPrice = formData.financials.purchasePrice
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(formData.financials.purchasePrice)
        : '';
      priceField.setText(formattedPrice);
      filledFields.add('Purchase_Price');
    }

    const earnestMoneyField = form.getTextField('Earnest_Money');
    if (earnestMoneyField) {
      const formattedEarnestMoney = formData.financials.earnestMoney
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(formData.financials.earnestMoney)
        : '';
      earnestMoneyField.setText(formattedEarnestMoney);
      filledFields.add('Earnest_Money');
    }

    const closingDateField = form.getTextField('Closing_Date');
    if (closingDateField) {
      if (formData.financials.closingDate) {
        const date = new Date(formData.financials.closingDate);
        const formattedDate = date.toLocaleDateString('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric'
        });
        closingDateField.setText(formattedDate);
      } else {
        closingDateField.setText('');
      }
      filledFields.add('Closing_Date');
    }

    // Contract Date (Today's Date)
    const contractDateField = form.getTextField('Contract_Date');
    if (contractDateField) {
      const today = new Date();
      const formattedToday = today.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
      contractDateField.setText(formattedToday);
      filledFields.add('Contract_Date');
    }

    // HOA Information
    const hoaField = form.getCheckBox('HOA_Exists');
    if (hoaField) {
      if (formData.associations.hoa) {
        hoaField.check();
      } else {
        hoaField.uncheck();
      }
      filledFields.add('HOA_Exists');
    }

    const feesField = form.getTextField('HOA_Fees');
    if (feesField) {
      const formattedFees = formData.associations.hoa && formData.associations.monthlyFees
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(formData.associations.monthlyFees)
        : '';
      feesField.setText(formattedFees);
      filledFields.add('HOA_Fees');
    }

    // Fixtures
    const fixturesField = form.getTextField('Fixtures_Included');
    if (fixturesField) {
      const fixturesText = formData.fixturesIncluded && formData.fixturesIncluded.length > 0
        ? formData.fixturesIncluded.join(', ')
        : 'Standard appliances';
      fixturesField.setText(fixturesText);
      filledFields.add('Fixtures_Included');
    }

    // Additional Terms
    const termsField = form.getTextField('Additional_Terms');
    if (termsField) {
      termsField.setText(formData.additionalTerms || '');
      filledFields.add('Additional_Terms');
    }

    // Log any fields that weren't filled
    console.log('Checking for empty fields in the PDF form...');
    const allFields = form.getFields();
    allFields.forEach(field => {
      const fieldName = field.getName();

      // Skip fields that were already filled
      if (filledFields.has(fieldName)) {
        return;
      }

      // Check if the field is a text field and is empty
      if (field.constructor.name === 'PDFTextField') {
        const value = field.getText();
        if (!value) {
          console.log(`Warning: Field ${fieldName} is empty`);
        }
      }
      // Check if the field is a checkbox
      else if (field.constructor.name === 'PDFCheckBox') {
        console.log(`Warning: Checkbox ${fieldName} was not explicitly set`);
      }
    });

    // Flatten the form (makes it non-editable)
    form.flatten();

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save();

    // Generate a descriptive filename
    const timestamp = Date.now();
    const buyerName = formData.buyer.fullName ? formData.buyer.fullName.replace(/\s+/g, '_') : 'Unnamed';
    const closingDateStr = formData.financials.closingDate
      ? new Date(formData.financials.closingDate).toISOString().split('T')[0]
      : 'NoDate';
    const filename = `Contract_${buyerName}_${closingDateStr}_${timestamp}.pdf`;
    const filePath = path.join(GENERATED_PDFS_DIR, filename);

    // Write the PDF to disk
    fs.writeFileSync(filePath, filledPdfBytes);

    return `/generated-contracts/${filename}`;
  } catch (error) {
    console.error('Error filling PDF form:', error);
    throw error;
  }
}

/**
 * POST /api/contract-generator
 * Generate a contract based on user input
 * Rate limited to 10 requests per hour per IP
 */
router.post('/', contractLimiter, async (req, res) => {
  try {
    let formData = req.body;

    // Extract the AI provider from the request (default to OpenAI if not provided)
    const aiProvider = formData.aiProvider || 'openai';
    delete formData.aiProvider; // Remove it from the form data

    console.log(`Using ${aiProvider} for contract generation`);

    // Infer missing details using the selected AI provider
    const inferredData = await inferMissingDetails(formData, aiProvider);

    // Merge inferred data with user data (only for missing fields)
    if (!formData.financials.earnestMoney && inferredData.earnestMoney) {
      formData.financials.earnestMoney = inferredData.earnestMoney;
    }

    if (!formData.financials.closingDate && inferredData.closingDate) {
      formData.financials.closingDate = inferredData.closingDate;
    }

    if (!formData.associations.hoa && inferredData.hoa !== undefined) {
      formData.associations.hoa = inferredData.hoa;
    }

    if (!formData.associations.monthlyFees && inferredData.monthlyFees) {
      formData.associations.monthlyFees = inferredData.monthlyFees;
    }

    if (formData.fixturesIncluded.length === 0 && inferredData.fixturesIncluded) {
      formData.fixturesIncluded = inferredData.fixturesIncluded;
    }

    // Fill the PDF form with the data
    const pdfUrl = await fillPdfForm(formData);

    // Return the URL to the generated PDF
    res.json({
      status: 'success',
      message: 'Contract generated successfully.',
      downloadUrl: pdfUrl,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating contract:', error);
    res.status(500).json({
      status: 'error',
      message: error.message || 'Failed to generate contract'
    });
  }
});

export default router;
