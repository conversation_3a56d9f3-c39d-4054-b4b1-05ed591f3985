import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaCalendarAlt, FaArrowLeft, FaSpinner, FaUser, FaHome, FaPhone, FaEnvelope, FaClock, FaCheck, FaGoogle, FaExclamationTriangle, FaDownload, FaCalendarPlus, FaCommentAlt, FaMagic, FaList, FaEdit, FaRobot } from 'react-icons/fa';
import { extractAppointmentDataWithClaude } from '../services/claudeBackendService';
import { useApp } from '../context/SimpleAppContext';
import Calendar from 'react-calendar';
import axios from 'axios';
import 'react-calendar/dist/Calendar.css';
import '../calendar-styles.css';
import appointmentService from '../services/appointmentService';
import { CreateAppointmentData } from '../models/Appointment';

// Define the interface for the form data
interface AppointmentFormData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  propertyAddress: string;
  propertyType: string;
  preferredDate: string;
  preferredTime: string;
  additionalNotes: string;
}

// Define the interface for the scheduled appointment
interface ScheduledAppointment {
  id: string;
  clientName: string;
  clientEmail: string;
  date: string;
  time: string;
  propertyAddress: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes: string;
  googleCalendarEventId?: string;
}

// Define the interface for the AI response
interface AppointmentResponse {
  suggestedSlots: Array<{
    date: string;
    slots: string[];
  }>;
  preparationTips: string[];
  followUpReminder: string;
}

const AppointmentScheduler = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const [isLoading, setIsLoading] = useState(false);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [aiResponse, setAiResponse] = useState<AppointmentResponse | null>(null);
  const [scheduledAppointment, setScheduledAppointment] = useState<ScheduledAppointment | null>(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Calendar integration states
  const [isAddingToCalendar, setIsAddingToCalendar] = useState(false);
  const [calendarError, setCalendarError] = useState('');
  const [calendarLinkGenerated, setCalendarLinkGenerated] = useState(false);
  const [calendarLink, setCalendarLink] = useState('');

  // Natural language input states
  const [naturalLanguageInput, setNaturalLanguageInput] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionError, setExtractionError] = useState('');
  const [extractionSuccess, setExtractionSuccess] = useState(false);
  const [inputMode, setInputMode] = useState<'natural' | 'form'>('natural'); // Default to natural language input
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);

  // Initialize form data
  const [formData, setFormData] = useState<AppointmentFormData>({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    propertyAddress: '',
    propertyType: 'House',
    preferredDate: '',
    preferredTime: '',
    additionalNotes: ''
  });

  // LLM provider state
  const [llmProvider, setLlmProvider] = useState<'openai' | 'claude'>('openai'); // Default to OpenAI

  // Available time slots
  const timeSlots = [
    '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM',
    '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM'
  ];

  // Generate Google Calendar link
  const generateGoogleCalendarLink = () => {
    if (!scheduledAppointment) {
      setCalendarError('No appointment to add to calendar');
      return;
    }

    try {
      setIsAddingToCalendar(true);
      setCalendarError('');

      // Parse date and time
      const { date, time, clientName, propertyAddress, notes } = scheduledAppointment;

      // Validate required data
      if (!date || !time) {
        throw new Error('Date and time are required for calendar integration');
      }

      // Format date for Google Calendar URL
      // Example date format: "June 15, 2023"
      let year, month, day, formattedMonth, formattedDay;

      try {
        // Try to parse the date using regex
        const dateMatch = date.match(/([A-Za-z]+)\s+(\d+),\s+(\d+)/);

        if (dateMatch) {
          [, month, day, year] = dateMatch;
          const monthIndex = new Date(`${month} 1, 2000`).getMonth() + 1; // 1-based month
          formattedDay = day.padStart(2, '0');
          formattedMonth = String(monthIndex).padStart(2, '0');
        } else {
          // Try to parse as a Date object
          const dateObj = new Date(date);
          if (!isNaN(dateObj.getTime())) {
            year = String(dateObj.getFullYear());
            formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
            formattedDay = String(dateObj.getDate()).padStart(2, '0');
          } else {
            throw new Error(`Could not parse date: ${date}`);
          }
        }
      } catch (error) {
        console.error('Error parsing date:', error);
        // Use current date as fallback
        const today = new Date();
        year = String(today.getFullYear());
        formattedMonth = String(today.getMonth() + 1).padStart(2, '0');
        formattedDay = String(today.getDate()).padStart(2, '0');
      }

      // Format time for Google Calendar URL
      // Example time format: "10:00 AM"
      let hour, minute, formattedHour, formattedMinute;

      try {
        const timeMatch = time.match(/(\d+):(\d+)\s+([AP]M)/);

        if (timeMatch) {
          const [, hourStr, minuteStr, period] = timeMatch;
          hour = parseInt(hourStr);
          minute = parseInt(minuteStr);

          // Convert to 24-hour format
          if (period === 'PM' && hour < 12) {
            hour += 12;
          } else if (period === 'AM' && hour === 12) {
            hour = 0;
          }
        } else {
          // Try to parse using convertTimeToMinutes function
          const totalMinutes = convertTimeToMinutes(time);
          if (totalMinutes !== -1) {
            hour = Math.floor(totalMinutes / 60);
            minute = totalMinutes % 60;
          } else {
            throw new Error(`Could not parse time: ${time}`);
          }
        }

        formattedHour = String(hour).padStart(2, '0');
        formattedMinute = String(minute).padStart(2, '0');
      } catch (error) {
        console.error('Error parsing time:', error);
        // Use current time as fallback
        const now = new Date();
        hour = now.getHours();
        minute = now.getMinutes();
        formattedHour = String(hour).padStart(2, '0');
        formattedMinute = String(minute).padStart(2, '0');
      }

      // Create start and end dates (1 hour appointment)
      const startDate = `${year}${formattedMonth}${formattedDay}T${formattedHour}${formattedMinute}00`;

      // End time is 1 hour later
      let endHour = hour + 1;
      if (endHour >= 24) {
        endHour = 0;
      }
      const formattedEndHour = String(endHour).padStart(2, '0');
      const endDate = `${year}${formattedMonth}${formattedDay}T${formattedEndHour}${formattedMinute}00`;

      // Create event details
      const eventTitle = encodeURIComponent(`Property Viewing: ${propertyAddress}`);
      const eventLocation = encodeURIComponent(propertyAddress);
      const eventDescription = encodeURIComponent(
        `Appointment with ${clientName}\n` +
        `Property Address: ${propertyAddress}\n` +
        `Status: ${scheduledAppointment.status}\n` +
        `Appointment ID: ${scheduledAppointment.id}\n` +
        (notes ? `Notes: ${notes}` : '')
      );

      // Generate Google Calendar link
      const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${eventTitle}&dates=${startDate}/${endDate}&details=${eventDescription}&location=${eventLocation}&sf=true&output=xml`;

      // Set the calendar link
      setCalendarLink(googleCalendarUrl);
      setCalendarLinkGenerated(true);

      console.log('Generated Google Calendar link:', googleCalendarUrl);

      // Update the scheduled appointment
      const updatedAppointment = {
        ...scheduledAppointment,
        googleCalendarEventId: 'link-generated' // Just a marker to show it's been processed
      };

      setScheduledAppointment(updatedAppointment);
    } catch (error: any) {
      console.error('Error generating Google Calendar link:', error);
      setCalendarError(error.message || 'Failed to generate Google Calendar link');
    } finally {
      setIsAddingToCalendar(false);
    }
  };

  // Helper function to check for relative dates in the input
  const checkForRelativeDates = (input: string): { hasRelativeDate: boolean, dateType?: string } => {
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('today')) {
      return { hasRelativeDate: true, dateType: 'today' };
    } else if (lowerInput.includes('tomorrow')) {
      return { hasRelativeDate: true, dateType: 'tomorrow' };
    } else if (lowerInput.includes('day after tomorrow')) {
      return { hasRelativeDate: true, dateType: 'day_after_tomorrow' };
    } else if (lowerInput.match(/next\s+(mon|tues|wednes|thurs|fri|satur|sun)day/)) {
      return { hasRelativeDate: true, dateType: 'next_weekday' };
    }

    return { hasRelativeDate: false };
  };

  // Extract appointment data from natural language using selected LLM
  const extractAppointmentData = async () => {
    if (!naturalLanguageInput.trim()) {
      setExtractionError('Please enter a description of the appointment');
      return;
    }

    setIsExtracting(true);
    setExtractionError('');
    setExtractionSuccess(false);

    // Check if the input contains relative dates
    const relativeDateCheck = checkForRelativeDates(naturalLanguageInput);
    console.log('Relative date check:', relativeDateCheck);

    // Log which LLM is being used in development mode
    if (import.meta.env.DEV) {
      console.log(`%c[DEV] Using ${llmProvider === 'openai' ? 'OpenAI' : 'Claude'} for appointment data extraction`,
        `color: ${llmProvider === 'openai' ? '#10a37f' : '#a27aff'}; font-weight: bold`);
    }

    try {
      let args;

      if (llmProvider === 'openai') {
        // Get OpenAI API key from environment variable
        let apiKey = import.meta.env.VITE_OPENAI_API_KEY;

        // If not available, try to get it from window.__ENV__
        if (!apiKey && typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_OPENAI_API_KEY) {
          apiKey = window.__ENV__.VITE_OPENAI_API_KEY;
        }

        // For development/testing, you can hardcode the key here (but remove before committing)
        if (!apiKey) {
          apiKey = '********************************************************************************************************************************************************************';
        }

        if (!apiKey) {
          throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
        }

        // Define the function schema for scheduling appointments
        const functionSchema = {
          name: 'scheduleAppointment',
          description: 'Schedule a real estate appointment with a client',
          parameters: {
            type: 'object',
            properties: {
              appointmentId: {
                type: 'string',
                description: 'Unique ID'
              },
              clientName: {
                type: 'string'
              },
              clientEmail: {
                type: 'string'
              },
              date: {
                type: 'string'
              },
              time: {
                type: 'string'
              },
              propertyAddress: {
                type: 'string'
              },
              status: {
                type: 'string',
                enum: ['confirmed', 'pending', 'cancelled']
              },
              notes: {
                type: 'string'
              }
            },
            required: ['appointmentId', 'clientName', 'clientEmail', 'date', 'time', 'propertyAddress', 'status']
          }
        };

        // Create the system message with instructions for the AI
        const systemMessage = `You are an AI assistant for real estate agents. Your task is to extract appointment details from the user's natural language input. Use the scheduleAppointment function to structure the appointment data.

For the time parameter, please format it as a standard time with AM/PM indicator (e.g., "10:00 AM", "2:30 PM").

For the date parameter, when the user mentions relative dates like "today", "tomorrow", or "next Monday", calculate the actual calendar date based on the current date (${new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}). Always provide a full date with month, day, and year (e.g., "June 15, 2023").

If the user doesn't specify a complete detail, make a reasonable assumption based on context. For example, if they only mention "tomorrow at 3", calculate the actual date for tomorrow (${new Date(Date.now() + 86400000).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}) and assume "3:00 PM" for the time.`;

        // Make the API request to OpenAI using axios
        const response = await axios.post(
          'https://api.openai.com/v1/chat/completions',
          {
            model: 'gpt-4o',
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'user', content: naturalLanguageInput }
            ],
            functions: [functionSchema],
            function_call: { name: 'scheduleAppointment' },
            temperature: 0.7,
            max_tokens: 800
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`,
              'OpenAI-Beta': 'assistants=v1'
            }
          }
        );

        console.log('OpenAI API response status:', response.status);
        console.log('OpenAI API response data:', response.data);

        // Extract the function call arguments
        const functionCall = response.data.choices[0].message.function_call;

        if (functionCall && functionCall.name === 'scheduleAppointment') {
          try {
            args = JSON.parse(functionCall.arguments);
          } catch (parseError) {
            console.error('Error parsing function call arguments:', parseError);
            throw new Error('Failed to parse appointment data from OpenAI');
          }
        } else {
          throw new Error('Function call not returned by the OpenAI API');
        }
      } else {
        // Use Claude via backend API
        try {
          args = await extractAppointmentDataWithClaude(naturalLanguageInput);
          console.log('Claude API response data:', args);
        } catch (claudeError) {
          console.error('Error with Claude API:', claudeError);
          throw new Error(`Claude API error: ${claudeError instanceof Error ? claudeError.message : 'Unknown error'}`);
        }
      }

      // Continue with the extracted data
      console.log('Extracted appointment data:', args);

      // Format the date if it's in a different format
      let formattedDate = args.date;
      if (args.date) {
        try {
          console.log('Original date from OpenAI:', args.date);

          // Handle relative date terms directly
          const lowerDate = args.date.toLowerCase();
          const today = new Date();
          let dateObj: Date;

          if (lowerDate.includes('today')) {
            console.log('Detected "today" in date string');
            dateObj = new Date(); // Today
          } else if (lowerDate.includes('tomorrow')) {
            console.log('Detected "tomorrow" in date string');
            dateObj = new Date(today.getTime() + 86400000); // Tomorrow (today + 1 day in ms)
          } else if (lowerDate.includes('day after tomorrow')) {
            console.log('Detected "day after tomorrow" in date string');
            dateObj = new Date(today.getTime() + 172800000); // Day after tomorrow (today + 2 days in ms)
          } else if (lowerDate.match(/next\s+(mon|tues|wednes|thurs|fri|satur|sun)day/)) {
            console.log('Detected "next [day of week]" in date string');
            // Calculate next occurrence of the specified day
            const dayMatch = lowerDate.match(/next\s+(mon|tues|wednes|thurs|fri|satur|sun)day/);
            if (dayMatch) {
              const dayPrefix = dayMatch[1];
              let targetDay: number;

              switch (dayPrefix) {
                case 'mon': targetDay = 1; break;
                case 'tues': targetDay = 2; break;
                case 'wednes': targetDay = 3; break;
                case 'thurs': targetDay = 4; break;
                case 'fri': targetDay = 5; break;
                case 'satur': targetDay = 6; break;
                case 'sun': targetDay = 0; break;
                default: targetDay = -1;
              }

              if (targetDay >= 0) {
                dateObj = new Date();
                const currentDay = dateObj.getDay();
                const daysToAdd = (targetDay + 7 - currentDay) % 7 || 7; // If today, then next week
                dateObj.setDate(dateObj.getDate() + daysToAdd);
              } else {
                // Fallback to regular parsing
                dateObj = new Date(args.date);
              }
            } else {
              // Fallback to regular parsing
              dateObj = new Date(args.date);
            }
          } else {
            // Try to parse the date normally
            dateObj = new Date(args.date);
          }

          // Format the date if we have a valid date object
          if (!isNaN(dateObj.getTime())) {
            const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
            formattedDate = dateObj.toLocaleDateString('en-US', options);
            console.log('Formatted date:', formattedDate);
          } else {
            console.log('Date parsing failed, using original string');

            // Check if the date is in the expected format (e.g., "June 15, 2023")
            const dateFormatRegex = /^[A-Za-z]+ \d+, \d{4}$/;
            if (!dateFormatRegex.test(formattedDate)) {
              console.log('Date is not in the expected format, trying alternative parsing');

              // Try to extract date components using regex
              const dateMatch = args.date.match(/(\d{4})-(\d{2})-(\d{2})/);
              if (dateMatch) {
                const [, year, month, day] = dateMatch;
                const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                const monthIndex = parseInt(month) - 1;
                formattedDate = `${monthNames[monthIndex]} ${parseInt(day)}, ${year}`;
                console.log('Reformatted date using regex:', formattedDate);
              }
            }
          }
        } catch (dateError) {
          console.error('Error formatting date:', dateError);
          // Keep the original date string if parsing fails
        }
      }

      // Format the time if needed
      let formattedTime = args.time || '';
      console.log('Original time from OpenAI:', formattedTime);

      // Check if time is in a valid format for our application
      // We expect times in format like "10:00 AM" or "2:30 PM"
      if (formattedTime && !formattedTime.match(/^\d{1,2}:\d{2} (AM|PM)$/)) {
        try {
          console.log('Time is not in the expected format, attempting to format');

          // Try to parse and format the time
          const timeDate = new Date(`2000-01-01 ${formattedTime}`);
          if (!isNaN(timeDate.getTime())) {
            formattedTime = timeDate.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true
            });
            console.log('Formatted time using Date object:', formattedTime);
          } else {
            console.log('Time parsing failed with Date object');

            // Try to extract time components using regex
            const timeMatch = formattedTime.match(/(\d{1,2}):(\d{2})(?::(\d{2}))?\s*(am|pm|AM|PM)?/);
            if (timeMatch) {
              const [, hours, minutes, , period] = timeMatch;
              const hour = parseInt(hours);
              const minute = parseInt(minutes);

              // Determine AM/PM
              let timePeriod = period ? period.toUpperCase() : (hour >= 12 ? 'PM' : 'AM');

              // Format the time
              formattedTime = `${hour}:${minutes.padStart(2, '0')} ${timePeriod}`;
              console.log('Reformatted time using regex:', formattedTime);
            }
          }
        } catch (timeError) {
          console.error('Error formatting time:', timeError);
          // Keep the original time string if parsing fails
        }
      }

      // Ensure the time is in our list of available time slots
      if (formattedTime && !timeSlots.includes(formattedTime)) {
        // Find the closest time slot
        const closestTime = findClosestTimeSlot(formattedTime);
        console.log(`Time "${formattedTime}" not in available slots, using closest: "${closestTime}"`);
        formattedTime = closestTime;
      }

      // Create the processed data object
      const processedData = {
        appointmentId: args.appointmentId || `appt-${Date.now()}`,
        clientName: args.clientName || '',
        clientEmail: args.clientEmail || '',
        clientPhone: '', // This might not be in the extracted data
        propertyAddress: args.propertyAddress || '',
        propertyType: 'House', // Default value
        date: formattedDate || '',
        time: formattedTime,
        status: args.status || 'pending',
        notes: args.notes || ''
      };

      // Store the extracted data for confirmation
      setExtractedData(processedData);

      // Update the form data with the extracted information
      setFormData({
        clientName: processedData.clientName,
        clientEmail: processedData.clientEmail,
        clientPhone: processedData.clientPhone,
        propertyAddress: processedData.propertyAddress,
        propertyType: processedData.propertyType,
        preferredDate: processedData.date,
        preferredTime: processedData.time,
        additionalNotes: processedData.notes
      });

      // Set the selected date for the calendar component
      if (formattedDate) {
        try {
          const dateObj = new Date(formattedDate);
          if (!isNaN(dateObj.getTime())) {
            setSelectedDate(dateObj);
          }
        } catch (dateError) {
          console.error('Error setting selected date:', dateError);
        }
      }

      setExtractionSuccess(true);
      setShowConfirmation(true);
      return processedData;
        } catch (parseError) {
          console.error('Error parsing function call arguments:', parseError);
          throw new Error('Failed to parse appointment data');
        }
      } else {
        throw new Error('Function call not returned by the API');
      }
    } catch (error: any) {
      console.error('Error extracting appointment data:', error);

      // Provide more detailed error messages
      if (error.response) {
        if (error.response.status === 429) {
          setExtractionError('OpenAI API rate limit exceeded. Please try again in a few minutes.');
        } else if (error.response.status === 401 || error.response.status === 403) {
          setExtractionError('Authentication error with OpenAI API. Please check your API key.');
        } else if (error.response.status >= 500) {
          setExtractionError('OpenAI server error. Please try again later.');
        } else {
          setExtractionError(`Error: ${error.response.data.error?.message || 'Failed to extract appointment data'}`);
        }
      } else {
        setExtractionError(error.message || 'Failed to extract appointment data');
      }

      return null;
    } finally {
      setIsExtracting(false);
    }
  };

  // Handle natural language input changes
  const handleNaturalLanguageInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNaturalLanguageInput(e.target.value);
    // Reset extraction states when input changes
    setExtractionSuccess(false);
    setExtractionError('');
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle date selection from calendar
  const handleDateChange = (value: any) => {
    if (value instanceof Date) {
      // Don't allow dates in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (value < today) {
        setError('Please select a future date');
        return;
      }

      setSelectedDate(value);
      const formattedDate = value.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      setFormData({
        ...formData,
        preferredDate: formattedDate
      });
      setShowCalendar(false);
      setError('');
    }
  };

  // Find the closest time slot to the given time
  const findClosestTimeSlot = (time: string): string => {
    // Default to the first time slot if we can't parse the time
    if (!time) return timeSlots[0];

    try {
      // Convert the input time to minutes since midnight for comparison
      const inputTime = convertTimeToMinutes(time);
      if (inputTime === -1) return timeSlots[0];

      // Convert all available time slots to minutes and find the closest one
      let closestSlot = timeSlots[0];
      let minDifference = Math.abs(inputTime - convertTimeToMinutes(timeSlots[0]));

      for (let i = 1; i < timeSlots.length; i++) {
        const slotMinutes = convertTimeToMinutes(timeSlots[i]);
        const difference = Math.abs(inputTime - slotMinutes);

        if (difference < minDifference) {
          minDifference = difference;
          closestSlot = timeSlots[i];
        }
      }

      return closestSlot;
    } catch (error) {
      console.error('Error finding closest time slot:', error);
      return timeSlots[0];
    }
  };

  // Convert time string to minutes since midnight
  const convertTimeToMinutes = (timeStr: string): number => {
    try {
      // Try to match different time formats
      let match = timeStr.match(/^(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)?$/);

      if (!match) {
        // Try to match just hour with AM/PM
        match = timeStr.match(/^(\d{1,2})\s*(AM|PM|am|pm)$/);
        if (match) {
          match[2] = match[2] || '0'; // Add minutes if not present
        }
      }

      if (!match) {
        // Try to match 24-hour format
        match = timeStr.match(/^(\d{1,2}):(\d{2})$/);
        if (match) {
          match[3] = ''; // No AM/PM for 24-hour format
        }
      }

      if (!match) return -1;

      let [, hourStr, minuteStr, period] = match;
      let hour = parseInt(hourStr);
      const minute = parseInt(minuteStr || '0');

      // Convert to 24-hour format for calculation
      if (period) {
        period = period.toUpperCase();
        if (period === 'PM' && hour < 12) hour += 12;
        if (period === 'AM' && hour === 12) hour = 0;
      }

      return hour * 60 + minute;
    } catch (error) {
      console.error('Error converting time to minutes:', error, timeStr);
      return -1;
    }
  };

  // Handle time selection
  const handleTimeChange = (time: string) => {
    setFormData({
      ...formData,
      preferredTime: time
    });
  };

  // Book appointment using OpenAI function calling API
  const bookAppointment = async () => {
    console.log('Starting appointment booking process...');
    console.log('Current user:', state.user);
    setIsBooking(true);
    setError('');

    try {
      // Get OpenAI API key from environment variable
      // First try to get it from import.meta.env
      let apiKey = import.meta.env.VITE_OPENAI_API_KEY;

      // If not available, try to get it from window.__ENV__ (which we'll set up)
      if (!apiKey && typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_OPENAI_API_KEY) {
        apiKey = window.__ENV__.VITE_OPENAI_API_KEY;
      }

      // For development/testing, you can hardcode the key here (but remove before committing)
      // This is a fallback for testing only
      if (!apiKey) {
        apiKey = '********************************************************************************************************************************************************************';
      }

      if (!apiKey) {
        throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
      }

      console.log('Using OpenAI API with key:', apiKey.substring(0, 10) + '...');

      // Define the function schema for scheduling appointments
      const functionSchema = {
        name: 'scheduleAppointment',
        description: 'Schedule a real estate appointment with a client',
        parameters: {
          type: 'object',
          properties: {
            appointmentId: {
              type: 'string',
              description: 'A unique identifier for the appointment'
            },
            clientName: {
              type: 'string',
              description: 'The name of the client'
            },
            clientEmail: {
              type: 'string',
              description: 'The email address of the client'
            },
            date: {
              type: 'string',
              description: 'The date of the appointment'
            },
            time: {
              type: 'string',
              description: 'The time of the appointment'
            },
            propertyAddress: {
              type: 'string',
              description: 'The address of the property'
            },
            status: {
              type: 'string',
              enum: ['confirmed', 'pending', 'cancelled'],
              description: 'The status of the appointment'
            },
            notes: {
              type: 'string',
              description: 'Additional notes about the appointment'
            }
          },
          required: ['appointmentId', 'clientName', 'clientEmail', 'date', 'time', 'propertyAddress', 'status']
        }
      };

      // Create the system message with instructions for the AI
      const systemMessage = `You are an AI assistant for real estate agents. Your task is to help schedule appointments with clients. Use the scheduleAppointment function to create a new appointment based on the client information provided.`;

      // Create the user message with the form data
      const userMessage = `
        I need to schedule an appointment with a client. Here are the details:

        Client Name: ${formData.clientName}
        Client Email: ${formData.clientEmail}
        Client Phone: ${formData.clientPhone}
        Property Address: ${formData.propertyAddress}
        Property Type: ${formData.propertyType}
        Preferred Date: ${formData.preferredDate}
        Preferred Time: ${formData.preferredTime}
        Additional Notes: ${formData.additionalNotes}

        Please schedule this appointment and generate a unique appointment ID.
      `;

      // Make the API request to OpenAI using axios
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemMessage },
            { role: 'user', content: userMessage }
          ],
          functions: [functionSchema],
          function_call: { name: 'scheduleAppointment' },
          temperature: 0.7,
          max_tokens: 800
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'OpenAI-Beta': 'assistants=v1'
          }
        }
      );

      console.log('API response status:', response.status);
      console.log('API response data:', response.data);

      // Extract the function call arguments
      const functionCall = response.data.choices[0].message.function_call;

      if (functionCall && functionCall.name === 'scheduleAppointment') {
        try {
          const args = JSON.parse(functionCall.arguments);
          console.log('Function call arguments:', args);

          // Create the scheduled appointment object
          console.log('Creating appointment from args:', args);
          console.log('Date format from OpenAI:', args.date);
          console.log('Time format from OpenAI:', args.time);

          const appointment: ScheduledAppointment = {
            id: args.appointmentId,
            clientName: args.clientName,
            clientEmail: args.clientEmail,
            date: args.date,
            time: args.time,
            propertyAddress: args.propertyAddress,
            status: args.status,
            notes: args.notes || ''
          };

          console.log('Created appointment object:', appointment);

          // Save to local storage service
          try {
            // Ensure we have a user ID
            const userId = state.user?.id || 'guest';
            console.log('Current user ID:', userId);

            const appointmentData: CreateAppointmentData = {
              clientName: args.clientName,
              clientEmail: args.clientEmail,
              clientPhone: formData.clientPhone,
              propertyAddress: args.propertyAddress,
              propertyType: formData.propertyType || 'House',
              date: args.date,
              time: args.time,
              status: args.status,
              notes: args.notes || '',
              userId: userId
            };

            console.log('Saving appointment data:', appointmentData);

            // Save the appointment with the user ID to the database
            const savedAppointment = await appointmentService.createClientAppointment(appointmentData);
            console.log('Appointment saved with ID:', savedAppointment.id);
            console.log('Appointment linked to user:', userId);

            // Verify the appointment was saved
            try {
              const allAppointments = await appointmentService.getAllClientAppointments();
              console.log('All appointments after saving:', allAppointments.length);

              // Check if our appointment is in the list
              const found = allAppointments.some(a => a.id === savedAppointment.id);
              console.log('Appointment found in storage:', found);

              // Check user-specific appointments
              const userAppointments = await appointmentService.getClientAppointmentsForUser(userId);
              console.log('User appointments after saving:', userAppointments.length);
            } catch (error) {
              console.error('Error verifying appointment was saved:', error);
            }
          } catch (error) {
            console.error('Error saving appointment to local storage:', error);
          }

          // Set the scheduled appointment
          setScheduledAppointment(appointment);
          setShowSuccessMessage(true);

          // Log the appointment details
          console.log('Appointment scheduled:', appointment);

          return appointment;
        } catch (parseError) {
          console.error('Error parsing function call arguments:', parseError);
          throw new Error('Failed to parse appointment data');
        }
      } else {
        throw new Error('Function call not returned by the API');
      }
    } catch (error: any) {
      console.error('Error booking appointment:', error);

      // Provide more detailed error messages
      if (error.response) {
        if (error.response.status === 429) {
          setError('OpenAI API rate limit exceeded. Please try again in a few minutes.');
        } else if (error.response.status === 401 || error.response.status === 403) {
          setError('Authentication error with OpenAI API. Please check your API key.');
        } else if (error.response.status >= 500) {
          setError('OpenAI server error. Please try again later.');
        } else {
          setError(`Error: ${error.response.data.error?.message || 'Failed to book appointment'}`);
        }
      } else {
        setError(error.message || 'Failed to book appointment');
      }

      // Log additional details for debugging
      console.log('Error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response?.data
      });

      return null;
    } finally {
      setIsBooking(false);
    }
  };

  // Generate appointment recommendations using OpenAI
  const generateAppointmentRecommendations = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Get OpenAI API key from environment variable
      // First try to get it from import.meta.env
      let apiKey = import.meta.env.VITE_OPENAI_API_KEY;

      // If not available, try to get it from window.__ENV__ (which we'll set up)
      if (!apiKey && typeof window !== 'undefined' && window.__ENV__ && window.__ENV__.VITE_OPENAI_API_KEY) {
        apiKey = window.__ENV__.VITE_OPENAI_API_KEY;
      }

      // For development/testing, you can hardcode the key here (but remove before committing)
      // This is a fallback for testing only
      if (!apiKey) {
        apiKey = '********************************************************************************************************************************************************************';
      }

      if (!apiKey) {
        throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
      }

      console.log('Using OpenAI API with key:', apiKey.substring(0, 10) + '...');

      // Create the system message with instructions for the AI
      const systemMessage = `You are an AI assistant for real estate agents. Your task is to help schedule appointments with clients and provide preparation tips. Based on the client information and property details, suggest optimal appointment slots and provide preparation advice.`;

      // Create the user message with the form data
      const userMessage = `
        I need to schedule an appointment with a client. Here are the details:

        Client Name: ${formData.clientName}
        Client Email: ${formData.clientEmail}
        Client Phone: ${formData.clientPhone}
        Property Address: ${formData.propertyAddress}
        Property Type: ${formData.propertyType}
        Preferred Date: ${formData.preferredDate}
        Preferred Time: ${formData.preferredTime}
        Additional Notes: ${formData.additionalNotes}

        Please suggest optimal appointment slots and provide preparation tips. Format your response as JSON with the following structure:
        {
          "suggestedSlots": [
            {
              "date": "Date in text format",
              "slots": ["Time slot 1", "Time slot 2", "Time slot 3"]
            }
          ],
          "preparationTips": ["Tip 1", "Tip 2", "Tip 3", "Tip 4"],
          "followUpReminder": "A reminder message"
        }
      `;

      // Make the API request to OpenAI using axios
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemMessage },
            { role: 'user', content: userMessage }
          ],
          temperature: 0.7,
          max_tokens: 800,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'OpenAI-Beta': 'assistants=v1'
          }
        }
      );

      console.log('API response status:', response.status);
      console.log('API response data:', response.data);

      const content = response.data.choices[0].message.content;

      try {
        // Since we're using response_format: { type: 'json_object' }, the content should be valid JSON
        const parsedResponse = JSON.parse(content);
        console.log('Parsed response:', parsedResponse);
        setAiResponse(parsedResponse);
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);

        // Fallback: try to extract JSON from the response
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonContent = jsonMatch[0];
          try {
            const parsedResponse = JSON.parse(jsonContent);
            console.log('Fallback parsed response:', parsedResponse);
            setAiResponse(parsedResponse);
          } catch (fallbackError) {
            throw new Error('Failed to parse AI response');
          }
        } else {
          throw new Error('Failed to parse AI response');
        }
      }
    } catch (error: any) {
      console.error('Error generating appointment recommendations:', error);

      // Provide more detailed error messages
      if (error.response) {
        if (error.response.status === 429) {
          setError('OpenAI API rate limit exceeded. Please try again in a few minutes.');
        } else if (error.response.status === 401 || error.response.status === 403) {
          setError('Authentication error with OpenAI API. Please check your API key.');
        } else if (error.response.status >= 500) {
          setError('OpenAI server error. Please try again later.');
        } else {
          setError(`Error: ${error.response.data.error?.message || 'Failed to generate appointment recommendations'}`);
        }
      } else {
        setError(error.message || 'Failed to generate appointment recommendations');
      }

      // Log additional details for debugging
      console.log('Error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response?.data
      });

      // Fallback to a simulated response if OpenAI fails
      generateFallbackResponse();
    } finally {
      setIsLoading(false);
    }
  };

  // Generate a fallback response if OpenAI fails
  const generateFallbackResponse = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' });
    };

    const fallbackResponse: AppointmentResponse = {
      suggestedSlots: [
        {
          date: formatDate(tomorrow),
          slots: ['10:00 AM', '2:00 PM', '4:30 PM']
        },
        {
          date: formatDate(dayAfterTomorrow),
          slots: ['9:30 AM', '1:00 PM', '3:30 PM']
        }
      ],
      preparationTips: [
        'Review property details and recent comparable sales before the meeting',
        'Prepare answers to common questions about the neighborhood',
        'Have financing options ready to discuss',
        'Bring property disclosure documents'
      ],
      followUpReminder: `Send a follow-up email to ${formData.clientName} within 24 hours of the appointment`
    };

    setAiResponse(fallbackResponse);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // If using natural language input, extract data first
    if (inputMode === 'natural' && !showConfirmation) {
      if (!naturalLanguageInput.trim()) {
        setError('Please enter a description of the appointment');
        return;
      }

      setIsExtracting(true);

      try {
        // Extract appointment data from natural language
        const extractedData = await extractAppointmentData();

        if (!extractedData) {
          setError('Failed to extract appointment details. Please try again or use the form input.');
          setIsExtracting(false);
          return;
        }

        // The confirmation panel will be shown by the extractAppointmentData function
        // setIsExtracting(false); // This is now handled in the finally block of extractAppointmentData
        // Stop here - don't book yet, wait for confirmation
        return;
      } catch (error: any) {
        console.error('Error during natural language processing:', error);
        setError(error.message || 'An error occurred during processing');
        setIsExtracting(false);
        return;
      } finally {
        // Ensure extraction state is reset
        setIsExtracting(false);
      }
    } else if (showConfirmation) {
      // If we're in confirmation mode, don't do anything on form submit
      // The booking will happen through handleConfirmExtraction
      return;
    } else {
      // Using form input - validate form fields
      if (!formData.clientName || !formData.clientEmail || !formData.propertyAddress) {
        setError('Please fill in all required fields');
        return;
      }

      if (!formData.preferredDate) {
        setError('Please select a preferred date');
        return;
      }

      if (!formData.preferredTime) {
        setError('Please select a preferred time');
        return;
      }

      // Start booking process
      setIsBooking(true);

      try {
        // Book the appointment
        const appointment = await bookAppointment();

        if (appointment) {
          // Clear the form after successful booking
          handleReset();
        }
      } catch (error) {
        console.error('Error in form submission booking process:', error);
        setError('An error occurred while booking the appointment. Please try again.');
      } finally {
        // Ensure booking state is reset
        setIsBooking(false);
      }
    }
  };

  // Handle confirmation of extracted data
  const handleConfirmExtraction = async () => {
    console.log('Confirming extracted appointment data...');
    console.log('Current user:', state.user);
    console.log('Extracted data:', extractedData);

    // Submit the form with the extracted data
    setIsBooking(true);

    try {
      // If we have extracted data, save it directly to avoid OpenAI API call
      if (extractedData) {
        console.log('Using extracted data to create appointment directly');

        // Ensure we have a user ID
        const userId = state.user?.id || 'guest';
        console.log('Current user ID for extracted appointment:', userId);

        // Create appointment data
        const appointmentData: CreateAppointmentData = {
          clientName: extractedData.clientName,
          clientEmail: extractedData.clientEmail,
          clientPhone: formData.clientPhone || '',
          propertyAddress: extractedData.propertyAddress,
          propertyType: formData.propertyType || 'House',
          date: extractedData.date,
          time: extractedData.time,
          status: extractedData.status || 'confirmed',
          notes: extractedData.notes || '',
          userId: userId
        };

        console.log('Saving extracted appointment data directly:', appointmentData);

        // Save the appointment with the user ID to the database
        const savedAppointment = await appointmentService.createClientAppointment(appointmentData);
        console.log('Extracted appointment saved with ID:', savedAppointment.id);

        // Create a ScheduledAppointment object for the UI
        const appointment: ScheduledAppointment = {
          id: savedAppointment.id,
          clientName: extractedData.clientName,
          clientEmail: extractedData.clientEmail,
          date: extractedData.date,
          time: extractedData.time,
          propertyAddress: extractedData.propertyAddress,
          status: extractedData.status || 'confirmed',
          notes: extractedData.notes || ''
        };

        // Verify the appointment was saved
        try {
          const allAppointments = await appointmentService.getAllClientAppointments();
          console.log('All appointments after saving extracted data:', allAppointments.length);

          // Check if our appointment is in the list
          const found = allAppointments.some(a => a.id === savedAppointment.id);
          console.log('Extracted appointment found in storage:', found);

          // Check user-specific appointments
          const userAppointments = await appointmentService.getClientAppointmentsForUser(userId);
          console.log('User appointments after saving extracted data:', userAppointments.length);
        } catch (error) {
          console.error('Error verifying appointment was saved:', error);
        }

        // Set the scheduled appointment
        setScheduledAppointment(appointment);
        setShowSuccessMessage(true);

        // Keep the confirmation panel visible but hide the form
        setShowConfirmation(false);
        // Clear the form after successful booking
        handleReset();

        // Make sure to reset the loading state
        setIsBooking(false);

        return appointment;
      } else {
        // Fall back to the regular booking process
        console.log('No extracted data available, using regular booking process');
        const appointment = await bookAppointment();

        if (appointment) {
          // Keep the confirmation panel visible but hide the form
          setShowConfirmation(false);
          // Clear the form after successful booking
          handleReset();
        } else {
          setIsBooking(false);
        }

        return appointment;
      }
    } catch (error) {
      console.error('Error confirming extracted appointment:', error);
      setError('An error occurred while booking the appointment. Please try again.');
      setIsBooking(false);
      return null;
    } finally {
      // Ensure the loading state is always reset, even if there's an error
      setIsBooking(false);
    }
  };

  // Handle editing of extracted data
  const handleEditExtraction = () => {
    // Keep the form data but hide the confirmation panel
    setShowConfirmation(false);
  };

  // Reset the form and results
  const handleReset = () => {
    setFormData({
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      propertyAddress: '',
      propertyType: 'House',
      preferredDate: '',
      preferredTime: '',
      additionalNotes: ''
    });
    setNaturalLanguageInput('');
    setExtractionSuccess(false);
    setExtractionError('');
    // Only clear confirmation state if no appointment was scheduled
    if (!scheduledAppointment) {
      setShowConfirmation(false);
      setExtractedData(null);
    }
    setAiResponse(null);
    setError('');
    setSelectedDate(null);
    // Don't reset the scheduled appointment or success message
    // so the user can see the confirmation
  };

  // Close the success message
  const handleCloseSuccessMessage = () => {
    setShowSuccessMessage(false);
  };

  // Generate ICS file for calendar
  const generateIcsFile = () => {
    if (!scheduledAppointment) {
      setCalendarError('No appointment to add to calendar');
      return;
    }

    try {
      setCalendarError('');

      // Parse date and time
      const { date, time, clientName, propertyAddress, notes, id } = scheduledAppointment;

      // Validate required data
      if (!date || !time) {
        throw new Error('Date and time are required for calendar integration');
      }

      // Format date for ICS file
      // Example date format: "June 15, 2023"
      let year, month, day, formattedMonth, formattedDay;

      try {
        // Try to parse the date using regex
        const dateMatch = date.match(/([A-Za-z]+)\s+(\d+),\s+(\d+)/);

        if (dateMatch) {
          [, month, day, year] = dateMatch;
          const monthIndex = new Date(`${month} 1, 2000`).getMonth() + 1; // 1-based month
          formattedDay = day.padStart(2, '0');
          formattedMonth = String(monthIndex).padStart(2, '0');
        } else {
          // Try to parse as a Date object
          const dateObj = new Date(date);
          if (!isNaN(dateObj.getTime())) {
            year = String(dateObj.getFullYear());
            formattedMonth = String(dateObj.getMonth() + 1).padStart(2, '0');
            formattedDay = String(dateObj.getDate()).padStart(2, '0');
          } else {
            throw new Error(`Could not parse date: ${date}`);
          }
        }
      } catch (error) {
        console.error('Error parsing date:', error);
        // Use current date as fallback
        const today = new Date();
        year = String(today.getFullYear());
        formattedMonth = String(today.getMonth() + 1).padStart(2, '0');
        formattedDay = String(today.getDate()).padStart(2, '0');
      }

      // Format time for ICS file
      // Example time format: "10:00 AM"
      let hour, minute, formattedHour, formattedMinute;

      try {
        const timeMatch = time.match(/(\d+):(\d+)\s+([AP]M)/);

        if (timeMatch) {
          const [, hourStr, minuteStr, period] = timeMatch;
          hour = parseInt(hourStr);
          minute = parseInt(minuteStr);

          // Convert to 24-hour format
          if (period === 'PM' && hour < 12) {
            hour += 12;
          } else if (period === 'AM' && hour === 12) {
            hour = 0;
          }
        } else {
          // Try to parse using convertTimeToMinutes function
          const totalMinutes = convertTimeToMinutes(time);
          if (totalMinutes !== -1) {
            hour = Math.floor(totalMinutes / 60);
            minute = totalMinutes % 60;
          } else {
            throw new Error(`Could not parse time: ${time}`);
          }
        }

        formattedHour = String(hour).padStart(2, '0');
        formattedMinute = String(minute).padStart(2, '0');
      } catch (error) {
        console.error('Error parsing time:', error);
        // Use current time as fallback
        const now = new Date();
        hour = now.getHours();
        minute = now.getMinutes();
        formattedHour = String(hour).padStart(2, '0');
        formattedMinute = String(minute).padStart(2, '0');
      }

      // Create start and end dates (1 hour appointment)
      const startDateTime = `${year}${formattedMonth}${formattedDay}T${formattedHour}${formattedMinute}00`;

      // End time is 1 hour later
      let endHour = hour + 1;
      if (endHour >= 24) {
        endHour = 0;
      }
      const formattedEndHour = String(endHour).padStart(2, '0');
      const endDateTime = `${year}${formattedMonth}${formattedDay}T${formattedEndHour}${formattedMinute}00`;

      // Create a unique identifier for the event
      const uid = `appointment-${id}-${Date.now()}@digitalrealtor.com`;

      // Create the ICS content
      const icsContent = [
        'BEGIN:VCALENDAR',
        'VERSION:2.0',
        'PRODID:-//Digital Realtor//Appointment Scheduler//EN',
        'CALSCALE:GREGORIAN',
        'METHOD:PUBLISH',
        'BEGIN:VEVENT',
        `UID:${uid}`,
        `DTSTAMP:${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}Z`,
        `DTSTART:${startDateTime}`,
        `DTEND:${endDateTime}`,
        `SUMMARY:Property Viewing: ${propertyAddress}`,
        `DESCRIPTION:Appointment with ${clientName}\nProperty Address: ${propertyAddress}\nStatus: ${scheduledAppointment.status}\nAppointment ID: ${id}${notes ? `\nNotes: ${notes}` : ''}`,
        `LOCATION:${propertyAddress}`,
        'STATUS:CONFIRMED',
        'SEQUENCE:0',
        'END:VEVENT',
        'END:VCALENDAR'
      ].join('\r\n');

      // Create a Blob with the ICS content
      const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });

      // Create a download link
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `appointment-${id}.ics`;

      // Trigger the download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Generated ICS file for appointment:', id);

      // Update the scheduled appointment
      const updatedAppointment = {
        ...scheduledAppointment,
        googleCalendarEventId: 'ics-generated' // Just a marker to show it's been processed
      };

      setScheduledAppointment(updatedAppointment);
    } catch (error: any) {
      console.error('Error generating ICS file:', error);
      setCalendarError(error.message || 'Failed to generate ICS file');
    }
  };

  return (
    <div className="py-12 min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/agent')}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              transition: 'all 0.3s ease',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            }}
            className="mb-4 transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
          >
            <FaArrowLeft className="mr-2" />
            Back to Dashboard
          </button>
          <div className="flex justify-between items-center mb-2">
            <h1 className="text-3xl font-bold text-white">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-secondary-400 to-secondary-600">
                Appointment Scheduler
              </span>
            </h1>
            <button
              onClick={() => navigate('/appointment-calendar')}
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                transition: 'all 0.3s ease',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              }}
              className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
            >
              <FaCalendarAlt className="mr-2" />
              View Calendar
            </button>
          </div>
          <p className="text-primary-200 mb-4">
            Get AI recommendations for scheduling client appointments and viewing preparations.
          </p>

          {/* LLM Indicator - shows in all modes */}
          <div className="mt-2 py-1.5 px-3 bg-gray-800/30 border border-accent-cyan/20 rounded-full inline-flex items-center shadow-sm hover:bg-gray-800/40 transition-colors">
            <div className="mr-1.5 h-2 w-2 rounded-full animate-pulse"
                 style={{ backgroundColor: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}></div>
            <span className="text-xs">
              Powered by <span className="font-semibold"
                style={{ color: llmProvider === 'openai' ? '#10a37f' : '#a27aff' }}>
                {llmProvider === 'openai' ? 'OpenAI' : 'Claude'}
              </span>
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Section */}
          <div className="glass-card p-6 rounded-xl shadow-lg border-glow">
            <h2 className="text-xl font-bold text-white mb-4">Client Information</h2>

            {error && (
              <div className="bg-red-500/20 text-red-300 p-3 rounded-lg mb-4">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              {/* Input Mode and LLM Provider Toggles */}
              <div className="flex flex-col md:flex-row justify-center items-center gap-4 mb-6">
                <div className="bg-dark-800 p-1 rounded-lg flex">
                  <button
                    type="button"
                    onClick={() => setInputMode('natural')}
                    style={{
                      background: inputMode === 'natural'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${inputMode === 'natural'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaCommentAlt className="mr-2" />
                      Natural Language
                    </span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setInputMode('form')}
                    style={{
                      background: inputMode === 'form'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${inputMode === 'form'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaList className="mr-2" />
                      Form Input
                    </span>
                  </button>
                </div>

                {/* LLM Provider Toggle */}
                <div className="bg-dark-800 p-1 rounded-lg flex">
                  <button
                    type="button"
                    onClick={() => setLlmProvider('openai')}
                    style={{
                      background: llmProvider === 'openai'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'openai'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaRobot className="mr-2" style={{ color: '#10a37f' }} />
                      OpenAI
                    </span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setLlmProvider('claude')}
                    style={{
                      background: llmProvider === 'claude'
                        ? 'linear-gradient(to right, #00b9ff, #9900ff)'
                        : 'transparent',
                      transition: 'all 0.3s ease',
                    }}
                    className={`px-4 py-2 rounded-lg transition-all transform ${llmProvider === 'claude'
                      ? 'text-white shadow-lg hover:shadow-xl'
                      : 'bg-transparent text-primary-300 hover:text-white hover:bg-dark-700'}`}
                  >
                    <span className="inline-flex items-center">
                      <FaRobot className="mr-2" style={{ color: '#a27aff' }} />
                      Claude
                    </span>
                  </button>
                </div>
              </div>

              {/* Natural Language Input Section */}
              {inputMode === 'natural' && !showConfirmation && (
                <div className="glass-card p-4 rounded-xl shadow-lg border-glow mb-6">
                  <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <FaCommentAlt className="text-secondary-400 mr-2" />
                    Natural Language Input
                  </h3>

                  <p className="text-primary-300 mb-3 text-sm">
                    Describe your appointment in natural language. For example:
                    <span className="block mt-2 ml-4 italic text-secondary-300">"Schedule an appointment for John Smith (<EMAIL>) tomorrow at 5:00 PM to view the property at 123 Main Street. This is a first-time buyer looking for a family home."</span>
                  </p>

                  <div className="mb-3">
                    <textarea
                      name="naturalLanguageInput"
                      value={naturalLanguageInput}
                      onChange={handleNaturalLanguageInputChange}
                      className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400 min-h-[100px]"
                      placeholder="Enter your appointment details in natural language..."
                      required={inputMode === 'natural'}
                    />
                  </div>

                  {/* Extraction Status Messages */}
                  {extractionError && (
                    <div className="mt-3 bg-red-500/20 text-red-300 p-3 rounded-lg flex items-start">
                      <FaExclamationTriangle className="text-red-300 mr-2 mt-1 flex-shrink-0" />
                      <p>{extractionError}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Confirmation Panel for Extracted Data */}
              {showConfirmation && extractedData && (
                <div className="glass-card p-4 rounded-xl shadow-lg border-glow mb-6">
                  <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <FaCheck className="text-green-400 mr-2" />
                    Confirm Appointment Details
                  </h3>

                  <p className="text-primary-300 mb-4">
                    We've extracted the following appointment details. Please review and confirm or edit as needed.
                  </p>

                  <div className="bg-dark-700/50 p-4 rounded-lg space-y-3 mb-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <p className="text-primary-400 text-sm">Client Name</p>
                        <p className="text-white">{extractedData.clientName}</p>
                      </div>
                      <div>
                        <p className="text-primary-400 text-sm">Client Email</p>
                        <p className="text-white">{extractedData.clientEmail}</p>
                      </div>
                      <div>
                        <p className="text-primary-400 text-sm">Date</p>
                        <p className="text-white">{extractedData.date}</p>
                      </div>
                      <div>
                        <p className="text-primary-400 text-sm">Time</p>
                        <p className="text-white">{extractedData.time}</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-primary-400 text-sm">Property Address</p>
                      <p className="text-white">{extractedData.propertyAddress}</p>
                    </div>

                    {extractedData.notes && (
                      <div>
                        <p className="text-primary-400 text-sm">Notes</p>
                        <p className="text-primary-200">{extractedData.notes}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-3">
                    <button
                      type="button"
                      onClick={handleConfirmExtraction}
                      style={{
                        background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                        color: 'white',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '0.5rem',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: 'none',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300"
                    >
                      <FaCheck className="mr-2" />
                      Confirm and Book
                    </button>

                    <button
                      type="button"
                      onClick={handleEditExtraction}
                      style={{
                        background: 'rgba(255, 255, 255, 0.1)',
                        color: 'white',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '0.5rem',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                      }}
                      className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 hover:bg-dark-600"
                    >
                      <FaEdit className="mr-2" />
                      Edit Details
                    </button>
                  </div>
                </div>
              )}

              {/* Form Input Fields - Only show when in form mode or when natural language has been processed and not in confirmation mode, and no appointment has been scheduled */}
              {(inputMode === 'form' || (inputMode === 'natural' && extractionSuccess && !showConfirmation)) && !scheduledAppointment && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="form-group">
                      <label className="block text-primary-200 mb-2 flex items-center">
                        <FaUser className="mr-2" />
                        Client Name *
                      </label>
                      <input
                        type="text"
                        name="clientName"
                        value={formData.clientName}
                        onChange={handleInputChange}
                        className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                        placeholder="Enter client name"
                        required={inputMode === 'form'}
                      />
                    </div>

                    <div className="form-group">
                      <label className="block text-primary-200 mb-2 flex items-center">
                        <FaEnvelope className="mr-2" />
                        Client Email *
                      </label>
                      <input
                        type="email"
                        name="clientEmail"
                        value={formData.clientEmail}
                        onChange={handleInputChange}
                        className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                        placeholder="Enter client email"
                        required={inputMode === 'form'}
                      />
                    </div>

                    <div className="form-group">
                      <label className="block text-primary-200 mb-2 flex items-center">
                        <FaPhone className="mr-2" />
                        Client Phone
                      </label>
                      <input
                        type="tel"
                        name="clientPhone"
                        value={formData.clientPhone}
                        onChange={handleInputChange}
                        className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                        placeholder="Enter client phone"
                      />
                    </div>

                    <div className="form-group">
                      <label className="block text-primary-200 mb-2 flex items-center">
                        <FaHome className="mr-2" />
                        Property Type
                      </label>
                      <select
                        name="propertyType"
                        value={formData.propertyType}
                        onChange={handleInputChange}
                        className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                      >
                        <option value="House">House</option>
                        <option value="Apartment">Apartment</option>
                        <option value="Condo">Condo</option>
                        <option value="Townhouse">Townhouse</option>
                        <option value="Land">Land</option>
                        <option value="Commercial">Commercial</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-group mb-4">
                    <label className="block text-primary-200 mb-2 flex items-center">
                      <FaHome className="mr-2" />
                      Property Address *
                    </label>
                    <input
                      type="text"
                      name="propertyAddress"
                      value={formData.propertyAddress}
                      onChange={handleInputChange}
                      className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                      placeholder="Enter property address"
                      required={inputMode === 'form'}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="form-group">
                      <label className="block text-primary-200 mb-2 flex items-center">
                        <FaCalendarAlt className="mr-2" />
                        Preferred Date *
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          name="preferredDate"
                          value={formData.preferredDate}
                          onClick={() => setShowCalendar(!showCalendar)}
                          className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400 cursor-pointer"
                          placeholder="Select preferred date"
                          readOnly
                          required={inputMode === 'form'}
                        />
                        {showCalendar && (
                          <div className="absolute z-10 mt-1 bg-dark-800 border border-primary-700 rounded-lg shadow-lg p-2">
                            <Calendar
                              onChange={handleDateChange}
                              value={selectedDate}
                              minDate={new Date()}
                              className="custom-calendar"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="form-group">
                      <label className="block text-primary-200 mb-2 flex items-center">
                        <FaClock className="mr-2" />
                        Preferred Time *
                      </label>
                      <select
                        name="preferredTime"
                        value={formData.preferredTime}
                        onChange={handleInputChange}
                        className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                        required={inputMode === 'form'}
                      >
                        <option value="">Select a time</option>
                        {timeSlots.map((time, index) => (
                          <option key={index} value={time}>{time}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="form-group mb-6">
                    <label className="block text-primary-200 mb-2">
                      Additional Notes
                    </label>
                    <textarea
                      name="additionalNotes"
                      value={formData.additionalNotes}
                      onChange={handleInputChange}
                      className="w-full bg-dark-700 border border-primary-700 rounded-lg p-3 text-white focus:outline-none focus:ring-2 focus:ring-secondary-400"
                      placeholder="Enter any additional information"
                      rows={4}
                    />
                  </div>
                </>
              )}

              {/* Only show the submit buttons when not in confirmation mode and no appointment has been scheduled */}
              {!showConfirmation && !scheduledAppointment && (
                <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                  <button
                    type="submit"
                    disabled={isLoading || (inputMode === 'natural' && !naturalLanguageInput.trim())}
                    style={{
                      background: 'linear-gradient(to right, #00b9ff, #9900ff)',
                      color: 'white',
                      padding: '1rem 2rem',
                      borderRadius: '9999px',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: 'none',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 w-full md:w-auto"
                  >
                    {isBooking ? (
                      <span className="inline-flex items-center">
                        <FaSpinner className="animate-spin mr-2" />
                        {isExtracting ? 'Processing Natural Language...' : 'Booking Appointment...'}
                      </span>
                    ) : (
                      <span className="inline-flex items-center">
                        <FaCalendarAlt className="mr-2" />
                        {inputMode === 'natural' ? 'Extract & Review' : 'Book Appointment'}
                      </span>
                    )}
                  </button>

                  <button
                    type="button"
                    onClick={handleReset}
                    style={{
                      background: 'white',
                      color: '#00b9ff',
                      padding: '1rem 2rem',
                      borderRadius: '9999px',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.3s ease',
                      border: 'none',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                    }}
                    className="transform hover:shadow-lg hover:-translate-y-0.5 hover:scale-105 transition-transform duration-300 w-full md:w-auto"
                  >
                    Reset Form
                  </button>
                </div>
              )}
            </form>
          </div>

          {/* Results Section */}
          <div className="glass-card p-6 rounded-xl shadow-lg border-glow">
            <h2 className="text-xl font-bold text-white mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-secondary-400 to-secondary-600">
                Appointment Details
              </span>
            </h2>

            {/* Success Message */}
            {showSuccessMessage && scheduledAppointment && (
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6 relative">
                <button
                  onClick={handleCloseSuccessMessage}
                  className="absolute top-2 right-2 text-green-300 hover:text-white"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <div className="flex items-center mb-3">
                  <div className="bg-green-500/30 rounded-full p-2 mr-3">
                    <FaCheck className="text-green-300 text-xl" />
                  </div>
                  <h3 className="text-lg font-semibold text-green-300">Appointment Scheduled Successfully!</h3>
                </div>

                <p className="text-primary-200 mb-4">
                  Your appointment has been scheduled. Here are the details:
                </p>

                <div className="bg-dark-700/50 p-4 rounded-lg space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <p className="text-primary-400 text-sm">Appointment ID</p>
                      <p className="text-white font-mono">{scheduledAppointment.id}</p>
                    </div>
                    <div>
                      <p className="text-primary-400 text-sm">Status</p>
                      <p className="text-secondary-400 font-semibold capitalize">{scheduledAppointment.status}</p>
                    </div>
                    <div>
                      <p className="text-primary-400 text-sm">Client</p>
                      <p className="text-white">{scheduledAppointment.clientName}</p>
                    </div>
                    <div>
                      <p className="text-primary-400 text-sm">Email</p>
                      <p className="text-white">{scheduledAppointment.clientEmail}</p>
                    </div>
                    <div>
                      <p className="text-primary-400 text-sm">Date</p>
                      <p className="text-white">{scheduledAppointment.date}</p>
                    </div>
                    <div>
                      <p className="text-primary-400 text-sm">Time</p>
                      <p className="text-white">{scheduledAppointment.time}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-primary-400 text-sm">Property Address</p>
                    <p className="text-white">{scheduledAppointment.propertyAddress}</p>
                  </div>

                  {scheduledAppointment.notes && (
                    <div>
                      <p className="text-primary-400 text-sm">Notes</p>
                      <p className="text-primary-200">{scheduledAppointment.notes}</p>
                    </div>
                  )}
                </div>

                {/* Calendar Integration */}
                <div className="mt-6 bg-dark-800/50 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <FaCalendarAlt className="text-blue-400 mr-2" />
                    Calendar Integration
                  </h4>

                  {calendarError && (
                    <div className="bg-red-500/20 text-red-300 p-3 rounded-lg mb-4 flex items-start">
                      <FaExclamationTriangle className="text-red-300 mr-2 mt-1 flex-shrink-0" />
                      <p>{calendarError}</p>
                    </div>
                  )}

                  {scheduledAppointment?.googleCalendarEventId ? (
                    <div className="bg-green-500/20 text-green-300 p-3 rounded-lg mb-4">
                      <p className="flex items-center">
                        <FaCheck className="mr-2" />
                        Calendar integration completed
                      </p>

                      {calendarLinkGenerated && (
                        <div className="mt-3">
                          <a
                            href={calendarLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center transition-colors inline-flex"
                          >
                            <FaGoogle className="mr-2" />
                            Open in Google Calendar
                          </a>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col space-y-3">
                      <p className="text-primary-200 mb-2">
                        Add this appointment to your calendar by choosing one of the options below:
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <button
                          onClick={generateGoogleCalendarLink}
                          disabled={isAddingToCalendar}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isAddingToCalendar ? (
                            <span className="inline-flex items-center">
                              <FaSpinner className="animate-spin mr-2" />
                              Generating Link...
                            </span>
                          ) : (
                            <span className="inline-flex items-center">
                              <FaGoogle className="mr-2" />
                              Add to Google Calendar
                            </span>
                          )}
                        </button>

                        <button
                          onClick={generateIcsFile}
                          className="bg-dark-700 hover:bg-dark-600 text-primary-200 px-4 py-2 rounded-lg flex items-center justify-center transition-colors"
                        >
                          <FaDownload className="mr-2" />
                          Download ICS File
                        </button>
                      </div>

                      <div className="text-primary-400 text-sm mt-2">
                        <p>* Google Calendar will open in a new tab</p>
                        <p>* ICS files can be imported into any calendar app (Outlook, Apple Calendar, etc.)</p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => window.print()}
                    className="bg-dark-700 hover:bg-dark-600 text-primary-200 px-4 py-2 rounded-lg flex items-center justify-center transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    Print Appointment Details
                  </button>
                </div>
              </div>
            )}

            {isBooking ? (
              <div className="flex flex-col items-center justify-center h-64">
                <FaSpinner className="animate-spin text-4xl text-secondary-400 mb-4" />
                <p className="text-primary-200">Booking your appointment...</p>
              </div>
            ) : !showSuccessMessage && (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <FaCalendarAlt className="text-4xl text-primary-500/50 mb-4" />
                <p className="text-primary-300 mb-2">Fill out the form to schedule an appointment</p>
                <p className="text-primary-500 text-sm">Select your preferred date and time to book a property viewing</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentScheduler;
