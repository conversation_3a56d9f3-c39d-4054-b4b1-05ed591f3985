import React from 'react';
import { FaCheckCircle, FaExclamationCircle, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';

type ValidationMessageType = 'error' | 'success' | 'info' | 'warning';

interface ValidationMessageProps {
  type: ValidationMessageType;
  message: string;
  className?: string;
}

/**
 * A reusable component for displaying validation messages with consistent styling
 */
const ValidationMessage: React.FC<ValidationMessageProps> = ({ type, message, className = '' }) => {
  // Determine the icon based on the message type
  const getIcon = () => {
    switch (type) {
      case 'error':
        return <FaExclamationCircle />;
      case 'success':
        return <FaCheckCircle />;
      case 'info':
        return <FaInfoCircle />;
      case 'warning':
        return <FaExclamationTriangle />;
      default:
        return <FaInfoCircle />;
    }
  };

  return (
    <div className={`validation-message validation-${type} ${className}`}>
      <span className="validation-icon">
        {getIcon()}
      </span>
      <span className="validation-text">{message}</span>
    </div>
  );
};

export default ValidationMessage;
