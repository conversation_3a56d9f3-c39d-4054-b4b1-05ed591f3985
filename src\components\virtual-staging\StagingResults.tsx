import React, { useState, useEffect } from 'react';
import { DesignStyle } from '../../pages/tools/VirtualStagingTool';
import { FaImage, FaExchangeAlt, FaExclamationTriangle } from 'react-icons/fa';

interface StagingResultsProps {
  originalImage: string;
  suggestions: {
    textSuggestions: string;
    imageUrl?: string;
  };
  designStyle: DesignStyle;
}

const StagingResults: React.FC<StagingResultsProps> = ({
  originalImage,
  suggestions,
  designStyle
}) => {
  const [showOriginal, setShowOriginal] = useState(false);
  const [imageLoadError, setImageLoadError] = useState(false);

  // Reset image load error when the image URL changes
  useEffect(() => {
    setImageLoadError(false);
  }, [suggestions.imageUrl]);

  // Handle image loading error
  const handleImageError = () => {
    console.error('Failed to load the staged image');
    setImageLoadError(true);
    // Automatically switch to original image view
    setShowOriginal(true);
  };

  // Format the text suggestions for better readability
  const formattedSuggestions = suggestions.textSuggestions
    .split('\n')
    .map((line, index) => (
      <React.Fragment key={index}>
        {line}
        <br />
      </React.Fragment>
    ));

  return (
    <div className="space-y-6">
      {/* Images Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-primary-200 mb-3 flex items-center">
          <FaImage className="mr-2 text-accent-cyan" />
          {suggestions.imageUrl ? 'Room Visualization' : 'Original Room'}
        </h3>

        {suggestions.imageUrl ? (
          <div className="relative">
            <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
              {imageLoadError && !showOriginal ? (
                <div className="w-full h-full flex flex-col items-center justify-center bg-dark-700 p-6 text-center">
                  <FaExclamationTriangle className="text-yellow-500 text-4xl mb-4" />
                  <h4 className="text-white font-medium mb-2">Image Loading Error</h4>
                  <p className="text-primary-300 text-sm mb-4">
                    The staged image couldn't be loaded due to Content Security Policy restrictions.
                  </p>
                  <button
                    onClick={() => setShowOriginal(true)}
                    className="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-5 py-2.5 rounded-lg transition-all duration-300 hover:shadow-lg focus:ring-2 focus:ring-blue-300 focus:outline-none"
                  >
                    <FaImage className="mr-2.5" />
                    View Original Image
                  </button>
                </div>
              ) : (
                <img
                  src={showOriginal ? originalImage : suggestions.imageUrl}
                  alt={showOriginal ? "Original Room" : "Staged Room Concept"}
                  className="object-cover w-full h-full transition-opacity duration-300"
                  onError={handleImageError}
                />
              )}
            </div>

            {/* Only show toggle button if image loaded successfully or we're showing original */}
            {(!imageLoadError || showOriginal) && (
              <button
                onClick={() => setShowOriginal(!showOriginal)}
                className="absolute bottom-4 right-4 bg-gray-800 hover:bg-gray-700 text-white font-medium px-4 py-2 rounded-lg flex items-center transition-all duration-300 hover:shadow-md focus:ring-2 focus:ring-gray-500 focus:outline-none disabled:bg-gray-600 disabled:text-gray-300 disabled:opacity-70 disabled:cursor-not-allowed disabled:hover:bg-gray-600 disabled:hover:shadow-none"
                disabled={imageLoadError && !showOriginal}
              >
                <FaExchangeAlt className="mr-2.5" />
                {showOriginal ? (imageLoadError ? 'Staged Image Unavailable' : 'View Staged') : 'View Original'}
              </button>
            )}

            <div className="absolute top-4 left-4 bg-dark-900/80 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
              {showOriginal ? 'Original Room' : `${designStyle} Style Concept`}
            </div>

            {/* Show warning if there was an error but we're viewing original */}
            {imageLoadError && showOriginal && (
              <div className="absolute top-4 right-4 bg-yellow-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium flex items-center shadow-sm">
                <FaExclamationTriangle className="mr-2" />
                Staged image unavailable
              </div>
            )}
          </div>
        ) : (
          <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
            <img
              src={originalImage}
              alt="Original Room"
              className="object-cover w-full h-full"
            />
          </div>
        )}
      </div>

      {/* Text Suggestions */}
      <div>
        <h3 className="text-lg font-medium text-primary-200 mb-3">
          {designStyle} Style Recommendations
        </h3>
        <div className="bg-dark-700/50 rounded-lg p-4 border border-primary-700/30 text-primary-200 whitespace-pre-line">
          {formattedSuggestions}
        </div>
      </div>
    </div>
  );
};

export default StagingResults;
