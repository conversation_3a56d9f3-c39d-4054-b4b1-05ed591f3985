import { rateLimitedFetch } from './rateLimitedFetch';

interface ChecklistItem {
  id: string;
  category: string;
  item: string;
  completed: boolean;
  relevantToReport: boolean;
}

export async function generateInspectionChecklist(apiKey: string, generalNotes: string): Promise<ChecklistItem[]> {
  try {
    const response = await rateLimitedFetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert home inspector assistant. Your task is to generate a comprehensive inspection checklist and then mark which items are relevant based on the inspection notes provided. Be thorough in identifying which checklist items are directly mentioned or strongly implied in the inspection notes."
          },
          {
            role: "user",
            content: `First, generate a detailed home inspection checklist organized by categories (e.g., Exterior, Roof, Plumbing, Electrical, etc.).

            Then, carefully analyze these inspection notes and mark which checklist items are relevant to the issues mentioned:

            "${generalNotes}"

            Return the result as a JSON array where each item has the following structure:
            {
              "id": "unique-id",
              "category": "Category Name",
              "item": "Description of the inspection item",
              "completed": false,
              "relevantToReport": true/false (true if this item is mentioned or implied in the inspection notes)
            }

            For any item that is marked as relevantToReport=true, also set completed=true since these items have already been inspected and documented in the report.

            Make sure to include at least 30 checklist items across different categories, covering all major aspects of a home inspection. Be thorough and specific with the checklist items.

            Organize the checklist in a logical order that a professional home inspector would follow, grouping related items together under appropriate categories.`
          }
        ],
        temperature: 0.7,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // Parse the JSON response
    try {
      const parsedContent = JSON.parse(content);
      const checklist = parsedContent.checklist || [];

      // Ensure all items have proper IDs and set completed=true for relevant items
      return checklist.map((item: any, index: number) => ({
        ...item,
        id: item.id || `item-${index}`,
        completed: item.relevantToReport ? true : (item.completed || false)
      }));
    } catch (error) {
      console.error("Error parsing OpenAI response:", error);
      throw new Error("Failed to parse the checklist data from OpenAI");
    }
  } catch (error) {
    console.error("Error generating inspection checklist:", error);
    throw error;
  }
}
